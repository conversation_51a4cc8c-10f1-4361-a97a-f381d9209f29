<?php

namespace App\Tests\Integration;

use App\Dto\ParticipationColdV1;
use App\Entity\Participant;
use App\Entity\Survey;
use App\Factory\AnswerFactory;
use App\Factory\ParticipantFactory;
use App\Factory\SurveyFactory;
use App\Form\SurveyColdAnswer1Type;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Clock\Clock;
use Symfony\Component\Clock\MockClock;
use Symfony\Component\Clock\Test\ClockSensitiveTrait;
use Zenstruck\Browser\Test\HasBrowser;
use Zenstruck\Foundry\Persistence\Proxy;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\ResetDatabase;

class ColdSurveyBrowserTest extends KernelTestCase
{
    use ResetDatabase;
    use Factories;
    use HasBrowser;
    use ClockSensitiveTrait;

    /**
     * @var Survey|mixed|object|(object&Proxy)|Proxy
     */
    private mixed $survey;

    public function getColdSurveyUrl(Participant $participant): string
    {
        return '/enquete-a-froid/'.$participant->getId().'/'.$participant->getToken();
    }

    protected function setUp(): void
    {
        parent::setUp();

        Clock::set(new MockClock('2024-12-05 12:00:00', date_default_timezone_get()));

        $this->survey = SurveyFactory::createOne([
            'label' => 'Titre de mon enquête',
            'sessionStartDate' => new \DateTimeImmutable('2024-11-28 08:00:00'),
            'sessionEndDate' => new \DateTimeImmutable('2024-11-28 17:00:00'),
            'sessionEndTime' => new \DateTimeImmutable('17:00:00'),
        ]);
    }

    public function test_fill_cold_survey(): void
    {
        $participant = ParticipantFactory::createOne([
            'survey' => $this->survey,
            'lastName' => 'Doe',
            'firstName' => 'John',
            'email' => '<EMAIL>',
            'completedHot' => true,
            'completedCold' => false,
        ]);

        $browser = $this->browser()
            ->visit($this->getColdSurveyUrl($participant))
            ->assertSeeIn('.survey-page', '1/8')
            ->assertSeeIn('legend', SurveyColdAnswer1Type::QUESTIONS['answer1'])
            ->checkField(array_key_first(ParticipationColdV1::ANSWER_1_CHOICES))
            ->click('suivant')
            ->assertSeeIn('.survey-page', '2/8')
            ->assertSeeIn('legend', SurveyColdAnswer1Type::QUESTIONS['answer2'])
            ->checkField(array_key_first(ParticipationColdV1::ANSWER_2_CHOICES))
            ->click('suivant')
        ;

        AnswerFactory::assert()->exists([
            'participant' => $participant,
            'questionId' => 'cold_1',
            'answer' => '5',
        ]);

        AnswerFactory::assert()->exists([
            'participant' => $participant,
            'questionId' => 'cold_2',
            'answer' => '5',
        ]);

        $browser
            ->assertSeeIn('.survey-page', '3/8')
            ->assertSeeIn('legend', SurveyColdAnswer1Type::QUESTIONS['answer3'])
            ->checkField(array_key_first(ParticipationColdV1::ANSWER_3_CHOICES))
            ->click('suivant')
            ->assertSeeIn('.survey-page', '4/8')
            ->assertSeeIn('label', SurveyColdAnswer1Type::QUESTIONS['answer4'])
            ->fillField(SurveyColdAnswer1Type::QUESTIONS['answer4'], 'Lorem ipsum dolor sit amet')
            ->click('suivant')
            ->assertSeeIn('.survey-page', '5/8')
            ->assertSeeIn('label', SurveyColdAnswer1Type::QUESTIONS['answer5'])
            ->fillField(SurveyColdAnswer1Type::QUESTIONS['answer5'], 'Lorem ipsum dolor sit amet')
            ->click('suivant')
            ->assertSeeIn('.survey-page', '6/8')
            ->assertSeeIn('legend', SurveyColdAnswer1Type::QUESTIONS['answer6'])
            ->checkField(array_key_first(ParticipationColdV1::ANSWER_6_CHOICES))
            ->click('suivant')
            ->assertSeeIn('.survey-page', '7/8')
            ->assertSeeIn('legend', SurveyColdAnswer1Type::QUESTIONS['answer7'])
            ->checkField(array_key_first(ParticipationColdV1::ANSWER_7_CHOICES))
            ->click('suivant')
            ->assertSeeIn('.survey-page', '8/8')
            ->assertSeeIn('legend', SurveyColdAnswer1Type::QUESTIONS['answer8'])
            ->checkField(array_key_first(ParticipationColdV1::ANSWER_8_CHOICES))
            ->click('suivant')
            ->assertSeeIn('.survey-page', '8/8')
            ->assertSeeIn('label', SurveyColdAnswer1Type::QUESTIONS['answer8comment'])
            ->fillField(SurveyColdAnswer1Type::QUESTIONS['answer8comment'], 'Lorem ipsum dolor sit amet')
            ->click('valider')
            ->assertOn($this->getColdSurveyUrl($participant).'/end')
            ->assertSeeIn('.survey-card', 'Merci')
        ;

        AnswerFactory::assert()->exists([
            'participant' => $participant,
            'questionId' => 'cold_8',
            'answer' => '1',
        ]);

        AnswerFactory::assert()->count(9, [
            'participant' => $participant,
        ]);

        ParticipantFactory::assert()->exists([
            'survey' => $this->survey,
            'lastName' => 'Doe',
            'firstName' => 'John',
            'email' => '<EMAIL>',
            'completedHot' => true,
            'completedCold' => true,
        ]);
    }

    public function test_cold_survey_participant_already_exists(): void
    {
        $participant = ParticipantFactory::createOne([
            'survey' => $this->survey,
            'lastName' => 'Doe',
            'firstName' => 'John',
            'email' => '<EMAIL>',
            'completedHot' => true,
            'completedCold' => true,
        ]);

        $browser = $this->browser()
            ->visit($this->getColdSurveyUrl($participant))
            ->assertSeeIn('.error-message', 'Vous avez déjà répondu')
        ;
    }

    public function test_navigate_backward_and_forward(): void
    {
        $participant = ParticipantFactory::createOne([
            'survey' => $this->survey,
            'lastName' => 'Doe',
            'firstName' => 'John',
            'email' => '<EMAIL>',
            'completedHot' => true,
            'completedCold' => false,
        ]);

        $browser = $this->browser()
            ->visit($this->getColdSurveyUrl($participant))
            ->assertSeeIn('.survey-page', '1/8')
            ->checkField(array_key_first(ParticipationColdV1::ANSWER_1_CHOICES))
            ->click('suivant')
            ->assertSeeIn('.survey-page', '2/8')
            ->checkField(array_key_first(ParticipationColdV1::ANSWER_2_CHOICES))
            ->click('suivant')
            ->assertSeeIn('.survey-page', '3/8')
            ->click('précédent')
            ->assertSeeIn('.survey-page', '2/8')
            ->assertChecked(array_key_first(ParticipationColdV1::ANSWER_2_CHOICES))
            ->click('précédent')
            // On revient à la page 1 et on modifie la réponse
            ->assertSeeIn('.survey-page', '1/8')
            ->assertChecked(array_key_first(ParticipationColdV1::ANSWER_1_CHOICES))
            ->checkField(array_keys(ParticipationColdV1::ANSWER_1_CHOICES)[1])
            ->click('suivant')
            ->assertSeeIn('.survey-page', '2/8')
            ->assertChecked(array_key_first(ParticipationColdV1::ANSWER_2_CHOICES))
            ->click('suivant')
            ->assertSeeIn('.survey-page', '3/8')
            ->assertNotChecked(array_key_first(ParticipationColdV1::ANSWER_3_CHOICES));

        // La réponse a été modifiée en revenant en arrière
        AnswerFactory::assert()->exists([
            'participant' => ParticipantFactory::first(),
            'questionId' => 'cold_1',
            'answer' => '4',
        ]);

        AnswerFactory::assert()->count(2, [
            'participant' => $participant,
        ]);
    }

    public function test_cold_survey_other_answer(): void
    {
        $participant = ParticipantFactory::createOne([
            'survey' => $this->survey,
            'lastName' => 'Doe',
            'firstName' => 'John',
            'email' => '<EMAIL>',
            'completedHot' => true,
            'completedCold' => false,
        ]);

        $browser = $this->browser()
            ->visit($this->getColdSurveyUrl($participant))
            ->assertSeeIn('.survey-page', '1/8')
            ->assertSeeIn('legend', SurveyColdAnswer1Type::QUESTIONS['answer1'])
            ->checkField(array_key_first(ParticipationColdV1::ANSWER_1_CHOICES))
            ->click('suivant')
            ->assertSeeIn('.survey-page', '2/8')
            ->assertSeeIn('legend', SurveyColdAnswer1Type::QUESTIONS['answer2'])
            ->checkField('Autre(s)')
            ->click('suivant')
            ->assertSeeIn('.survey-page', '2/8')
            ->assertSeeIn('label', SurveyColdAnswer1Type::QUESTIONS['answer2other'])
            ->fillField(SurveyColdAnswer1Type::QUESTIONS['answer2other'], 'Lorem ipsum dolor sit amet')
            ->click('suivant')
            ->assertSeeIn('.survey-page', '3/8')
        ;

        AnswerFactory::assert()->exists([
            'participant' => ParticipantFactory::first(),
            'questionId' => 'cold_2',
            'answer' => 'other',
        ]);

        AnswerFactory::assert()->exists([
            'participant' => ParticipantFactory::first(),
            'questionId' => 'cold_2_other',
            'answer' => 'Lorem ipsum dolor sit amet',
        ]);

        $browser
            ->click('précédent')
            ->assertSeeIn('.survey-page', '2/8')
            ->click('précédent')
            ->assertSeeIn('.survey-page', '2/8')
            ->assertChecked('Autre(s)')
            ->assertSeeIn('legend', SurveyColdAnswer1Type::QUESTIONS['answer2'])
            ->checkField(array_key_first(ParticipationColdV1::ANSWER_2_CHOICES))
            ->click('suivant')
            ->assertSeeIn('.survey-page', '3/8')
        ;
    }

    public function test_cold_survey_expired(): void
    {
        // Avec la date de fin de session à -16 jours => ça ne passe pas
        $expiredSurvey = SurveyFactory::createOne([
            'label' => 'Titre de mon enquête',
            'sessionStartDate' => new \DateTimeImmutable('2024-11-13 09:00:00'),
            'sessionEndDate' => new \DateTimeImmutable('2024-11-13 17:00:00'),
        ]);

        $participant = ParticipantFactory::createOne([
            'survey' => $expiredSurvey,
            'lastName' => 'Doe',
            'firstName' => 'John',
            'email' => '<EMAIL>',
            'completedHot' => true,
            'completedCold' => false,
        ]);

        $this->browser()
            ->visit($this->getColdSurveyUrl($participant))
            ->assertSeeIn('.error-message', 'Désolé')
        ;

        // Avec la date de fin de session à pile -15 jours => ça passe
        $notExpiredSurvey = SurveyFactory::createOne([
            'label' => 'Titre de mon enquête',
            'sessionStartDate' => new \DateTimeImmutable('2024-11-13 09:00:00'),
            'sessionEndDate' => new \DateTimeImmutable('2024-11-14 17:00:00'),
        ]);

        $participant = ParticipantFactory::createOne([
            'survey' => $notExpiredSurvey,
            'lastName' => 'Doe',
            'firstName' => 'John',
            'email' => '<EMAIL>',
            'completedHot' => true,
            'completedCold' => false,
        ]);

        $this->browser()
            ->visit($this->getColdSurveyUrl($participant))
            ->assertSeeIn('.survey-page', '1/8')
        ;
    }
}
