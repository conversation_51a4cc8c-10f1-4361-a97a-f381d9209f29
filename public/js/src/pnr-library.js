import API from './../src/api';
import <PERSON>queteur from './../src/requeteur';

var api = new API(Routing),
    requeteur = new Requeteur(api),
    templateHtml = $('#js-template-pnr').html(),
    $pnr = $('#js-pnr-container'),
    $pnrContainer = $('#js-pnr-container .js-pnr-content');

$(document).ready(function () {
    $(document).on('submit', '#form-library', function (e) {
        e.preventDefault();
        if($('.loaderPage').length) {
            $('.loaderPage').show();
        }
        $pnrContainer.html('');
        $pnr.find('.js-pnr-empty').hide();
        var pnr = requeteur.pnrList({term:$('input[name="term"]').val()});
        pnr.then(function (valeurs) {
            if(valeurs) {
                $.each(valeurs, function (_idx, item) {
                    var html = templateHtml;
                    html = html.replace(/{thumbUrl}/gi, item.image[0].low.src);
                    var $item = $($.parseHTML(html));
                    $pnrContainer.append($item);
                });
            }
            else {
                $pnr.find('.js-pnr-empty').show();
            }
            if($('.loaderPage').length) {
                $('.loaderPage').hide();
            }
        }).catch(function (err) {
            var errorMsg = "global."+err.errors[0]['message'];
            console.log(Translator.trans("global.alt"))
            $pnr.find('.js-pnr-empty').html(Translator.trans("global."+err.errors[0]['message']));
            $pnr.find('.js-pnr-empty').show();
            if($('.loaderPage').length) {
                $('.loaderPage').hide();
            }
        });
    });
});