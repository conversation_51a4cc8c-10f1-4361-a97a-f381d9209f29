import './../vendor/foundation-datepicker-master/js/foundation-datepicker';
import './../vendor/multi-select-master/js/jquery.multi-select';
import './../vendor/jquery.quicksearch';
import objectPath from 'object-path';
import moment from 'moment';

/**
* @returns {FormBuilder}
* @constructor
*/
var FormBuilder = function () {
	var that = this;
	this.operators = null;
	this.aggregations = null;
	var limit = "";

	/**
	* @param criterium
	* @param {boolean} editing
	*/
	this.createForm = function (criterium, editing) {
		var $form = $("<div></div>");
		$form.append(this.getOperatorSelect(criterium));
		if (JSON.parse(criterium.accepteAgregat)) {
			$form.append(this.getOperatorAggregationSelect(criterium.type));
		}
		if (criterium.valeurs.length) {
			criterium.valeurs.forEach(function (valeurs, i) {

				var $choices = $('<div class="select-choices"></div>');

				$form.append($choices);
				if (!editing && $('#current-selection-name').data('access_selection_import_ids')) {
					var $import = $('<div class="select-import">' +
					'<a class="btn btn--green mbl" href="#" id="select-import-show" data-active=" ' + Translator.trans('comptage.import') + '" data-inactive="' + Translator.trans('comptage.back_list') + '" style="display: none;"></a>' +
					'<div id="select-import-container" style="display: none;">' +
					'<p><i>'+ Translator.trans('js.idList')+'</i></p>' +
					'<textarea data-error=".modal-errors" class="pasted_ids" name="pasted_ids" rows="10"></textarea>' +
					'</div>' +
					'</div>');
					$form.append($import);
				}
				
				function togglePastedIdsBox(show, empty) {
					var $toggleBtn = $('#select-import-show');
					$toggleBtn.text($toggleBtn.data('active'));
					if (show) {
						$('#select-import-container').show();
						$('.select-choices').hide();
						$toggleBtn.text($toggleBtn.data('inactive'));
					} else {
						$('#select-import-container').hide();
						$('.select-choices').show();
						$toggleBtn.text($toggleBtn.data('active'));
					}
					$('label[for="pasted_ids"]').remove();
					if (empty) {
						$('.pasted_ids').empty().val('');
					}
				}
				
				setTimeout(function () {
					var $toggleBtn = $('#select-import-show');
					$toggleBtn.text($toggleBtn.data('active'));
                    $toggleBtn.show();
					$toggleBtn.off();
					$toggleBtn.click(function () {
						if (!$('#select-import-container').is(':visible')) {
							togglePastedIdsBox(true, true);
						}
						else {
							var ids = $.trim($('.pasted_ids').val());
							if (ids === "" || ids !== "" && confirm(Translator.trans('js.idLooseList'))) {
								togglePastedIdsBox(false, true);
							}
						}
					});
					
				}, 0);

				var datas = app.formBuilder.getNestedSelectDatas(valeurs.valeurs);
				
				if (!$.isArray(datas)) {
					
					var $selectRoot = that.select(
						Translator.trans('comptage.form.filters'),
						Object.keys(datas).map(function (v) {
							return {valeurReelle: v, valeurAffichee: v};
						}),
						{
							class: "listen-root listen-change"
						}
					);
					
					$form.on('change.filtreMultiSelect', '.listen-change', function () {
						var path = $(this).data('path') || "";
						path += (path === "" ? "" : ".") + $(this).val();
						var rawDatas = objectPath.get(datas, path);
						var hasChild = $.isArray(rawDatas);
						var newDatas = [];
						
						if (!hasChild) {
							newDatas = Object.keys(rawDatas).map(function (v) {
								return {valeurReelle: v, valeurAffichee: v};
							});
							
							var $new = that.select("", newDatas, {
								multiple: hasChild,
								class: !hasChild ? "listen-change" : "",
								name: !hasChild ? "" : "valeur" + (i + 1),
								"data-path": path
							});
							
							$(this).nextAll().remove();
							$(this).after($new);
							
							$('.ms-elem-selectable').addClass('visible').removeClass('hidden');
							
						} else {
							newDatas = rawDatas;
							var flatDatas = rawDatas.map(function (v) {
								return v.valeurAffichee;
							});
							if (!editing) {
								$.each($('.ms-elem-selectable'), function (index, val) {
									if (flatDatas.indexOf($(this).text()) !== -1) {
										$(this).addClass('visible');
										$(this).removeClass('hidden');
									} else {
										$(this).removeClass('visible');
										$(this).addClass('hidden');
									}
								});
							} else {
								$('[name=valeur1]').closest('label').replaceWith(that.select(Translator.trans('comptage.form.values'), newDatas, {
									multiple: false,
									name: "valeur1"
								}));
							}
						}
						
					});
					
					$choices.append($selectRoot);
				}
				
				var selectedValues = app.getSelectedValue(criterium.id);
				var clonedValues = JSON.parse(JSON.stringify(valeurs.valeurs));
				clonedValues = clonedValues.map(function (v) {
					v.valeurAffichee = v.valeurAffichee.split('<xxx>').pop();
					return v;
				});
				
				setTimeout(function() {
					if (_.differenceWith(selectedValues, clonedValues.map(function (c) { return c.valeurReelle}), _.isEqual).length && !selectedValues.find('vide')) {
					console.log('plop');
					console.dir(selectedValues);
					console.dir(clonedValues);
						togglePastedIdsBox(true, false);
						$('.pasted_ids').val(selectedValues.join("\n"));
					}
				}, 0);
				
				var $select = that.select(Translator.trans('comptage.form.values'), clonedValues, {
					multiple: !editing,
					name: "valeur" + (i + 1)
				}, {value: "valeurReelle", label: "valeurAffichee"}, selectedValues);
				$choices.append($select);
				if (!editing) {
					that.setMultiple($select);
				}
			});
		} else {
			if (criterium.type === 'periode') {
				var maxNbMonthes = 24;
				if(criterium.controleValeur == "true") {
					var moisMini = moment(criterium.valeurMinimum, "YYYYMM").format("MM");
					var anneeMini = moment(criterium.valeurMinimum, "YYYYMM").format("YYYY");
					var startDate = new Date(anneeMini + '/' + moisMini + '/01');
					var currentDate   = new Date();
					var endMoment   = moment(currentDate);
					var startMoment = moment(startDate);
					maxNbMonthes = endMoment.diff(startMoment, 'months');
				}
				var $predefinedPeriods = $('<label>' + Translator.trans('comptage.form.predefinedPeriod') + '</label>');
				if(maxNbMonthes >= 3) {
					$predefinedPeriods.append(that.button('3 ' + Translator.trans('comptage.form.predefinedPeriodLastMonth'), 'button', {
						class: "btn-edit predefinedPeriod mlm",
						'data-nbmonthes': 3
					}));
				}
				if(maxNbMonthes >= 6) {
					$predefinedPeriods.append(that.button('6 ' + Translator.trans('comptage.form.predefinedPeriodLastMonth'), 'button', {
						class: "btn-edit predefinedPeriod mlm",
						'data-nbmonthes': 6
					}));
				}
				if(maxNbMonthes >= 12) {
					$predefinedPeriods.append(that.button('12 ' + Translator.trans('comptage.form.predefinedPeriodLastMonth'), 'button', {
						class: "btn-edit predefinedPeriod mlm",
						'data-nbmonthes': 12
					}));
				}
				if(maxNbMonthes >= 24) {
					$predefinedPeriods.append(that.button('24 ' + Translator.trans('comptage.form.predefinedPeriodLastMonth'), 'button', {
						class: "btn-edit predefinedPeriod mlm",
						'data-nbmonthes': 24
					}));
				}
				$form.prepend($predefinedPeriods);
				$form.on('click.predefinedPeriod', '.predefinedPeriod', function (e) {
					var startDate = moment().subtract(parseInt($(this).data('nbmonthes')), 'month');
					var endDate = moment().subtract(1, 'month');
					$form.find('select[name=operateur]').val('[]');
					$form.find('input[name=valeur1]').val(startDate.format('MM/YYYY'));
					if ($form.find('input[name=valeur2]').length === 0) {
						that.addValeur2($(this).closest('form'), $(this).closest('form').data('criterium'));
					}
					$form.find('input[name=valeur2]').val(endDate.format('MM/YYYY'));
				});
			}
			limit = criterium.valeurMinimum;
			this.appendTypeToForm($form, criterium.type, 1, limit);

			if(typeof criterium.form !== "undefined") {
				if(typeof criterium.form.valeur2 !== "undefined") {
					this.appendTypeToForm($form, criterium.type, 2);
				}
			}
		}
		if (criterium.description.length) {
			$form.prepend('<span class="label-inline svg-info mbl"><span class="svg-info-link mrs"><svg class="svg" viewBox="0 0 31.3 21" width="20" height="20" alt=""><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="/img/sprite-svg.svg#info"></use></svg></span>'+criterium.description+'</span>');
		}
		return $form;
	};

	this.addValeur2 = function ($form, criterium) {
		$form = $form.find('div');
		//if(criterium.type == "periode" && criterium.controleValeur == "true") {
			limit = criterium.valeurMinimum;
			//var dateLimit = moment(criterium.valeurMinimum + '01', "YYYYMMDD").format('YYYY-MM-DD');
			//limit = moment(dateLimit).add(1, 'month').format("YYYYMM");
		//}

		this.appendTypeToForm($form, criterium.type, 2, limit);
	};
	
	this.appendTypeToForm = function ($form, type, index, limit) {
		limit = limit || "";
		switch (type) {
			case "string":
			$form.append(that.string("Valeur" + index, {name: "valeur" + index}));
			break;
			case "number":
			$form.append(that.number("Valeur" + index, {name: "valeur" + index}));
			break;
			case "date":
			$form.append(that.date("Valeur" + index + " (Format attendu : jj/mm/aaaa)", {name: "valeur" + index}, limit));
			break;
			case "periode":
			$form.append(that.periode("Valeur" + index + " (Format attendu : mm/aaaa)", {name: "valeur" + index}, limit));
			break;
			case "periodeQuotidienne":
			$form.append(that.periodeQuotidienne("Valeur" + index + " (format attendu : jj/mm/aaaa)", {name: "valeur" + index}, limit));
			break;
			case "boolean":
			break;
			case "string_special":
			break;
		}
	};
	
	this.removeValeur2 = function ($form) {
		$form.find('[name=valeur2]').closest('label').remove();
	};
	
	this.getNestedSelectDatas = function (values) {
		var selectDatas = {};
		if (values[0].valeurAffichee.indexOf('<xxx>') === -1) {
			return values;
		}
		values.forEach(function (value) {
			var parts = value.valeurAffichee.split('<xxx>');
			var partsCopy = JSON.parse(JSON.stringify(parts));
			partsCopy.pop();
			objectPath.push(selectDatas, partsCopy, {
				valeurReelle: value.valeurReelle,
				valeurAffichee: parts[parts.length - 1]
			});
		});
		return selectDatas;
	};
	
	/**
	* @param operator
	* @returns {jQuery}
	*/
	this.getOperatorSelect = function (criterium) {
		var operator = criterium.type;
		var currentOperator = "";
		if(typeof criterium.form !== "undefined") {
			currentOperator = criterium.form.operateur;
		}

		var op = null;
		$.each(that.operators, function (index, val) {
			if (val.type === operator) {
				op = val;
				return false;
			}
		});
		var selected = op.valeurs.filter(function (v) {return JSON.parse(v.selected);})[0];
		var $select = that.select(Translator.trans('comptage.form.operator'), op.valeurs, {name: "operateur", required: true}, {
			value: "id",
			label: "libelle"
		});

		if(currentOperator.length) {
			$select.find('[value="' + currentOperator + '"]').attr('selected', 'selected');
		}
		else {
			$select.find('[value="' + selected.id + '"]').attr('selected', 'selected');
		}
		return $select;
	};
	
	/**
	* @param operator
	* @returns {jQuery}
	*/
	this.getOperatorAggregationSelect = function (operator) {
		var op = null;
		$.each(that.aggregations, function (index, val) {
			if (val.type === operator) {
				op = val;
				return false;
			}
		});
		return that.select(Translator.trans('comptage.form.aggregation'), op.valeurs, {name: "operateurAgregat", required: true}, {
			value: "id",
			label: "libelle"
		});
	};
	
	/**
	* @param label
	* @param {Object} values
	* @param attributes
	* @param {Object=} getters
	* @param {string=} getters.value
	* @param {string=} getters.label
	* @return {jQuery}
	*/
	this.select = function (label, values, attributes, getters, selectedValues) {
		if (typeof getters === "undefined") {
			getters = {
				value: "valeurReelle",
				label: "valeurAffichee"
			}
		}
		if (typeof selectedValues === "undefined") {
			selectedValues = [];
		}
		var $select = $('<select></select>');
		if (typeof attributes !== "undefined") {
			$.each(attributes, function (index, val) {
				$select.attr(index, val);
			});
		}
		var options = "";
		if (!attributes.required && !attributes.multiple) {
			options += "<option value='' selected disabled>" + Translator.trans('comptage.form.select') + "</option>";
		}
		$.each(values, function (index, val) {
			options += '<option value="' + val[getters.value] + '"';
			if ($.inArray(val[getters.value], selectedValues) !== -1) {
				options += ' selected="selected"';
			}
			options += '>' + val[getters.label] + '</option>';
		});
		$select.append(options);
		return that.wrapLabel(label, $select);
	};
	
	this.wrapLabel = function (label, $input) {
		return $('<label>' + label + '</label>').append($input);
	};
	
	this.setMultiple = function ($select) {
		$select.multiSelect({
			cssClass: 'ms-container-custom',
			selectableHeader: '<div class="search search--2 mbs mtm">' +
			'<input id="search-1" name="search-1" type="search" class="search-input" autocomplete="off" placeholder="' + Translator.trans('global.search') + '">' +
			'<button id="btn-select-search">' +
			'<svg class="svg svg--color" viewBox="0 0 31.3 21" width="25" height="25" alt="' + Translator.trans('global.search') + '"> '+
			'<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="' + Routing.asset("img/sprite-svg.svg#search") + '"></use>' +
			'</svg>' +
			'</button>' +
			'</div>',
			selectableFooter: '<div class="text-center"><a href="#" id="select-all" class="btn btn-edit">' + Translator.trans('global.select_all') + '</a></div>',
			selectionFooter: "<div class='text-center'><a href='#' id='deselect-all' class='btn btn-edit'>" + Translator.trans('global.unselect_all') + "</a></div>",
			afterInit: function(ms) {
				var that = this;
				
				setTimeout(function () {
					that.qs1 = $('#search-1').quicksearch('.ms-elem-selectable:not(.ms-selected)', {
						'removeDiacritics': true
					});
					
					$('#select-all').click(function(e) {
						e.preventDefault();
						const selectables = [];
						$('.ms-elem-selectable').each(function(){
							if($(this).css('display') !== 'none'){
								selectables.push(this);
							}
						});
						if (selectables.length) {
							var toSelect = [];
							var texts = $.map(selectables, function(val) {
								return $(val).text();
							});
							texts.forEach(function(t){
								toSelect.push($('select').find('option:contains('+t+')').filter(function() {
									return $(this).text() === t;
								}).attr('value'));
							});
							that.$element.multiSelect('select', toSelect);
						} else {
							that.$element.multiSelect('select_all');
						}
					});
					$('#deselect-all').click(function(e) {
						e.preventDefault();
						that.$element.multiSelect('deselect_all');
						$('label[for="pasted_ids"]').remove();
					});
					
					$('#btn-select-search').click(function (e) {
						e.preventDefault();
						$("#search-1").val('').change();
						that.qs1.reset();
					});
					
					$('#search-1').bind('keyup change', function () {
						var search = $(this).val();
						var $btn = $('#btn-select-search');
						if(search === "" && $(this).data('searching')){
							$btn.find('svg').removeClass('svg--red').addClass('svg--color').setSvg('search');
							$(this).data('searching', false);
						} else if (search !== "" && !$(this).data('searching')){
							$btn.find('svg').removeClass('svg--color').addClass('svg--red').setSvg('close');
							$(this).data('searching', true);
						}
					});

					$('.ms-selectable').on('focus', '#search-1', function (e) {
						$(this).select();
					});
					
				}, 500);
				
			},
			afterSelect: function(){
				this.qs1.cache();
			},
			afterDeselect: function(){
				this.qs1.cache();
			}
		});
	};
	
	/**
	* @param type
	* @param label
	* @param attributes
	* @returns {*|jQuery|HTMLElement}
	*/
	this.input = function (type, label, attributes, limit = "") {
		limit = limit || "";
		if (attributes.class === "validation-date_fr") {
			var $input = $('<input type="' + type + '">');
		}
		else {
			var $input = $('<input required type="' + type + '">');
		}
		if (typeof attributes !== "undefined") {
			$.each(attributes, function (index, val) {
				$input.attr(index, val);
			});
		}
		
		// Création des datepicker si le navigateur ne le supporte pas
		if (attributes.class === "validation-date_fr") {
			limit = limit.length ? moment(limit, "DD/MM/YYYY").format("DD/MM/YYYY") : "01/01/1000";
			$input.attr('autocomplete', 'off');
			setTimeout(function(){
				var $dates = $('input.validation-date_fr');
				$dates.fdatepicker({
					language: 'fr',
					startDate: limit
				});
			}, 0);
		}
		
		// Création des datepicker si le navigateur ne le supporte pas
		if (attributes.class === "validation-period") {
			limit = limit.length ? moment(limit, "YYYYMM").format("MM/YYYY") : "01/1000";
			$input.attr('autocomplete', 'off');
			setTimeout(function(){
				var $dates = $('input.validation-period');
				$dates.fdatepicker({
					language: 'fr',
					startDate: limit
				});
			}, 0);
		}
		
		// Création des datepicker si le navigateur ne le supporte pas
		if (attributes.class === "validation-period-quotidienne") {
			limit = limit.length ? moment(limit, "DD/MM/YYYY").format("DD/MM/YYYY") : "01/01/1000";
			$input.attr('autocomplete', 'off');
			setTimeout(function(){
				var $dates = $('input.validation-period-quotidienne');
				$dates.fdatepicker({
					language: 'fr',
					startDate: limit
				});
			}, 0);
		}
		
		return that.wrapLabel(label, $input);
	};
	
	/**
	* @param label
	* @param attributes
	* @returns {*|jQuery|HTMLElement}
	*/
	this.string = function (label, attributes) {
		return this.input('text', label, attributes);
	};
	
	/**
	* @param label
	* @param attributes
	* @returns {*|jQuery|HTMLElement}
	*/
	this.number = function (label, attributes) {
		return this.input('number', label, attributes);
		
	};
	
	/**
	* @param label
	* @param attributes
	* @returns {*|jQuery|HTMLElement}
	*/
	this.date = function (label, attributes, limit = "") {
		limit = limit || "";
		attributes.class = "validation-date_fr";
		attributes['data-date-format'] = "dd/mm/yyyy";
		return this.input('text', label, attributes, limit);
	};
	
	/**
	* @param label
	* @param attributes
	* @returns {*|jQuery|HTMLElement}
	*/
	this.periode = function (label, attributes, limit = "") {
		limit = limit || "";
		attributes['class'] = "validation-period";
		attributes['data-date-format'] = "mm/yyyy";
		attributes['data-start-view'] = "year";
		attributes['data-min-view'] = "year";
		return this.input('text', label, attributes, limit);
	};
	
	/**
	* @param label
	* @param attributes
	* @returns {*|jQuery|HTMLElement}
	*/
	this.periodeQuotidienne = function (label, attributes, limit = "") {
		limit = limit || "";
		attributes['class'] = "validation-period-quotidienne";
		attributes['data-date-format'] = "dd/mm/yyyy";
		return this.input('text', label, attributes, limit);
	};
	
	/**
	* @param text
	* @param type
	* @param attributes
	* @returns {*|jQuery|HTMLElement}
	*/
	this.button = function (text, type, attributes) {
		var $button = $('<button type="' + type + '"></button>');
		if (typeof attributes !== "undefined") {
			$.each(attributes, function (index, val) {
				$button.attr(index, val);
			});
		}
		$button.append(text);
		return $button;
	};
	
	return this;
};
export default FormBuilder;