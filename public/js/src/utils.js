// Permet de prendre en compte les champs disabled lors de la serialization d'un formulaire
var proxy = $.fn.serializeArray;

$.fn.serializeArray = function(){
	var inputs = this.find(':disabled');
	inputs.prop('disabled', false);
	var serialized = proxy.apply( this, arguments );
	inputs.prop('disabled', true);
	return serialized;
};


// Transforme les champs d'un formulaire en objet
$.fn.serializeObject = function () {
	var o = {};
	var a = this.serializeArray();
	$.each(a, function () {
		if (o[this.name] !== undefined) {
			if (!o[this.name].push) {
				o[this.name] = [o[this.name]];
			}
			o[this.name].push(this.value || '');
		} else {
			o[this.name] = this.value || '';
		}
	});
	return o;
};

$.fn.setSvg = function (icon) {
	var s = $(this).find('use').attr('xlink:href');
	var n = s.indexOf('#');
	s = s.substring(0, n != -1 ? n : s.length);
	s += "#" + icon;
	$(this).find('use').attr('xlink:href', s);
};

$.fn.display = function (bool) {
	if (bool) {
		$(this).show();
	} else {
		$(this).hide();
	}
};

$.fn.toggleClasses = function (class1, class2) {
	if ($(this).hasClass(class1)) {
		$(this).removeClass(class1).addClass(class2);
	} else {
		$(this).addClass(class1).removeClass(class2);
	}
};

/**
 * @param {function} func
 */
$.fn.replaceTemplate = function (func) {
	var $this = $(this);
	var id = $this.attr('id');
	var $newTemplate = $($this.clone().html());
	func.apply($newTemplate);
	$this.replaceWith('<template id="'+ id + '">' + $newTemplate[0].outerHTML + '</template>');
};

/**
 * @param {function} func
 */
// all HTML4 entities as defined here: http://www.w3.org/TR/html4/sgml/entities.html
// added: lt, gt, quot and apos
var escapeHtmlEntities = {
  34 : 'quot',
  38 : 'amp',
  39 : 'apos',
  60 : 'lt',
  62 : 'gt',
  160 : 'nbsp',
  161 : 'iexcl',
  162 : 'cent',
  163 : 'pound',
  164 : 'curren',
  165 : 'yen',
  166 : 'brvbar',
  167 : 'sect',
  168 : 'uml',
  169 : 'copy',
  170 : 'ordf',
  171 : 'laquo',
  172 : 'not',
  173 : 'shy',
  174 : 'reg',
  175 : 'macr',
  176 : 'deg',
  177 : 'plusmn',
  178 : 'sup2',
  179 : 'sup3',
  180 : 'acute',
  181 : 'micro',
  182 : 'para',
  183 : 'middot',
  184 : 'cedil',
  185 : 'sup1',
  186 : 'ordm',
  187 : 'raquo',
  188 : 'frac14',
  189 : 'frac12',
  190 : 'frac34',
  191 : 'iquest',
  192 : 'Agrave',
  193 : 'Aacute',
  194 : 'Acirc',
  195 : 'Atilde',
  196 : 'Auml',
  197 : 'Aring',
  198 : 'AElig',
  199 : 'Ccedil',
  200 : 'Egrave',
  201 : 'Eacute',
  202 : 'Ecirc',
  203 : 'Euml',
  204 : 'Igrave',
  205 : 'Iacute',
  206 : 'Icirc',
  207 : 'Iuml',
  208 : 'ETH',
  209 : 'Ntilde',
  210 : 'Ograve',
  211 : 'Oacute',
  212 : 'Ocirc',
  213 : 'Otilde',
  214 : 'Ouml',
  215 : 'times',
  216 : 'Oslash',
  217 : 'Ugrave',
  218 : 'Uacute',
  219 : 'Ucirc',
  220 : 'Uuml',
  221 : 'Yacute',
  222 : 'THORN',
  223 : 'szlig',
  224 : 'agrave',
  225 : 'aacute',
  226 : 'acirc',
  227 : 'atilde',
  228 : 'auml',
  229 : 'aring',
  230 : 'aelig',
  231 : 'ccedil',
  232 : 'egrave',
  233 : 'eacute',
  234 : 'ecirc',
  235 : 'euml',
  236 : 'igrave',
  237 : 'iacute',
  238 : 'icirc',
  239 : 'iuml',
  240 : 'eth',
  241 : 'ntilde',
  242 : 'ograve',
  243 : 'oacute',
  244 : 'ocirc',
  245 : 'otilde',
  246 : 'ouml',
  247 : 'divide',
  248 : 'oslash',
  249 : 'ugrave',
  250 : 'uacute',
  251 : 'ucirc',
  252 : 'uuml',
  253 : 'yacute',
  254 : 'thorn',
  255 : 'yuml',
  402 : 'fnof',
  913 : 'Alpha',
  914 : 'Beta',
  915 : 'Gamma',
  916 : 'Delta',
  917 : 'Epsilon',
  918 : 'Zeta',
  919 : 'Eta',
  920 : 'Theta',
  921 : 'Iota',
  922 : 'Kappa',
  923 : 'Lambda',
  924 : 'Mu',
  925 : 'Nu',
  926 : 'Xi',
  927 : 'Omicron',
  928 : 'Pi',
  929 : 'Rho',
  931 : 'Sigma',
  932 : 'Tau',
  933 : 'Upsilon',
  934 : 'Phi',
  935 : 'Chi',
  936 : 'Psi',
  937 : 'Omega',
  945 : 'alpha',
  946 : 'beta',
  947 : 'gamma',
  948 : 'delta',
  949 : 'epsilon',
  950 : 'zeta',
  951 : 'eta',
  952 : 'theta',
  953 : 'iota',
  954 : 'kappa',
  955 : 'lambda',
  956 : 'mu',
  957 : 'nu',
  958 : 'xi',
  959 : 'omicron',
  960 : 'pi',
  961 : 'rho',
  962 : 'sigmaf',
  963 : 'sigma',
  964 : 'tau',
  965 : 'upsilon',
  966 : 'phi',
  967 : 'chi',
  968 : 'psi',
  969 : 'omega',
  977 : 'thetasym',
  978 : 'upsih',
  982 : 'piv',
  8226 : 'bull',
  8230 : 'hellip',
  8242 : 'prime',
  8243 : 'Prime',
  8254 : 'oline',
  8260 : 'frasl',
  8472 : 'weierp',
  8465 : 'image',
  8476 : 'real',
  8482 : 'trade',
  8501 : 'alefsym',
  8592 : 'larr',
  8593 : 'uarr',
  8594 : 'rarr',
  8595 : 'darr',
  8596 : 'harr',
  8629 : 'crarr',
  8656 : 'lArr',
  8657 : 'uArr',
  8658 : 'rArr',
  8659 : 'dArr',
  8660 : 'hArr',
  8704 : 'forall',
  8706 : 'part',
  8707 : 'exist',
  8709 : 'empty',
  8711 : 'nabla',
  8712 : 'isin',
  8713 : 'notin',
  8715 : 'ni',
  8719 : 'prod',
  8721 : 'sum',
  8722 : 'minus',
  8727 : 'lowast',
  8730 : 'radic',
  8733 : 'prop',
  8734 : 'infin',
  8736 : 'ang',
  8743 : 'and',
  8744 : 'or',
  8745 : 'cap',
  8746 : 'cup',
  8747 : 'int',
  8756 : 'there4',
  8764 : 'sim',
  8773 : 'cong',
  8776 : 'asymp',
  8800 : 'ne',
  8801 : 'equiv',
  8804 : 'le',
  8805 : 'ge',
  8834 : 'sub',
  8835 : 'sup',
  8836 : 'nsub',
  8838 : 'sube',
  8839 : 'supe',
  8853 : 'oplus',
  8855 : 'otimes',
  8869 : 'perp',
  8901 : 'sdot',
  8968 : 'lceil',
  8969 : 'rceil',
  8970 : 'lfloor',
  8971 : 'rfloor',
  9001 : 'lang',
  9002 : 'rang',
  9674 : 'loz',
  9824 : 'spades',
  9827 : 'clubs',
  9829 : 'hearts',
  9830 : 'diams',
  338 : 'OElig',
  339 : 'oelig',
  352 : 'Scaron',
  353 : 'scaron',
  376 : 'Yuml',
  710 : 'circ',
  732 : 'tilde',
  8194 : 'ensp',
  8195 : 'emsp',
  8201 : 'thinsp',
  8204 : 'zwnj',
  8205 : 'zwj',
  8206 : 'lrm',
  8207 : 'rlm',
  8211 : 'ndash',
  8212 : 'mdash',
  8216 : 'lsquo',
  8217 : 'rsquo',
  8218 : 'sbquo',
  8220 : 'ldquo',
  8221 : 'rdquo',
  8222 : 'bdquo',
  8224 : 'dagger',
  8225 : 'Dagger',
  8240 : 'permil',
  8249 : 'lsaquo',
  8250 : 'rsaquo',
  8364 : 'euro'
};
String.prototype.htmlSpecialChars = function () {
  return this.replace(/[\u00A0-\u2666<>\&]/g, function(c) {
    return '&' + (escapeHtmlEntities[c.charCodeAt(0)] || '#'+c.charCodeAt(0)) + ';';
  });
};

export function imageLinkIsNot404(url) {
	return new Promise(function imgPromise(resolve, reject) {
		var imgElement = new Image();
		imgElement.addEventListener('load', function () {
			resolve(this);
		});
		imgElement.addEventListener('error', function () {
			reject();
		})
		imgElement.src = url;
	});
}

export function formatDateTime(dateTime) {
    if(dateTime !== "") {
        var dateTimeObj = new Date(dateTime);
        if(dateTime == "now") {
            dateTimeObj = new Date();
        }
        var formattedDateTime = ('0' + dateTimeObj.getDate()).slice(-2)
			+ '/' + ('0' + (dateTimeObj.getMonth() + 1)).slice(-2)
			+ '/' + dateTimeObj.getFullYear() + ' '
			+ ('0' + dateTimeObj.getHours()).slice(-2) + ':'
			+ ('0' + dateTimeObj.getMinutes()).slice(-2);
        return formattedDateTime;
    }
    return false;
}

export function numberFormat(number, isFloat) {
    isFloat = isFloat || false;
    number = number.toString().replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1 ");
    if (isFloat) {
        number = number.replace(".", ",");
    }
    return number;
}

export function arrayColumn(array, columnName) {
    return array.map(function(value,index) {
        return value[columnName];
    })
}

export function arrayUnique(array){
    return array.filter(function(el, index, arr) {
        return index == arr.indexOf(el);
    });
}

export function accents(s){
    var r = s.toLowerCase();
    r = r.replace(new RegExp("\\s", 'g'),"");
    r = r.replace(new RegExp("[àáâãäå]", 'g'),"a");
    r = r.replace(new RegExp("æ", 'g'),"ae");
    r = r.replace(new RegExp("ç", 'g'),"c");
    r = r.replace(new RegExp("[èéêë]", 'g'),"e");
    r = r.replace(new RegExp("[ìíîï]", 'g'),"i");
    r = r.replace(new RegExp("ñ", 'g'),"n");
    r = r.replace(new RegExp("[òóôõö]", 'g'),"o");
    r = r.replace(new RegExp("œ", 'g'),"oe");
    r = r.replace(new RegExp("[ùúûü]", 'g'),"u");
    r = r.replace(new RegExp("[ýÿ]", 'g'),"y");
    r = r.replace(new RegExp("\\W", 'g'),"");
    return r;
};

export function displaySpanErrorForSearch(li, spanError){
    if ($(li).length === 0) {
        $(spanError).css('display', 'block');
    } else {
        $(spanError).css('display', 'none');
    }
}

export function buttonSearchState($btn, search, $this){
    if (!search.length && $this.data('searching')) {
        $btn.find('svg').removeClass('svg--red').addClass('svg--blue').setSvg('search');
        $this.data('searching', false);
    } else if (search.length && !$this.data('searching')) {
        $btn.find('svg').removeClass('svg--blue').addClass('svg--red').setSvg('close');
        $this.data('searching', true);
    }
}

export function pourcentToUnits(pourcent, total) {
    var pourcentCalcule = pourcent + 1;
    var taillePourcent = total / pourcentCalcule;
    var unites = total - taillePourcent;
    if(pourcent >= 100) {
        unites = total;
    }
    return Math.floor(unites);
}

export function pushMatomo(mTitle) {
    if(typeof _paq !== "undefined") {
        _paq.push(['setDocumentTitle', mTitle]);
        _paq.push(['trackPageView']);
    }
}

export function pushGoalMatomo(typeOp, budget) {
    if(typeof _paq !== "undefined") {
        if(window.location.href.indexOf("demo") === -1 && window.location.href.indexOf("preprod-prod") === -1) {
            if(typeOp == "0") {
                typeOp = 3;
            }
            if(typeOp == "1") {
                typeOp = 4;
            }
            if(typeOp == "2") {
                typeOp = 5;
            }
            _paq.push(['trackGoal', typeOp, budget]);
        }
    }
}

export function addMonths(date, months) {
    var d = date.getDate();
    date.setMonth(date.getMonth() + +months);
    if (date.getDate() != d) {
      date.setDate(0);
    }
    return date;
}
