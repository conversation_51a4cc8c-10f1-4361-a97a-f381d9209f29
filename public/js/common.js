import API from './src/api';
import <PERSON>queteur from './src/requeteur';
import ModalFactory from './src/modal';
import {RequeteurError} from './src/api';
import svg4everybody from 'svg4everybody';
import 'jquery-ui/ui/widgets/datepicker';
import {pushGoalMatomo,pushMatomo} from './src/utils';

import RequeteurWallet from './src/requeteur-wallet';

var api = new API(Routing),
    requeteur = new Requeteur(api),
    modalFactory = new ModalFactory();

var pageTitle = document.title;
var pageMTitle = $("#matomo-vars").attr("data-mtitle");

if($("template#session-expiree-tpl").length) {
    var timer = setInterval(function() {
        if(!$("#modal-userSession").length) {
            $.ajax({
                url: Routing.generate('aquitem_requeteur_check_user_session'),
                error: function (xhr, ajaxOptions, thrownError) {
                    if (xhr.status == 401) {
                        clearInterval(timer);
                        var modalSession = modalFactory.create({
                            title: Translator.trans("global.sessionExpiree"),
                            body: $("#session-expiree-tpl").html(),
                            id: "userSession",
                            validateBtn: "",
                            cancelBtn: Translator.trans("global.connexion"),
                            onClose: function () {
                                $(".loaderWindow").height('');
                                $('.loaderWindow').show();
                                window.location.reload();
                            }
                        });
                    }
                }
            });
        }
    },600000);
}

if($('#language')[0] !== undefined) {
    if ($('#language')[0].dataset.langue == "en") {
        var help = false;
        import('jquery-ui/ui/i18n/datepicker-en-AU');
    } else {
        var help = true;
        import('jquery-ui/ui/i18n/datepicker-fr');
    }
};

Foundation.addToJquery($);

jQuery.cachedScript = function( url, options ) {

  // Allow user to set any option except for dataType, cache, and url
  options = $.extend( options || {}, {
    dataType: "script",
    cache: true,
    url: url
  });

  // Use $.ajax() since it is more flexible than $.getScript
  // Return the jqXHR object so we can chain callbacks
  return jQuery.ajax( options );
};

export function closeMenu() {
    $('#globalNav').removeClass('targeted');
    // Si le navigateur est IE
    if((navigator.userAgent.indexOf("MSIE") != -1 ) || (!!document.documentMode == true )){
        $('.header-1-link2').find('.svg--menu').css('display', 'block');
        $('.header-1-link2').find('.svg--menu-close').css('display', 'none');
        $('.header-1-link2').removeClass('header-1-link2--close');
    } else {
        $('.header-1-link2').find('svg').setSvg('menu');
        /* $('.header-1-link2').find('svg').attr('width', '25').attr('height', '25'); */
        $('.header-1-link2').removeClass('header-1-link2--close');
    }
};

$(document).on("click", function(e){
    if(!$(e.target).closest('.toggleOpener').length) {
        $('.toggle-target').filter(':visible').stop(true, true).slideUp();
    }
});

export function expand(el, selector)
{
	let id = el.attr('id');
    let opener = el.attr('aria-controls');
    var $target = $('#' + opener).attr('aria-hidden', el.attr('aria-expanded')).stop(true).slideToggle();

    if(el.closest('#globalNavExtended').length || el.closest('#mobileNav').length || el.closest('#criterium-list').length) {
        $('.toggle-subtarget').not($target).filter(':visible').stop(true, true).slideUp();
    }
    else {
        $(selector).each(function(el) {
            if(opener !== $(this).attr('aria-controls')) {
                $(this).removeClass('is-active');
            }
        });
        $('.toggle-target').not($target).filter(':visible').stop(true, true).slideUp();
    }
    if(el.closest('#mobileNav').length) {
        $(selector).each(function(el) {
            $(this).removeClass('is-active');
        });
    }

    let state = el.attr('aria-expanded') === 'true' ? false : true;
    el.attr('aria-expanded', state).toggleClass('is-active');

//     if(el.find('i').hasClass('fa-angle-down'))
//         el.find('i').toggleClass('fa-angle-up');
	if(!state) {
		el.find('i').addClass('fa-angle-down').removeClass('fa-angle-up');
    }
    else {
		el.find('i').addClass('fa-angle-up').removeClass('fa-angle-down');
    }
}

$(document).on('ready', function(){
    $(document).foundation();

    let selector = '.toggleOpener';
    $(document).on('click', selector, function (e) {

        expand($(this), selector);

        if($(this).hasClass('notification-1')) {
            if($('#header').data('simulateur') == "0") {
                localStorage.setItem('locale-notification-1', $('.notification').data('local-notification1'));
            }
            else {
                localStorage.setItem('local-admin-notification-1', $('.notification').data('local-notification1'));
            }
            $(this).removeClass('notification-1').removeClass('notification-2').addClass('notification-0');
        }
        else {
            if($('#header').data('simulateur') == "0") {
                localStorage.setItem('locale-notification-2', $('.notification').data('local-notification2'));
            }
            else {
                localStorage.setItem('local-admin-notification-2', $('.notification').data('local-admin-notification2'));
            }
            $(this).removeClass('notification-2').addClass('notification-0');
        }
    });

    $('#header').on('click', 'a[data-action="read-more-info"]', function (e) {
        let idNews = $(this).data('id'),
            lang = $(this).data('lang'),
            modal = modalFactory.create({
            mustacheTemplate: $('#simple-modal').html(),
            body: $('#detail-news-tpl').html(),
            crossForCancel: true,
            submitOnEnter: false,
            onOpened: function() {
                this.$body.find('.loaderModal').show();
                let that = this;
                $.ajax({
                    url: Routing.generate('aquitem_requeteur_notification_preview', {'id': idNews, 'lang': lang}),
                    type: "GET",
                })
                .done(function (response) {
                    let news = JSON.parse(response);
                    let $start_date = that.$body.find('[data-field="start_date"]');
                    let $title = that.$body.find('[data-field="title"]');
                    let $heading = that.$body.find('[data-field="heading"]');
                    let $youtube_link = that.$body.find('[data-field="youtube_link"]');
                    let $content = that.$body.find('[data-field="content"]');
                    var date = new Date(news.startDate);
                    $start_date.html($.datepicker.formatDate('d',date) + ' ' + Translator.trans("global.mois." + $.datepicker.formatDate('m',date)).toLowerCase() + " " + $.datepicker.formatDate('yy',date));
                    $title.html(news.title);
                    if (news.heading) {
                        $heading.html(news.heading);
                    }
                    if(news.youtubeLink) {
                        $youtube_link.parent().removeClass('hide');
                        $youtube_link.find('iframe').attr('src', news.youtubeLink);
                    }
                    if (news.content) {
                        $content.html(news.content);
                    }
                    that.$body.find('.loaderModal').hide();
                });
            }
        });
    });


    /* choix du type de règle à creer */
    $('.regle-creation').on('click', '.regle-bloc-select-type', function (e) {
        e.preventDefault();
        $('.regle-bloc-select').find('span.regle-bloc-nb').addClass('hide');
        $(this).find('span.regle-bloc-nb').removeClass('hide');
        $('.regle-bloc-select').removeClass('btn--moema');
        $(this).addClass('btn--moema');
        $('.regle-bloc-select').parent().removeClass('active');
        $(this).parent().toggleClass('active');
    });
    $('.regle-creation').on('click', '.regle-bloc-select-type', function (e) {
        e.preventDefault();
        var typeRegle = $(this).attr('data-type-regle');
        if(typeRegle == "avantage-fid") {
            if($('template#session-en-cours-tpl').length) {
                var modal = modalFactory.create({
                    title: Translator.trans("regles.creation.titles.promoEnCours"),
                    body: $('#session-en-cours-tpl').html(),
                    validateBtn: Translator.trans("global.erase"),
                    cancelBtn: Translator.trans("global.close"),
                    onSubmit: function () {
                        location.href = Routing.generate('aquitem_requeteur_regle_reset_session', {type: typeRegle, resetAll: 2});
                    },
                });
            }
            else {
                var idSession = $(this).data('id-session');
                location.href = Routing.generate('aquitem_requeteur_regle_etapes', {type: "avantage-fid", idSession: idSession}, true);
            }
        }
        else {
            location.href = Routing.generate('aquitem_requeteur_regle_etapes', {type: typeRegle}, true);
        }
    });

    $('#img-assign').on('click', '.fileLink', function (e) {
        var key = $(this).data('key');
        var url = Routing.generate('aquitem_requeteur_ckeditor_browse_file', {}, true);
        window.open(url + '?key=' + key, 'files', "width=1200, height=600");
    });

    // carte wallet
    if($("#carte-wallet-en-cours-tpl").length) {
        $(".btn-nouvelle-carte-wallet").on('click', function(e) {
            e.preventDefault();
            var validateBtn = Translator.trans("wallet.labels.voirCarte");
            var cancelBtn = Translator.trans("global.cancel");
            if($('#cartes-wallet').length) {
                validateBtn = "";
                cancelBtn = Translator.trans("global.close");
            }
            var modal = modalFactory.create({
                title: Translator.trans("menu.nouvelleCarteWallet"),
                body: $("#carte-wallet-en-cours-tpl").html(),
                validateBtn: validateBtn,
                cancelBtn: cancelBtn,
                onSubmit: function() {
                    $('.loaderWindow').show()
                    location.href = Routing.generate('aquitem_requeteur_carte_wallet_liste', {}, true);
                },
            });
        });
    }

    //notification
    if($('.notification').length) {
        if($('.notification').attr('data-local-notification1').length) {
            if(($('#header').data('simulateur') == "0" && $('.notification').attr('data-local-notification1') != localStorage.getItem('locale-notification-1'))
                || ($('#header').data('simulateur') == "1" && $('.notification').attr('data-local-notification1') != localStorage.getItem('local-admin-notification-1'))) {
                $(('.notification')).removeClass('notification-0').removeClass('notification-2').addClass('notification-1');
            }
        }
        if($('#header').data('simulateur') == "0") {
            if(!$('.notification').attr('data-local-notification1').length && $('.notification').attr('data-local-notification2')) {
                let nbNotifs = $('.notification').attr('data-local-notification2').length ? parseInt($('.notification').attr('data-local-notification2')) : 0;
                let nbLocalNotifs = localStorage.getItem('locale-notification-2') ? parseInt(localStorage.getItem('locale-notification-2')) : 0;
                if(nbNotifs > nbLocalNotifs) {
                    $(('.notification')).removeClass('notification-0').removeClass('notification-1').addClass('notification-2');
                }
            }
        } else {
            if(!$('.notification').attr('data-local-notification1').length && $('.notification').attr('data-local-admin-notification2')) {
                let nbAdminNotifs = $('.notification').attr('data-local-admin-notification2').length ? parseInt($('.notification').attr('data-local-admin-notification2')) : 0;
                let nbLocalAdminNotifs = localStorage.getItem('local-admin-notification-2') ? parseInt(localStorage.getItem('local-admin-notification-2')) : 0;
                if(nbAdminNotifs > nbLocalAdminNotifs) {
                    $(('.notification')).removeClass('notification-0').removeClass('notification-1').addClass('notification-2');
                }
            }
        }
    }

    // Si le navigateur est IE
    if((navigator.userAgent.indexOf("MSIE") != -1 ) || (!!document.documentMode == true )){
        $('.header-1-link2').append(
            '<svg class="svg svg--menu svg--menu-close" style="display:none" viewBox="0 0 31.3 21" width="20" height="20" alt="ouverture du menu">\n' +
            '<path id="closemenu" d="m 19.932865,11.200639 c -0.273973,-0.273973 -0.273973,-0.684932 0,-0.958905 L 30.206838,-0.03223807 c 0.273973,-0.2739726 0.410959,-0.6849315 0.410959,-0.9589041 0,-0.27397263 -0.136986,-0.68493153 -0.410959,-0.95890423 l -1.917808,-1.917808 c -0.273973,-0.273973 -0.684932,-0.410959 -0.958904,-0.410959 -0.410959,0 -0.684932,0.136986 -0.958905,0.410959 L 16.097249,6.406118 c -0.273973,0.2739726 -0.684932,0.2739726 -0.958904,0 L 4.8643717,-3.8678544 c -0.273972,-0.273973 -0.684931,-0.410959 -0.958904,-0.410959 -0.273972,0 -0.684931,0.136986 -0.958904,0.410959 l -1.917808,1.917808 c -0.27397304,0.2739727 -0.41095904,0.6849316 -0.41095904,0.95890423 0,0.2739726 0.136986,0.6849315 0.41095904,0.9589041 L 11.302728,10.241734 c 0.273973,0.273973 0.273973,0.684932 0,0.958905 L 1.0287557,21.474612 c -0.27397304,0.273972 -0.41095904,0.684931 -0.41095904,0.958904 0,0.273972 0.136986,0.684931 0.41095904,0.958904 l 1.917808,1.917808 c 0.273973,0.273973 0.684932,0.410959 0.958904,0.410959 0.273973,0 0.684932,-0.136986 0.958904,-0.410959 L 15.138345,15.036255 c 0.273972,-0.273973 0.684931,-0.273973 0.958904,0 l 10.273972,10.273973 c 0.273973,0.273973 0.684932,0.410959 0.958905,0.410959 0.273972,0 0.684931,-0.136986 0.958904,-0.410959 l 1.917808,-1.917808 c 0.273973,-0.273973 0.410959,-0.684932 0.410959,-0.958904 0,-0.273973 -0.136986,-0.684932 -0.410959,-0.958904 L 19.932865,11.200639 Z" inkscape:connector-curvature="0"></path>\n' +
            '</svg>'
        );
        $('#globalNav .sidebar-nav-item').find('svg').css('display', 'none');
    }

    $('.deconnexion').on("click", function(e){
        if ($(this).data('simul') == "1") {
            localStorage.removeItem('locale-notification-1');
            localStorage.removeItem('locale-notification-2');
        }
    });

	$('.header-1-link2').click(function (e) {
		e.preventDefault();
		$('#globalNav').toggleClass('targeted');
        // Si le navigateur est IE
        if((navigator.userAgent.indexOf("MSIE") != -1 ) || (!!document.documentMode == true )){
            if($('#globalNav').hasClass('targeted')) {
                $(this).find('.svg--menu').css('display', 'none');
                $(this).find('.svg--menu-close').css('display', 'block');
                $(this).addClass('header-1-link2--close');
            }
            else {
                $(this).find('.svg--menu').css('display', 'block');
                $(this).find('.svg--menu-close').css('display', 'none');
                $(this).removeClass('header-1-link2--close');
            }
        }
        else {
            if($('#globalNav').hasClass('targeted')) {
                $(this).find('svg').setSvg('closemenu');
                $(this).find('svg').attr('width', '20').attr('height', '20');
                $(this).addClass('header-1-link2--close');
            }
            else {
                $(this).find('svg').setSvg('menu');
                $(this).find('svg').attr('width', '25').attr('height', '25');
                $(this).removeClass('header-1-link2--close');
            }
        }
		return false;
	});

	// Sticky
	var stickyNavTop = 0;
	var $stickyBox = $('.stickyBox');
	if ($stickyBox.length) {
		stickyNavTop = $stickyBox.parent().parent().offset().top;
	}

	var stickyNav = function(){
		var scrollTop = $(window).scrollTop();
		if (scrollTop > stickyNavTop) {
			$stickyBox.addClass('sticky');
		} else {
			$stickyBox.removeClass('sticky');
		}
	};

	stickyNav();

	$(window).scroll(function() {
		stickyNav();
	});

	let LEGACY_SUPPORT = true;
	svg4everybody();

	$( ".selector" ).datepicker({
		showOn: "both",
		buttonImage: "/../img/datepicker.gif",
        dateFormat: "dd/mm/yy",
        changeYear: true,
	});

    $("#login-req").on('submit', function (e) {
        window.localStorage.clear();
    });

    /* creer une opération */
    $('.btn-creer-operation').on('click', function (e) {
        e.preventDefault();
        // balise title
        document.title = Translator.trans("menu.creerOperation");
        $("#matomo-vars").attr("data-mtitle", "Nouvelle opération");
        pushMatomo("Nouvelle opération");
        var $btn = $(this),
            modal = modalFactory.create({
            title: Translator.trans("operations.actions.newOperation"),
            body: $('#operation-create-form-tpl').html(),
            validateBtn: Translator.trans("global.validate"),
            submitOnEnter: false,
            id: "creation-operation",
            errorText: "",
            hash: true,
            showHelp: help,
            onOpened: function () {
                if($btn.closest('.page-operation').length) {
                    var typeOp = $('.page-operation').attr('data-operation-type');
                    this.$body.find('.save-operation-types input[value="' + typeOp + '"]').prop('checked', true);
                }
            },
            onSubmit: function () {
                var limitNomOp = this.$body.find('#save-operation-name').data('maxlength');
                if(limitNomOp != "") {
                    limitNomOp = parseInt(limitNomOp);
                }
                else {
                    limitNomOp = 255;
                }
                var serializedForm = $('#operation-create-form').serializeObject();
                var lengthNomOp = this.$body.find('#save-operation-name').val().length;
                if(lengthNomOp > limitNomOp) {
                    var erreurs = [];
                    erreurs.push({"message": Translator.trans("operations.actions.errorLimitNomOp", {"limit": limitNomOp})});
                    var error = new RequeteurError(erreurs);
                    this.addErrors(error);
                    this.onError(error);
                }
                else {
                    var datas = {
                        libelleOperation: this.$body.find('#save-operation-name').val(),
                        descriptionOperation: this.$body.find('#save-operation-desc').val()
                    };
                    if(serializedForm['save-operation-type']) {
                        datas.typeOperation = parseInt(serializedForm['save-operation-type']);
                    }
                    var op = requeteur.createOperation(datas);
                    op.then(function (response) {
                        pushGoalMatomo(8, 0);
                        if($('#operation-create-form').data('hors-fid') == "1") {
                            location.href = Routing.generate('aquitem_requeteur_operation_selection_creation_hors_fid', {idOperation: response.id}, true);
                        }
                        else {
                            location.href = Routing.generate('aquitem_requeteur_operation_selection_creation', {idOperation: response.id}, true);
                        }
                    }).catch(function (err) {
                        var modal = modalFactory.create({
                            onOpened: function () {
                                var erreurs = [];
                                erreurs.push({"message": err.errors[0].message});
                                var error = new RequeteurError(erreurs);
                                    this.addErrors(error);
                                    this.onError(error);
                            },
                            onSuccess: function () {
                                this.$closeBtn.focus();
                            },

                        });
                    }),
                    op.catch(function (err) {
                        console.log(err)
                    });
                    this.endLoading();
                }
            },
            onClose: function () {
                // balise title
                document.title = pageTitle;
                $("#matomo-vars").attr("data-mtitle", pageMTitle);
                pushMatomo(pageMTitle);
            }
        });
    });


    /* creer une opération WALLET */
	$('.btn-creer-operation-wallet').on('click', function (e) {
		e.preventDefault();
        var requeteurWallet = new RequeteurWallet(api);
		// balise title
		document.title = Translator.trans("menu.creerOperationWallet");
		modalFactory.create({
			title: Translator.trans("operationsWallet.titles.newOperationWallet"),
			body: $('#operation-wallet-create-form-tpl').html(),
			validateBtn: Translator.trans("global.validate"),
			submitOnEnter: false,
			id: "creation-operation",
			errorText: "",
			hash: true,
			onSubmit: function () {
				var limitNomOp = this.$body.find('#save-operation-name').data('maxlength');
				if(limitNomOp != "") {
					limitNomOp = parseInt(limitNomOp);
				}
				else {
					limitNomOp = 255;
				}
				var serializedForm = $('#operation-create-form').serializeObject();
				var lengthNomOp = this.$body.find('#save-operation-name').val().length;
				if(lengthNomOp > limitNomOp) {
					var erreurs = [];
					erreurs.push({"message": Translator.trans("operations.actions.errorLimitNomOp", {"limit": limitNomOp})});
					var error = new RequeteurError(erreurs);
					this.addErrors(error);
					this.onError(error);
				}
				else {
					var datas = {
						libelleOperation: this.$body.find('#save-operation-name').val(),
						descriptionOperation: this.$body.find('#save-operation-desc').val()
					};
					if(serializedForm['save-operation-type']) {
						datas.typeOperation = parseInt(serializedForm['save-operation-type']);
					}
					var op = requeteurWallet.createOperation(datas);
					op.then(function (response) {
						if($('#operation-create-form').data('hors-fid') == "1") {
							location.href = Routing.generate('aquitem_requeteur_operation_wallet_selection_creation_hors_fid', {idOperation: response.id}, true);
						}
						else {
							location.href = Routing.generate('aquitem_requeteur_operation_wallet_selection_creation', {idOperation: response.id}, true);
						}
					}).catch(function (err) {
						var modal = modalFactory.create({
							onOpened: function () {
								var erreurs = [];
								erreurs.push({"message": err.errors[0].message});
								var error = new RequeteurError(erreurs);
								this.addErrors(error);
								this.onError(error);
							},
							onSuccess: function () {
								this.$closeBtn.focus();
							},

						});
					}),
						op.catch(function (err) {
							console.log(err)
						});
					this.endLoading();
				}
			}
		});
	});


    /* creer un modele */
    $('.btn-creer-modele').on('click', function (e) {
        e.preventDefault();
        var modal = modalFactory.create({
            title: Translator.trans("modeles.newmodele"),
            body: $('#modele-create-form-tpl').html(),
            validateBtn: Translator.trans("global.validate"),
            submitOnEnter: false,
            onSubmit: function () {
                var datas = {
                    libelle: this.$body.find('#save-modele-name').val(),
                    description: this.$body.find('#save-modele-desc').val(),
                    typeEditeur: this.$body.find('input[name="editor"]:checked').val(),
                };
                console.log(datas)
                var modele = requeteur.nouveauModeleEmail(datas);
                modele.then(function (response) {
                    location.href = Routing.generate('aquitem_requeteur_personnaliser_modele_email_editor', {idModele: response}, true);
                })
                .catch(function (err) {
                    var modal = modalFactory.create({
                        onOpened: function () {
                            var erreurs = [];
                            erreurs.push({"message": err.errors[0].message});
                            var error = new RequeteurError(erreurs);
                                this.addErrors(error);
                                this.onError(error);
                        },
                        onSuccess: function () {
                            this.$closeBtn.focus();
                        },

                    });
                });
                this.endLoading();
            }
        });
    });

    $('.select-all-btn').find('.select-all').click(function (e) {
        e.preventDefault();
        var target = $(this).closest('.select-all-btn').data('target-class');
        $('.'+target).find(':checkbox:visible').prop('checked', 'checked');
    });
    $('.select-all-btn').find('.unselect-all').click(function (e) {
        e.preventDefault();
        var target = $(this).closest('.select-all-btn').data('target-class');
        $('.'+target).find(':checkbox').prop('checked', '');

    });

    $('.aidesquare,.aidesquare-footer').on('click', function (e) {
        e.preventDefault();
        if(window.location.hash.length) {
            var href = Routing.generate('aquitem_requeteur_aide', {idModal: window.location.hash}, true)
        }
        else {
            var href = Routing.generate('aquitem_requeteur_aide');
        }
        window.open(href);
        pushGoalMatomo(10, 0);
    });

    $('#btn-explorer-selection-horsfid').on('click', function (e) {
        e.preventDefault();
        modalFactory.create({
            title: Translator.trans('horsFid.titles.explore'),
            body: $('#selection-explorer-horsfid-form-tpl').html(),
            validateBtn: "",
            cancelBtn: Translator.trans('global.close'),
            onOpened: function () {
                this.$body.find('.loaderWindow').hide();
                var modal = this;
                if(modal.$body.find('#pagination').length) {
                    var nbParPage = parseInt(modal.$body.find('#pagination').data('nb')),
                        total = modal.$body.find('#pagination').data('total');
                    modal.$body.find('#pagination').on('click', 'a', function (ev) {
                        modal.$body.find('.loaderWindow').show();
                        ev.preventDefault();
                        var position = parseInt($(this).attr('data-pos')),
                            that = $(this);
                        var positionNext = position + nbParPage;
                        var positionPrev = position - nbParPage;
                        modal.$body.find('#pagination a.next').attr('data-pos', positionNext);
                        modal.$body.find('#pagination a.prev').attr('data-pos', positionPrev);
                        modal.$body.find('#pagination a.next').toggleClass('hide', positionNext >= total);
                        modal.$body.find('#pagination a.prev').toggleClass('hide', positionPrev < 0);

                        var explore = requeteur.explorerSelectionHorsFid({
                            idSelection: $(this).closest('#pagination').data('id-selection'),
                            premierResultat: position,
                            maxResultats: nbParPage,
                        });
                        explore.then(function (datas) {
                            exploreDatas = "";
                            $.each(datas, function(cle,data) {
                                if(cle == 0) {
                                    exploreDatas += '<thead>';
                                }
                                exploreDatas += '<tr>';
                                if($.isArray(data)) {
                                    $.each(data, function(key,val) {
                                        if(cle == 0) {
                                            exploreDatas += '<th class="headCol"><i>' + val + '</i></th>';
                                        }
                                        else {
                                            exploreDatas += '<td>' + val + '</td>'
                                            if(key == data.length - 1) {
                                                exploreDatas += '</tr>';
                                            }
                                        }
                                    });
                                }
                                else {
                                    if(cle == 0) {
                                        exploreDatas += '<th class="headCol"><i>' + data + '</i></th>';
                                    }
                                    else {
                                        exploreDatas += '<td>' + data + '</td>'
                                    }
                                }
                                if(cle == 0) {
                                    exploreDatas += '</tr></thead>';
                                }
                            });
                            modal.$body.find('#explore-table').html(exploreDatas);
                            modal.$body.find('.loaderWindow').hide();
                        }).catch(function (err) {
                            console.log(err)
                        });
                    });
                }
            }
        });
    });
    $(".clearable").each(function() {
        var $inp = $(this).find('input[type="text"]'),
            $cle = $(this).find('.cross');
        if($inp.val() != "") {
            $cle.show();
        }
        $inp.on('input', function(){
            $cle.toggle(!!this.value);
        });
        $cle.on('touchstart click', function(e) {
            e.preventDefault();
            $inp.val("").trigger('input');
            $inp.focus();
        });
    });

    var scrollHeight = {
        'op-en-cours': 0,
        'op-a-venir': 0,
    };
    var lineHeight = $('.operations-list-wrap tr.operation-item').height() + 4; // hauteur ligne + 3px de bordure +1px mystère
    var visibleBlocHeight = lineHeight * 5; // 189 => 1 ligne de 38px + 4 lignes de 37px visibles
    $('.home-page').on('click', '.navOp a', function (e) {
        var typeOp = $(this).closest('.operations-list').find('.operations-list-wrap table').attr('class');
        if($(this).parent().hasClass('nextOp')) {
            var levelHeight = (scrollHeight[typeOp] + lineHeight);
        }
        else if($(this).parent().hasClass('prevOp')) {
            var levelHeight = (scrollHeight[typeOp] - lineHeight);
        }
        var blocHeight = parseInt(lineHeight * ($(this).closest('.operations-list').find('.operations-list-wrap table tr.operation-item').length)+1)-1,
            $that = $(this);
        $(this).closest('.operations-list').find(".operations-list-wrap").animate({ scrollTop: levelHeight}, 400, function() {
            if($that.parent().hasClass('nextOp')) {
                scrollHeight[typeOp] = scrollHeight[typeOp] + 1;
                if(visibleBlocHeight + scrollHeight[typeOp] >= blocHeight) {
                    $that.closest('.navOp').find('.prevOp a').removeClass('hide');
                    $that.addClass('hide');
                }
                else if((scrollHeight[typeOp] + lineHeight) < blocHeight) {
                    $that.closest('.navOp').find('.prevOp a').removeClass('hide');
                    $that.removeClass('hide');
                }
            }
            else if($that.parent().hasClass('prevOp')) {
                if(scrollHeight[typeOp] == 0) {
                    $that.closest('.navOp').find('.nextOp a').removeClass('hide');
                    $that.addClass('hide');
                }
                else if((scrollHeight[typeOp] - lineHeight) < blocHeight) {
                    $that.closest('.navOp').find('.nextOp a').removeClass('hide');
                    $that.removeClass('hide');
                }
            }
        });
    });

    $(".operations-list-wrap").scroll( function(){
        if($(this).find('.op-en-cours').length) {
            scrollHeight['op-en-cours'] = $(this).scrollTop();
        }
        else {
            scrollHeight['op-a-venir'] = $(this).scrollTop();
        }
    });

    //contacts enseigne
    $('footer').on('click', '.voir-contacts', function (e) {
        e.preventDefault();
        var title = $(this).text();
        var modal = modalFactory.create({
            id: "contacts-enseigne",
            title: title,
            body: $('#voir-contacts-tpl').html(),
            validateBtn: "",
            cancelBtn: Translator.trans("global.close"),
        });
    });

	/* export des pdv */
	$('#page').on('click', 'a[data-action="export-pdv"]', function (e) {
		e.preventDefault();
		let exportUrl = $(this).attr('href');
		let idSession = typeof $(this).data('id-session') !== "undefined" ? $(this).data('id-session') : 0;
		var modal = modalFactory.create({
			title: Translator.trans("pdv.titles.export"),
			successText: Translator.trans("pdv.actions.exportSuccessText"),
			submitOnEnter: false,
			validateBtn: Translator.trans("global.export"),
			onOpened: function () {
				let textAction = Translator.trans("pdv.actions.exportAll");
				if (idSession) {
					textAction = Translator.trans("pdv.actions.exportSearch");
				}
				this.$body.html(textAction).addClass("pbxl");
			},
			onSubmit: function () {
				location.href = exportUrl;
				let that = this;
				setTimeout(function () {
					that.onSuccess();
				}, 1000);
			}
		});
	});


});
$(window).load(function(){
    $(".loaderWindow").height($( window ).height());
    $(".loaderWindow").hide();
});
$(document).ready(function(){
    /* si on est pas sur la page de listing des modèles email dans une op */
    if(!$('#mode-list-container.email').length) {
        $('html, body').scrollTop(0);

        $(window).on('load', function() {
            setTimeout(function(){
                $('html, body').scrollTop(0);
            }, 0);
        });
    }
});

$.fn.disableLink = function (isDisabled) {
	var disable = false;
  if (typeof isDisabled === "function") {
    disable = isDisabled();
  } else if (typeof isDisabled !== "undefined") {
    disable = isDisabled;
  }
  if (disable) {
    $(this).removeAttr('disabled');
  } else {
    $(this).attr('disabled', true);
  }
};
