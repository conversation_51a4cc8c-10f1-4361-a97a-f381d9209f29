window.kojqui={version:"2.2.3"},function(a,b){"use strict";a.kojqui.utils=b(a.j<PERSON><PERSON><PERSON>,a.ko,a.jQuery.ui.core)}(this,function(a,b){"use strict";var c,d,e,f,g;return c=(a.ui.version||"").match(/^(\d)\.(\d+)/),d=c?{major:parseInt(c[1],10),minor:parseInt(c[2],10)}:null,e=["foreach","if","ifnot","with","html","text","options"],f=Object.create||function(a){function b(){}return b.prototype=a,new b},g=function(a){var c=new a;b.bindingHandlers[c.widgetName]={after:b.utils.arrayGetDistinctValues(e.concat(c.after||[])),init:c.init.bind(c),update:c.update.bind(c)}},{uiVersion:d,descendantControllingBindings:e,createObject:f,register:g}}),function(a,b){"use strict";a.kojqui.BindingHandler=b(a.jQuery,a.ko,a.kojqui.utils,a.jQuery.ui.widget)}(this,function(a,b,c){"use strict";var d,e,f,g;return d="__kojqui_options",e=function(a,c){var d={};return b.utils.arrayForEach(c,function(c){void 0!==a[c]&&(d[c]=b.utils.unwrapObservable(a[c]))}),d},f=function(c,d,e){b.isObservable(e.refreshOn)&&b.computed({read:function(){e.refreshOn(),a(d)[c]("refresh")},disposeWhenNodeIsRemoved:d})},g=function(a){this.widgetName=a,this.widgetEventPrefix=a,this.options=[],this.events=[],this.after=[],this.hasRefresh=!1},g.prototype.init=function(g,h,i,j,k){var l,m,n,o,p;return l=this.widgetName,m=h(),n=e(m,this.options),o=e(m,this.events),p=!b.utils.arrayFirst(c.descendantControllingBindings,function(a){return this.hasOwnProperty(a)},i()),p&&b.applyBindingsToDescendants(k,g),b.utils.domData.set(g,d,n),a.each(o,function(a,b){o[a]=b.bind(j)}),a(g)[l](b.utils.extend(n,o)),this.hasRefresh&&f(l,g,m),b.isWriteableObservable(m.widget)&&m.widget(a(g)),b.utils.domNodeDisposal.addDisposeCallback(g,function(){a(g)[l]("destroy")}),{controlsDescendantBindings:p}},g.prototype.update=function(c,f){var g,h,i,j;g=this.widgetName,h=f(),i=b.utils.domData.get(c,d),j=e(h,this.options),a.each(j,function(b,d){d!==i[b]&&a(c)[g]("option",b,j[b])}),b.utils.domData.set(c,d,j)},g.prototype.on=function(c,d,e){var f;f=d===this.widgetEventPrefix?d:this.widgetEventPrefix+d,f=[f.toLowerCase(),".",this.widgetName].join(""),a(c).on(f,e),b.utils.domNodeDisposal.addDisposeCallback(c,function(){a(c).off(f)})},g}),function(a,b){"use strict";a.kojqui.Accordion=b(a.jQuery,a.ko,a.kojqui.utils,a.kojqui.BindingHandler,a.jQuery.ui.accordion)}(this,function(a,b,c,d){"use strict";var e=function(){d.call(this,"accordion"),1===c.uiVersion.major&&8===c.uiVersion.minor?(this.options=["active","animated","autoHeight","clearStyle","collapsible","disabled","event","fillSpace","header","icons","navigation","navigationFilter"],this.events=["change","changestart","create"],this.hasRefresh=!1,this.eventToWatch="change"):(this.options=["active","animate","collapsible","disabled","event","header","heightStyle","icons"],this.events=["activate","beforeActivate","create"],this.hasRefresh=!0,this.eventToWatch="activate")};return e.prototype=c.createObject(d.prototype),e.prototype.constructor=e,e.prototype.init=function(c,e){var f,g,h;return f=this.widgetName,g=e(),h=d.prototype.init.apply(this,arguments),b.isWriteableObservable(g.active)&&this.on(c,this.eventToWatch,function(){g.active(a(c)[f]("option","active"))}),h},c.register(e),e}),function(a,b){"use strict";a.kojqui.Autocomplete=b(a.kojqui.BindingHandler,a.kojqui.utils,a.jQuery.ui.autocomplete)}(this,function(a,b){"use strict";var c=function(){a.call(this,"autocomplete"),this.options=["appendTo","autoFocus","delay","disabled","minLength","position","source"],1===b.uiVersion.major&&8===b.uiVersion.minor?this.events=["change","close","create","focus","open","search","select"]:(this.options.push("messages"),this.events=["change","close","create","focus","open","response","search","select"])};return c.prototype=b.createObject(a.prototype),c.prototype.constructor=c,b.register(c),c}),function(a,b){"use strict";a.kojqui.Button=b(a.kojqui.BindingHandler,a.kojqui.utils,a.jQuery.ui.button)}(this,function(a,b){"use strict";var c=function(){a.call(this,"button"),this.options=["disabled","icons","label","text"],this.events=["create"],this.hasRefresh=!0};return c.prototype=b.createObject(a.prototype),c.prototype.constructor=c,b.register(c),c}),function(a,b){"use strict";a.kojqui.Buttonset=b(a.kojqui.BindingHandler,a.kojqui.utils,a.jQuery.ui.button)}(this,function(a,b){"use strict";var c=function(){a.call(this,"buttonset"),this.options=["items","disabled"],this.events=["create"],this.hasRefresh=!0};return c.prototype=b.createObject(a.prototype),c.prototype.constructor=c,b.register(c),c}),function(a,b){"use strict";a.kojqui.Datepicker=b(a.jQuery,a.ko,a.kojqui.BindingHandler,a.kojqui.utils,a.jQuery.ui.datepicker)}(this,function(a,b,c,d){"use strict";var e=function(){c.call(this,"datepicker"),this.options=["altField","altFormat","appendText","autoSize","buttonImage","buttonImageOnly","buttonText","calculateWeek","changeMonth","changeYear","closeText","constrainInput","currentText","dateFormat","dayNames","dayNamesMin","dayNamesShort","defaultDate","duration","firstDay","gotoCurrent","hideIfNoPrevNext","isRTL","maxDate","minDate","monthNames","monthNamesShort","navigationAsDateFormat","nextText","numberOfMonths","prevText","selectOtherMonths","shortYearCutoff","showAnim","showButtonPanel","showCurrentAtPos","showMonthAfterYear","showOn","showOptions","showOtherMonths","showWeek","stepMonths","weekHeader","yearRange","yearSuffix","beforeShow","beforeShowDay","onChangeMonthYear","onClose","onSelect"],this.hasRefresh=!0};return e.prototype=d.createObject(c.prototype),e.prototype.constructor=e,e.prototype.init=function(d,e){var f,g,h,i,j,k;return f=c.prototype.init.apply(this,arguments),g=this.widgetName,h=e(),i=b.utils.unwrapObservable(h.value),i&&a(d)[g]("setDate",i),b.isObservable(h.value)&&(j=h.value.subscribe(function(b){a(d)[g]("setDate",b)}),b.utils.domNodeDisposal.addDisposeCallback(d,function(){j.dispose()})),b.isWriteableObservable(h.value)&&(k=a(d)[g]("option","onSelect"),a(d)[g]("option","onSelect",function(b){var c,e;c=a(d)[g]("option","dateFormat"),e=a.datepicker.parseDate(c,b),h.value(e),"function"==typeof k&&k.apply(this,Array.prototype.slice.call(arguments))})),f},d.register(e),e}),function(a,b){"use strict";a.kojqui.Dialog=b(a.jQuery,a.ko,a.kojqui.BindingHandler,a.kojqui.utils,a.jQuery.ui.dialog)}(this,function(a,b,c,d){"use strict";var e=function(){c.call(this,"dialog"),1===d.uiVersion.major&&8===d.uiVersion.minor?(this.options=["autoOpen","buttons","closeOnEscape","closeText","dialogClass","disabled","draggable","height","maxHeight","maxWidth","minHeight","minWidth","modal","position","resizable","show","stack","title","width","zIndex"],this.events=["beforeClose","create","open","focus","dragStart","drag","dragStop","resizeStart","resize","resizeStop","close"]):1===d.uiVersion.major&&9===d.uiVersion.minor?(this.options=["autoOpen","buttons","closeOnEscape","closeText","dialogClass","draggable","height","hide","maxHeight","maxWidth","minHeight","minWidth","modal","position","resizable","show","stack","title","width","zIndex"],this.events=["beforeClose","create","open","focus","dragStart","drag","dragStop","resizeStart","resize","resizeStop","close"]):(this.options=["appendTo","autoOpen","buttons","closeOnEscape","closeText","dialogClass","draggable","height","hide","maxHeight","maxWidth","minHeight","minWidth","modal","position","resizable","show","title","width"],this.events=["beforeClose","create","open","focus","dragStart","drag","dragStop","resizeStart","resize","resizeStop","close"])};return e.prototype=d.createObject(c.prototype),e.prototype.constructor=e,e.prototype.init=function(d,e){var f,g,h;return f=document.createElement("DIV"),f.style.display="none",d.parentNode.insertBefore(f,d),b.utils.domNodeDisposal.addDisposeCallback(f,function(){b.removeNode(d)}),g=c.prototype.init.apply(this,arguments),h=e(),h.isOpen&&b.computed({read:function(){a(d)[this.widgetName](b.utils.unwrapObservable(h.isOpen)?"open":"close")},disposeWhenNodeIsRemoved:d,owner:this}),b.isWriteableObservable(h.isOpen)&&(this.on(d,"open",function(){h.isOpen(!0)}),this.on(d,"close",function(){h.isOpen(!1)})),b.isWriteableObservable(h.width)&&this.on(d,"resizestop",function(a,b){h.width(Math.round(b.size.width))}),b.isWriteableObservable(h.height)&&this.on(d,"resizestop",function(a,b){h.height(Math.round(b.size.height))}),g},d.register(e),e}),function(a,b){"use strict";a.kojqui.Menu=b(a.kojqui.BindingHandler,a.kojqui.utils,a.jQuery.ui.menu)}(this,function(a,b){"use strict";var c=function(){a.call(this,"menu"),this.options=1===b.uiVersion.major&&b.uiVersion.minor<11?["disabled","icons","menus","position","role"]:["disabled","icons","items","menus","position","role"],this.events=["blur","create","focus","select"],this.hasRefresh=!0};return c.prototype=b.createObject(a.prototype),c.prototype.constructor=c,b.register(c),c}),function(a,b){"use strict";a.kojqui.Progressbar=b(a.kojqui.BindingHandler,a.kojqui.utils,a.jQuery.ui.progressbar)}(this,function(a,b){"use strict";var c=function(){a.call(this,"progressbar"),this.events=["change","create","complete"],this.hasRefresh=!0,this.options=1===b.uiVersion.major&&8===b.uiVersion.minor?["disabled","value"]:["disabled","max","value"]};return c.prototype=b.createObject(a.prototype),c.prototype.constructor=c,b.register(c),c}),function(a,b){"use strict";a.kojqui.Selectmenu=b(a.jQuery,a.ko,a.kojqui.BindingHandler,a.kojqui.utils,a.jQuery.ui.selectmenu)}(this,function(a,b,c,d){"use strict";var e,f;return e="__kojqui_selectmenu_value",f=function(){c.call(this,"selectmenu"),this.after=["value"],this.options=["appendTo","disabled","icons","position","width"],this.events=["change","close","create","focus","open","select"],this.hasRefresh=!0},f.prototype=d.createObject(c.prototype),f.prototype.constructor=f,f.prototype.init=function(d,e){var f,g;return f=e(),g=c.prototype.init.apply(this,arguments),f.hasOwnProperty("isOpen")&&b.computed({read:function(){a(d)[this.widgetName](b.utils.unwrapObservable(f.isOpen)?"open":"close")},disposeWhenNodeIsRemoved:d,owner:this}),b.isWriteableObservable(f.isOpen)&&(this.on(d,"open",function(){f.isOpen(!0)}),this.on(d,"close",function(){f.isOpen(!1)})),this.on(d,"change",function(){a(d).trigger("change")}),g},f.prototype.update=function(d,f,g){var h,i;c.prototype.update.apply(this,arguments),g().hasOwnProperty("value")&&(h=b.utils.domData.get(d,e),i=b.utils.unwrapObservable(g().value),h!==i&&a(d).selectmenu("refresh"))},d.register(f),f}),function(a,b){"use strict";a.kojqui.Slider=b(a.jQuery,a.ko,a.kojqui.BindingHandler,a.kojqui.utils,a.jQuery.ui.slider)}(this,function(a,b,c,d){"use strict";var e,f;return e="__kojqui_options",f=function(){c.call(this,"slider"),this.widgetEventPrefix="slide",this.options=["animate","disabled","max","min","orientation","range","step","value","values"],this.events=["create","start","slide","change","stop"]},f.prototype=d.createObject(c.prototype),f.prototype.constructor=f,f.prototype.init=function(d,f){var g,h,i;return g=c.prototype.init.apply(this,arguments),h=f(),i=h.realtime?"slide":"change",b.isWriteableObservable(h.value)&&this.on(d,i,function(c,f){var g=a(d).find(".ui-slider-handle").index(f.handle);0===g&&(b.utils.domData.get(d,e).value=f.value,h.value(f.value))}),b.isWriteableObservable(h.values)&&this.on(d,i,function(a,c){b.utils.domData.get(d,e).value=c.values,h.values(c.values)}),g},d.register(f),f}),function(a,b){"use strict";a.kojqui.Spinner=b(a.jQuery,a.ko,a.kojqui.BindingHandler,a.kojqui.utils,a.jQuery.ui.spinner)}(this,function(a,b,c,d){"use strict";var e=function(){c.call(this,"spinner"),this.widgetEventPrefix="spin",this.options=["culture","disabled","icons","incremental","max","min","numberFormat","page","step"],this.events=["create","start","spin","stop","change"]};return e.prototype=d.createObject(c.prototype),e.prototype.constructor=e,e.prototype.init=function(d,e){var f,g,h;return f=c.prototype.init.apply(this,arguments),g=this.widgetName,h=e(),h.value&&b.computed({read:function(){a(d)[g]("value",b.utils.unwrapObservable(h.value))},disposeWhenNodeIsRemoved:d}),b.isWriteableObservable(h.value)&&(this.on(d,"spin",function(a,b){h.value(b.value)}),this.on(d,"change",function(){h.value(a(d)[g]("value"))})),f},d.register(e),e}),function(a,b){"use strict";a.kojqui.Tabs=b(a.jQuery,a.ko,a.kojqui.BindingHandler,a.kojqui.utils,a.jQuery.ui.tabs)}(this,function(a,b,c,d){"use strict";var e,f,g;return e=function(c,d){var e=d();b.isWriteableObservable(e.selected)&&this.on(c,"show",function(b,d){a(c)[0]===b.target&&e.selected(d.index)})},f=function(c,d){var e=d();b.isWriteableObservable(e.active)&&this.on(c,"activate",function(b,d){a(c)[0]===b.target&&e.active(d.newTab.index())})},g=function(){c.call(this,"tabs"),this.version=d.uiVersion,1===this.version.major&&8===this.version.minor?(this.options=["ajaxOptions","cache","collapsible","cookie","disabled","event","fx","idPrefix","panelTemplate","selected","spinner","tabTemplate"],this.events=["add","create","disable","enable","load","remove","select","show"],this.hasRefresh=!1):(this.options=["active","collapsible","disabled","event","heightStyle","hide","show"],this.events=["activate","beforeActivate","beforeLoad","create","load"],this.hasRefresh=!0)},g.prototype=d.createObject(c.prototype),g.prototype.constructor=g,g.prototype.init=function(a,b){var d=c.prototype.init.apply(this,arguments);return 1===this.version.major&&8===this.version.minor?e.call(this,a,b):f.call(this,a,b),d},d.register(g),g}),function(a,b){"use strict";a.kojqui.Tooltip=b(a.jQuery,a.ko,a.kojqui.BindingHandler,a.kojqui.utils,a.jQuery.ui.tooltip)}(this,function(a,b,c,d){"use strict";var e=function(){c.call(this,"tooltip"),this.options=["content","disabled","hide","items","position","show","tooltipClass","track"],this.events=["create","open","close"]};return e.prototype=d.createObject(c.prototype),e.prototype.constructor=e,e.prototype.init=function(d,e){var f,g;return f=e(),g=c.prototype.init.apply(this,arguments),f.isOpen&&b.computed({read:function(){a(d)[this.widgetName](b.utils.unwrapObservable(f.isOpen)?"open":"close")},disposeWhenNodeIsRemoved:d,owner:this}),b.isWriteableObservable(f.isOpen)&&(this.on(d,"open",function(){f.isOpen(!0)}),this.on(d,"close",function(){f.isOpen(!1)})),g},d.register(e),e});