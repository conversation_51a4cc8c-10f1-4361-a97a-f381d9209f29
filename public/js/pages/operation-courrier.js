import ModalFactory from './../src/modal';
import API from "../src/api";
import <PERSON>queteur from "../src/requeteur";
import {RequeteurError} from './../src/api';

var api = new API(Routing),
    requeteur = new Requeteur(api),
    modalFactory = new ModalFactory();

var $personnalisationModele = $('#operation-personnalisation-modele-courrier'),
    idSelection = $personnalisationModele.data('idselection'),
    idOperation = $personnalisationModele.data('idoperation'),
    $zones = $('textarea[name="form"]'),
    persoModifiee = false;


$(window).bind('beforeunload', function(e){
    if(persoModifiee) {
        return false;
    }
});

$(document).ready(function () {

    var stickySidebar = $('.sticky');

    if (stickySidebar.length > 0) {
        var stickyHeight = stickySidebar.height(),
            sidebarTop = stickySidebar.offset().top;
    }
    $('.sticky-container').css('min-height', stickyHeight);
    
    // on scroll move the sidebar
    $(window).scroll(function () {
        if (stickySidebar.length > 0) {
            var scrollTop = $(window).scrollTop();
            if (sidebarTop < scrollTop) {
                stickySidebar.css('top', scrollTop - sidebarTop);
                // stop the sticky sidebar at the footer to avoid overlapping
                var sidebarBottom = stickySidebar.offset().top + stickyHeight,
                    stickyStop = $('.sticky-container').offset().top + $('.sticky-container').height();

                if (stickyStop < sidebarBottom) {
                    var stopPosition = $('.sticky-container').height() - stickyHeight;
                    stickySidebar.css('top', stopPosition);
                }
            }
            else {
                stickySidebar.css('top', '0');
            }
        }
    });
    $(window).resize(function () {
        if (stickySidebar.length > 0) {
            stickyHeight = stickySidebar.height();
        }
    });

    if(typeof CKEDITOR !== 'undefined') {
        $.each(CKEDITOR.instances, function(key, value) {
            var current = CKEDITOR.instances[value.name].getData();
            current = $("<div/>").html(current).text();
            if($('#'+value.name).data('max') < 2147483647) {
                var max = $('#'+value.name).data('max');
            }
            $('label[for="'+value.name+'"]').attr('data-libelle', $('label[for="'+value.name+'"]').text());
            if(typeof max != 'undefined') {
                var tailleCourante = parseInt(max) - parseInt(current.length);
            }
            else {
                var tailleCourante = 'illimités';
            }
            if(value.name == 'form_remarque'){
                $('label[for="'+value.name+'"]').append('');
            }
            else{
                $('label[for="'+value.name+'"]').append(' ('+ Translator.trans("js.caracRestant")+' : <span class="nbrestants">'+tailleCourante+'</span>)');
            }
            $('#'+value.name).prev().append('<br><span class="error little-info error_'+value.name+' hide"><strong><em>'+Translator.trans("operations.compteCaracErrorCourrier")+' '+max+' '+Translator.trans("global.caracteres")+'</em></strong></span>');
            value.on('change', function (e) {
                current = CKEDITOR.instances[value.name].getData();
                current = $("<div/>").html(current).text();
                if($.trim(current) != $.trim($('textarea#'+value.name).text())) {
                    persoModifiee = true;
                }
                else {
                    persoModifiee = false;
                }
                if(typeof max != 'undefined' && max > 0) {
                    tailleCourante = parseInt(max) - parseInt(current.length);
                    $('label[for="' + value.name + '"]').find('span.nbrestants').text(tailleCourante);
                    if (parseInt(tailleCourante) < 0) {
                        $('.error_' + value.name).removeClass('hide');
                        $('label[for="' + value.name + '"]').find('span.nbrestants').addClass('error');
                    }
                    else {
                        $('.error_' + value.name).addClass('hide');
                        $('label[for="' + value.name + '"]').find('span.nbrestants').removeClass('error');
                    }
                }
            });
        });
    }

    /* Enregistrement */
    $personnalisationModele.on('click', '.btn-enregistrer-modele', function (e) {
        e.preventDefault();
        persoModifiee = false;
        var maxZoneInvalid = [];
        var modal = modalFactory.create({
            title: Translator.trans('js.customTemplate'),
            body: $('#perso-save-form-tpl').html(),
            validateBtn: Translator.trans("global.save"),
            successText: Translator.trans("operations.actions.successPersoModele"),
            errorText: Translator.trans("operations.actions.errorPersoModele"),
            onOpened: function () {
                $.each(CKEDITOR.instances, function(key, value) {
                    if($('label[for="' + value.name + '"]').find('span.nbrestants').hasClass('error')) {
                        maxZoneInvalid.push($('label[for="'+value.name+'"]').data('libelle'));
                    }
                });
                if(maxZoneInvalid.length) {
                    var errorMsg = "";
                    $.each(maxZoneInvalid, function(key, zone) {
                        if(errorMsg != "") {
                            errorMsg = errorMsg + '<br>';
                        }
                        errorMsg = errorMsg + Translator.trans("operations.actions.maxZoneInvalid1") + '"' + zone + '" ' + Translator.trans("operations.actions.maxZoneInvalid2");
                    });
                    var erreurs = [];
                    erreurs.push({'message': errorMsg});
                    var err = new RequeteurError(erreurs);
                    this.addErrors(err);
                    this.onError();
                }
            },
            onSubmit: function () {
                var idZone = [],
                    texteZone = [],
                    commentaire = '';
                $.each(CKEDITOR.instances, function(key, value) {
                    if(value.name != 'form_remarque') {
                        idZone.push($('#'+value.name).data('id-zone'));
                        texteZone.push($('<div>').html(CKEDITOR.instances[value.name].getData()).html());
                    }
                });
                if(typeof CKEDITOR.instances.form_remarque != 'undefined') {
                    commentaire = $('<div>').html(CKEDITOR.instances.form_remarque.getData()).html();
                }
                var datas = {
                    idOperation: idOperation,
                    idZone: idZone,
                    texteZone: texteZone,
                    commentaire: commentaire,
                };
                this.handle(requeteur.enregistrerModeleCourrier(datas));
            },
            onSuccess: function (values) {
                this.$closeBtn.focus();
            },
            onClose: function () {
            },
            onError: function () {
            },
        });
    });
});