import API from './../src/api';
import Requeteur from './../src/requeteur';
import ModalFactory from './../src/modal';
import {RequeteurError} from './../src/api';
import 'jquery-highlight';
import {displaySpanErrorForSearch,buttonSearchState} from './../src/utils';

var api = new API(Routing);
var requeteur = new Requeteur(api);
var modalFactory = new ModalFactory();
var $quotas = $('#selection-quotas'),
    timerQuotasSearch = null;
$(document).ready(function () {
    if($quotas.find('input[name="activerQuotas"]:checked').val() == "true") {
        $quotas.find('#quotas-valider').prop('disabled',true).addClass('btn--disabled').removeClass('btn--red');
    }
    $quotas.on('click', 'input[name="activerQuotas"]', function (e) {
        if ($(this).is(':checked')) {
            if ($(this).val() == "true") {
                $('.details-quotas').removeClass('hide');
                $quotas.find('#quotas-valider').prop('disabled',true).addClass('btn--disabled').removeClass('btn--red');
            }
            else {
                $('.details-quotas').addClass('hide');
                $quotas.find('#quotas-valider').prop('disabled',false).removeClass('btn--disabled').addClass('btn--red');
            }
        }
    });

    $quotas.on('keyup', 'input[name="valeursQuotas"]', function (e) {
        $quotas.find('#quotas-valider').prop('disabled',true).addClass('btn--disabled').removeClass('btn--red');
        $quotas.find('.comptage-avec-quotas .erreur').addClass('hide');
    });


    $quotas.on('click', '#quotas-appliquer', function (e) {
        e.preventDefault();
        if($('#quotas-details-critere').find('input[name="referencesQuotas"]:checked').length) {
            var modal = modalFactory.create({
                title: Translator.trans("quotas.appliquerQuotas"),
                body: $('#appliquer-quota-form-tpl').html(),
                validateBtn: Translator.trans("global.validate"),
                errorText: "",
                onSubmit: function () {
                    var serializedForm = $('#appliquer-quota-form').serializeObject();
                    if(serializedForm['appliquer-quota-value'] != "" && !$.isNumeric(serializedForm['appliquer-quota-value'])) {
                        var erreurs = [];
                        erreurs.push({"message": Translator.trans('quotas.isNotNumeric')});
                        var error = new RequeteurError(erreurs);
                        this.addErrors(error);
                        this.onError(error);
                    }
                    else {
                        $('#quotas-details-critere').find('input[name="referencesQuotas"]:checked').each(function () {
                            var id = $(this).data('id-ref');
                            $('#valeursQuotas-'+id).val(serializedForm['appliquer-quota-value']);
                            $(this).prop('checked', false);
                        });
                        this.close();
                    }
                },
            });
        }
    });

    $quotas.on('click', '#quotas-critere', function (e) {
        e.preventDefault();
        var modal = modalFactory.create({
            title: Translator.trans("quotas.appliquerQuotas"),
            body: $('#critere-quota-form-tpl').html(),
            validateBtn: Translator.trans("global.validate"),
            onOpened: function () {
                var that = this;
                var serializedQuotasForm = $('#selection-quotas-form').serializeObject();
                that.$body.find('#critere-quota-value option[value="'+serializedQuotasForm["idCritereQuotas"]+'"]').prop('selected', true);
                that.$body.find('#critere-quota-value').on("change", function (e) {
                    that.$body.find('.info').removeClass('hide');
                });
            },
            onSubmit: function () {
                var modal = this;
                var serializedForm = $('#critere-quota-form').serializeObject();
                var idCritereQuotas = serializedForm['critere-quota-value'];
                var libCritereQuotas = modal.$body.find('option[value="'+idCritereQuotas+'"]').data('libelle');

                $quotas.find('input[name="idCritereQuotas"]').val(idCritereQuotas);
                $quotas.find('span.critere-quotas').html(libCritereQuotas);
                var criteresPromise = requeteur.listerCriteresQuotas();
                criteresPromise.then(function (criteres) {
                    $quotas.find('#quotas-details-critere').html('');
                    $.each(criteres, function(cle,critere) {
                        if(critere.id == idCritereQuotas) {
                            $.each(critere.valeursDisponibles, function(key,value) {
                                var $baseDetailCriteresQuotas = modal.$body.find('#base-quotas-details').clone();
                                // checkboxes
                                $baseDetailCriteresQuotas.find('input[name="referencesQuotas"]').attr('id', 'referencesQuotas-' + value.id);
                                $baseDetailCriteresQuotas.find('input[name="referencesQuotas"]').val(value.id);
                                $baseDetailCriteresQuotas.find('input[name="referencesQuotas"]').attr('data-id-ref', value.id);
                                $baseDetailCriteresQuotas.find('label.libelle-critere').attr('for', 'referencesQuotas-' + value.id).find('span').attr('data-search', value.libelle.toLowerCase()).html(value.libelle);
                                // inputs
                                $baseDetailCriteresQuotas.find('input[name="valeursQuotas"]').attr('id', 'valeursQuotas-' + value.id);
                                $baseDetailCriteresQuotas.find('input[name="valeursQuotas"]').attr('data-id-ref', value.id);
                                $baseDetailCriteresQuotas.find('input[name="valeursQuotas"]').closest('label').attr('for', 'valeursQuotas-' + value.id);
                                $quotas.find('#quotas-details-critere').append($baseDetailCriteresQuotas.find('tr'));
                            });
                        }
                    });
                });
                modal.close();
            },
            onClose: function () {
                $('html, body').stop().animate({
                    scrollTop: $quotas.find('#quotas-details-liste').offset().top
                }, 1000);
            },
        });
    });

    $quotas.on('click', '#quotas-calculer', function (e) {
        e.preventDefault();
        if($('#selection-quotas-form')[0].checkValidity() === false) {
            return true;
        }
        $('.comptage-avec-quotas .loader').removeClass('hide');
        $('.comptage-avec-quotas span').addClass('hide');
        $('.comptage-avec-quotas span+span').addClass('hide');
        var requestDatas = app.createRequest();

        var quotasDatas = setQuotasRequest();
        requestDatas['activerQuotas'] = quotasDatas['activerQuotas'];
        requestDatas['idCritereQuotas'] = quotasDatas['idCritereQuotas'];
        requestDatas['idCritereTri'] = quotasDatas['idCritereTri'];
        requestDatas['maximiserQuotas'] = quotasDatas['maximiserQuotas'];
        requestDatas['referencesQuotas'] = quotasDatas['referencesQuotas'];
        requestDatas['valeursQuotas'] = quotasDatas['valeursQuotas'];

        let calculation = requeteur.calculate(app.createRequest());
        if(typeof $('#operations-process-container').data('id-format-routage') !== "undefined") {
            calculation = $('#operations-process-container').data('id-format-routage') !== "" ? requeteur.calculateForWallet(app.createRequest(), $('#operations-process-container').data('id-format-routage')) : requeteur.calculate(app.createRequest());
        }
        calculation.then(function (data) {
            if (typeof data.resultatCalcul !== "undefined") {
                if(data.resultatCalcul > 1) {
                    $('.comptage-avec-quotas span.value-count').html(data.resultatCalcul.toLocaleString() + ' ' + Translator.trans("global.contacts")).removeClass('hide');
                }
                else {
                    $('.comptage-avec-quotas span.value-count').html(data.resultatCalcul.toLocaleString() + ' ' + Translator.trans("comptage.contact")).removeClass('hide');
                }
                $('.comptage-avec-quotas span.title-count').removeClass('hide');
                $('.comptage-avec-quotas .loader').addClass('hide');
                if(data.resultatCalcul >= 0) {
                    $('#quotas-valider').prop('disabled',false);
                    $('#quotas-valider').removeClass('btn--disabled').addClass('btn--red');
                }
            }
        }).catch(function (err) {
            $('.comptage-avec-quotas .loader').addClass('hide');
            $('.comptage-avec-quotas .erreur').html('<span class="modal-errors error">' + err.errors[0].message + '</span>');
            $('.comptage-avec-quotas .erreur').removeClass('hide');
        });
    });

    $quotas.on('click', '#quotas-valider', function (e) {
        e.preventDefault();
        if($('#selection-quotas-form')[0].checkValidity() === false) {
            return true;
        }
        var href = $(this).attr('href'),
            idSession = $(this).data('id-session');

        var requestDatas = app.createRequest();
        var quotasDatas = setQuotasRequest();
        requestDatas['activerQuotas'] = quotasDatas['activerQuotas'];
        requestDatas['idCritereQuotas'] = quotasDatas['idCritereQuotas'];
        requestDatas['idCritereTri'] = quotasDatas['idCritereTri'];
        requestDatas['maximiserQuotas'] = quotasDatas['maximiserQuotas'];
        requestDatas['referencesQuotas'] = quotasDatas['referencesQuotas'];
        requestDatas['valeursQuotas'] = quotasDatas['valeursQuotas'];
        requestDatas['idSession'] = idSession;
        $.post(Routing.generate('aquitem_requeteur_quotas_to_session'),
            requestDatas,
            function(datas) {
                location.href = href;
            }
        );
    });

    /** Recherche d'enseignes **/
    $('#search-quotas').on('keyup change', function () {
        if (timerQuotasSearch != null) {
            clearTimeout(timerQuotasSearch);
            timerQuotasSearch = null;
        }

        timerQuotasSearch = setTimeout(function () {
            var $quotas = $('.quotas-details');
            $quotas.unhighlight();
            var $this = $('#search-quotas');
            var search = $this.val().toLowerCase();
            search = search.replace(/[àáâãäå]/g,"a").replace(/[èéêë]/g,"e").replace(/[î]/g,"i").replace(/[ô]/g,"o").replace(/[ù]/g,"u").replace(/[ç]/g,"c").replace(/[æ]/g,"ae").replace(/[œ]/g,"oe");
            $quotas.each(function () {
                var $this = $(this);
                var text = $this.find('span.search-item').data('search');
                if (text.indexOf(search) === -1) {
                    $this.slideUp(100);
                    $this.removeClass('visible');
                } else {
                    $this.slideDown(100);
                    $this.addClass('visible');
                }

                $(".quotas-details.visible").highlight($this.val());

                displaySpanErrorForSearch(".quotas-details.visible",'span#noResult');

                var $btn = $('#btn-close-search-quotas');
                buttonSearchState($btn, search, $this);
            });
            $('.enseignesParCateg').each(function () {
                var $this = $(this);
                if ($this.find(".quotas-details").length === $this.find(".quotas-details:not(.visible)").length){
                    $($this.children()[0]).css('display', 'none');
                } else {
                    $($this.children()[0]).css('display', 'block');
                }
            });
        }, 300);
    });

    /** Raz de la saisie de recherche **/
    $('#btn-close-search-quotas').click(function(e){
        e.preventDefault();
        $('#search-quotas').val('').change();
        this.blur();
    });
});
function setQuotasRequest () {
    var datas = {
        'referencesQuotas': [],
        'valeursQuotas': [],
    };

    $('input[name="referencesQuotas"]').each(function(cle,valeur) {
        datas['referencesQuotas'].push($(this).val());
        var valQuotas = $('#valeursQuotas-' + $(this).val()).val();
        // if(valQuotas != "" && !$.isNumeric(valQuotas)) {
        //    valQuotas = "";
        // }
        datas['valeursQuotas'].push(valQuotas);
    });

    var serializedForm = $('#selection-quotas-form').serializeObject();
    $.each(serializedForm, function(key,value) {
        if(key != "referencesQuotas" && key != "valeursQuotas") {
            datas[key] = value;
        }
    });
    return datas;
}
