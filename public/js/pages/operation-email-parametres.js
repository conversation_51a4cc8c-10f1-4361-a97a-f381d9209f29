import API from './../src/api';
import <PERSON><PERSON><PERSON> from './../src/requeteur';
import ModalFactory from './../src/modal';
import EmojiPanel from './../emojipanel';
import {getMessageTagsAndDefaults,addMessageTags} from './../pages/operation-tag-email';
import {formatTagForUser} from './../pages/operation-email-utils';
import {getPromisesModeleEmail} from './../pages/modele-email-parametres';

var api = new API(Routing),
	requeteur = new Requeteur(api),
	modalFactory = new ModalFactory(),
	$formModele = $('#operation-personnalisation-modele-email'),
	idOperation = $formModele.data('idoperation'),
	allTags = $formModele.data('alltags'),
	selectTags = $formModele.data('selecttags'),
	prefixeTag = $formModele.data('prefixetag'),
	suffixeTag = $formModele.data('suffixetag'),
	prefixeDesinscription = $formModele.data('prefixedesinscription'),
	suffixeDesinscription = $formModele.data('suffixedesinscription'),
	prefixeMiroir = $formModele.data('prefixemiroir'),
	suffixeMiroir = $formModele.data('suffixemiroir'),
	typeEditeur = $formModele.data('editeur'),
    sujet = $formModele.data('sujet'),
	idTagSenderId = $formModele.data('tags-sender-id'),
	senderId = $formModele.data('sender-id'),
	domainesEmetteur = $formModele.data('domaines-emetteur'),
	emetteur = $formModele.data('nom-emetteur'),
    emailEmetteur = $formModele.data('email-emetteur'),
    emailReponse = $formModele.data('email-reponse'),
	tagsAndDefaults,
	messageHTML = $formModele.data('message-html'),
	donneesJson = $formModele.data('donnees-json'),
	urlsTracees = $formModele.data('urlstracees'),
	caretStart = [],
	savedHtml = $formModele.data('message-html'),
	headerHTML = $formModele.data('header-html'),
	pattern = new RegExp("\\" + prefixeTag + "([^" + suffixeTag + "]+)\\" + suffixeTag, "gi"),
	typeMailing = $formModele.data('typemailing'),
    emailReponse = $formModele.data('emailreponse'),
	isGestionModele = $('.personnaliser-gestion-modele-email-editor').length;

if (idTagSenderId == 0) {
	idTagSenderId = "";
}

window.selectTags = selectTags;
window.allTags = allTags;
window.prefixeTag = prefixeTag;
window.suffixeTag = suffixeTag;
window.prefixeDesinscription = prefixeDesinscription;
window.suffixeDesinscription = suffixeDesinscription;
window.prefixeMiroir = prefixeMiroir;
window.suffixeMiroir = suffixeMiroir;
window.idOperation = idOperation;
window.savedHtml = savedHtml;

$(document.body).on("keydown", this, function (event) {
    if (event.keyCode == 116) {
        if(!$('#email-previsu-test').length) {
            var href = $(this).attr('href');
            if (contentHasChanged()) {
                event.preventDefault();
                modalEnregistrerEmail(true, href);
            }
        }
    }
});
$(document).ready(function () {

	/* emojis */
	var charStart = 0;
	$formModele.on('blur', '#sujetId', function(e) {
		charStart = this.selectionStart;
	});
	var emojiButton = new EmojiPanel({
		container: '#page',
		trigger: '#trigger',
		json_url: '/js/emojis.json',
		pack_url: '/img/emojis.svg',
		cible: '#sujetId'
	});
	emojiButton.addListener('select', emoji => {
		var start = charStart;
		if(start.length == 0) {
			start = $(emojiButton.options.cible).val().length;
		}
		var before = $(emojiButton.options.cible).val().substring(0, start);
		var after  = $(emojiButton.options.cible).val().substring(start, $(emojiButton.options.cible).val().length);

		$(emojiButton.options.cible).val(before + emoji.char + after);
	});
	$('#trigger').on('click', function(e) {
		var positions = $('#trigger').offset();
		$('.EmojiPanel--trigger').css({position: "absolute", top: (positions.top+34), left: (positions.left-128), transform: "none"});
	});
	/**/

	/* OMG : on enlève du CSS sur page-content car ça laisse un fond gris en bas et c'est moche */
	$('.page-content').css('min-height', 'auto');
	$('body').css('background-color', '#fff');

	initParametersEmail();

	/* Enregistrement des modif. */
	$('.personnaliser-modele-email-editor').on('click.btnSubmit', '.btn-submit', function(e) {
		e.preventDefault();
		modalEnregistrerEmail();
	});

	$('body').on('click', '.tabs-title a, a.cross-cancel, a.header-1-link1, .sidebar-nav-item a, a.deconnexion', function (e) {
        if(!$('#email-previsu-test').length) {
            var href = $(this).attr('href');
            if (contentHasChanged()) {
                e.preventDefault();
                modalEnregistrerEmail(true, href);
            }
        }
    });
	/*
	window.onbeforeunload = function () {
		if (contentHasChanged()) {
			return confirm("Attention, si vous fermez cette fenêtre, les données non sauvegardées seront perdues.");
		}
	}
	*/
});

/************************************************************
 *  Parametres : Emetteur Objet / Pré header
 */
function initParametersEmail() {
	var preHeader = '';

	// Pré-header caché
	if(typeEditeur == 2) {
		var messageHTMLStripo = $('<div/>').html(messageHTML);
		if (messageHTMLStripo.find('div.pre-header-text').length) {
			preHeader = $(messageHTMLStripo).find('div.pre-header-text').html();
		}
	}
	else {
		if ($(messageHTML).find('div.pre-header-text').length) {
			preHeader = $(messageHTML).find('div.pre-header-text').html();
		}
	}

	$('#parameters-form').on('change.emetteur', '#nomEmetteur', function (e) {
		$('body').trigger('email.changed');
	});
	// position du curseur à la perte de focus
	$('#parameters-form').on('blur.sujet', '#sujetId', function (e) {
		caretStart[0] = this.selectionStart;
	});
	$('#parameters-form').on('blur.header', '#preHeaderId', function (e) {
		caretStart[1] = this.selectionStart;
	});
	$('#parameters-form').on('change.sujet', '#sujetId', function (e) {
		$('body').trigger('tags.changed');
	});
	$('#parameters-form').on('change.header', '#preHeaderId', function (e) {
		$('body').trigger('tags.changed');
	});

	// à la sélection d'un tag, on place la valeur sélectionnée dans le textarea à la position préalablement quittée
	$('#parameters-form').on('click.ajoutTag', '[data-action=ajoutTag]', function (e) {
		e.preventDefault();
		$('body').trigger('tags.changed');
		var libTag = $('#' + $(this).data('type-tag')).val();
		var start = caretStart[$(this).data('num')];
		var text = $('#' + $(this).data('field')).val();
		if (start === undefined) {
			start = text.length;
		}
		var newTag = prefixeTag + addMessageTags(libTag, $(this).data('type-tag')) + suffixeTag;
		var before = text.substring(0, start);
		var after = text.substring(start, text.length);
		$('#' + $(this).data('field')).val(before + newTag + after);
		caretStart[$(this).data('num')] = $('#' + $(this).data('field')).val().length;
		$(this).blur();
	});
	$('#preHeaderId').val(preHeader);

    if($('#sujetId').val() == "" || $('#nomEmetteur').val() == "" || $('#emailEmetteur').val() == "" || $('#emailReponse').val() == "") {
		$('#parameters-link-label').find('.fa-exclamation-circle').removeClass('hide');
	}
}

function parsePreHeader() {
	if (typeEditeur === 0 || typeEditeur === 2) {
		if ($('#preHeaderId').length) {
			if(typeEditeur == 2) {
				messageHTML = $('<div/>').html(messageHTML);
				messageHTML = messageHTML[0].outerHTML;
			}
			if ($('#preHeaderId').val() !== '' && $(messageHTML).find('div.pre-header-text').length === 0) {
				messageHTML = $('#pre-header-tpl').html() + messageHTML;
			}
			if ($(messageHTML).find('div.pre-header-text').length) {
				if ($('#preHeaderId').val() !== '') {
					messageHTML = messageHTML.replace($(messageHTML).find('div.pre-header-text')[0].outerHTML, $(messageHTML).find('div.pre-header-text').html($('#preHeaderId').val())[0].outerHTML);
				}
				else {
					messageHTML = messageHTML.replace($(messageHTML).find('div.pre-header-text')[0].outerHTML, '');
				}
			}
		}
	}
	// dans le messageHTML, remplacement de tag {nom-0} => {ref0}
	messageHTML = replaceUserTextTags(messageHTML);

	return messageHTML;
}
function replaceUserTextTags(text) {
	$.each(selectTags, function (key, values) {
		var formatedTag = prefixeTag + formatTagForUser(values) + suffixeTag;
		var replacePattern = new RegExp(formatedTag.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'), 'g');
		text = text.replace(replacePattern, prefixeTag + values.id + suffixeTag);
	});
	return text;
}
/************************************************************
 * Enregistrement des données du mail
 */
function modalEnregistrerEmail(leaveRedirect, href) {
	leaveRedirect = leaveRedirect || false;
	href = href || "";
	var success = false,
		cancelBtn = Translator.trans('global.cancel');
	if(leaveRedirect) {
		cancelBtn = Translator.trans('global.continueWithoutSave');
	}
	var modal = modalFactory.create({
		title: Translator.trans("operations.titles.persoTitle"),
		body: $('#perso-save-form-tpl').html(),
		validateBtn: Translator.trans("global.save"),
		cancelBtn: cancelBtn,
		successText: Translator.trans("operations.actions.successPersoModele"),
		errorText: Translator.trans("operations.actions.errorPersoModele"),
		onOpened: function () {
			if(leaveRedirect) {
				this.$body.find('.alert').removeClass('hide');
			}
		},
		onSubmit: function () {
			//gestion des modeles
			if($('.personnaliser-gestion-modele-email-editor').length) {
				var promises = getPromisesModeleEmail();
			}
			else {
				var datas = getEmailDatas();
				var promises = [enregistrerModeleEmail(datas)];

	            if ($.trim($('#emailEmetteur').val()) != '') {
	                if(domainesEmetteur.length) {
	                    var ind = $.trim($('#emailEmetteur').val()).indexOf("@");
	                    var emetteurDomain = $.trim($('#emailEmetteur').val()).substr((ind+1));
	                    var domainOK = false;
	                    $.each(domainesEmetteur, function(key,domain) {
	                        if(domain == emetteurDomain || $.trim($('#emailEmetteur').val()) == domain) {
	                            domainOK = true;
	                        }
	                    });
	                    if(!domainOK) {
	                        this.onError();
	                    }
	                }
	            }
	            promises.push(enregistrerSujetEtEmetteur(datas));
			}
			this.handle(Promise.all(promises));
		},
		onSuccess: function () {
			sujet = $('#sujetId').val();
			emailEmetteur = $('#emailEmetteur').val();
			emailReponse = $('#emailReponse').val();
            emetteur = $('#nomEmetteur').val();
            savedHtml = messageHTML;
            success = true;
		},
		onClose: function () {
            if($('#sujetId').val() != "" && $('#nomEmetteur').val() != "" && $('#emailEmetteur').val() != "" && $('#emailReponse').val() != "") {
                $('#parameters-link-label').find('.fa-exclamation-circle').addClass('hide');
            }
            if(!this.skipMpdal && !success && leaveRedirect) {
				$('.loaderWindow').show();
				location.href = href;
			}
			this.skipMpdal = false;
        }
	});
	if(!leaveRedirect) {
		modal.onSubmit();
	}
	return Promise.resolve();
}

function enregistrerModeleEmail(datas) {
	return requeteur.enregistrerModeleEmailSansSujetNiEmetteur(datas);
}

function enregistrerSujetEtEmetteur(datas) {
	return requeteur.enregistrerSujetEmetteurEmail(datas);
}
function contentHasChanged() {
	if(typeof window.savedModeleHtml !== "undefined") {
		savedHtml = window.savedModeleHtml;
		typeMailing = window.typeMailing;
		emailReponse = window.emailReponse;
		sujet = window.sujet;
		emailEmetteur = window.emailEmetteur;
		emailReponse = window.emailReponse;
		emetteur = window.emetteur;
	}
	if (typeEditeur === 0) {
        messageHTML = parsePreHeader();
        savedHtml = replaceUserTextTags(savedHtml);
    }
	if(isGestionModele) {
		var changedTypeMailing = false;
		var changedEmailReponse = false;
		if($.trim(typeMailing) != $.trim($('#typeMailing').val())) {
			changedTypeMailing = true;
		}
		if($.trim(emailReponse) != $.trim($('#emailReponse').val())) {
			changedEmailReponse = true;
		}
	}
	var changedObject = false;
	if($.trim(sujet) != $.trim($('#sujetId').val())) {
		changedObject = true;
	}
	var changedFromEmail = false;
	if($.trim(emailEmetteur) != $('#emailEmetteur').val()) {
		changedFromEmail = true;
	}
	var changedReponseEmail = false;
	if($.trim(emailReponse) != $('#emailReponse').val()) {
		changedReponseEmail = true;
	}
	var changedFromName = false;
	if($.trim(emetteur) != $('#nomEmetteur').val()) {
		changedFromName = true;
	}
	return ($.trim(messageHTML).replace(/\s/g, '') !== $.trim(savedHtml).replace(/\s/g, '')) || changedObject || changedFromEmail || changedEmailReponse || changedFromName || changedTypeMailing || changedEmailReponse;
}

/************************************************************
 * Utiles
 */

export function getEmailDatas() {
	/* sujet */
	let sujet = $('#sujetId').val();
	sujet = replaceUserTextTags(sujet);

	/* tags et valeurs défaut des tags */
	tagsAndDefaults = getMessageTagsAndDefaults();

	/* pre header */
    if (typeEditeur === 0 || typeEditeur === 2) {
        messageHTML = parsePreHeader();
    }

	/* urls tracées */
	var urlsTraceesHref = _.map(urlsTracees, 'url');
	var urlsTraceesLibelles = _.map(urlsTracees, 'libelle');
	var urlsTraceesThemes = _.map(urlsTracees, 'idTheme');

	var savedDonneesJson = donneesJson;
	if(typeEditeur === 1) {
		savedDonneesJson = JSON.stringify(donneesJson);
	}

	var datas = {
		idOperation: idOperation,
		idTagSenderId: idTagSenderId,
		senderId: senderId,
		emetteur: $('#nomEmetteur').val(),
        emailEmetteur: $('#emailEmetteur').val(),
        emailReponse: $('#emailReponse').val(),
		sujet: sujet,
		headerHTML: headerHTML,
		messageHTML: messageHTML,
		donneesJson: savedDonneesJson,
		typeEditeur: typeEditeur,
		prefixeTag: prefixeTag,
		suffixeTag: suffixeTag,
		prefixeDesinscription: prefixeDesinscription,
		suffixeDesinscription: suffixeDesinscription,
		prefixeMiroir: prefixeMiroir,
		suffixeMiroir: suffixeMiroir,
		idTags: tagsAndDefaults.refTags,
		refTags: tagsAndDefaults.tags,
		valeursApercuTags: tagsAndDefaults.apercus,
		valeursDefautTags: tagsAndDefaults.defaultsUser,
		urlsTraceesHref: urlsTraceesHref,
		urlsTraceesThemes: urlsTraceesThemes,
		urlsTraceesLibelles: urlsTraceesLibelles,
        testPackOK: "true",
        motifsInterdits: ["generated-image-tags", "/operation/image-tags/"],
	};
	return datas;
}
