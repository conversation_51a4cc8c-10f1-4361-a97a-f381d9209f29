CKEDITOR.dialog.add('dialogEditImgZone', function(editor) {
  return {
    title: 'Ajouter une zone d\'image éditable',
    minWidth: 400,
    minHeight: 50,
    contents: [
      {
        id: 'imgZone',
        elements: [
          {
            type: 'text',
            id: 'nom',
            label: 'Nom de la zone',
            validate: CKEDITOR.dialog.validate.notEmpty("Choisisez un nom de zone"),

            setup: function(element) {
              this.setValue(element.getAttribute('data-name'));
            },

            commit: function(element) {
              element.setAttribute('class', 'edit');
              element.setAttribute('data-name',this.getValue());
            }
          },
          {
            type: 'text',
            id: 'widthMax',
            label: 'Largeur maximum',
            validate: function() {
                if (!this.getValue()){
                    alert('Renseignez une largeur maximum');
                    return false;
                }

                if (!this.getValue().match('^[0-9]*$')) {
                    alert('Renseignez une largeur maximum');
                    return false;
                }
            },
            setup: function(element) {
              this.setValue(element.getAttribute('data-widthMax'));
            },

            commit: function(element) {
              element.setAttribute('data-widthMax', this.getValue());
            }
          },
          {
            type: 'select',
            id: 'required',
            label: 'Zone obligatoire',
            validate: CKEDITOR.dialog.validate.notEmpty("Veuillez choisir si votre zone est obligatoire ou non."),
            items: [['oui']],
            'default': 'oui',
            setup: function(element) {
              this.setValue(element.getAttribute('data-required'));
            },

            commit: function(element) {
              element.setAttribute('data-required', this.getValue());
            }
          }
        ]
      }
    ],

    onShow: function() {
      var selection = editor.getSelection();
      var element = selection.getStartElement();

      this.setValueOf('imgZone', 'widthMax', element.$.naturalWidth);
      if (!element.hasClass('edit')) {
        this.insertMode = true;
      }
      else
        this.insertMode = false;

      this.element = element;
      if (!this.insertMode)
        this.setupContent(this.element);
    },

    onOk: function() {
        var tagElement = this.element;
        this.commitContent(tagElement);

        if (this.insertMode)
          editor.insertElement(tagElement);
    }
  };
});
