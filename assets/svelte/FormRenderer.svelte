<script>
  import { onMount } from 'svelte';

  // Importation des composants
  import Text from './ui/Text.svelte';
  import Button from './ui/Button.svelte';
  import ButtonGroup from './ui/ButtonGroup.svelte';
  import RichTextarea from './ui/RichTextarea.svelte';
  import Select from './ui/Select.svelte';
  import FileUpload from './ui/FileUpload.svelte';

  // Props
  let { formSchema } = $props();

  // État local avec runes
  let currentPageId = $state(formSchema.pages[0].id);
  let formData = $state({});
  let isLoading = $state(false);
  let error = $state(null);


  // Registre des composants
  const componentRegistry = {
    Text,
    Button,
    ButtonGroup,
    RichTextarea,
    Select,
    FileUpload
  };

  // Obtenir la page courante
  let currentPage = $derived(formSchema.pages.find(page => page.id === currentPageId));
  $inspect(currentPage);

  // Naviguer vers une autre page
  function navigateTo(pageId, values = {}) {
    // Si la page est "dynamic-previous", déterminer la page précédente en fonction de l'input-type
    if (pageId === 'dynamic-previous') {
      const inputType = formData['input-type'];
      pageId = inputType === 'text' ? 'text-input' : 'audio-upload';
    }

    // Mettre à jour les valeurs du formulaire si nécessaire
    if (Object.keys(values).length > 0) {
      formData = { ...formData, ...values };
    }

    currentPageId = pageId;
  }

  // Gérer les actions des composants
  async function handleAction(action) {
    if (!action) return;

    switch (action.type) {
      case 'navigate':
        navigateTo(action.target, action.setValues || {});
        break;

      case 'submit':
          console.log(currentPage);
          if (validateCurrentPage()) {
              if (currentPage.api) {
                await submitForm(action.target);
              } else {
                navigateTo(action.target);
              }
        } else {
            console.log('aaaaaaaaaa');
        }
        break;

      case 'copy':
        copyToClipboard(action.target, action.successMessage);
        break;

      case 'export':
        exportContent(action.target, action.options);
        break;
    }
  }

  // Valider la page courante
  function validateCurrentPage() {
    // Logique de validation simple
    const requiredFields = currentPage.components
      .filter(comp => comp.props?.required)
      .map(comp => comp.bind);

    for (const field of requiredFields) {
      if (!formData[field]) {
        error = `Le champ ${field} est requis`;
        return false;
      }
    }

    error = null;
    return true;
  }

  // Soumettre le formulaire à l'API
  async function submitForm(nextPageId) {
    try {
      isLoading = true;
      error = null;

      // Construire les données de la requête
      const requestData = {};
      const { requestMapping } = currentPage.api;

      for (const [key, value] of Object.entries(requestMapping)) {
        if (Array.isArray(value)) {
          // Si la valeur est un tableau, prendre la première valeur non nulle
          for (const field of value) {
            if (formData[field]) {
              requestData[key] = formData[field];
              break;
            }
          }
        } else {
          requestData[key] = formData[value];
        }
      }

      // Appel API
      const response = await fetch(currentPage.api.endpoint, {
        method: currentPage.api.method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error(`Erreur API: ${response.status}`);
      }

      const data = await response.json();

      // Mapper la réponse aux données du formulaire
      const { responseMapping } = currentPage.api;
      for (const [key, field] of Object.entries(responseMapping)) {
        formData = {
          ...formData,
          [field]: data[key]
        };
      }

      // Naviguer vers la page suivante
      navigateTo(nextPageId);
    } catch (err) {
      error = err.message;
      console.error('Erreur lors de la soumission du formulaire:', err);
    } finally {
      isLoading = false;
    }
  }

  // Copier dans le presse-papier
  function copyToClipboard(targetField, successMessage) {
    const text = formData[targetField];
    if (!text) return;

    navigator.clipboard.writeText(text)
      .then(() => {
        // Afficher un message de succès
        alert(successMessage || 'Copié dans le presse-papier !');
      })
      .catch(err => {
        console.error('Erreur lors de la copie:', err);
      });
  }

  // Exporter le contenu
  function exportContent(targetField, options = {}) {
    const content = formData[targetField];
    if (!content) return;

    const filename = options.filename || 'export.txt';
    const format = options.format || 'text';

    const mimeTypes = {
      text: 'text/plain',
      html: 'text/html',
      json: 'application/json'
    };

    const blob = new Blob([content], { type: mimeTypes[format] });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  // Initialiser les valeurs par défaut
  onMount(() => {
    const defaults = {};

    formSchema.pages.forEach(page => {
      page.components?.forEach(component => {
        if (component.props?.defaultValue && component.bind) {
          defaults[component.bind] = component.props.defaultValue;
        }
      });
    });

    formData = defaults;
  });

  // Fonction pour obtenir la valeur d'un champ lié
  function getBindValue(component) {
    if (!component.bind) return undefined;
    return formData[component.bind];
  }

  // Fonction pour mettre à jour la valeur d'un champ lié
  function updateBindValue(component, value) {
      console.log(component, value);
    if (!component.bind) return;
    formData = { ...formData, [component.bind]: value };
  }
</script>

<div class="form-container">
  <h1>{formSchema.title}</h1>
  <p>{formSchema.description}</p>

  {#if isLoading}
    <div class="loading">
      <span class="spinner"></span>
      <p>Chargement en cours...</p>
    </div>
  {:else if error}
    <div class="error">
      <p>{error}</p>
    </div>
  {/if}

  <div class="page">
    <h2>{currentPage.title}</h2>

    <!-- Rendu des composants -->
    {#each currentPage.components || [] as component}
      {#if component.component === 'ButtonGroup' && component.children}
        <svelte:component
          this={componentRegistry[component.component]}
          {...component.props}
        >
          {#each component.children as child}
            <svelte:component
              this={componentRegistry[child.component]}
              {...child.props}
              on:click={() => handleAction(child.action)}
            />
          {/each}
        </svelte:component>
      {:else}
        <svelte:component
          this={componentRegistry[component.component]}
          {...component.props}
          value={getBindValue(component)}
          oninput={(e) => updateBindValue(component, e.detail)}
        />
      {/if}
    {/each}

    <!-- Rendu des actions -->
    {#if currentPage.actions}
      <div class="actions">
        {#each currentPage.actions as action}
          <svelte:component
            this={componentRegistry[action.component]}
            {...action.props}
            on:click={() => handleAction(action.action)}
          />
        {/each}
      </div>
    {/if}
  </div>
</div>

<style>
  .form-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
  }

  .page {
    margin-top: 2rem;
  }

  .actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    justify-content: flex-end;
  }

  .loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 2rem 0;
  }

  .spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 2s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .error {
    background-color: #ffebee;
    color: #c62828;
    padding: 1rem;
    border-radius: 4px;
    margin: 1rem 0;
  }
</style>
