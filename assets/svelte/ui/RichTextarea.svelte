<script>
  let {
    id = '',
    label = '',
    placeholder = '',
    rows = 5,
    value = '',
    required = false,
    readonly = false,
    autofocus = false
  } = $props();

  // Événement personnalisé pour la mise à jour de la valeur
  function handleInput(e) {
    const newValue = e.target.value;
    dispatch('input', newValue);
  }

  function dispatch(name, detail) {
    const event = new CustomEvent(name, { detail });
    document.dispatchEvent(event);
  }
</script>

<div class="textarea-container">
  {#if label}
    <label for={id}>{label}{required ? ' *' : ''}</label>
  {/if}

  <textarea
    {id}
    {placeholder}
    {rows}
    {required}
    {readonly}
    {autofocus}
    {value}
    oninput={handleInput}
  ></textarea>
</div>

<style>
  .textarea-container {
    margin-bottom: 1rem;
  }

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: inherit;
    font-size: 1rem;
    resize: vertical;
  }

  textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
  }

  textarea[readonly] {
    background-color: #f9f9f9;
    cursor: default;
  }
</style>
