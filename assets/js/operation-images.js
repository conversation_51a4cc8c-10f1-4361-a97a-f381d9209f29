const $ = require('jquery');
global.$ = global.jQuery = $;
require('jquery-ui');
require('blueimp-load-image/js/load-image.all.min');
require('blueimp-load-image/js/load-image-meta');
// require('../../public/js/vendor/mosaico/vendor/load-image.all.min');
require('../../public/js/vendor/mosaico/vendor/canvas-to-blob.min');
require('../../public/js/vendor/mosaico/vendor/jquery.iframe-transport');
require('../../public/js/foundation-6.4.2.min');
require('blueimp-file-upload/js/jquery.fileupload.js');
// require('../../public/js/vendor/mosaico/vendor/jquery.fileupload-process.js');
require('blueimp-file-upload/js/jquery.fileupload-process.js');
// require('../../public/js/vendor/mosaico/vendor/jquery.fileupload-image.js');
require('blueimp-file-upload/js/jquery.fileupload-image.js');
// require('../../public/js/vendor/mosaico/vendor/jquery.fileupload-validate.js');
require('blueimp-file-upload/js/jquery.fileupload-validate.js');
const Mustache = require('../../public/js/vendor/mustache.min');
global.Mustache = Mustache;
const routes = require('../../assets/js/fos_js_routes.json');
const Routing = require('../../public/bundles/fosjsrouting/js/router');
Routing.setRoutingData(routes);
global.Routing = Routing;
const Translator = require('../../public/bundles/bazingajstranslation/js/translator.min');
global.Translator = Translator;
require('../../public/js/src/utils');
require('../../public/js/pages/operation-browser');
require('cropperjs/dist/cropper');
