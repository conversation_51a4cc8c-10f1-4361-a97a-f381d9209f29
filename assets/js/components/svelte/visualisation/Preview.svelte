<script>
	import _ from "lodash";
	import Translator from "bazinga-translator";
	const T = Translator;
	import { onMount, tick, createEventDispatcher } from "svelte";
	const dispatch = createEventDispatcher();
	import { bind } from "svelte-simple-modal";
	import { createPopperActions } from "svelte-popperjs";
	import * as utils from "Components/utils";
	import {
		edition,
		selectedFields,
		selectedTypeParams,
		chart,
		visualisationFactory,
		selectedType,
		currentParams,
		lastDatas,
		esTransformer,
		lastParameters,
		elasticQuery,
		modal,
		visualisation,
		parametersHasChanged,
		selectedColorScheme,
		isSampleRequest,
		selectedSampleObject,
		previewComparison,
		comparisonFilter,
		isAdmin
	} from "Components/svelte/visualisation/stores";
	import {
		generateParameters,
		checkBucketLength,
		addPreviewOnlyParameters,
	} from "Components/svelte/visualisation/visualisation";
	import { clickOutside } from "Components/svelte/directives/clickOutside.js";

	// COMPONENTS
	import ElasticQuery from "Components/svelte/visualisation/modal/ElasticQuery";
	import Popover from "Components/svelte/visualisation/Popover";
	import PopoverColorScheme from "Components/svelte/visualisation/PopoverColorScheme";
	import { updateLegend } from "Components/utils";
	import ExportSample from "Components/svelte/visualisation/modal/ExportSample.svelte";

	// PROPS
	let previewEl;
	let jqPreviewEl;
	let error = false;
	let previewable = true;
	let initialized = false;

	// REACTIVE STATEMENTS
	$: {
		previewable = true;
		$selectedTypeParams.forEach(zone => {
			if (!("optional" in zone) || zone.optional === false) {
				if (
					!(zone.id in $selectedFields) ||
					$selectedFields[zone.id].length === 0
				) {
					previewable = false;
				}
			}
		});
	}

	// Pour simplifier le debug en console
	$: window.chart = $chart;

	// EVENTS
	onMount(async () => {
		jqPreviewEl = previewEl ? jQuery(previewEl) : null;

		jqPreviewEl.on("visualisation.selfUpdate", async () => {
			await $esTransformer.update($chart);
			utils.updateLegend($chart);
		});

		jqPreviewEl.on("visualisation.updateTitle", async (e, data) => {
			if ($visualisation) {
				$visualisation.display_name = data.title;
			}
		});
	});

	// ACTIONS
	// Initialisation appelée une fois que le composant de la page est chargé
	export const init = async function () {
		if ($edition) {
			await new Promise(resolve => setTimeout(resolve, 100));
			await refresh(addPreviewOnlyParameters($visualisation.params));
		}
		await tick();
		initialized = true;
		dispatch("ready");
	};

	// Mise à jour de la prévisualisation avec chargement des données
	// @TODO utiliser le construct de elasticTransformer plutôt que de dupliquer le code
	export const refresh = async function (forcedParams = null) {

		let initalParams =
				forcedParams !== null ? forcedParams : generateParameters(true);

		if (typeof initalParams.sample !== "undefined" && initalParams.sample !== null) {
			$isSampleRequest = true;
		}

		if (!previewable) {
			return false;
		}

		startLoading();

		const width = jqPreviewEl.width();
		const height = jqPreviewEl.height();
		$chart = $visualisationFactory.create($selectedType.name);
		$chart.preview = true;
		currentParams.refresh(forcedParams);

		const _params = _.cloneDeep($currentParams);
		$currentParams.state = _params.state = jQuery.extend(initalParams.state, _params.state);
		
		let preRequestResults = await $esTransformer.preRequest($selectedType.name, _params.state, _params, $chart, $chart);
		if (typeof preRequestResults.state !== "undefined") {
			$currentParams.state = _params.state = preRequestResults.state;
		}

		if (typeof $chart.initializeState !== 'undefined') {
			_params.state = Object.assign(_params.state, $chart.initializeState(null, { params : _params }));
		}

		try {
			let res = await $esTransformer.fetchAndTransform(
				_params,
				$selectedType.name,
				0,
				_params.state
			);
			checkBucketLength(res.originalDatas, $currentParams.buckets);
			$lastDatas = _.cloneDeep(res.originalDatas);
			jqPreviewEl.html("");
			if (
				$esTransformer.noDataTypes.indexOf($selectedType.name) === -1 &&
				Array.isArray(res.datas) &&
				!res.datas.length
			) {
				jqPreviewEl.html(
					'<span class="preview-error">' +
						T.trans("js.visualisation.empty") +
						"</span>"
				);
			} else {
				const chartParameters = $esTransformer.convertParams(
					_params,
					res.datas,
					$esTransformer.typeMapping[$selectedType.name].converter
				);
				if (typeof res.otherDatas !== "undefined") {
					$chart.otherDatas = res.otherDatas;
				}
				await $chart.construct(chartParameters, width, height, "#preview");
				if ($chart.options.legend && $chart.showLegendForPrintOnly === false) {
					utils.showLegend($chart);
				}
				jQuery(".preview-text").text(T.trans("general.reload"));
				lastParameters.refresh(forcedParams);
			}
			if (typeof $chart.onFinishPreview === "function") {
				$chart.onFinishPreview();
			}

			if (initialized) {
				await simulateComparison();
			}
			dispatch("updated");
		} catch (e) {
			console.error(e);
			error = true;
			jqPreviewEl.html(
				`<span class="preview-error">${Translator.trans(
					"js.visualisation.error"
				)}</span>`
			);
		}
	};

	// Mise à jour de la prévisualisation sans mettre à jour les données
	export const render = function (clear = false) {
		if (!previewable || typeof $chart.params === "undefined") {
			return false;
		}
		currentParams.refresh();
		const chartParameters = $esTransformer.convertParams(
			_.cloneDeep($currentParams),
			$chart.params.data,
			$esTransformer.typeMapping[$selectedType.name].converter
		);
		chartParameters.esParameters.state = $chart.state;
		$chart.setParams(chartParameters);
		$chart.resetSavedColors();
		$chart.preview = true;
		if (clear) {
			reset();
			$chart.reset();
		}
		$chart.render("#preview");
		if ($chart.options.legend) {
			utils.updateLegend($chart);
		}
		dispatch("updated");
	};

	export const simulateComparison = async function(update = false) {
		if ($previewComparison) {
			let comparisonFilterSimulated = null;
			if ($isSampleRequest && $selectedSampleObject) {
				// Si la comparaison provient du sample
				comparisonFilterSimulated = $selectedSampleObject.comparisonFilter;
			} else if ($comparisonFilter) {
				// Si la comparaison provient d'un filtre ajouté manuellement
				comparisonFilterSimulated = $comparisonFilter;
			}
			let comparisonParams = _.clone($chart.params.esParameters);
			comparisonParams.comparison = true;
			let result = await $esTransformer.fetchAndTransform(
				comparisonParams,
				$selectedType.name,
				0,
				comparisonParams.state,
				true
			);
			$chart.updateComparison(result.datas, comparisonFilterSimulated);
		} else {
			if (typeof $chart.resetComparison === "function") {
				$chart.resetComparison();
				render();
			}
		}
	}

	// Suppression de la prévisualisation en cours
	export const reset = function () {
		if (jqPreviewEl) {
			jqPreviewEl.html("");
		}
	};

	// Mise à jour de la taille de la prévisualisation
	export const resize = function () {
		if (jqPreviewEl && typeof $chart.resize === "function") {
			$chart.resize(jqPreviewEl);
		}
	};

	// Affichage du loader
	export const startLoading = function () {
		if (jqPreviewEl) {
			jqPreviewEl.html(
				'<div data-controller="loader" data-loader-size-value="40" class="p-5" />'
			);
		}
	};

	// Affichage de la modale contenant la dernière requête elasticsearch
	function showElasticQueryModal() {
		modal.set(bind(ElasticQuery));
	}

	// Affichage de la modale permettant de sauvegarder un sample
	function showExportSampleModal() {
		modal.set(bind(ExportSample));
	}

	// POPOVER
	let showTooltip = false;
	const [popperRef, popperContent, getInstance] = createPopperActions();
	const popperOptions = {
		placement: "bottom-start",
		modifiers: [{ name: "offset", options: { offset: [0, 0] } }],
	};
</script>

<svelte:window on:resize={_.debounce(resize, 300)} />

<div class="card card-form sticky-under-navbar">
	<div class="card-body">
		<div class="card-title d-flex justify-content-between mb-3">
			<div class="d-flex align-items-center">
				<div
					role="button"
					class="form-control form-select me-2"
					use:popperRef
					on:click|preventDefault|stopPropagation={() =>
						(showTooltip = !showTooltip)}
				>
					{$selectedColorScheme?.label ?? "Palette de couleur"}
				</div>
				<button
					disabled={!previewable}
					class:btn-notification={$parametersHasChanged && previewable}
					class="btn btn-tertiary"
					on:click={() => refresh()}
				>
					<span class="preview-text">{T.trans("general.preview")}</span>
				</button>
			</div>
			{#if $elasticQuery?.debug?.queries}
				<div>
					<button
							class="btn btn-icon btn-tertiary"
							on:click={showElasticQueryModal}><i class="fa fa-code" /></button
					>
					{#if $isAdmin}
						<button
								class="btn btn-icon btn-tertiary"
								on:click={showExportSampleModal}><i class="fal fa-vial-circle-check" /></button
						>
					{/if}
				</div>
			{/if}
		</div>
		<div class="card-title">
			{!_.isEmpty($visualisation?.display_name) ? $visualisation?.display_name : $visualisation?.title ?? ""}
		</div>
		<div id="preview-container" class="sticky-top">
			<div class="mt-2" bind:this={previewEl} id="preview" />
			<div class="clearfix" />
			<div id="visu-actions" />
		</div>
	</div>
</div>

{#if showTooltip}
	<div
		style="z-index: 2000"
		use:popperContent={popperOptions}
		use:clickOutside
		on:clickOutside={() => (showTooltip = false)}
	>
		<Popover placement="bottom" classes="popover-select" showArrow={false}>
			<PopoverColorScheme bind:showTooltip />
		</Popover>
	</div>
{/if}
