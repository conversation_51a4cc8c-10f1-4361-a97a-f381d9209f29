<script>
	import _ from "lodash";
	import * as utils from "Components/utils";
	import Translator from "bazinga-translator";
	const T = Translator;
	import { onMount, tick } from "svelte";
	import { dndzone } from "svelte-dnd-action";
	import {
		getListCalcul,
		getListRegroupement,
		addSubForm,
		addRangeFromTo,
	} from "Components/svelte/visualisation/visualisation";
	import {
		selectedSource,
		currentParams,
	} from "Components/svelte/visualisation/stores";

	// PROPS
	export let showTooltip;
	export let field = {};

	let subFormContainer;
	let calculs = [];
	let regroupements = [];
	let hasCustomSort = false;
	let customSortValues = [];
	let previousForm = [];

	// EVENTS
	onMount(async () => {
		if (typeof field.form === "undefined") {
			field.form = {};
		}
		previousForm = _.clone(field.form);
		if (field.zone.type === "aggregation") {
			calculs = getListCalcul(field);
			if (typeof field.calcul === "undefined") {
				field.calcul = calculs[0].type;
			}

			await onSelectAggregationChange(false);
			await tick();

		} else if (field.zone.type === "bucket") {
			let hideBucketTypes = field.zone?.hide_bucket_types ?? [];
			regroupements = getListRegroupement(field.type, hideBucketTypes);
			if (typeof field.group_type === "undefined") {
				field.group_type = regroupements[0].type;
			}

			// Restauration du tri personnalisé
			if ("sort_custom" in field) {
				hasCustomSort = true;
				customSortValues = field.sort_custom.map(v => ({ id: v, name: v }));
			}

			await onSelectRegroupementChange(false);
			await tick();

			// Restauration des ranges
			let ranges = getFormValue(field.form, "ranges");
			if (ranges && ranges.length > 0) {
				let jQForm = jQuery(subFormContainer);
				ranges.forEach(r => {
					addRangeFromTo(jQForm, r.from, r.to, r.key);
				});
				jQForm.find("table.range").find(".range-row").first().remove();
			}
		}
		await onSubFormChange(true);
	});

	// Event modification du type de bucket
	async function onSelectRegroupementChange(triggerChange = true) {
		let jQform = jQuery(subFormContainer);

		addSubForm(jQform, field.group_type, field.form, field.zone.hide_form);

		if (field.group_type === "range_field" || field.group_type === "date_range") {
			jQform.closest(".popover").addClass("popover-range");
		} else {
			jQform.closest(".popover").removeClass("popover-range");
		}

		if (triggerChange) {
			onSubFormChange(false);
		}

		currentParams.refresh();
	}

	// Event modification du type d'aggregation
	async function onSelectAggregationChange() {
		let jQform = jQuery(subFormContainer);

		addSubForm(jQform, field.calcul, field.form, field.zone.hide_form);

		currentParams.refresh();
	}

	// Event modification d'une des valeurs du formulaire
	async function onSubFormChange(init = false) {
		// C'est moche hein ?
		await tick();

		let jQform = jQuery(subFormContainer);
		field.form = utils.serializeFormValues(jQform);

		if (hasFormValueChanged("sort") || init === true) {
			let sortValue = getFormValue(field.form, "sort");
			hasCustomSort = sortValue === "_custom";

			if (hasCustomSort) {
				if (customSortValues.length === 0) {
					const filters = generateParameters().filters;
					const result = await utils.getCompletion(
						$selectedSource.id,
						field.label,
						filters
					);
					customSortValues = result
						.splice(0, 19)
						.map(v => ({ id: v, name: v }));
					field.sort_custom = customSortValues.map(v => v.name);
				}
			} else {
				customSortValues = [];
				delete field.sort_custom;
			}

			const jQsortAggs = jQform.find('select[name="terms[sort_agg]"]');
			if (sortValue === "_count") {
				let hidden = false;
				if (field.zone.hide_form) {
					hidden = field.zone.hide_form.find(item => {
						if (typeof item === "object") {
							return item.field_type === field.group_type && item.form_fields.includes('sort_agg');
						}
					}) ? true : false;
				}
				if (!hidden) {
					jQsortAggs.closest(".form-group").removeClass("d-none");
				}
				updateSortAggSelect(getFormValue(previousForm, "sort_agg"));
			} else {
				jQsortAggs.closest(".form-group").addClass("d-none");
			}
			field.form = utils.serializeFormValues(jQform);
		}

		if (field.group_type === "range_field") {
			let ranges = [];
			jQuery.each(jQform.find(".range-row"), function () {
				const range = {};
				const from = jQuery(this).find("input").first().val();
				const to = jQuery(this).find("input").eq(1).val();
				const key = jQuery(this).find("input").last().val();
				if (!from.isEmpty()) {
					range["from"] = from;
				}
				if (!to.isEmpty()) {
					range["to"] = to;
				}
				if (!key.isEmpty()) {
					range["key"] = key;
				}
				ranges.push(range);
			});
			field.form = [{ name: "ranges", value: ranges }];
		}

		previousForm = _.cloneDeep(field.form);
		currentParams.refresh();
	}

	function hasFormValueChanged(name) {
		return getFormValue(field.form, name) !== getFormValue(previousForm, name);
	}

	function getFormValue(form, name) {
		return (
			form.length > 0 && _.find(form, { name }) && _.find(form, { name }).value
		);
	}

	// Permet de mettre à jour la liste des aggregations sélectionnables pour le tri par valeur
	function updateSortAggSelect(currentValue) {
		const aggregations = generateParameters().aggregations;

		const jQsortAggs = jQuery(subFormContainer).find(
			'select[name="terms[sort_agg]"]'
		);
		if (aggregations.length) {
			jQsortAggs.each(function () {
				const jQthis = jQuery(this);
				let val = currentValue;
				let valExists = false;
				jQthis.empty();
				aggregations.forEach(agg => {
					let value =
						agg.type === "count" ? "_count" : agg.champ + "_" + agg.type;
					let label = agg.label + " " + agg.agg_label;
					valExists = valExists || val === value;
					jQthis.append("<option value='" + value + "'>" + label + "</option>");
				});
				if (!val || val === "" || !valExists) {
					val = jQthis.find("option").eq(0).attr("value");
				}
				jQthis.val(val);
				jQthis.change();
			});
			if (aggregations.length === 1) {
				jQsortAggs.each(function () {
					jQuery(this).closest(".form-group").addClass("d-none");
				});
			}
		} else {
			jQsortAggs.each(function () {
				const jQthis = jQuery(this);
				jQthis.empty();
				jQthis.closest(".form-group").addClass("d-none");
			});
		}
	}

	function generateReversePaths(field) {
		const parts = field.label.split('.');
		const reversePaths = [];

		for (let i = 1; i < parts.length; i++) {
			reversePaths.push(parts.slice(0, i).join('.'));
		}

		return reversePaths;
	}

	// DRAG AND DROP
	function handleSort(e) {
		customSortValues = e.detail.items;
		field.sort_custom = customSortValues.map(v => v.name);
		currentParams.refresh();
	}
</script>

<div class="dialog-add-field">
	<div class="bucket-form-type">
		{T.trans("js.visualisation.popover.type")} : <span>{field.type}</span>
	</div>
	{#if field.zone.type === "aggregation"}
		<div class="mb-3">
			<select
				class="form-control form-select select-calcul"
				bind:value={field.calcul}
				on:change={onSelectAggregationChange}
			>
				{#each calculs as calcul}
					<option value={calcul.type}>{calcul.label}</option>
				{/each}
			</select>
		</div>
		{#if field.calcul === "reverse_nested"}
			<div class="mb-3">
				<label for="select-reverse-path-{field.id}"
					>{T.trans("js.visualisation.popover.reverse_path")}</label
				>
				<select
					id="select-reverse-path-{field.id}"
					class="form-control form-select select-reverse-path"
					bind:value={field.reverse_path}
					on:change={onSelectAggregationChange}
				>
					<option value={null}>Document</option>
					{#each generateReversePaths(field) as path}
						<option value={path}>{path}</option>
					{/each}
				</select>
			</div>
		{/if}
	{:else if field.zone.type === "bucket"}
		<div class="mb-3">
			<label for="select-regroupement-{field.id}"
				>{T.trans("js.visualisation.popover.regroup")}</label
			>
			<select
				id="select-regroupement-{field.id}"
				class="form-control form-select select-regroupement"
				bind:value={field.group_type}
				on:change={onSelectRegroupementChange}
			>
				{#each regroupements as regroupement}
					<option value={regroupement.type}>{regroupement.label}</option>
				{/each}
			</select>
		</div>
	{/if}
	<form
		class="regroupement-form"
		bind:this={subFormContainer}
		on:change={onSubFormChange}
	/>

	{#if hasCustomSort}
		<ul
			class=""
			use:dndzone={{ items: customSortValues }}
			on:consider={handleSort}
			on:finalize={handleSort}
		>
			{#each customSortValues as value (value.id)}
				<li class=""><i class="fa fa-arrows-alt me-1" /> {value.name}</li>
			{/each}
		</ul>
	{/if}

	<div class="text-center">
		<button
			class="btn btn-primary btn-sm btn-popover"
			on:click={() => (showTooltip = false)}
			>{T.trans("general.validate")}</button
		>
	</div>
</div>

<style>
	ul {
		width: 100%;
		padding: 1em;
	}
	ul > li {
		text-align: left;
		border: 1px solid grey;
		margin: 0.2em;
		padding-left: 0.5em;
	}
	ul > li:hover {
		cursor: pointer;
	}
</style>
