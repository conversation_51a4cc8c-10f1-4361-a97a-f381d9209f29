<script>
	import { onMount } from "svelte";
	import Translator from "bazinga-translator";
	const T = Translator;

	export let name;
	let id = "chart_params_options_" + name;
	export let isCustom = true;

	onMount(() => {});

	function onChange() {
		jQuery(".svelte-colorpicker").each((i, group) => {
			jQuery(group).data("colorpicker").setIsCustom(isCustom);
		});
	}
</script>

<label for={id}>{T.trans("js.visualisation.form.colorpicker.label")}&nbsp;:</label>
<select
	bind:value={isCustom}
	on:change={onChange}
	class="form-select"
	data-name={name}
	name={id}
	{id}
>
	<option value={true}>{T.trans("js.visualisation.form.color_custom")}</option>
	<option value={false}>{T.trans("js.visualisation.form.color_palette_reference")}</option>
</select>
