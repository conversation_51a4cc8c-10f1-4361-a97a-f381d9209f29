<script>
	import _ from "lodash";
	import { onMount, createEventDispatcher } from "svelte";
	const dispatch = createEventDispatcher();
	import { getCompletion } from "Components/utils";
	import Translator from "bazinga-translator";
	const T = Translator;

	// COMPONENTS
	import SearchList from "Components/svelte/filter/SearchList";

	// PROPS
	export let id;
	export let source;
	export let field;
	export let filter = {};
	export let inclusion;
	export let onChange = () => {};
	export let valid;

	let loading = true;
	let items = [];
	let selected = [];
	let emptyValues = false;
	let nullValues = false;
	let mounted = false;
	let parent = "-1";

	// REACTIVE STATEMENTS
	$: {
		if (mounted) {
			let innerFilter = selected;
			let type = "terms";

			if (nullValues) {
				type = "missing";
				innerFilter = { missing: field.label };
				if (selected) {
					innerFilter.terms = [...selected];
				}
			}

			if (emptyValues) {
				if (nullValues) {
					innerFilter.terms = [...selected, ""];
				} else {
					innerFilter = [...selected, ""];
				}
			}

			filter = {
				field_type: field.type,
				type: type,
				label: field.label,
				filter: innerFilter,
				inclusion: inclusion,
				parent: parent,
				source: source.id,
			};

			valid = nullValues || emptyValues || selected.length > 0;
		}
	}

	// EVENTS
	onMount(async () => {
		items = await getCompletion(source.id, field.label, []);
		loading = false;
		if (filter.parent) {
			parent = filter.parent;
		}
		if (filter.filter && filter.type === "missing") {
			nullValues = true;
			if ("terms" in filter.filter) {
				emptyValues = true;
				selected = filter.filter.terms.filter(i => i !== "");
			}
		} else if (filter.filter) {
			if (_.isArray(filter.filter)) {
				selected = filter.filter.filter(i => i !== "");
				if (filter.filter.includes("")) {
					emptyValues = true;
				}
			} else {
				selected = [filter.filter];
			}
		}
		mounted = true;
	});

	// ACTIONS
	export function getFilter() {
		return filter;
	}
</script>

<div class="filter-box-form-container">
	{#if loading}
		<div data-controller="loader" data-loader-size-value="40" class="p-5" />
	{:else}
		<SearchList bind:selected {items} {inclusion} {onChange} addRawValueOnKeydown={true} />
		<div class="d-flex mt-2">
			<div class="form-group me-2">
				<div class="form-check form-switch">
					<input
						bind:checked={nullValues}
						type="checkbox"
						class="form-check-input"
						id="{id}_null"
					/>
					<label class="checkbox-switch form-check-label" for="{id}_null">
						NULL
					</label>
				</div>
			</div>
			<div class="form-group">
				<div class="form-check form-switch">
					<input
						bind:checked={emptyValues}
						type="checkbox"
						class="form-check-input"
						id="{id}_empty"
					/>
					<label class="checkbox-switch form-check-label" for="{id}_empty">
						{T.trans("js.filters.empty")}
					</label>
				</div>
			</div>
		</div>
	{/if}
</div>
