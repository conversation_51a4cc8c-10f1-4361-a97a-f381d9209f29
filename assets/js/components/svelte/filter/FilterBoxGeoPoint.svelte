<script>
	import _ from "lodash";
	import { onMount, createEventDispatcher } from "svelte";
	const dispatch = createEventDispatcher();
	import allDepartements from "alienor-charts/public/departements.json";
	import allRegions from "alienor-charts/public/regions.json";
	import SearchList from "Components/svelte/filter/SearchList";
	import Translator from "bazinga-translator";
	const T = Translator;

	const departements = Object.keys(allDepartements).map(key => ({
		id: key,
		...allDepartements[key],
	}));
	const regions = Object.keys(allRegions).map(key => ({
		id: key,
		...allRegions[key],
	}));

	// PROPS
	export let id;
	export let inclusion;
	export let field;
	export let filter = {};
	export let source;
	export let valid;

	let mounted = false;
	let selectedType = "departement";
	let selectedDepartements = [];
	let selectedRegions = [];
	let nullValues = false;
	let parent = "-1";

	// REACTIVE STATEMENTS
	$: {
		if (mounted) {
			let innerFilter;
			let type = "geo_polygon";
			let params = {};

			if (nullValues) {
				type = "missing";
				innerFilter = { missing: field.label };
			} else {
				const optionsVal = [];
				const optionsText = [];
				if (selectedType === "departement") {
					selectedDepartements.forEach(d => {
						optionsVal.push(d.id);
						optionsText.push(d.name);
					});
				} else if (selectedType === "region") {
					selectedRegions.forEach(r => {
						optionsVal.push(r.id);
						optionsText.push(r.name);
					});
				}
				innerFilter = selectedType + " : " + optionsText.join(", ");
				params.typeValue = JSON.stringify(optionsVal);
				params.typeRegroupment = selectedType;
				const correspondance = {};
				for (let i = 0; i < optionsVal.length; i++) {
					correspondance[optionsVal[i]] = optionsText[i];
				}
				params.otherDatas = [correspondance];
			}

			filter = {
				field_type: field.type,
				type: type,
				label: field.label,
				filter: innerFilter,
				params: params,
				inclusion: inclusion,
				parent: parent,
				source: source.id,
			};

			valid = nullValues || (selectedType == 'departement' && selectedDepartements.length > 0) || (selectedType == 'region' && selectedRegions.length > 0);
		}
	}

	// EVENTS
	onMount(() => {
		if (filter.parent) {
			parent = filter.parent;
		}
		if (filter.filter && filter.type === "missing") {
			nullValues = true;
		} else if (filter.filter) {
			if (filter.filter.includes("departement")) {
				selectedType = "departement";
				selectedDepartements = _.map(filter.params.otherDatas[0], (name, id) =>
					_.find(departements, { id })
				);
			} else if (filter.filter.includes("region")) {
				selectedType = "region";
				selectedRegions = _.map(filter.params.otherDatas[0], (name, id) =>
					_.find(regions, { id })
				);
			}
		}
		mounted = true;
	});
</script>

<div class="filter-box-form-container">
	<div class="mb-3">
		<select class="form-select" bind:value={selectedType}>
			<option />
			<option value="departement">{T.trans("js.filters.geo.department")}</option>
			<option value="region">{T.trans("js.filters.geo.region")}</option>
		</select>
	</div>
	
	{#if mounted}
		<div>
			{#if selectedType === "departement"}
				<SearchList
					bind:selected={selectedDepartements}
					items={departements}
					labelProperty="name"
					{inclusion}
				/>
			{:else if selectedType === "region"}
				<SearchList
					bind:selected={selectedRegions}
					items={regions}
					labelProperty="name"
					{inclusion}
				/>
			{/if}
		</div>
	{/if}
	
	<div class="mt-2">
		<div class="form-check form-switch">
			<input
				bind:checked={nullValues}
				type="checkbox"
				class="form-check-input"
				id="{id}_null"
			/>
			<label class="checkbox-switch form-check-label" for="{id}_null">
				NULL
			</label>
		</div>
	</div>
</div>
