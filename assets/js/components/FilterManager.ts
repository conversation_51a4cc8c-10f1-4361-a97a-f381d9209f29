// @ts-nocheck
import acUtils from "../alienor-charts/src/js/utils";
import {getFilterLabel as utilsGetFilterLabel} from "./utils";
import * as moment from "moment";
import * as _ from "lodash";
import {
    FilterFieldType,
    FilterFilter,
    FilterItem,
    FilterType,
    FilterValue,
    FilterValueGeo,
    getFilterViewer
} from "./FilterViewer";
import type {VisualisationBase, VisualisationType} from "../alienor-charts/index";
import type {DashboardGrid} from "../index";

export type FilterEvent = {
    [key: string]: any,
    element: HTMLElement
    type: FilterType
    field_type: FilterFieldType
    parent: string | number
    ranges: [number, number]
    chart: VisualisationBase
}

export default class FilterManager {

    filters = [];
    dashboard: DashboardGrid;
    onUpdateFilters;
    filterViewer = getFilterViewer()

    constructor(dashboard: DashboardGrid, onUpdateFilters?: () => void) {
        this.dashboard = dashboard;
        this.onUpdateFilters = onUpdateFilters
    }

    /**
     * Initialisation
     */
    init() {
        this.initListener();
    }

    /**
     * Restaure un tableau de filtre
     * @param filters - tableau de filtres à restaurer
     */
    restore (filters: FilterValue[]): void {
        this.filterViewer.removeAll();
        this.dashboard.filters = [];
        filters.forEach(filter => {
            this.addFilter(filter, true);
        });
        // callback de mise à jour des filtres
        this.onUpdateFilters(this.getFilters());
    }

    /**
     * Rafraichit la liste visuelle des filtres
     */
    refresh (): void {
        this.getItems().forEach(item => {
            item.label = this.getFilterLabel(item.value);
        });
        this.filterViewer.refresh();
        // callback de mise à jour des filtres
        this.onUpdateFilters(this.getFilters());
    }

    /**
     * Supprime tous les filtres
     */
    resetFilters (): void {
        let items = this.getItems();
        items.forEach(item => {
            // ça marche pas sans le timeout :)
            setTimeout(() => {
                this.removeTag(item);
            }, 0);
        });
        this.dashboard.onResetFilters();
    }

    /**
     * Retourne la liste des tags
     */
    getItems (): FilterItem[] {
        return this.filterViewer.getItems();
    }

    /**
     * Ajoute un tag/filtre
     */
    addFilter (filter: FilterValue, disableCallback = false): void {
        filter.nested = !!this.dashboard.nestedFilters;
        this.dashboard.filters.push(filter);
        this.filterViewer.addFilter({
            value: filter,
            label: this.getFilterLabel(filter)
        });
        if (!disableCallback) {
            // callback de mise à jour des filtres
            this.onUpdateFilters(this.getFilters());
        }
    }

    /**
     * Envoi l'évènement onFilter au dashboard
     */
    onFilter (filter?: FilterValue): void {
        this.dashboard.onFilter(filter);
    }

    /**
     * Recherche et supprime la comparaison existante sur un filtre
     * autre que celui passé en paramètre
     */
    preventMultipleComparisonFromFilter(filter?: FilterItem): void {
        if (typeof filter.value.comparison !== "undefined") {
            let filterWithComparison = this.getItems().find(item => {
                return typeof item.value.comparison !== "undefined" && item !== filter;
            });
            if (filterWithComparison) {
                this.filterViewer.removeComparisonFromFilter(filterWithComparison);
            }
        }
    }

    /**
     * Supprime un tag/filtre
     */
    removeTag (tag: FilterItem): void {
        this.filterViewer.removeFilter(tag);
    }

    /**
     * Trouve un tag en fonction de son label
     */
    getTagByLabel (label: string): FilterItem {
        let tag = null;
        for (const item of this.getItems()) {
            if (item.label === label) {
                return item;
            }
        }
        return tag;
    }

    /**
     * Trouve un tag en fonction de son parent
     */
    getTagByParent (parent: string): FilterItem|null {
        for (const item of this.getItems()) {
            if (parseInt(item.value.parent) === parseInt(parent)) {
                return item;
            }
        }
        return null;
    }

    /**
     * Trouve un tag en fonction du champ du filtre
     */
    getTagByField (field): FilterValue|null {
        for (const item of this.getItems()) {
            if (item.value.label === field) {
                return item.value;
            }
        }
        return null;
    }

    /**
     * Retourne le tableau de filtres actuels
     */
    getFilters (): FilterValue[] {
        return this.dashboard.filters;
    }

    /**
     * Vérifie si un filtre exist
     */
    filterExist (filter: FilterValue): boolean {
        let response = false;
        for (const val of this.getFilters()) {
            if (JSON.stringify(filter) === JSON.stringify(val)) response = true;
        }
        return response;
    }

    /**
     * Met à jour un tag selon son label
     */
    updateTagByLabel (label: string, filter: FilterValue, new_label = null, keepComparion = false): void {
        if (typeof this.filterViewer !== "undefined") {

            let oldTag = this.getTagByField(label);
            let tagToUpdate = this.getTagByFilterLabelOrValue(label);

            // conservation de la comparaison dans le filtre lors de sa modification
            if (keepComparion && typeof oldTag.comparison !== "undefined") {
                filter.comparison = oldTag.comparison;
            }

            for (let i = 0; i < this.dashboard.filters.length; i++) {
                if (this.dashboard.filters[i].label === filter.label) {
                    this.dashboard.filters.splice(i, 1);
                    if (oldTag.parent !== filter.parent) {
                        let chart = this.dashboard.getChartById(oldTag.parent);
                        if (chart && chart.onItemRemoved !== undefined && tagToUpdate) {
                            chart.onItemRemoved(tagToUpdate);
                        }
                        filter.updated = true;
                    }
                    break;
                }
            }

            if (oldTag) {
                filter.inclusion = oldTag.inclusion;
            }
            this.dashboard.filters.push(filter);

            if (tagToUpdate) {
                tagToUpdate.value = filter;
                if (new_label) {
                    tagToUpdate.label = new_label;
                }
            }

            this.refresh();
        }
    }

    /**
     * Trouve un tag en fonction de son label ou du champ du filtre
     */
    getTagByFilterLabelOrValue (label: string): FilterItem|null {
        for (const item of this.getItems()) {
            if (item.value.label === label || item.value.filter === label) {
                return item;
            }
        }
        return null;
    }

    /**
     * Supprime un tag selon son label
     */
    removeTagByLabel (label: string): void {
        let item = this.getTagByFilterLabelOrValue(label);
        if (item) {
            this.removeTag(item);
        }
    }

    /**
     * Retourne le label a appliqué à un filtre
     */
    getFilterLabel (filter: FilterValue): string {
        let custom_label = filter.label;

        return utilsGetFilterLabel(filter, { dashboard: this.dashboard });
    };

    getRangeOperator (range: string): string {
        let operators = {
            gte: '>=',
            gt: '>',
            lt: '<',
            lte: '<='
        };
        return operators[range];
    };

    /**
     * retourne True si le tag existe
     */
    tagExist (label: string): boolean {
        if (typeof this.filterViewer !== "undefined") {
            for (const item of this.getItems()) {
                if (typeof(item.value.filter) === "object" && Object.values(item.value.filter).includes(label)) {
                    return true;
                }

                if (item.value.label === label || item.value.filter === label) {
                    return true;
                }
            }
        }
        return false;
    };

    /**
     * Alterne le filtre entre inclursion et exclusion
     */
    toggleFilter (label: string): void {
        let tag = this.getTagByLabel(label);
        tag.value.inclusion = tag.value.inclusion === "must" ? "must_not" : "must";
        this.refresh();
        this.dashboard.onFilter(tag.value);
    };

    /**
     * Trouve un tag en fonction de son label ou du champ du filtre
     */
    getTagByTypeRegroupement (typeRegroupement: string): FilterItem|null {
        for (const item of this.getItems()) {
            if (typeof item.value.params !== 'undefined' && typeof item.value.params.typeRegroupment !== 'undefined' && item.value.params.typeRegroupment == typeRegroupement) {
                return item;
            }
        }
        return null;
    }

    /**
     * Met à jour un tag selon son label
     */
    updateTagByTypeRegroupement (typeRegroupement: string, filter: FilterValue, new_label: string): void {
        if (typeof this.filterViewer !== "undefined") {

            let oldTag = this.getTagByTypeRegroupement(typeRegroupement);
            let tagToUpdate = this.getTagByTypeRegroupement(typeRegroupement);

            for (let i = 0; i < this.dashboard.filters.length; i++) {
                if (typeof this.dashboard.filters[i].params === 'undefined' || this.dashboard.filters[i].params.typeRegroupment == 'undefined') {
                    continue;
                }
                if (this.dashboard.filters[i].label === filter.label && this.dashboard.filters[i].params.typeRegroupment === filter.params.typeRegroupment && this.dashboard.filters[i].type === filter.type) {
                    this.dashboard.filters.splice(i, 1);
                    if (oldTag.parent !== filter.parent) {
                        let chart = this.dashboard.getChartById(oldTag.parent);
                        if (chart && chart.onItemRemoved !== undefined && tagToUpdate) {
                            chart.onItemRemoved(tagToUpdate);
                        }
                        filter.updated = oldTag.inclusion;
                    }
                    break;
                }
            }

            if (oldTag) {
                filter.inclusion = oldTag.value.inclusion;
            }
            this.dashboard.filters.push(filter);
            if (tagToUpdate) {
                tagToUpdate.value = filter;
                if (typeof(new_label) !== "undefined") {
                    tagToUpdate.label = new_label;
                }
            }
            this.refresh();
        }
    };

    /**
     * Supprime un tag selon son label
     */
    removeTagByTypeRegroupement (typeRegroupement: string): void {
        let item = this.getTagByTypeRegroupement(typeRegroupement);
        if (item) {
            this.removeTag(item);
        }
    };

    removeIsochroneById (value, typeRegroupement: string, removeTag: boolean): void|false {
        let tag = this.getTagByTypeRegroupement(typeRegroupement);

        if (!tag) {
            return false;
        }

        if (removeTag) {
            if (tag.value.filterData.length === 1) {
                this.removeTagByTypeRegroupement(tag.value.params.typeRegroupment);
                return;
            }
        }

        let indexToDelete = null;
        for (let i = 0; i < tag.value.filterData.length; i++) {
            if (tag.value.filterData[i].idIsochrone == value) {
                indexToDelete = i;
            }
        }

        if (indexToDelete !== null) {
            tag.value.filterData.splice(indexToDelete, 1);
            if (tag.value.filterData.length === 0) {
                this.removeTag(tag);
            } else {
                this.onFilter();
                this.refresh();
            }
        }
    };

    /**
     * Applique des filtres selon le type de visualisation
     */
    applyVisualFilter (index: string, type: VisualisationType, filter: FilterItem) {
        'use strict';
        if (filter.value.disabled) {
            return;
        }
        let extent;
        let chart = this.dashboard.getChartById(index);
        if (type === "pieChart") {
            chart.applyVisualFilters([filter.value]);
        } else if (type === "treemapChart") {
            setTimeout(() => {
                let pValue = null;
                if (filter.value.hasOwnProperty('parentFilter')) {
                    let pTag = this.getTagByField(filter.value.parentFilter);
                    pValue = pTag.filter[0];
                }
                if (Array.isArray(filter.value.filter)) {
                    filter.value.filter.forEach(f => {
                        chart.selectNode(f, pValue);
                    });
                } else if (typeof filter.value.filter.gte !== 'undefined') {
                    chart.selectNode(filter.value.filter.gte, pValue);
                }
            }, 500);
        } else if (type === "dotRepartitionChart") {
            chart.restoreFilter(filter);
        } else if (type === "lineChart") {
            if (acUtils.isElasticType("string", filter.value.field_type)) {
                let positions = filter.value.filter.map(v => chart.x_axis[0](v));
                let min = Math.min(...positions);
                let max = Math.max(...positions);
                extent = [min, max + chart.x_axis[0].bandwidth()];
            } else {
                extent = [Math.max(chart.x_axis[0].range()[0], chart.x_axis[0](parseInt(filter.value.filter.gte))), Math.min(chart.x_axis[0].range()[1], chart.x_axis[0](parseInt(filter.value.filter.lte)))];
            }
            chart.setBrush(extent);
        } else if (type === "barChart") {
            if (acUtils.isElasticType("string", filter.value.field_type)) {
                filter.value.filter.forEach(f => {
                    chart.selectNode(f);
                });
                chart.selected = filter.value.filter;
            } else {
                if (filter.value.ranges) {

                    chart.selectNode(filter.value.ranges_name);
                    // filter.value.filter.forEach(function(f) {
                    //  });
                } else {
                    extent = [Math.max(chart.x.range()[0], chart.x(parseInt(filter.value.filter.gte))), Math.min(chart.x.range()[1], chart.x(parseInt(filter.value.filter.lte)))];
                    chart.setBrush(extent);
                }
            }
        } else if (type === "mapChart") {
            setTimeout(() => {
                chart.restoreFilter(filter.value.filter);
            }, 100);
        } else if (type === "heatmapChart") {
            chart.restoreFilter(filter);
        } else if (type === "listChart" || type === "tableChart") {
            chart.restoreFilter(filter);
        } else if (type === "selectorChart") {
            chart.applyVisualFilters([filter.value]);
        }
    };

    textStandardize (texte: string): string {
        texte = texte.toLowerCase();
        let accents = 'ÀÁÂÃÄÅàáâãäåßÒÓÔÕÕÖØòóôõöøÈÉÊËèéêëðÇçÐÌÍÎÏìíîïÙÚÛÜùúûüÑñŠšŸÿýŽž-';
        let accentsOut = "AAAAAAaaaaaaBOOOOOOOooooooEEEEeeeeeCcDIIIIiiiiUUUUuuuuNnSsYyyZz ";
        let textes = texte.split('');
        textes.forEach((letter, index) => {
            let i = accents.indexOf(letter);
            if (i != -1) {
                textes[index] = accentsOut[i];
            }
        });
        return textes.join('');
    };

    /**
     * Supprime une brush d'un/des visualisation(s)
     * @param reload -> si undefined, supprime toutes les brushs
     * @param chart -> si true, recharge également la visualisation
     */
    removeBrushChart (chart: VisualisationBase, reload = false) {
        if (reload === true) {
            chart.removeBrush();
        } else {
            chart.removeBrush();
        }
        chart.$parent.find('.brush').find('.extent').attr('width', 0);
        chart.$parent.find('.brush').find('.resize').css('display', "none");
        this.clearBrushDecoration(chart);
    }

    /**
     * Supprime les changements visuels qui ont été ajoutés par une brush
     */
    clearBrushDecoration (chart: VisualisationBase) {
        'use strict';
        let circles;
        let bars;
        if (typeof chart === "undefined") {
            circles = $('.lineChart').find('circle');
            bars = $('.barChart').find('rect.bar');
        } else {
            circles = chart.$parent.find('.lineChart').find('circle');
            bars = chart.$parent.find('.barChart').find('rect.bar');
        }
        $.each(circles, (index, val) => {
            val.classList.remove('brushed');
        });
        $.each(bars, (index, val) => {
            val.classList.remove('brushed');
        });
    }

    /**
     * Mise en place des écouteurs d'évémenents
     */
    initListener () {

        const that = this;

        /** TAGSINPUT EVENTS **/

        /**
         * S'éxécute lors de la création d'un filtre
         */
        that.filterViewer.on('itemAdded', event => {

            // Recherche et supprime la comparaison existante sur un filtre si besoin
            this.preventMultipleComparisonFromFilter(event.item);

            for (let key in event.item.value) {
                if (event.item.value[key] === "true") {
                    event.item.value[key] = true;
                }
            }

            if (that.dashboard.visualisations) {
                for (const chart of that.dashboard.visualisations) {
                    if (chart.onItemAdded !== undefined && event.item) {
                        chart.onItemAdded(event.item);
                    }
                }
            }
        });

        /**
         * S'éxécute lorsque le filtre est activé / désactivé
         */
        that.filterViewer.on('itemToggled', event => {
            let parent = that.dashboard.getChartById(event.item.value.parent);
            if (parent && ["lineChart", "barChart", "heatmapChart", "treemapChart", "pieChart", "tableChart", "listChart", "selectorChart"].indexOf(parent.name) !== -1) {
                if (event.item.value.disabled) {
                    that.filterViewer.trigger('itemRemoved', event.item, { disabled: true });
                } else {
                    that.updateTagByLabel(event.item.value.label, event.item.value);
                    if (typeof parent.applyVisualFilters === "function") {
                        parent.applyVisualFilters(that.getFilters());
                    } else {
                        that.applyVisualFilter(event.item.value.parent, parent.name, event.item);
                    }
                }
            }
            that.refresh();
            that.onFilter(event.item.value);
        });

        /**
         * S'exécute lorsque le filtre est modifié
         */
        that.filterViewer.on('itemUpdated', event => {
            let parent = that.dashboard.getChartById(event.item.value.parent);

            // Recherche et supprime la comparaison existante sur un filtre si besoin
            this.preventMultipleComparisonFromFilter(event.item);

            // détachement du filtre de sa visu parente
            if (parent && parent.disableFilterOnEdit(event.item)) {
                delete event.item.value.parent;
				parent.unselectAllNodes();
            }

            // mise à jour du filtre
            that.updateTagByLabel(event.item.value.label, event.item.value);

            // application visuelle des modifications à la visu parente
            // si elle n'a pas été détachée
            if (parent && !parent.disableFilterOnEdit(event.item)) {
                if (typeof parent.applyVisualFilters === "function") {
                    parent.applyVisualFilters(that.getFilters());
                } else {
                    that.applyVisualFilter(event.item.value.parent, parent.name, event.item);
                }
            }

            that.refresh();
            that.onFilter(event.item.value);
        });

        /**
         * S'éxécute avant la suppression d'un filtre
         */
        that.filterViewer.on('beforeItemRemove', event => {
            if (event.item.value.required) {
                event.cancel = true;
            }
            return event;
        });

        /**
         * S'éxécute après la suppression d'un filtre
         */
        that.filterViewer.on('itemRemoved', event => {
            // Execute les fonction 'onItemRemoved' de tous les widgets présent
            for (const chart of that.dashboard.visualisations) {
                if (chart.onItemRemoved !== undefined && event.item) {
                    chart.onItemRemoved(event.item);
                }
            }

            if (!event.disabled) {
                for (let i = 0; i < that.getFilters().length; i++) {
                    if (typeof event.item !== "undefined") {
                        if (that.getFilters()[i] === event.item.value) {
                            that.getFilters().splice(i, 1);
                            break;
                        }
                    }
                }
            }

            let parent = typeof event.item === "undefined" ? 0 : event.item.value.parent;
            let chart = that.dashboard.getChartById(parent);

            try {
                if (typeof event.item !== "undefined" && typeof chart !== "undefined" && typeof chart.brush === "function") {
                    that.removeBrushChart(that.dashboard.getChartById(parent));
                }
                that.onFilter(event.item.value);
            } catch (e) {
                that.onFilter(event.item.value);
            }

            if (chart && typeof chart.applyVisualFilters === "function") {
                chart.applyVisualFilters(that.getFilters());
            }

            // callback de mise à jour des filtres
            that.onUpdateFilters(that.getFilters());
        });

        /** CLICK EVENTS **/

        /**
         * treemapChart
         * TODO : filtre sur range
         */

        that.dashboard.$element.on('treemapChart.click', (e, data: FilterEvent) => {

            let chart = data.chart;

            if (data.element.__data__.depth === 0) {
                return false;
            }

            if (chart.name === "pieChart" && chart.options.zoom) {
                return false;
            }

            let classes = data.element.classList;
            let index = chart.parent;
            let nodeKey = data.element.__data__.key;
            let filterSource = chart.getSourceId();
            let filterParent: FilterValue = null;
            let filterValue = data.label;
            let field = data.field;
            let type = data.type;
            let fieldType = data.fieldType;
            let bucketType = data.bucketType;
            let parentFieldType: FilterFieldType = data.parentType;
            let parentField: FilterFieldType = data.parentField;
            let parentLabel: FilterType = data.parentLabel;
            if (data.element.__data__.data.hasOwnProperty("group")) {
                filterValue = data.element.__data__.data.group.map(d => d.name);
            }
            let depth = _.findIndex(chart.options.x, { field });
            let $siblings = $(chart.getNodes());

            if (acUtils.isElasticType('string', fieldType)) {
                type = 'term';
            }
            if (bucketType === "range_field" || fieldType === "integer") {
                if (bucketType === "range_field") {
                    let ranges = data.data.data.ranges;
                    filterValue = {
                        "gte": ranges[0],
                        "lt": ranges[1]
                    };
                } else {
                    filterValue = {
                        "gte": parseInt(filterValue),
                        "lt": parseInt(filterValue) + parseInt(chart.options.x[depth].interval)
                    };
                }

                type = "range";
                fieldType = "integer";
                $.each($siblings.not($(data.element)), (index, val) => {
                    val.classList.add("unselected");
                    val.classList.remove("selected");
                });
            }

            let filter: FilterValue = {
                "type": type,
                "field_type": fieldType,
                "label": field,
                "filter": filterValue,
                "source": filterSource,
                "parent": index,
                "inclusion": "must",
            };

            if (typeof parentField !== "undefined") {
                filter.parentFilter = parentField;
                let filterParentValue = [parentLabel];
                if (parentFieldType === "integer") {
                    depth = _.findIndex(chart.options.x, { field: parentField });
                    filterParentValue = {
                        "gte": parseInt(parentLabel),
                        "lt": parseInt(parentLabel) + parseInt(chart.options.x[depth].interval)
                    };
                }

                filterParent = {
                    "type": parentFieldType === "integer" ? "range" : "terms",
                    "field_type": parentFieldType,
                    "label": parentField,
                    "filter": filterParentValue,
                    "source": filterSource,
                    "parent": index,
                    "inclusion": "must"
                };
            }

            let isParentSelected = false;
            if (typeof parentField !== "undefined") {
                isParentSelected = $siblings.filter('.selected[data-field="' + parentField + '"][data-label="' + parentLabel + '"]').length > 0;
            }
            let removed = !isParentSelected && classes.contains('selected');
            let swapParent = false;
            let apply = true;
            let tag = this.getTagByField(field);
            let parentTag = this.getTagByField(parentField);

            if (!removed && filterParent !== null && this.tagExist(parentField)) {
                if (filterParent.type === "range" || parentTag.filter.indexOf(parentLabel) === -1) {
                    swapParent = true;
                }
            }

            if (isParentSelected || swapParent) {
                chart.unselectAllNodes();
            }

            let removeChild = false;

            if (!removed) {
                if (typeof parentField === "undefined") {
                    let childField = $siblings.filter('[data-parentfield="' + field + '"]').first();
                    if (childField.length) {
                        let childFilter = childField.data().field;
                        if (this.tagExist(childFilter)) {
                            removeChild = childFilter;
                        }
                    }
                }
                chart.selectNode(nodeKey);
            } else {
                chart.unselectNode(data.element.__data__.data.hasOwnProperty("group") ? "Autres" : filterValue);
                if (this.tagExist(field)) {
                    if ((_.isArray(filterValue) && tag.filter.length === filterValue.length) || (!_.isArray(filterValue) && tag.filter.length === 1) || tag.type === 'range') {
                        this.removeTagByLabel(field);
                        apply = false;
                        if (filterParent !== null && this.tagExist(parentField) && (parentTag.filter.length === 1 || parentTag.type === 'range')) {
                            this.removeTagByLabel(parentField);
                        }
                    }
                }
            }

            if (apply) {

                if (filter.type !== "range") {
                    filter.filter = $.unique(_.flatten($siblings.filter('.selected[data-field="' + field + '"]').toArray().map(v => {
                        if (v.__data__.data.hasOwnProperty("group")) {
                            return v.__data__.data.group.map(d => d.name);
                        }
                        return v.__data__.data.name;
                    })));
                }

                if (this.tagExist(field)) {
                    let e = filter.type === "range" ? true : tag.filter.indexOf(filterValue) === -1;
                    if (swapParent || removed || e) {
                        this.updateTagByLabel(field, filter, "");
                    }
                } else {
                    this.addFilter(filter);
                }

                if (filterParent !== null) {
                    if (this.tagExist(parentField)) {
                        if (filterParent.type !== "range") {
                            filterParent.filter = $.unique($siblings.filter('.selected[data-parentfield="' + parentField + '"]').toArray().map(v => v.__data__.parent.data.name));
                        }
                        this.updateTagByLabel(parentField, filterParent, "");
                    } else {
                        this.addFilter(filterParent);
                    }
                }

                if (removeChild) {
                    this.removeTagByLabel(removeChild);
                }

                this.onFilter(filter);
            }

            chart.selected = $.unique($siblings.filter('.selected[data-field="' + field + '"]').toArray().map(v => v.__data__.key));

        });

        /**
         * pieChart
         * - filtre sur string : cumul
         * - autre : remplacement
         * - ne fonctionne pas : histogramme date
         */
        that.dashboard.$element.on('pieChart.click', (e, data: FilterEvent) => {

            let chart = data.chart;
            let node = data.data;

            if (node.depth === 0) {
                return false;
            }

            let index = chart.parent;
            let nodeKey = node.key;
            let filterSource = chart.getSourceId();
            let filterParent: FilterValue = null;
            let filterValue = data.label;
            let field = data.field;
            let type = data.type;
            let fieldType = data.fieldType;
            let bucketType = data.bucketType;
            let parentFieldType: FilterFieldType = data.parentType;
            let parentField: FilterFieldType = data.parentField;
            let parentLabel: FilterType = data.parentLabel;
            if (node.data.hasOwnProperty("group")) {
                filterValue = node.data.group.map(d => d.name);
            }
            let depth = _.findIndex(chart.options.x, { field });

            if (acUtils.isElasticType('string', fieldType)) {
                type = 'terms';
            }
            if (bucketType === "range_field" || fieldType === "integer") {
                if (bucketType === "range_field") {
                    let ranges = data.data.data.ranges;
                    filterValue = {
                        "gte": ranges[0],
                        "lt": ranges[1]
                    };
                } else {
                    filterValue = {
                        "gte": parseInt(filterValue),
                        "lt": parseInt(filterValue) + parseInt(chart.options.x[depth].interval)
                    };
                }

                type = "range";
                fieldType = "integer";
            }

            let filter: FilterValue = {
                "type": type,
                "field_type": fieldType,
                "label": field,
                "filter": filterValue,
                "source": filterSource,
                "parent": index,
                "inclusion": "must",
            };

            if (typeof parentField !== "undefined") {
                filter.parentFilter = parentField;
                let filterParentValue = [parentLabel];
                if (parentFieldType === "integer") {
                    depth = _.findIndex(chart.options.x, { field: parentField });
                    filterParentValue = {
                        "gte": parseInt(parentLabel),
                        "lt": parseInt(parentLabel) + parseInt(chart.options.x[depth].interval)
                    };
                }

                filterParent = {
                    "type": parentFieldType === "integer" ? "range" : "terms",
                    "field_type": parentFieldType,
                    "label": parentField,
                    "filter": filterParentValue,
                    "source": filterSource,
                    "parent": index,
                    "inclusion": "must"
                };
            }

            let removed = chart.selected.includes(nodeKey); // true si la valeur est déjà sélectionnée
            let swapParent = false; // sera true si on change de secteur parent
            let apply = true; // sera false si le filtre du secteur est supprimé
            let tag = this.getTagByField(field); // Filtre existant du secteur sélectionné
            let parentTag = this.getTagByField(parentField); // Filtre existant du secteur parent

            // Si se n'est pas une chaine de caractère, pas de cumul on remplace le filtre
            // Si on sélectionne un sous-niveau d'un autre parent, on repart de zéro
            if (filter.type === "range") {
                swapParent = true;
            } else if (!removed && filterParent !== null && parentTag) {
                if (filterParent.type === "range" || parentTag.filter.indexOf(parentLabel) === -1) {
                    swapParent = true;
                }
            }

            if (swapParent) {
                chart.unselectAllNodes();
            }

            if (!removed) {
                // Sélection des valeurs
                chart.selectNode(nodeKey);
                if (filterParent) {
                    chart.selectNode(node.parent.key);
                }
            } else {
                // Suppression car la valeur est déjà sélectionnée
                chart.unselectNode(node.data.hasOwnProperty("group") ? "Autres" : nodeKey);
                if (this.tagExist(field)) {
                    // Si on supprime le dernier element d'un secteur, on supprime le filtre associé
                    if (
                        tag.type === 'range'
                        || tag.filter.length === 1
                        || (_.isArray(filterValue) && _.uniq(filterValue).length === tag.filter.length) // suppression du autres
                    ) {
                        this.removeTagByLabel(field);
                        apply = false;
                        // On supprime également le filtre parent si il y a 2 niveaux
                        if (filterParent !== null && this.tagExist(parentField)) {
                            this.removeTagByLabel(parentField);
                        }
                    }
                }
                // Si on supprime le filtre de niveau 1, on supprime aussi les filtres de niveau 2
                if (!apply && node.depth === 1 && chart.options.x.length > 1) {
                    let childTag = this.getTagByField(chart.options.x[1].field);
                    if (childTag && childTag.parent === index) {
                        this.removeTagByLabel(chart.options.x[1].field);
                    }
                }
            }

            if (apply) {

                // Recalcul des valeurs du filtre en fonction de ce qui est sélectionné
                if (filter.type !== "range") {
                    filter.filter = _.uniq(chart.nodes
                        .filter(d => d.depth === node.depth && chart.selected.includes(d.key))
                        .map(d => {
                            if (d.data.hasOwnProperty("group")) {
                                return d.data.group.map(d => d.name);
                            }
                            return d.data.name;
                        })
                        .flatten());
                }

                // On ajoute ou met à jour le filtre
                if (this.tagExist(field)) {
                    let e = filter.type === "range" ? true : tag.filter.indexOf(filterValue) === -1;
                    if (swapParent || removed || e) {
                        this.updateTagByLabel(field, filter, "");
                    }
                } else {
                    this.addFilter(filter);
                }

                // Recalcul des valeurs du filtre parent en fonction de ce qui est sélectionné
                if (filterParent !== null) {
                    if (filterParent.type !== "range") {
                        filterParent.filter = _.uniq(chart.nodes
                            .filter(d => d.depth === node.parent.depth && chart.selected.includes(d.key))
                            .map(d => {
                                if (d.data.hasOwnProperty("group")) {
                                    return d.data.group.map(d => d.name);
                                }
                                return d.data.name;
                            })
                            .flatten());
                    }

                    // On ajoute ou met à jour le filtre parent
                    if (this.tagExist(parentField)) {
                        this.updateTagByLabel(parentField, filterParent, "");
                    } else {
                        this.addFilter(filterParent);
                    }
                }

                this.onFilter(filter);
            }

            // Mise à jour du store utilisé par les composants svelte du pieChart
            chart.selectedStore.set(chart.selected);
        })

        // Suppression du filtre lors du clic sur le bouton relatif dans la légende du pieChart
        that.dashboard.$element.on('pieChart.clear', (e, data: { chart: VisualisationBase, fields: string[] }) => {
            let chart: VisualisationBase = data.chart;

            data.fields.forEach(field => {
                if (that.tagExist(field)) {
                    that.removeTagByLabel(field);
                }
            })

            chart.selected = [];
            chart.selectedStore.set([]);
        });

        /**
         * iconColorChart
         * iconGaugeChart
         */

        that.dashboard.$element.on('iconColorChart.click iconGaugeChart.click', (e, data: FilterEvent) => {
            let el = e.type === "iconColorChart" ? data.element : data.element.closest('.svgicon');
            let chart = data.chart;

            if (data.type === "range") {
                data.filter = {};
                if (data.ranges.hasOwnProperty('from')) {
                    data.filter.gte = data.ranges.from;
                }
                if (data.ranges.hasOwnProperty('to')) {
                    data.filter.lt = data.ranges.to;
                }
            } else {
                data.filter = data.field_type === "boolean" ? data.data.id : [data.data.id]
            }

            let filter: FilterValue = {
                "type": data.type,
                "field_type": data.field_type,
                "label": data.field,
                "filter": data.filter,
                "source": data.chart.getSourceId(),
                "parent": chart.parent,
                "inclusion": "must"
            };

            const isSelected = el.classList.contains(chart.selectedClass);
            let added = false;

            if (that.tagExist(data.field)) {
                if (!isSelected) {
                    added = true;
                }
                that.removeTagByLabel(data.field);
            } else {
                added = true;
            }

            chart.unselectAllNodes();

            if (added) {
                chart.selectNode(data.data.id);
                that.addFilter(filter);
                that.onFilter(filter);
                chart.selected = data.data.id;
            } else {
                chart.selected = false;
            }

        });

        /**
         * tableChart
         * listChart
         */
        that.dashboard.$element.on('tableChart.click listChart.click', (e, data: FilterEvent) => {
            let $chart = data.chart.$parent;
            let field = data.field;
            let label = data.data;
            let removed = false;

            if (data.element.classList.contains(data.chart.selectedClass)) {
                if (that.tagExist(field)) {
                    let tag = that.getTagByField(field);
                    if (Array.isArray(tag.filter) && tag.filter.length === 1 || !Array.isArray(tag.filter)) {
                        that.removeTagByLabel(field);
                        removed = true;
                    }
                }
                data.chart.unselectNode(label);
            } else {
                data.chart.selectNode(label);
            }

            if (removed) {
                return;
            }

            let filtered = $chart[0].querySelectorAll('.' + data.chart.selectedClass);

            if (data.bucketType === "range_field" || data.field_type === "integer") {
                filtered.forEach((val) => {
                    if (field === val.dataset.field && val.__data__ !== label) {
                        data.chart.unselectNode(val.__data__);
                    }
                })
            }

            let selectedLabels = {};
            let allLabels = [];
            filtered = $chart[0].querySelectorAll('.' + data.chart.selectedClass);
            filtered.forEach((val) => {
                let field = val.dataset.field;
                if (!selectedLabels.hasOwnProperty(field)) {
                    selectedLabels[field] = [];
                }
                selectedLabels[field].push(val.__data__);
                selectedLabels[field] = _.uniq(selectedLabels[field]);
                allLabels.push(val.__data__);
            })

            let filterValue = selectedLabels[field];

            let type: FilterType = "term";

            if (data.bucketType === "range_field" || data.field_type === "integer") {
                if (data.bucketType === "range_field") {
                    filterValue = data.ranges;
                } else {
                    filterValue = {
                        "gte": parseInt(filterValue),
                        "lt": parseInt(filterValue) + parseInt(chart.options.x[0].interval)
                    };
                }
                type = 'range';
            }

            let filter: FilterValue = {
                "type": type,
                "field_type": "string",
                "label": field,
                "filter": filterValue,
                "source": data.chart.getSourceId(),
                "parent": data.parent,
                "inclusion": "must"
            };

            if (that.tagExist(field)) {
                that.updateTagByLabel(field, filter);
            } else {
                that.addFilter(filter);
            }
            that.onFilter(filter);
            if (data.chart.name === "listChart") {
                filterValue.forEach(label => {
                    data.chart.selectNode(label);
                });
            }

            data.chart.selected = _.uniq(allLabels);
        });

        that.dashboard.$element.on('click', '.tableChart .tableChart-filter-line', function() {
            let $siblings = $(this).closest('tr').find('.tableChart-filter');
            if ($siblings.not('.tableChart-filter-selected').length) {
                $siblings.not('.tableChart-filter-selected').click();
            } else {
                $siblings.click();
            }
        });

        /**
         * clockChart
         */

        that.dashboard.$element.on('clockChart.click', (e, data: FilterEvent) => {
            let hour = data.data.name;
            let remove = data.element.classList.contains(data.chart.selectedClass);
            let label = "de " + hour + " à " + (hour + 1);
            let f = {};
            if (acUtils.isElasticType("number", data.field_type)) {
                f = {
                    "gte": hour,
                    "lte": hour + 1
                };
            } else if (acUtils.isElasticType("date", data.field_type)) {
                f = {
                    "gte": moment.utc("01/01/1970 " + hour + ":00", 'DD/MM/YYYY HH:mm').valueOf(),
                    "lte": moment.utc("01/01/1970 " + (hour + 1) + ":00", 'DD/MM/YYYY HH:mm').valueOf() - 1,
                };
            }
            let filter: FilterValue = {
                "field_type": data.field_type,
                "type": "range",
                "label": data.field,
                "label_format": "HH:mm",
                // "value_as_string": label,
                "filter": f,
                "source": data.chart.getSourceId(),
                "parent": data.parent,
                "inclusion": "must"
            };

            data.chart.clearSelected();

            if (!remove) {
                data.chart.selectNode(hour);
                let tag = that.getTagByFilterLabelOrValue(data.field);
                if (tag) {
                    that.updateTagByLabel(data.field, filter, label);
                } else {
                    that.addFilter(filter);
                }
                data.chart.selected = hour;
            } else {
                data.chart.selected = false;
                that.removeTagByLabel(data.field);
            }
            that.onFilter(filter);
        });

        /**
         * pyramidChart
         */

        that.dashboard.$element.on('pyramidChart.click', (element, data: FilterEvent) => {

            let removed = data.element.classList.contains(data.chart.selectedClass);
            let age = data.data.x;
            let sexe = data.data.id;
            let chart = data.chart;
            let source = data.chart.getSourceId();
            let fieldType: FilterFieldType;
            let f = {};

            if (acUtils.isElasticType("number", data.x[0].type)) {
                f = {
                    "gte": age,
                    "lte": age + (data.x[0].interval - 1)
                };
            }
            if (acUtils.isElasticType('string', data.x[1].type)) {
                data.x[1].type = 'term';
            }

            if (data.originalData.hasOwnProperty("ranges")) {
                let ranges = data.originalData.ranges;
                f = {
                    "gte": ranges[0],
                    "lt": ranges[1]
                };
                fieldType = "integer";
            }

            let filter1: FilterValue = {
                "type": "range",
                "label": data.x[0].field,
                "field_type": fieldType,
                "filter": f,
                "source": source,
                "parent": chart.parent,
                "inclusion": "must"
            };

            let filter2: FilterValue = {
                "type": data.x[1].type,
                "label": data.x[1].field,
                "field_type": fieldType,
                "filter": [sexe],
                "source": source,
                "parent": chart.parent,
                "inclusion": "must"
            };
            let apply = true;

            if (!removed) {
                chart.selectNode(age, sexe);
            } else {
                that.removeTagByLabel(filter1.label);
                that.removeTagByLabel(filter2.label);
                chart.unselectAllNodes();
                apply = false;
            }


            let $siblings = $(chart.getNodes());
            if (apply) {

                if (that.tagExist(filter1.label)) {
                    that.updateTagByLabel(filter1.label, filter1, "");
                } else {
                    that.addFilter(filter1);
                }

                if (that.tagExist(filter2.label)) {
                    that.updateTagByLabel(filter2.label, filter2, "");
                } else {
                    that.addFilter(filter2);
                }

                that.onFilter(filter1);
            }

            chart.selected = $.unique($siblings.filter("." + chart.selectedClass).toArray().map(v => ({
                filter1: v.__data__.x,
                filter2: v.__data__.id
            })));

            if (chart.selected.length > 1) {
                chart.unselectAllNodes();
                chart.selectNode(age, sexe);
                chart.selected = $.unique($siblings.filter("." + chart.selectedClass).toArray().map(v => ({
                    filter1: v.__data__.x,
                    filter2: v.__data__.id
                })));
            }

        });

        /**
         * wordCloudChart
         */

        that.dashboard.$element.on('wordcloudChart.click', (e, data: FilterEvent) => {
            let chart = data.chart;
            let removed = data.element.classList.contains(chart.selectedClass);
            let filter: FilterValue = {
                "type": "term",
                "field_type": "string",
                "label": data.field,
                "filter": [data.data.name],
                "source": data.chart.getSourceId(),
                "parent": chart.parent,
                "inclusion": "must"
            };

            let apply = true;

            let tag = that.getTagByField(data.field);

            if (!removed) {
                chart.selectNode(data.data.name);
            } else {
                chart.unselectNode(data.data.name);
                if (that.tagExist(data.field) && Array.isArray(tag.filter) && tag.filter.length === 1) {
                    that.removeTagByLabel(data.field);
                    apply = false;
                }
            }

            if (apply) {
                filter.filter = chart.getSelectedNodesValues();
                if (that.tagExist(data.field) && Array.isArray(tag.filter)) {
                    if (removed || tag.filter.indexOf(data.data.name) === -1) {
                        that.updateTagByLabel(data.field, filter, "");
                    }
                } else {
                    that.addFilter(filter);
                }
                that.onFilter(filter);
            }

            chart.selected = chart.getSelectedNodesValues();
        });

        /**
         * selectorChart
         */

        that.dashboard.$element.on('selectorChart.click', (e, data: FilterEvent) => {
            let removed = data.removed;
            let chart: VisualisationBase = data.chart;

            let filterValue: FilterFilter = [data.data.name];
            let type: FilterType = "term";

            if (data.bucketType === "range_field" || data.field_type === "integer") {
                if (data.bucketType === "range_field") {
                    filterValue = data.ranges;
                } else {
                    filterValue = {
                        "gte": parseInt(filterValue),
                        "lt": parseInt(filterValue) + parseInt(chart.options.x[0].interval)
                    };
                }
                type = 'range';
                chart.unselectAllNodes();
            } else if (data.field_type === "boolean") {
                filterValue = (typeof data.data.name === "boolean" ? data.data.name : JSON.parse(data.data.name)) ? 1 : 0;
            }

            let filter: FilterValue = {
                "type": type,
                "field_type": data.field_type,
                "label": data.field,
                "filter": filterValue,
                "source": data.chart.getSourceId(),
                "parent": chart.parent,
                "inclusion": "must"
            };

            let apply = true;

            let tag = that.getTagByField(data.field);

            // Si il y un filtre sur ce champ créé depuis une autre visu, on le supprime
            const changeParent = tag && data?.parent !== tag.parent;
            if (changeParent) {
                that.removeTagByLabel(data.field);
                chart.selfUpdate();
            }

            if (!chart.options.filters_multiple) {
                chart.unselectAllNodes();
            }

            if (!removed) {
                chart.selectNode(data.data);
            } else {
                chart.unselectNode(data.data.name);
                if (that.tagExist(data.field) && ((Array.isArray(tag.filter) && tag.filter.length === 1) || !Array.isArray((tag.filter)))) {
                    that.removeTagByLabel(data.field);
                    apply = false;
                }
            }

            if (apply) {
                if (data.multiple) {
                    filter.filter = chart.getSelectedNodesValues();
                }
                if (that.tagExist(data.field)) {
                    if (removed || (Array.isArray(tag.filter) && tag.filter.indexOf(data.data.name) === -1) || !Array.isArray((tag.filter)))  {
                        that.updateTagByLabel(data.field, filter, "");
                    }
                } else {
                    that.addFilter(filter);
                }
                that.onFilter(filter);
            }

            chart.selected = chart.getSelected();
        });

        // Suppression du filtre lors du clic sur le bouton relatif dans le template liste d'un selectorChart
        that.dashboard.$element.on('selectorChart.clear', (e, data: { chart: VisualisationBase, field: string }) => {
            let chart: VisualisationBase = data.chart;

            chart.unselectAllNodes();

            if (that.tagExist(data.field)) {
                that.removeTagByLabel(data.field);
            }

            chart.selected = chart.getSelected();
        });

        /**
         * heatmapAxisChart
         * TODO: filtre sur type date
         */

        that.dashboard.$element.on('heatmapAxisChart.click', (e, data: FilterEvent) => {
            let chart = data.chart;
            let filterSource = data.chart.getSourceId();

            let filter1Type: FilterType = data.x[0].bucketType;
            let filter2Type: FilterType = data.x[1].bucketType;

            let filter1Value: FilterFilter = data.data.x;
            let filter2Value: FilterFilter = data.data.y;

            if (filter1Type === "range_field" || data.x[0].type === "integer") {
                if (filter1Type === "range_field") {
                    filter1Value = data.data.x_ranges;
                } else {
                    filter1Value = {
                        "gte": parseInt(filter1Value),
                        "lt": parseInt(filter1Value) + parseInt(data.x[0].interval)
                    };
                }
                filter1Type = 'range';
            }

            if (filter2Type === "range_field" || data.x[1].type === "integer") {
                if (filter2Type === "range_field") {
                    filter2Value = data.data.y_ranges;
                } else {
                    filter2Value = {
                        "gte": parseInt(filter2Value),
                        "lt": parseInt(filter2Value) + parseInt(data.x[1].interval)
                    };
                }
                filter2Type = 'range';
            }

            let filter1: FilterValue = {
                "type": filter1Type,
                "label": data.x[0].field,
                "filter": filter1Value,
                "source": filterSource,
                "parent": data.parent,
                "inclusion": "must",
                "parentFilter": data.x[1].field,
            };

            let filter2: FilterValue = {
                "type": filter2Type,
                "label": data.x[1].field,
                "filter": filter2Value,
                "source": filterSource,
                "parent": data.parent,
                "inclusion": "must",
                "parentFilter": data.x[0].field,
            };

            let classes = data.element.classList;

            let removed = classes.contains(chart.selectedClass);
            let apply = true;

            chart.unselectAllNodes();

            if (!removed) {
                chart.selectNode(data.data.key);
            } else {
                chart.unselectNode(data.data.key);
                if (that.tagExist(data.x[0].field)) {
                    that.removeTagByLabel(data.x[0].field);
                    apply = false;
                }
                if (that.tagExist(data.x[1].field)) {
                    that.removeTagByLabel(data.x[1].field);
                    apply = false;
                }
            }

            if (apply) {

                if (that.tagExist(data.x[0].field)) {
                    that.updateTagByLabel(data.x[0].field, filter1, "");
                } else {
                    that.addFilter(filter1);
                }

                if (that.tagExist(data.x[1].field)) {
                    that.updateTagByLabel(data.x[1].field, filter2, "");
                } else {
                    that.addFilter(filter2);
                }

                that.onFilter(filter1);

            }

            chart.selected = chart.getSelectedNodesValues();
        });

        /**
         * heatmapChart
         */

        that.dashboard.$element.on('heatmapChart.click', (e, data: FilterEvent) => {

            let chart = data.chart;

            let remove = false;
            let filter: FilterValue = {
                "field_type": data.field_type,
                "type": data.type,
                "label": data.field,
                "filter": data.data,
                "source": chart.getSourceId(),
                "parent": data.parent,
                "inclusion": "must"
            };

            if (data.element.classList.contains(chart.selectedClass)) {
                remove = true;
            }

            $.each(chart.$parent.find('rect.day.selected, text.month-label.selected, text.year-label.selected, text.legend_division.selected'), (index, val) => {
                val.classList.remove(chart.selectedClass);
            });

            if (!remove) {
                data.element.classList.add(chart.selectedClass);
                if (this.tagExist(filter.label)) {
                    this.updateTagByLabel(filter.label, filter);
                } else {
                    this.addFilter(filter);
                }
                chart.selected = data.date;
            } else {
                this.removeTagByLabel(filter.label);
                chart.selected = false;
            }
            this.onFilter(filter);
        });

        /**
         * dotRepartitionChart
         */
        that.dashboard.$element.on('dotRepartitionChart.click', (e, data: FilterEvent) => {
            let chart = data.chart;
            let filterValue = data.data;
            let type: FilterType = "terms";
            let remove = data.element.classList.contains(chart.selectedClass);

            if (data.bucketType === "range_field" || data.field_type === "integer") {
                if (data.bucketType === "range_field") {
                    let ranges = data.ranges;
                    filterValue = {
                        "gte": ranges[0],
                        "lt": ranges[1]
                    };
                } else {
                    filterValue = {
                        "gte": filterValue,
                        "lt": filterValue + parseInt(chart.options.x[0].interval)
                    };
                }
                type = 'range';
                chart.unselectAllNodes();
            }

            let filter: FilterValue = {
                "type": type,
                "field_type": data.field_type,
                "label": data.field,
                "filter": filterValue,
                "source": chart.getSourceId(),
                "parent": data.parent,
                "inclusion": "must"
            };

            if (remove) {
                chart.unselectNode(type === "terms" ? filterValue : data.data);
            } else {
                chart.selectNode(type === "terms" ? filterValue : data.data);
            }

            if (!((Array.isArray(filter.filter) && filter.filter.length === 1) || filter.type === 'range')) {
                filter.filter = chart.getSelectedNodesValues();
            }

            if (that.tagExist(data.field)) {
                if (chart.getSelectedNodes().length > 0) {
                    that.updateTagByLabel(data.field, filter, "");
                } else {
                    that.removeTagByLabel(filter.label);
                }
            } else {
                that.addFilter(filter);
            }
            that.onFilter(filter);

            chart.selected = chart.getSelectedNodesValues();
        });

        /**
         * barChart
         * barHChart
         * TODO : filtre sur range
         */

        that.dashboard.$element.on('barChart.click barHChart.click', (e, data: FilterEvent) => {
            let el: HTMLElement = data.element;
            let chart = data.chart;
            let rangeScale;
            let rangeScaleParent;
            let filterParent : FilterValue;

            if (!chart.canCreateFilter) {
                return false;
            }

            if (data.x.length === 1 && data.y.length === 1) {
                rangeScale = chart.data.filter(d => (d.id == data.data.id) ? d : null);
            } else {
                rangeScale = chart.data.filter(d => (d.id == data.data.parent) ? d : null);
            }

            rangeScaleParent = rangeScale[0].values.filter(d => (d.id == data.data.id) ? d : null);

            let firstX = chart.options.x[0];
            let lastX = chart.options.x[data.x.length - 1];

            if (data.x.length > 1 && lastX.type === "date") {
                return false;
            }

            const lastXhasRange = lastX.hasOwnProperty("ranges");
            let filter: FilterValue = {
                "type": lastXhasRange ? "range" : "term",
                "field_type": lastXhasRange ? "integer" : data.x[data.x.length - 1].type,
                "label": "",
                "filter": [data.y.length > 1 ? data.data.parent : data.data.id],
                "source": chart.$parent.data('source'),
                "parent": chart.parent,
                "inclusion": "must",
                "ranges": lastXhasRange,
                "ranges_name": lastXhasRange ? data.data.id : null
            };

            if (lastXhasRange) {
                filter.filter = (rangeScale?.[0]?.values && rangeScaleParent?.[0]?.ranges?.length)
                    ? {"gte": rangeScaleParent[0].ranges[0], "lte": rangeScaleParent[0].ranges[1] - 1 }
                    : {"gte": rangeScale[0].ranges[0], "lte": rangeScale[0].ranges[1] - 1 };
            }

            const firstXhasRange = firstX.hasOwnProperty("ranges");
            if (data.x.length > 1) {
                filterParent = {
                    "type": firstXhasRange ? "range" : "term",
                    "field_type": firstXhasRange ? "integer" : data.x[0].type,
                    "label": "",
                    "filter": [data.data.parent],
                    "source": chart.$parent.data('source'),
                    "parent": chart.parent,
                    "inclusion": "must",
                    "ranges": firstXhasRange,
                    "ranges_name": firstXhasRange ? data.data.parent : null,
                    "parentFilter": data.x[0].field,
                };
                if (firstXhasRange && rangeScaleParent?.[0]?.ranges)  {
                    filter.filter = {"gte": rangeScaleParent[0].ranges[0], "lte": rangeScaleParent[0].ranges[1] };
                }
            }

            if (chart.name === "barChart") {
                filter.label = lastX.field;

                //Xn
                if (data.x.length > 1) {
                    filterParent.label = firstX.field;
                    filterParent.filter = firstXhasRange ? {"gte": rangeScale[0].ranges[0], "lte": rangeScale[0].ranges[1] - 1 } : [data.data.parent];
                }
            } else if (chart.name === "barHChart") {
                filter.label = data.x[data.x.length - 1].field;

                //division
                if (data.x.length > 1) {
                    filterParent.label = data.x[0].field;
                    filterParent.filter = firstXhasRange ? {"gte": rangeScale[0].ranges[0], "lte": rangeScale[0].ranges[1] - 1 } : [data.data.parent];
                }
            }

            let classes = el.classList;

            let removed = classes.contains(chart.selectedClass);
            let reseted = false;
            let apply = true;
            let tag = that.getTagByField(filter.label);
            let tagParent;

            if (filterParent) {
                tagParent = that.getTagByField(filterParent.label);
            }

            if (!removed) {
                // Si on change de X, on désélectionne tout
                if (!firstXhasRange) {
                    if (filterParent && that.tagExist(filterParent.label) && tagParent.filter[0] !== filterParent.filter[0]) {
                        chart.unselectAllNodes();
                        reseted = true;
                    }
                } else if (filter.ranges && that.tagExist(filter.label)) {
                    chart.unselectAllNodes();
                    reseted = true;
                }
                chart.selectNode((filter.ranges) ? filter.ranges_name : filter.filter[0], filterParent ? (filterParent.ranges ? filterParent.ranges_name : filterParent.filter[0]) : undefined);
            } else {
                chart.unselectNode((filter.ranges) ? filter.ranges_name : filter.filter[0], filterParent ? filterParent.filter[0] : undefined);

                if (that.tagExist(filter.label) && (tag.filter.length === 1 || filter.ranges)) {
                    that.removeTagByLabel(filter.label);
                    apply = false;
                    if (filterParent && that.tagExist(filterParent.label) && (tagParent.filter.length === 1 || firstXhasRange)) {
                        that.removeTagByLabel(filterParent.label);
                    }
                }
            }

            let $siblings = chart.$parent.find('.bar');

            let filterSelector = `.${chart.selectedClass}[data-field="${filter.label}"]`;

            if (filterParent) {
                filterSelector += '[data-parentfield="' + filterParent.label + '"]';
            }

            if (apply) {
                if (filter.type === "term") {
                    filter.filter = _.uniq($siblings.filter(filterSelector).toArray().map(v => data.y.length > 1 ? v.__data__.parent : v.__data__.id));
                }
                if (that.tagExist(filter.label)) {
                    if (removed || reseted || tag.filter.length !== filter.filter.length) {
                        that.updateTagByLabel(filter.label, filter, "");
                    }
                } else {
                    that.addFilter(filter);
                }
                that.onFilter(filter);

                if (filterParent) {
                    if (that.tagExist(filterParent.label)) {
                        if (removed || reseted || (Array.isArray(tagParent.filter) && tagParent.filter.indexOf(filterParent.filter[0]) === -1)) {
                            that.updateTagByLabel(filterParent.label, filterParent, "");
                        }
                    } else {
                        that.addFilter(filterParent);
                    }
                    that.onFilter(filterParent);
                }
            }

            if (filterParent) {
                chart.selected = _.uniqWith($siblings.filter(filterSelector).toArray().map(v => ({ filter1: v.__data__.id, filter2: v.__data__.parentId })), _.isEqual);
            } else {
                chart.selected = _.uniqWith($siblings.filter(filterSelector).toArray().map(v => ({ filter1: data.y.length > 1 ? v.__data__.parent : v.__data__.id })), _.isEqual);
            }
        });

        /**
         * lineChart
         * ... chart with brushable X axis
         */

        that.dashboard.$element.on("brushend", (e, chart: VisualisationBase, parameters) => {
            let extent = parameters.extent;
            let label = parameters.label;
            let type = parameters.type;
            let parent = parameters.parent;
            let xScale = parameters.xScale;
            let interval = parameters.interval;
            let selected;
            let new_label;
            let source = chart.getSourceId();
            let filter: FilterValue;

            if (extent === null) {
                that.removeTagByLabel(label);
                return false;
            }

            if (acUtils.isElasticType('date', type)) {
                extent = extent.map(parameters.xScale.invert);
                filter = {
                    "field_type": type,
                    "type": "range",
                    "range_type": "custom",
                    "label": label,
                    "source": source,
                    "filter": {
                        "gte": extent[0].getTime(),
                        "lte": extent[1].getTime()
                    },
                    "interval": interval,
                    "parent": parent,
                    "inclusion": "must"
                };
                new_label = label + " du " + moment.utc(extent[0]).format("DD/MM/YYYY") + " au " + moment.utc(extent[1]).format("DD/MM/YYYY");
            } else if (parameters.ranges) {
                let selectedRange = xScale.domain().filter(d => (extent[0] <= xScale(d)) && ((xScale(d) + xScale.bandwidth()) <= extent[1]));
                let range = _.find(parameters.ranges, { key: selectedRange[0] });
                let filterValue = null;

                if (range) {
                    filterValue = { gte: range.from, lt: range.to };
                } else {
                    if (this.tagExist(label)) {
                        this.removeTagByLabel(label);
                    }
                    return false;
                }

                filter = {
                    "field_type": "integer",
                    "type": "range",
                    "label": label,
                    "source": source,
                    "filter": filterValue,
                    "parent": parent,
                    "inclusion": "must"
                };
            }  else if (acUtils.isElasticType('string', type)) {
                selected = xScale.domain().filter(d => (extent[0] <= xScale(d)) && ((xScale(d) + xScale.bandwidth()) <= extent[1]));
                if (selected.length === 0) {
                    if (this.tagExist(label)) {
                        this.removeTagByLabel(label);
                    }
                    return false;
                }
                let label_selected = [];
                $.each(selected, (index, val) => {
                    label_selected.push("\"" + val + "\"");
                });
                new_label = label + " contient " + selected.join(' ou ');
                filter = {
                    "field_type": type,
                    "type": "term",
                    "label": label,
                    "source": source,
                    "filter": selected,
                    "parent": parent,
                    "inclusion": "must"
                };
            } else {
                extent = extent.map(parameters.xScale.invert);
                filter = {
                    "field_type": type,
                    "type": "range",
                    "label": label,
                    "source": source,
                    "filter": {
                        "gte": parseInt(extent[0]),
                        "lte": parseInt(extent[1])
                    },
                    "parent": parent,
                    "inclusion": "must"
                };
                new_label = label + " de " + parseInt(extent[0]) + " à " + parseInt(extent[1]);
            }

            if (that.tagExist(label)) {
                that.updateTagByLabel(label, filter, new_label, true);
            } else {
                that.addFilter(filter);
            }
            that.onFilter(filter);
        });

        /**
         * mapChart
         */

        that.dashboard.$element.on("mapChart.click", (e, data: FilterEvent) => {
            let filter: FilterValueGeo = {
                "type": "geo_distance",
                "field_type": data.type,
                "label": data.field,
                "filter": {},
                "value_as_string": "",
                "source": data.chart.mapChart.getSourceId(),
                "parent": data.parent,
                "inclusion": "must"
            };

            if (data.data.radius === null) {
                filter.type = "geo_polygon";
                filter.filter = {};
                filter.filter.points = data.data.points;
            } else {
                filter.type = "geo_distance";
                filter.filter = {};
                filter.filter.points = data.data.points;
                filter.filter.radius = data.data.radius;
            }
            filter.params = {
                typeRegroupment: 'geoshape',
            };
            if (filter.filter.points.length !== 0) {
                let tag = that.getTagByTypeRegroupement(filter.params.typeRegroupment);
                if (tag) {
                    that.updateTagByTypeRegroupement(filter.params.typeRegroupment, filter, filter.label);
                } else {
                    that.addFilter(filter);
                }
            } else {
                that.removeTagByTypeRegroupement(filter.params.typeRegroupment);
            }
            that.onFilter(filter);
        });

        that.dashboard.$element.on("mapChart.isochroneClick", (e, data: FilterEvent) => {
            let filter: FilterValue = {
                "field_type": data.type,
                "label": data.field,
                "source": data.chart.mapFactory.mapChart.getSourceId(),
                "parent": data.parent,
                "inclusion": "must",
                "type": "geo_isochrone",
                "filter": data.data.isMagasin ? "isochrone magasin" : "isochrone concurrent",
                "filterData": [{
                    "points": data.data.points,
                    "radius": data.data.radius,
                    "unit": data.data.unit,
                    "transport": data.data.transport,
                    "nom": data.data.name,
                    "libelle": data.data.libelle,
                    "idIsochrone": data.data.idIsochrone,
                }],
                "params": {
                    'typeRegroupment': data.data.isMagasin ? "isochrone magasin" : "isochrone concurrent",
                }
            };
            if (filter.filterData[0].points.length !== 0 && filter.filterData[0].radius.length !== 0) {
                let tag = that.getTagByTypeRegroupement(filter.params.typeRegroupment);
                if (tag && typeof tag.value.params !== 'undefined' && typeof tag.value.params.typeRegroupment !== 'undefined') {
                    filter.filterData[0].idIsochrone = tag.value.filterData[0].idIsochrone;
                    // Si un isochrone pour le magasin de nom data.data.libelle existe, il sera supprimé
                    if (tag.value.params.typeRegroupment === "isochrone magasin") {
                        that.removeIsochroneById(data.data.idIsochrone, filter.params.typeRegroupment, false);
                        that.addFilter(filter);
                    } else {
                        // Voir lastName
                        that.removeIsochroneById(data.data.idIsochrone, filter.params.typeRegroupment, false);
                        that.addFilter(filter);
                    }
                    filter.filterData = tag.value.filterData.concat(filter.filterData);
                    that.updateTagByTypeRegroupement(filter.params.typeRegroupment, filter, filter.params.typeRegroupment);
                } else {
                    that.addFilter(filter);
                }
            } else {
                that.removeTag(that.getTagByTypeRegroupement(filter.params.typeRegroupment));
            }
            that.onFilter(filter);
        });

        that.dashboard.$element.on("mapChart.removeIsochroneById", (e, data: FilterEvent) => {
            if (data.isMagasin) {
                that.removeIsochroneById(data.idIsochrone, "isochrone magasin", false);
            } else {
                that.removeIsochroneById(data.idIsochrone, "isochrone concurrent", false);
            }
        });

        that.dashboard.$element.on("mapChart.selectionPointSecondaire", (e, data: FilterEvent) => {
            let filter;
            let tag = that.getTagByFilterLabelOrValue(data.field);
            if (tag) {
                let filters = tag.value;
                if (data.data['params']['element'].length === 0) {
                    that.removeTag(that.getTagByFilterLabelOrValue(data.field));
                    filter = filters;
                } else {
                    filters.filter = data.data['params']['element'];
                    that.updateTagByLabel(data.field, filters, '');
                    filter = filters;
                }
            } else {
                filter = {
                    "field_type": data.type,
                    "type": data.type,
                    "label": data.field,
                    "source": typeof data.chart.params.options.sourceSecondaire !== "undefined" ? parseInt(data.chart.params.options.sourceSecondaire) : null,
                    "parent": data.parent,
                    "inclusion": "must",
                    "filter": data.data['params']['element'],
                };
                that.addFilter(filter);
            }
            that.onFilter(filter);
        });

        that.dashboard.$element.on("mapChart.selectionMagasin", (e, data: FilterEvent) => {
            let filter;
            let tag = that.getTagByFilterLabelOrValue(data.field);
            if (tag) {
                let filters = tag.value;
                // si le filtre contient deja notre magasin
                if (filters.filter.includes(data.data['params']['element'])) {
                    if (tag.value.filter.length === 1) {
                        let item = that.getTagByFilterLabelOrValue(data.field);
                        if (item) {
                            that.removeTag(item);
                        }
                        filter = filters;
                    } else {
                        filters.filter = filters.filter.filter(value => value != data.data['params']['element']);
                        that.updateTagByLabel(data.field, filters, '');
                        filter = filters;
                    }
                } else {
                    filters.filter.push(data.data['params']['element']);
                    that.updateTagByLabel(data.field, filters, '');
                    filter = filters;
                }
            } else {
                filter = {
                    "field_type": data.type,
                    "type": data.type,
                    "label": data.field,
                    "source": data.chart.mapChart.getSourceId(),
                    "parent": data.parent,
                    "inclusion": "must",
                    "filter": [data.data['params']['element']],
                };
                if (typeof data.data['params']['coordinates'] !== 'undefined') {
                    filter.params = {
                        "coordinates": {'lat': data.data['params']['coordinates'][1], 'lon': data.data['params']['coordinates'][0]},
                    };
                }
                that.addFilter(filter);
            }
            that.onFilter(filter);
        });

        that.dashboard.$element.on("mapChart.selectZone", (e, data: FilterEvent) => {
            let filter: FilterValue = {
                "field_type": "geo_point",
                "type": "geo_polygon",
                "label": data.field,
                "source": data.chart.mapChart.getSourceId(),
                "parent": data.parent,
                "inclusion": "must",
                "filter": data.data.params.typeRegroupement + " : " + data.data.params.typeName,
                "params" : {}
            };

            let correspondance = {};
            correspondance[data.data.params.typeValue] = data.data.params.typeName;

            filter.params = {
                typeValue: JSON.stringify([data.data.params.typeValue]),
                typeRegroupment: data.data.params.typeRegroupement,
                otherDatas: [correspondance]
            };

            let tag = that.getTagByTypeRegroupement(data.data.params.typeRegroupement);
            if (tag) {
                // Si typeRegroupement (depart ou region) && le type est le même que celui du tag
                if (typeof tag.value.params !== 'undefined' && typeof tag.value.params.typeRegroupment !== 'undefined' && tag.value.params.typeRegroupment == data.data.params.typeRegroupement) {
                    // Uniformisation du texte depart / région entrant afin de tester correctement l'existance dans la chaine
                    let typeNameStandardize = that.textStandardize(data.data.params.typeName);
                    let filterTextStandardize = that.textStandardize(tag.value.filter);
                    let values;

                    // Si le depart / region selectionné n'est pas dans la liste des regions / depart du tag ==> ajout de celui-ci
                    if (filterTextStandardize.search(typeNameStandardize) === -1) {
                        filter.filter = tag.value.filter + ", " + data.data.params.typeName;
                        values = JSON.parse(tag.value.params.typeValue);
                        values.push(data.data.params.typeValue);
                        filter.params.typeValue = JSON.stringify(values);
                        filter.params.otherDatas[0] = { ...filter.params.otherDatas[0], ...tag.value.params.otherDatas[0] };
                        tag.value.params.otherDatas[0][data.data.params.typeValue] = data.data.params.typeName;
                        that.updateTagByTypeRegroupement(data.data.params.typeRegroupement, filter, filter.label);
                    } else {
                        // Cas suppression (reclick sur le departement) Si il n'y à que lui dans le tag (on supprime le tag)
                        if (Object.values(tag.value.params.otherDatas[0]).length === 1) {
                            that.removeTagByTypeRegroupement(data.data.params.typeRegroupement);
                        } else {
                            let newFilter = tag.value;

                            // On supprime la valeur du départ / région dans le tableau des values
                            values = JSON.parse(tag.value.params.typeValue);
                            values.splice(values.indexOf(data.data.params.typeValue), 1);
                            newFilter.params.typeValue = JSON.stringify(values);
                            // Supression de la correspondance departement/region X value
                            delete newFilter.params.otherDatas[0][data.data.params.typeValue];
                            newFilter.filter = data.data.params.typeRegroupement + " : " + Object.values(newFilter.params.otherDatas[0]).join(', ');
                            that.updateTagByTypeRegroupement(data.data.params.typeRegroupement, newFilter, newFilter.label);
                        }
                    }
                } else {
                    that.addFilter(filter);
                }
            } else {
                that.addFilter(filter);
            }
            that.onFilter(filter);
        });

        /**
         * A supprimer ?
         */

        that.dashboard.$element.on('click', 'rect.event-rect', function() {
            let parent = $(this).attr('data-parent');
            that.removeTag(that.getTagByParent(parent));
        });

        that.dashboard.$element.on('click', '.filter-block > .close', function() {
            let parent = $(this).closest('.filter-block');
            parent.remove();
        });

        that.dashboard.$element.on('click', '.filter-block .inclusion', function() {
            $(this).closest('.filter-block').find('.inclusion').removeClass('active');
            $(this).addClass('active');
        });


    };

}

