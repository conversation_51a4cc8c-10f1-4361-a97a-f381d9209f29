import { Controller } from '@hotwired/stimulus';
import Sortable from "sortablejs";

export default class extends Controller {

	connect() {
		Array.from(document.querySelectorAll('[data-price]'), (input: HTMLInputElement) => {
			let price = parseInt(input.dataset.price);
			let priceNode = document.getElementById(`price-${input.dataset.id}`);

			input.addEventListener('change', () => {
				let newPrice = parseInt(input.value) > 0 ? parseInt(input.value) * price : 0;
				priceNode.innerText = `${newPrice}€ HT`;
			})

			input.dispatchEvent(new Event('change'));
		})
	}

}