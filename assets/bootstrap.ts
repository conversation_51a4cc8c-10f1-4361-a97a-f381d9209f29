// register any custom, 3rd party controllers here
// app.register('some_controller_name', SomeImportedController);
import { registerControllers, startStimulusApp } from 'vite-plugin-symfony/stimulus/helpers';
import { SvelteController, registerSvelteControllerComponents } from 'vite-plugin-symfony/stimulus/helpers/svelte';
registerSvelteControllerComponents(import.meta.glob('./svelte/controllers/**/*.svelte'));

const app = startStimulusApp();

registerControllers(
    app,
    import.meta.glob('./controllers/*_controller.(js|ts)', {
        query: '?stimulus' /**
         * always true, the `lazy` behavior is managed internally with
         * import.meta.stimulusFetch (see reference)
         */,
        eager: true,
    })
);
