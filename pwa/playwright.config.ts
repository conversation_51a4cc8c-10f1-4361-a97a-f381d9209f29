import { devices, PlaywrightTestConfig } from '@playwright/test';

const config: PlaywrightTestConfig = {
	// webServer: {
	// 	command: 'npm run build  && npm run preview',
	// 	port: 4173
	// },
	use: {
		baseURL: process.env.CI ? 'http://api' : 'https://localhost',
		locale: 'fr-FR',
		timezoneId: 'Europe/Paris',
		contextOptions: {
			ignoreHTTPSErrors: true
		}
	},
  workers: process.env.CI ? 3 : undefined,
	testDir: 'src/tests/integration',
	testMatch: /(.+\.)?(test|spec)\.[jt]s/,
	fullyParallel: true,
	projects: [
		{ name: 'setup', testMatch: /.*\.setup\.ts/ },
		{
			name: 'chromium',
			use: {
				...devices['Desktop Chrome'],
				storageState: 'src/tests/integration/.auth/user.json'
			},
			dependencies: ['setup']
		}
	]
};

export default config;
