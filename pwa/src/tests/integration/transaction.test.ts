import { expect, test, type Page } from '@playwright/test';

test('test create transaction', async ({ page }) => {
	await page.goto('/customer/3');

	await page.locator('#transaction-amount').fill('150');

	// Check avant validation
	await expect(page.getByText('150 PTS')).toBeVisible();
	await expect(page.getByText('Cela génère 150 points de fidélité')).toBeVisible();
	await expect(page.getByText('Avant encaissement : 0 point')).toBeVisible();
	await expect(page.getByText('un chèque de 10€')).toBeVisible();

	// Validation
	await page.getByRole('button', { name: 'Valider encaissement' }).click();

	// Check après validation
	await expect(page.getByText('PLus que 50 points avant un chèque de 10€')).toBeVisible();
	await expect(page.getByText('50 PTS')).toBeVisible();

	// Historique
	await expect(page.getByText("Envoi d'un SMS Fidélité")).toBeVisible();
	await expect(page.getByText("Génération d'un chèque fidelite de 10 €")).toBeVisible();
	await expect(page.getByText('+ 150 points (encaissement de 150 €)')).toBeVisible();

	// Nouveau chèque
	await expect(page.getByText('Chèque Fidélité 10€ - à partir du')).toBeVisible();

	// Nouvelle transaction avec chèque
	await page.locator('#transaction-amount').fill('260');
	await page.getByText('Chèque Bienvenue 15€').click();

	// Check avant validation
	await expect(page.getByText('295 PTS')).toBeVisible();
	await expect(page.getByText('Cela génère 245 points de fidélité')).toBeVisible();
	await expect(page.getByText('Avant encaissement : 50 points')).toBeVisible();
	await expect(page.getByText('un chèque de 20€')).toBeVisible();

	// Validation
	await page.getByRole('button', { name: 'Valider encaissement' }).click();

	// Check après validation
	await expect(page.getByText('PLus que 5 points avant un chèque de 10€')).toBeVisible();
	await expect(page.getByText('95 PTS')).toBeVisible();

	// Nouveau chèque
	await expect(page.getByText('Chèque Fidélité 20€')).toBeVisible();

	// Historique
	await expect(page.getByText("Utilisation d'un chèque bienvenue de 15 €")).toBeVisible();
	await expect(page.getByText("Génération d'un chèque fidelite de 20 €")).toBeVisible();
	await expect(page.getByText('+ 245 points (encaissement de 260 €)')).toBeVisible();
});
