import { test as setup } from '@playwright/test';
import fs from 'fs';
import path from 'path';

const authFile = 'src/tests/integration/.auth/user.json';

setup('authenticate', async ({ request, baseURL }) => {
	// On appelle l'api pour reset la base de données et obtenir un token de connexion
	const response = await request.get(`/playwright/init`);
	if (response.status() !== 200) {
		throw new Error("Impossible d'appeler l'api de préparation de la base de données");
	}
	const data = await response.json();

	if (!fs.existsSync(path.dirname(authFile))) {
		fs.mkdirSync(path.dirname(authFile));
	}

	// On sauvegarde le fichier à la main car l'api de playwright ne permet pas de setter le localstorage manuellement
	await fs.writeFileSync(
		path.join(process.cwd(), authFile),
		JSON.stringify({
			cookies: [],
			origins: [
				{
					origin: baseURL,
					localStorage: [
						{
							name: 'jwt',
							value: data.token
						}
					]
				}
			]
		})
	);
});
