/********************************************************************************
 * /!\ Ce fichier est généré automatiquement par la librairie openapi-zod-client *
 * /!\ NE PAS LE MODIFIER A LA MAIN  											*
 * /!\ Il faut modifier le template dans src/lib/openapi-client/template.hbs   	*
 *********************************************************************************/

import { makeApi, Zodios, type ZodiosOptions } from '@zodios/core';
import { z } from 'zod';

export const CampaignAnniversaire_campaign_write_campaign_write_enabled_campaign_write_voucherEnabled =
	z
		.object({
			voucherAmount: z.number().int().gte(1).lte(999).default(5),
			voucherValidityMonth: z.number().int().gte(1).lte(99).default(2),
			richSmsEnabled: z.boolean(),
			richSmsContent: z.string().max(2000).nullish(),
			voucherEnabled: z.boolean(),
			enabled: z.boolean(),
			smsContent: z.string()
		})
		.passthrough();

export const CampaignAnniversaire_jsonld_campaign_read = z
	.object({
		'@context': z
			.union([
				z.string(),
				z
					.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
					.passthrough()
			])
			.optional(),
		'@id': z.string().optional(),
		'@type': z.string().optional(),
		voucherAmount: z.number().int().gte(1).lte(999).default(5),
		voucherValidityMonth: z.number().int().gte(1).lte(99).default(2),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		voucherEnabled: z.boolean(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const ValidationError_jsonld = z
	.object({
		'@context': z.string().optional(),
		'@type': z.string().optional(),
		'hydra:title': z.string().optional(),
		'hydra:description': z.string().optional(),
		violations: z.array(
			z.object({ propertyPath: z.string(), message: z.string() }).partial().passthrough()
		)
	})
	.passthrough();

export const CampaignBienvenue_campaign_write_campaign_write_enabled_campaign_write_voucherEnabled =
	z
		.object({
			voucherAmount: z.number().int().gte(1).lte(999).default(5),
			voucherValidityMonth: z.number().int().gte(1).lte(99).default(2),
			richSmsEnabled: z.boolean(),
			richSmsContent: z.string().max(2000).nullish(),
			voucherEnabled: z.boolean(),
			enabled: z.boolean(),
			smsContent: z.string()
		})
		.passthrough();

export const CampaignBienvenue_jsonld_campaign_read = z
	.object({
		'@context': z
			.union([
				z.string(),
				z
					.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
					.passthrough()
			])
			.optional(),
		'@id': z.string().optional(),
		'@type': z.string().optional(),
		voucherAmount: z.number().int().gte(1).lte(999).default(5),
		voucherValidityMonth: z.number().int().gte(1).lte(99).default(2),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		voucherEnabled: z.boolean(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignDormant_campaign_write_campaign_write_enabled_campaign_write_voucherEnabled = z
	.object({
		voucherAmount: z.number().int().gte(1).lte(999).default(5),
		voucherValidityMonth: z.number().int().gte(1).lte(99).default(2),
		customerInactiveSinceMonth: z.number().int().gte(1).lte(99).default(6),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		voucherEnabled: z.boolean(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignDormant_jsonld_campaign_read = z
	.object({
		'@context': z
			.union([
				z.string(),
				z
					.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
					.passthrough()
			])
			.optional(),
		'@id': z.string().optional(),
		'@type': z.string().optional(),
		voucherAmount: z.number().int().gte(1).lte(999).default(5),
		voucherValidityMonth: z.number().int().gte(1).lte(99).default(2),
		customerInactiveSinceMonth: z.number().int().gte(1).lte(99).default(6),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		voucherEnabled: z.boolean(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignExpirationCheque_campaign_write_campaign_write_enabled = z
	.object({
		expiresInDays: z.number().int().gte(1).lte(9).default(7),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignExpirationCheque_jsonld_campaign_read = z
	.object({
		'@context': z
			.union([
				z.string(),
				z
					.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
					.passthrough()
			])
			.optional(),
		'@id': z.string().optional(),
		'@type': z.string().optional(),
		expiresInDays: z.number().int().gte(1).lte(9).default(7),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignFidelite_campaign_write_campaign_write_voucherEnabled = z
	.object({
		earnedPoint: z.number().int().gte(1).lte(999).default(1),
		spentAmount: z.number().int().gte(1).lte(999).default(1),
		thresholdPoint: z.number().int().gte(1).lte(9999).default(100),
		voucherAmount: z.number().int().gte(1).lte(999).default(5),
		voucherValidityMonth: z.number().int().gte(1).lte(99).default(2),
		voucherMaxAmount: z.number().int().gte(1).lte(999).default(50),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		voucherEnabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignFidelite_jsonld_campaign_read = z
	.object({
		'@context': z
			.union([
				z.string(),
				z
					.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
					.passthrough()
			])
			.optional(),
		'@id': z.string().optional(),
		'@type': z.string().optional(),
		earnedPoint: z.number().int().gte(1).lte(999).default(1),
		spentAmount: z.number().int().gte(1).lte(999).default(1),
		thresholdPoint: z.number().int().gte(1).lte(9999).default(100),
		voucherAmount: z.number().int().gte(1).lte(999).default(5),
		voucherValidityMonth: z.number().int().gte(1).lte(99).default(2),
		voucherMaxAmount: z.number().int().gte(1).lte(999).default(50),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		voucherEnabled: z.boolean(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignGoogleReview_campaign_write_campaign_write_enabled_campaign_write_enabled = z
	.object({ enabled: z.boolean(), smsContent: z.string() })
	.passthrough();

export const CampaignGoogleReview_jsonld_campaign_read = z
	.object({
		'@context': z
			.union([
				z.string(),
				z
					.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
					.passthrough()
			])
			.optional(),
		'@id': z.string().optional(),
		'@type': z.string().optional(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignSmsFilter_jsonld_campaign_read = z
	.object({
		'@context': z.union([
			z.string(),
			z
				.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
				.passthrough()
		]),
		'@id': z.string(),
		'@type': z.string(),
		civilities: z.array(z.enum(['Monsieur', 'Madame', 'Non genré'])).nullable(),
		minimumAverageTransactionAmount: z.number().nullable(),
		maximumAverageTransactionAmount: z.number().nullable(),
		minimumTransactionCount: z.number().int().nullable(),
		maximumTransactionCount: z.number().int().nullable(),
		minimumTransactionDate: z.string().datetime({ offset: true }).nullable(),
		maximumTransactionDate: z.string().datetime({ offset: true }).nullable(),
		countries: z
			.array(
				z.enum([
					'de',
					'at',
					'be',
					'bg',
					'cy',
					'hr',
					'dk',
					'es',
					'ee',
					'fi',
					'fr',
					'gb',
					'gr',
					'hu',
					'ie',
					'it',
					'lv',
					'lt',
					'lu',
					'mt',
					'nl',
					'pl',
					'pt',
					'cz',
					'ro',
					'sk',
					'si',
					'se',
					'ch',
					'gf',
					'pf',
					'mf',
					'pm',
					'gp',
					'mq',
					're',
					'yt',
					'bl',
					'wf',
					'nc'
				])
			)
			.nullable()
	})
	.partial()
	.passthrough();

export const CampaignSms_jsonld_campaign_read = z
	.object({
		'@context': z
			.union([
				z.string(),
				z
					.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
					.passthrough()
			])
			.optional(),
		'@id': z.string().optional(),
		'@type': z.string().optional(),
		name: z.string().max(255),
		startDate: z.string().datetime({ offset: true }),
		customerCount: z.number().int(),
		filters: CampaignSmsFilter_jsonld_campaign_read.optional(),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignSmsFilter_jsonld_campaign_write_campaign_write_sms = z
	.object({
		civilities: z.array(z.enum(['Monsieur', 'Madame', 'Non genré'])).nullable(),
		minimumAverageTransactionAmount: z.number().nullable(),
		maximumAverageTransactionAmount: z.number().nullable(),
		minimumTransactionCount: z.number().int().nullable(),
		maximumTransactionCount: z.number().int().nullable(),
		minimumTransactionDate: z.string().datetime({ offset: true }).nullable(),
		maximumTransactionDate: z.string().datetime({ offset: true }).nullable(),
		countries: z
			.array(
				z.enum([
					'de',
					'at',
					'be',
					'bg',
					'cy',
					'hr',
					'dk',
					'es',
					'ee',
					'fi',
					'fr',
					'gb',
					'gr',
					'hu',
					'ie',
					'it',
					'lv',
					'lt',
					'lu',
					'mt',
					'nl',
					'pl',
					'pt',
					'cz',
					'ro',
					'sk',
					'si',
					'se',
					'ch',
					'gf',
					'pf',
					'mf',
					'pm',
					'gp',
					'mq',
					're',
					'yt',
					'bl',
					'wf',
					'nc'
				])
			)
			.nullable()
	})
	.partial()
	.passthrough();

export const CampaignSms_jsonld_campaign_write_campaign_write_sms = z
	.object({
		name: z.string().max(255),
		startDate: z.string().datetime({ offset: true }),
		filters: CampaignSmsFilter_jsonld_campaign_write_campaign_write_sms.optional(),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		company: z.string().optional(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignSms_CampaignSmsCountCustomersInputDto_jsonld_campaign_write_campaign_write_sms =
	z
		.object({
			civilities: z.array(z.enum(['Monsieur', 'Madame', 'Non genré'])).nullable(),
			minimumAverageTransactionAmount: z.number().nullable(),
			maximumAverageTransactionAmount: z.number().nullable(),
			minimumTransactionCount: z.number().int().nullable(),
			maximumTransactionCount: z.number().int().nullable(),
			minimumTransactionDate: z.string().datetime({ offset: true }).nullable(),
			maximumTransactionDate: z.string().datetime({ offset: true }).nullable(),
			countries: z
				.array(
					z.enum([
						'de',
						'at',
						'be',
						'bg',
						'cy',
						'hr',
						'dk',
						'es',
						'ee',
						'fi',
						'fr',
						'gb',
						'gr',
						'hu',
						'ie',
						'it',
						'lv',
						'lt',
						'lu',
						'mt',
						'nl',
						'pl',
						'pt',
						'cz',
						'ro',
						'sk',
						'si',
						'se',
						'ch',
						'gf',
						'pf',
						'mf',
						'pm',
						'gp',
						'mq',
						're',
						'yt',
						'bl',
						'wf',
						'nc'
					])
				)
				.nullable()
		})
		.partial()
		.passthrough();

export const CampaignSms_CampaignSmsCountCustomersOutputDto_jsonld_campaign_read = z
	.object({
		'@context': z.union([
			z.string(),
			z
				.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
				.passthrough()
		]),
		'@id': z.string(),
		'@type': z.string(),
		customerCount: z.number().int(),
		frenchCustomerCount: z.number().int(),
		otherCustomerCount: z.number().int(),
		consumedCredit: z.number().int()
	})
	.partial()
	.passthrough();

export const Company_EmptyInputDto_jsonld_write = z.object({}).partial().passthrough();

export const Company_EmptyOutputDto_jsonld_read = z
	.object({
		'@context': z.union([
			z.string(),
			z
				.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
				.passthrough()
		]),
		'@id': z.string(),
		'@type': z.string()
	})
	.partial()
	.passthrough();

export const Company_PaymentSessionStatusDto_jsonld_read = z
	.object({
		'@context': z.union([
			z.string(),
			z
				.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
				.passthrough()
		]),
		'@id': z.string(),
		'@type': z.string(),
		status: z.enum(['paid', 'processing', 'error'])
	})
	.partial()
	.passthrough();

export const Company_CompanyCheckPinInputDto_jsonld_write = z
	.object({ pin: z.string() })
	.partial()
	.passthrough();

export const Company_CompanyCheckPinOutputDto_jsonld_read = z
	.object({
		'@context': z.union([
			z.string(),
			z
				.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
				.passthrough()
		]),
		'@id': z.string(),
		'@type': z.string(),
		token: z.string()
	})
	.partial()
	.passthrough();

export const SecuredArea_company_write_config = z
	.object({
		companyStartPaymentSMS: z.boolean(),
		companySetReminder: z.boolean(),
		campaignSmsPost: z.boolean(),
		campaignAnniversairePatch: z.boolean(),
		campaignBienvenuePatch: z.boolean(),
		campaignDormantPatch: z.boolean(),
		campaignExpirationChequePatch: z.boolean(),
		campaignFidelitePatch: z.boolean(),
		campaignGoogleReviewPatch: z.boolean(),
		customerDelete: z.boolean(),
		customerSetLoyaltyPoints: z.boolean()
	})
	.passthrough();

export const Company_company_write_config = z
	.object({
		pin: z
			.string()
			.min(4)
			.max(4)
			.regex(/^(.*\d{4}.*)$/)
			.optional(),
		plainPassword: z
			.string()
			.min(12)
			.max(255)
			.regex(/^((?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{12,255}.*)$/)
			.nullish(),
		firstname: z
			.string()
			.max(255)
			.regex(/^(((?!\d).)*)$/),
		lastname: z
			.string()
			.max(255)
			.regex(/^(((?!\d).)*)$/),
		phoneNumber: z
			.string()
			.min(10)
			.max(10)
			.regex(/^(0[67]\d{8})$/),
		name: z.string().max(255),
		senderId: z
			.string()
			.max(11)
			.regex(/^(((?!^[0-9]*$).)*)$/),
		activityArea: z.enum([
			'Bijouterie',
			'Camping',
			'Chaussures',
			'Coiffure',
			'Commerce alimentaire',
			'Commerce de détail',
			'Fleuriste',
			'Garagiste',
			'Hôtel',
			'Institut',
			'Jardinerie',
			'Lingerie',
			'Parfumerie',
			'Prêt à porter',
			'Restauration',
			'Autre'
		]),
		customActivityArea: z.string().max(75).nullish(),
		postalCode: z
			.string()
			.min(5)
			.max(5)
			.regex(/^(\d{5})$/),
		city: z.string().max(255),
		securedAreas: SecuredArea_company_write_config.optional(),
		googleReviewUrl: z
			.string()
			.max(255)
			.regex(
				/^(.*(^(https:\/\/g\.page\/r\/[\w-]+\/review|https:\/\/g\.co\/kgs\/[\w-]+|https:\/\/search\.google\.com\/local\/writereview\?placeid=[\w-]+)$).*)$/
			)
			.url()
			.nullish()
	})
	.passthrough();

export const SecuredArea_jsonld_read = z
	.object({
		'@context': z
			.union([
				z.string(),
				z
					.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
					.passthrough()
			])
			.optional(),
		'@id': z.string().optional(),
		'@type': z.string().optional(),
		companyUpdateConfig: z.boolean(),
		companyStartPaymentSMS: z.boolean(),
		companySetReminder: z.boolean(),
		campaignSmsPost: z.boolean(),
		campaignAnniversairePatch: z.boolean(),
		campaignBienvenuePatch: z.boolean(),
		campaignDormantPatch: z.boolean(),
		campaignExpirationChequePatch: z.boolean(),
		campaignFidelitePatch: z.boolean(),
		campaignGoogleReviewPatch: z.boolean(),
		customerDelete: z.boolean(),
		customerSetLoyaltyPoints: z.boolean()
	})
	.passthrough();

export const Subscription_jsonld_read = z
	.object({
		'@context': z.union([
			z.string(),
			z
				.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
				.passthrough()
		]),
		'@id': z.string(),
		'@type': z.string(),
		smsCredit: z.number().int(),
		fiducialSocieteId: z.string().max(8).nullable(),
		expirationDate: z.string().datetime({ offset: true }),
		active: z.boolean(),
		cancelled: z.boolean()
	})
	.partial()
	.passthrough();

export const CampaignFidelite_jsonld_read = z
	.object({
		'@context': z
			.union([
				z.string(),
				z
					.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
					.passthrough()
			])
			.optional(),
		'@id': z.string().optional(),
		'@type': z.string().optional(),
		earnedPoint: z.number().int().gte(1).lte(999).default(1),
		spentAmount: z.number().int().gte(1).lte(999).default(1),
		thresholdPoint: z.number().int().gte(1).lte(9999).default(100),
		voucherAmount: z.number().int().gte(1).lte(999).default(5),
		voucherValidityMonth: z.number().int().gte(1).lte(99).default(2),
		voucherMaxAmount: z.number().int().gte(1).lte(999).default(50),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		voucherEnabled: z.boolean(),
		type: z
			.enum([
				'campaign_anniversaire',
				'campaign_bienvenue',
				'campaign_dormant',
				'campaign_expiration_cheque',
				'campaign_fidelite',
				'campaign_google_review',
				'campaign_sms'
			])
			.optional(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignAnniversaire_jsonld_read = z
	.object({
		'@context': z
			.union([
				z.string(),
				z
					.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
					.passthrough()
			])
			.optional(),
		'@id': z.string().optional(),
		'@type': z.string().optional(),
		voucherAmount: z.number().int().gte(1).lte(999).default(5),
		voucherValidityMonth: z.number().int().gte(1).lte(99).default(2),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		voucherEnabled: z.boolean(),
		type: z
			.enum([
				'campaign_anniversaire',
				'campaign_bienvenue',
				'campaign_dormant',
				'campaign_expiration_cheque',
				'campaign_fidelite',
				'campaign_google_review',
				'campaign_sms'
			])
			.optional(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignBienvenue_jsonld_read = z
	.object({
		'@context': z
			.union([
				z.string(),
				z
					.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
					.passthrough()
			])
			.optional(),
		'@id': z.string().optional(),
		'@type': z.string().optional(),
		voucherAmount: z.number().int().gte(1).lte(999).default(5),
		voucherValidityMonth: z.number().int().gte(1).lte(99).default(2),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		voucherEnabled: z.boolean(),
		type: z
			.enum([
				'campaign_anniversaire',
				'campaign_bienvenue',
				'campaign_dormant',
				'campaign_expiration_cheque',
				'campaign_fidelite',
				'campaign_google_review',
				'campaign_sms'
			])
			.optional(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignExpirationCheque_jsonld_read = z
	.object({
		'@context': z
			.union([
				z.string(),
				z
					.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
					.passthrough()
			])
			.optional(),
		'@id': z.string().optional(),
		'@type': z.string().optional(),
		expiresInDays: z.number().int().gte(1).lte(9).default(7),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		type: z
			.enum([
				'campaign_anniversaire',
				'campaign_bienvenue',
				'campaign_dormant',
				'campaign_expiration_cheque',
				'campaign_fidelite',
				'campaign_google_review',
				'campaign_sms'
			])
			.optional(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignDormant_jsonld_read = z
	.object({
		'@context': z
			.union([
				z.string(),
				z
					.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
					.passthrough()
			])
			.optional(),
		'@id': z.string().optional(),
		'@type': z.string().optional(),
		voucherAmount: z.number().int().gte(1).lte(999).default(5),
		voucherValidityMonth: z.number().int().gte(1).lte(99).default(2),
		customerInactiveSinceMonth: z.number().int().gte(1).lte(99).default(6),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		voucherEnabled: z.boolean(),
		type: z
			.enum([
				'campaign_anniversaire',
				'campaign_bienvenue',
				'campaign_dormant',
				'campaign_expiration_cheque',
				'campaign_fidelite',
				'campaign_google_review',
				'campaign_sms'
			])
			.optional(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignGoogleReview_jsonld_read = z
	.object({
		'@context': z
			.union([
				z.string(),
				z
					.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
					.passthrough()
			])
			.optional(),
		'@id': z.string().optional(),
		'@type': z.string().optional(),
		type: z
			.enum([
				'campaign_anniversaire',
				'campaign_bienvenue',
				'campaign_dormant',
				'campaign_expiration_cheque',
				'campaign_fidelite',
				'campaign_google_review',
				'campaign_sms'
			])
			.optional(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const Company_jsonld_read = z
	.object({
		'@context': z
			.union([
				z.string(),
				z
					.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
					.passthrough()
			])
			.optional(),
		'@id': z.string().optional(),
		'@type': z.string().optional(),
		id: z.number().int().optional(),
		email: z.string().max(255).email(),
		firstname: z
			.string()
			.max(255)
			.regex(/^(((?!\d).)*)$/),
		lastname: z
			.string()
			.max(255)
			.regex(/^(((?!\d).)*)$/),
		phoneNumber: z
			.string()
			.min(10)
			.max(10)
			.regex(/^(0[67]\d{8})$/),
		name: z.string().max(255),
		senderId: z
			.string()
			.max(11)
			.regex(/^(((?!^[0-9]*$).)*)$/),
		senderIdVerified: z.boolean().nullish(),
		activityArea: z.enum([
			'Bijouterie',
			'Camping',
			'Chaussures',
			'Coiffure',
			'Commerce alimentaire',
			'Commerce de détail',
			'Fleuriste',
			'Garagiste',
			'Hôtel',
			'Institut',
			'Jardinerie',
			'Lingerie',
			'Parfumerie',
			'Prêt à porter',
			'Restauration',
			'Autre'
		]),
		customActivityArea: z.string().max(75).nullish(),
		postalCode: z
			.string()
			.min(5)
			.max(5)
			.regex(/^(\d{5})$/),
		city: z.string().max(255),
		companyType: z.enum(['default', 'fiducial']).nullish(),
		reminder: z.string().max(180).nullish(),
		stats: z.array(z.string()).nullish(),
		securedAreas: SecuredArea_jsonld_read.optional(),
		registrationCompleted: z.boolean().optional(),
		googleReviewUrl: z
			.string()
			.max(255)
			.regex(
				/^(.*(^(https:\/\/g\.page\/r\/[\w-]+\/review|https:\/\/g\.co\/kgs\/[\w-]+|https:\/\/search\.google\.com\/local\/writereview\?placeid=[\w-]+)$).*)$/
			)
			.url()
			.nullish(),
		subscription: z.union([Subscription_jsonld_read, z.null()]).optional(),
		campaignFidelite: CampaignFidelite_jsonld_read.optional(),
		campaignAnniversaire: CampaignAnniversaire_jsonld_read.optional(),
		campaignBienvenue: CampaignBienvenue_jsonld_read.optional(),
		campaignExpirationCheque: CampaignExpirationCheque_jsonld_read.optional(),
		campaignDormant: CampaignDormant_jsonld_read.optional(),
		campaignGoogleReview: CampaignGoogleReview_jsonld_read.optional(),
		archived: z.boolean().optional()
	})
	.passthrough();

export const Company_CompanyStripePortalDto_jsonld_read = z
	.object({
		'@context': z.union([
			z.string(),
			z
				.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
				.passthrough()
		]),
		'@id': z.string(),
		'@type': z.string(),
		url: z.string()
	})
	.partial()
	.passthrough();

export const Company_company_write_reminder = z
	.object({ reminder: z.string().max(180).nullable() })
	.partial()
	.passthrough();

export const Company_PaymentStartDto_jsonld_read = z
	.object({
		'@context': z.union([
			z.string(),
			z
				.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
				.passthrough()
		]),
		'@id': z.string(),
		'@type': z.string(),
		link: z.string().url()
	})
	.partial()
	.passthrough();

export const Company_CompanyStatsDto_jsonld_read = z
	.object({
		'@context': z.union([
			z.string(),
			z
				.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
				.passthrough()
		]),
		'@id': z.string(),
		'@type': z.string(),
		companyStats: z.array(
			z
				.object({
					id: z.enum([
						'new_clients',
						'total_ca',
						'nb_transactions',
						'avg_ca',
						'nb_voucher',
						'total_voucher_amount',
						'sms_auto_sent',
						'sms_campaign_sent'
					]),
					value: z.number()
				})
				.partial()
				.passthrough()
		)
	})
	.partial()
	.passthrough();

export const Company_company_write_stats = z
	.object({
		stats: z.array(
			z.enum([
				'new_clients',
				'total_ca',
				'nb_transactions',
				'avg_ca',
				'nb_voucher',
				'total_voucher_amount',
				'sms_auto_sent',
				'sms_campaign_sent'
			])
		)
	})
	.partial()
	.passthrough();

export const Company_SubscribeWithFiducialInputDto_jsonld_write = z
	.object({ societeId: z.string().max(8) })
	.partial()
	.passthrough();

export const Company_SubscribeWithFiducialOutputDto_jsonld_read = z
	.object({
		'@context': z.union([
			z.string(),
			z
				.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
				.passthrough()
		]),
		'@id': z.string(),
		'@type': z.string(),
		isSubscribed: z.boolean()
	})
	.partial()
	.passthrough();

export const Company_CompanyGetSubscriptionOutput_jsonld_read = z
	.object({
		'@context': z.union([
			z.string(),
			z
				.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
				.passthrough()
		]),
		'@id': z.string(),
		'@type': z.string(),
		status: z.enum([
			'active',
			'canceled',
			'incomplete',
			'incomplete_expired',
			'past_due',
			'paused',
			'trialing',
			'unpaid'
		])
	})
	.partial()
	.passthrough();

export const CompanyDraft_jsonld_write = z
	.object({
		pin: z
			.string()
			.min(4)
			.max(4)
			.regex(/^(.*\d{4}.*)$/),
		email: z.string().max(255).email(),
		firstname: z
			.string()
			.max(255)
			.regex(/^(((?!\d).)*)$/),
		lastname: z
			.string()
			.max(255)
			.regex(/^(((?!\d).)*)$/),
		phoneNumber: z
			.string()
			.min(10)
			.max(10)
			.regex(/^(0[67]\d{8})$/),
		name: z.string().max(255),
		senderId: z
			.string()
			.max(11)
			.regex(/^(((?!^[0-9]*$).)*)$/),
		activityArea: z.enum([
			'Bijouterie',
			'Camping',
			'Chaussures',
			'Coiffure',
			'Commerce alimentaire',
			'Commerce de détail',
			'Fleuriste',
			'Garagiste',
			'Hôtel',
			'Institut',
			'Jardinerie',
			'Lingerie',
			'Parfumerie',
			'Prêt à porter',
			'Restauration',
			'Autre'
		]),
		postalCode: z
			.string()
			.min(5)
			.max(5)
			.regex(/^(\d{5})$/),
		city: z.string().max(255),
		cguAccepted: z.boolean(),
		plainPassword: z
			.string()
			.min(12)
			.max(255)
			.regex(/^((?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{12,255}.*)$/)
			.nullable()
	})
	.passthrough();

export const CompanyDraft_jsonld_read = z
	.object({
		'@context': z.union([
			z.string(),
			z
				.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
				.passthrough()
		]),
		'@id': z.string(),
		'@type': z.string(),
		id: z.number().int()
	})
	.partial()
	.passthrough();

export const CompanyDraft_CompanyDraftCheckEmailAvailabilityInputDto_jsonld_write = z
	.object({ email: z.string().email() })
	.partial()
	.passthrough();

export const CompanyDraft_CompanyDraftCheckEmailAvailabilityOutputDto_jsonld_read = z
	.object({
		'@context': z.union([
			z.string(),
			z
				.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
				.passthrough()
		]),
		'@id': z.string(),
		'@type': z.string(),
		valid: z.boolean()
	})
	.partial()
	.passthrough();

export const CompanyDraft_CompanyDraftValidateDto_jsonld_read = z
	.object({
		'@context': z.union([
			z.string(),
			z
				.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
				.passthrough()
		]),
		'@id': z.string(),
		'@type': z.string(),
		token: z.string()
	})
	.partial()
	.passthrough();

export const Customer_jsonld_customer_write = z
	.object({
		company: z.string().optional(),
		firstname: z
			.string()
			.max(255)
			.regex(/^(((?!\d).)*)$/),
		lastname: z
			.string()
			.max(255)
			.regex(/^(((?!\d).)*)$/),
		civility: z.enum(['Monsieur', 'Madame', 'Non genré']),
		phoneNumber: z.string().max(35).nullable(),
		phoneNumberCountryCode: z
			.enum([
				'de',
				'at',
				'be',
				'bg',
				'cy',
				'hr',
				'dk',
				'es',
				'ee',
				'fi',
				'fr',
				'gb',
				'gr',
				'hu',
				'ie',
				'it',
				'lv',
				'lt',
				'lu',
				'mt',
				'nl',
				'pl',
				'pt',
				'cz',
				'ro',
				'sk',
				'si',
				'se',
				'ch',
				'gf',
				'pf',
				'mf',
				'pm',
				'gp',
				'mq',
				're',
				'yt',
				'bl',
				'wf',
				'nc'
			])
			.nullable()
			.default('fr'),
		email: z.string().max(255).email().nullish(),
		birthDate: z.string().nullish(),
		postalCode: z.string().min(5).max(5).nullish(),
		city: z.string().max(255).nullish(),
		note: z.string().max(255).nullish(),
		comment: z.string().max(500).nullish()
	})
	.passthrough();

export const Voucher_jsonld_customer_read = z
	.object({
		'@context': z.union([
			z.string(),
			z
				.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
				.passthrough()
		]),
		'@id': z.string(),
		'@type': z.string(),
		id: z.number().int(),
		type: z.enum(['fidelite', 'anniversaire', 'bienvenue', 'dormant']),
		amount: z.number().int(),
		burnt: z.boolean(),
		burntDate: z.string().datetime({ offset: true }).nullable(),
		expirationDate: z.string().datetime({ offset: true }).nullable(),
		availabilityDate: z.string().datetime({ offset: true }).nullable(),
		available: z.boolean()
	})
	.partial()
	.passthrough();

export const Customer_jsonld_customer_read = z
	.object({
		'@context': z
			.union([
				z.string(),
				z
					.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
					.passthrough()
			])
			.optional(),
		'@id': z.string().optional(),
		'@type': z.string().optional(),
		id: z.number().int().optional(),
		firstname: z
			.string()
			.max(255)
			.regex(/^(((?!\d).)*)$/),
		lastname: z
			.string()
			.max(255)
			.regex(/^(((?!\d).)*)$/),
		civility: z.enum(['Monsieur', 'Madame', 'Non genré']),
		phoneNumber: z.string().max(35).nullable(),
		phoneNumberCountryCode: z
			.enum([
				'de',
				'at',
				'be',
				'bg',
				'cy',
				'hr',
				'dk',
				'es',
				'ee',
				'fi',
				'fr',
				'gb',
				'gr',
				'hu',
				'ie',
				'it',
				'lv',
				'lt',
				'lu',
				'mt',
				'nl',
				'pl',
				'pt',
				'cz',
				'ro',
				'sk',
				'si',
				'se',
				'ch',
				'gf',
				'pf',
				'mf',
				'pm',
				'gp',
				'mq',
				're',
				'yt',
				'bl',
				'wf',
				'nc'
			])
			.nullable()
			.default('fr'),
		phoneNumberVerified: z.boolean().nullish(),
		stopSmsReceived: z.boolean().optional(),
		email: z.string().max(255).email().nullish(),
		birthDate: z.string().nullish(),
		postalCode: z.string().min(5).max(5).nullish(),
		city: z.string().max(255).nullish(),
		note: z.string().max(255).nullish(),
		comment: z.string().max(500).nullish(),
		commentDate: z.string().datetime({ offset: true }).nullish(),
		loyaltyPoints: z.number().int().optional(),
		vouchers: z.array(Voucher_jsonld_customer_read).optional(),
		createdAt: z.string().datetime({ offset: true }).optional()
	})
	.passthrough();

export const Customer_customer_write = z
	.object({
		company: z.string().optional(),
		firstname: z
			.string()
			.max(255)
			.regex(/^(((?!\d).)*)$/),
		lastname: z
			.string()
			.max(255)
			.regex(/^(((?!\d).)*)$/),
		civility: z.enum(['Monsieur', 'Madame', 'Non genré']),
		phoneNumber: z.string().max(35).nullable(),
		phoneNumberCountryCode: z
			.enum([
				'de',
				'at',
				'be',
				'bg',
				'cy',
				'hr',
				'dk',
				'es',
				'ee',
				'fi',
				'fr',
				'gb',
				'gr',
				'hu',
				'ie',
				'it',
				'lv',
				'lt',
				'lu',
				'mt',
				'nl',
				'pl',
				'pt',
				'cz',
				'ro',
				'sk',
				'si',
				'se',
				'ch',
				'gf',
				'pf',
				'mf',
				'pm',
				'gp',
				'mq',
				're',
				'yt',
				'bl',
				'wf',
				'nc'
			])
			.nullable()
			.default('fr'),
		email: z.string().max(255).email().nullish(),
		birthDate: z.string().nullish(),
		postalCode: z.string().min(5).max(5).nullish(),
		city: z.string().max(255).nullish(),
		note: z.string().max(255).nullish(),
		comment: z.string().max(500).nullish()
	})
	.passthrough();

export const Customer_customer_write_loyalty_points = z
	.object({ loyaltyPoints: z.number().int() })
	.partial()
	.passthrough();

export const Campaign_jsonld_history_read = z
	.object({
		'@context': z
			.union([
				z.string(),
				z
					.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
					.passthrough()
			])
			.optional(),
		'@id': z.string().optional(),
		'@type': z.string().optional(),
		smsContent: z.string()
	})
	.passthrough();

export const Sms_jsonld_history_read = z
	.object({
		'@context': z.union([
			z.string(),
			z
				.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
				.passthrough()
		]),
		'@id': z.string(),
		'@type': z.string(),
		campaign: Campaign_jsonld_history_read,
		sentDate: z.string().datetime({ offset: true }),
		status: z.string().nullable(),
		sent: z.boolean().nullable()
	})
	.partial()
	.passthrough();

export const CustomerTransaction_jsonld_history_read = z
	.object({
		'@context': z.union([
			z.string(),
			z
				.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
				.passthrough()
		]),
		'@id': z.string(),
		'@type': z.string(),
		id: z.number().int(),
		removable: z.boolean()
	})
	.partial()
	.passthrough();

export const CustomerHistory_jsonld_history_read = z
	.object({
		'@id': z.string(),
		'@type': z.string(),
		type: z.enum([
			'create',
			'edit',
			'add_points',
			'edit_points',
			'sms',
			'stop_sms_form',
			'add_voucher',
			'use_voucher'
		]),
		vars: z.array(z.string()),
		relatedVoucher: z.string().nullable(),
		relatedSms: z.union([Sms_jsonld_history_read, z.null()]),
		relatedTransaction: z.union([CustomerTransaction_jsonld_history_read, z.null()]),
		createdAt: z.string().datetime({ offset: true }),
		removed: z.boolean()
	})
	.partial()
	.passthrough();

export const CustomerTransaction_jsonld_transaction_write = z
	.object({
		customer: z.string(),
		amount: z.number().int().gte(1).lte(1000000),
		vouchers: z.array(z.string()).optional()
	})
	.passthrough();

export const CustomerTransaction_jsonld_read = z
	.object({
		'@context': z
			.union([
				z.string(),
				z
					.object({ '@vocab': z.string(), hydra: z.literal('http://www.w3.org/ns/hydra/core#') })
					.passthrough()
			])
			.optional(),
		'@id': z.string().optional(),
		'@type': z.string().optional(),
		id: z.number().int().optional(),
		amount: z.number().int().gte(1).lte(1000000),
		vouchers: z.array(z.string()).optional()
	})
	.passthrough();

export const Faq_jsonld_faq_read = z
	.object({
		'@id': z.string(),
		'@type': z.string(),
		title: z.string(),
		content: z.string(),
		position: z.number().int()
	})
	.partial()
	.passthrough();

export const ForgotPassword_request = z
	.object({ email: z.union([z.string(), z.number()]) })
	.passthrough();

export const ForgotPassword_reset = z
	.object({
		password: z
			.string()
			.min(12)
			.max(255)
			.regex(/^((?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{12,255}.*)$/)
			.nullable()
	})
	.passthrough();

export const getOpenAiRecommandationsLanding_Body = z
	.object({ smsContent: z.string(), campaignTheme: z.string() })
	.partial()
	.passthrough();

export const getOpenAiRecommandationsSms_Body = z
	.object({
		campaignType: z.enum(['Anniversaire', 'Bienvenue', 'Dormant', 'Expiration', 'Fidelite', 'Sms']),
		voucherAmount: z.number(),
		voucherValidityMonth: z.number(),
		campaignTheme: z.string(),
		voucherEnabled: z.boolean(),
		richSmsEnabled: z.boolean()
	})
	.partial()
	.passthrough();

export const auth_Body = z
	.object({ email: z.string(), password: z.string() })
	.partial()
	.passthrough();

export const Tutorial_jsonld_tutorial_read = z
	.object({
		'@id': z.string(),
		'@type': z.string(),
		title: z.string(),
		youtubeUrl: z.string().regex(/^(https:\/\/www\.youtube\.com\/embed\/.*.*)$/),
		position: z.number().int()
	})
	.partial()
	.passthrough();

export const Campaign_history_read = z.object({ smsContent: z.string() }).passthrough();

export const CampaignAnniversaire_campaign_read = z
	.object({
		voucherAmount: z.number().int().gte(1).lte(999).default(5),
		voucherValidityMonth: z.number().int().gte(1).lte(99).default(2),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		voucherEnabled: z.boolean(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignAnniversaire_read = z
	.object({
		voucherAmount: z.number().int().gte(1).lte(999).default(5),
		voucherValidityMonth: z.number().int().gte(1).lte(99).default(2),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		voucherEnabled: z.boolean(),
		type: z
			.enum([
				'campaign_anniversaire',
				'campaign_bienvenue',
				'campaign_dormant',
				'campaign_expiration_cheque',
				'campaign_fidelite',
				'campaign_google_review',
				'campaign_sms'
			])
			.optional(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignBienvenue_campaign_read = z
	.object({
		voucherAmount: z.number().int().gte(1).lte(999).default(5),
		voucherValidityMonth: z.number().int().gte(1).lte(99).default(2),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		voucherEnabled: z.boolean(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignBienvenue_read = z
	.object({
		voucherAmount: z.number().int().gte(1).lte(999).default(5),
		voucherValidityMonth: z.number().int().gte(1).lte(99).default(2),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		voucherEnabled: z.boolean(),
		type: z
			.enum([
				'campaign_anniversaire',
				'campaign_bienvenue',
				'campaign_dormant',
				'campaign_expiration_cheque',
				'campaign_fidelite',
				'campaign_google_review',
				'campaign_sms'
			])
			.optional(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignDormant_campaign_read = z
	.object({
		voucherAmount: z.number().int().gte(1).lte(999).default(5),
		voucherValidityMonth: z.number().int().gte(1).lte(99).default(2),
		customerInactiveSinceMonth: z.number().int().gte(1).lte(99).default(6),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		voucherEnabled: z.boolean(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignDormant_read = z
	.object({
		voucherAmount: z.number().int().gte(1).lte(999).default(5),
		voucherValidityMonth: z.number().int().gte(1).lte(99).default(2),
		customerInactiveSinceMonth: z.number().int().gte(1).lte(99).default(6),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		voucherEnabled: z.boolean(),
		type: z
			.enum([
				'campaign_anniversaire',
				'campaign_bienvenue',
				'campaign_dormant',
				'campaign_expiration_cheque',
				'campaign_fidelite',
				'campaign_google_review',
				'campaign_sms'
			])
			.optional(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignExpirationCheque_campaign_read = z
	.object({
		expiresInDays: z.number().int().gte(1).lte(9).default(7),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignExpirationCheque_read = z
	.object({
		expiresInDays: z.number().int().gte(1).lte(9).default(7),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		type: z
			.enum([
				'campaign_anniversaire',
				'campaign_bienvenue',
				'campaign_dormant',
				'campaign_expiration_cheque',
				'campaign_fidelite',
				'campaign_google_review',
				'campaign_sms'
			])
			.optional(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignFidelite_campaign_read = z
	.object({
		earnedPoint: z.number().int().gte(1).lte(999).default(1),
		spentAmount: z.number().int().gte(1).lte(999).default(1),
		thresholdPoint: z.number().int().gte(1).lte(9999).default(100),
		voucherAmount: z.number().int().gte(1).lte(999).default(5),
		voucherValidityMonth: z.number().int().gte(1).lte(99).default(2),
		voucherMaxAmount: z.number().int().gte(1).lte(999).default(50),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		voucherEnabled: z.boolean(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignFidelite_read = z
	.object({
		earnedPoint: z.number().int().gte(1).lte(999).default(1),
		spentAmount: z.number().int().gte(1).lte(999).default(1),
		thresholdPoint: z.number().int().gte(1).lte(9999).default(100),
		voucherAmount: z.number().int().gte(1).lte(999).default(5),
		voucherValidityMonth: z.number().int().gte(1).lte(99).default(2),
		voucherMaxAmount: z.number().int().gte(1).lte(999).default(50),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		voucherEnabled: z.boolean(),
		type: z
			.enum([
				'campaign_anniversaire',
				'campaign_bienvenue',
				'campaign_dormant',
				'campaign_expiration_cheque',
				'campaign_fidelite',
				'campaign_google_review',
				'campaign_sms'
			])
			.optional(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignGoogleReview_campaign_read = z
	.object({ id: z.number().int().optional(), enabled: z.boolean(), smsContent: z.string() })
	.passthrough();

export const CampaignGoogleReview_read = z
	.object({
		type: z
			.enum([
				'campaign_anniversaire',
				'campaign_bienvenue',
				'campaign_dormant',
				'campaign_expiration_cheque',
				'campaign_fidelite',
				'campaign_google_review',
				'campaign_sms'
			])
			.optional(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignSmsFilter_campaign_read = z
	.object({
		civilities: z.array(z.enum(['Monsieur', 'Madame', 'Non genré'])).nullable(),
		minimumAverageTransactionAmount: z.number().nullable(),
		maximumAverageTransactionAmount: z.number().nullable(),
		minimumTransactionCount: z.number().int().nullable(),
		maximumTransactionCount: z.number().int().nullable(),
		minimumTransactionDate: z.string().datetime({ offset: true }).nullable(),
		maximumTransactionDate: z.string().datetime({ offset: true }).nullable(),
		countries: z
			.array(
				z.enum([
					'de',
					'at',
					'be',
					'bg',
					'cy',
					'hr',
					'dk',
					'es',
					'ee',
					'fi',
					'fr',
					'gb',
					'gr',
					'hu',
					'ie',
					'it',
					'lv',
					'lt',
					'lu',
					'mt',
					'nl',
					'pl',
					'pt',
					'cz',
					'ro',
					'sk',
					'si',
					'se',
					'ch',
					'gf',
					'pf',
					'mf',
					'pm',
					'gp',
					'mq',
					're',
					'yt',
					'bl',
					'wf',
					'nc'
				])
			)
			.nullable()
	})
	.partial()
	.passthrough();

export const CampaignSms_campaign_read = z
	.object({
		name: z.string().max(255),
		startDate: z.string().datetime({ offset: true }),
		customerCount: z.number().int(),
		filters: CampaignSmsFilter_campaign_read.optional(),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		id: z.number().int().optional(),
		enabled: z.boolean(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignSmsFilter_campaign_write_campaign_write_sms = z
	.object({
		civilities: z.array(z.enum(['Monsieur', 'Madame', 'Non genré'])).nullable(),
		minimumAverageTransactionAmount: z.number().nullable(),
		maximumAverageTransactionAmount: z.number().nullable(),
		minimumTransactionCount: z.number().int().nullable(),
		maximumTransactionCount: z.number().int().nullable(),
		minimumTransactionDate: z.string().datetime({ offset: true }).nullable(),
		maximumTransactionDate: z.string().datetime({ offset: true }).nullable(),
		countries: z
			.array(
				z.enum([
					'de',
					'at',
					'be',
					'bg',
					'cy',
					'hr',
					'dk',
					'es',
					'ee',
					'fi',
					'fr',
					'gb',
					'gr',
					'hu',
					'ie',
					'it',
					'lv',
					'lt',
					'lu',
					'mt',
					'nl',
					'pl',
					'pt',
					'cz',
					'ro',
					'sk',
					'si',
					'se',
					'ch',
					'gf',
					'pf',
					'mf',
					'pm',
					'gp',
					'mq',
					're',
					'yt',
					'bl',
					'wf',
					'nc'
				])
			)
			.nullable()
	})
	.partial()
	.passthrough();

export const CampaignSms_campaign_write_campaign_write_sms = z
	.object({
		name: z.string().max(255),
		startDate: z.string().datetime({ offset: true }),
		filters: CampaignSmsFilter_campaign_write_campaign_write_sms.optional(),
		richSmsEnabled: z.boolean(),
		richSmsContent: z.string().max(2000).nullish(),
		company: z.string().optional(),
		smsContent: z.string()
	})
	.passthrough();

export const CampaignSms_CampaignSmsCountCustomersInputDto_campaign_write_campaign_write_sms = z
	.object({
		civilities: z.array(z.enum(['Monsieur', 'Madame', 'Non genré'])).nullable(),
		minimumAverageTransactionAmount: z.number().nullable(),
		maximumAverageTransactionAmount: z.number().nullable(),
		minimumTransactionCount: z.number().int().nullable(),
		maximumTransactionCount: z.number().int().nullable(),
		minimumTransactionDate: z.string().datetime({ offset: true }).nullable(),
		maximumTransactionDate: z.string().datetime({ offset: true }).nullable(),
		countries: z
			.array(
				z.enum([
					'de',
					'at',
					'be',
					'bg',
					'cy',
					'hr',
					'dk',
					'es',
					'ee',
					'fi',
					'fr',
					'gb',
					'gr',
					'hu',
					'ie',
					'it',
					'lv',
					'lt',
					'lu',
					'mt',
					'nl',
					'pl',
					'pt',
					'cz',
					'ro',
					'sk',
					'si',
					'se',
					'ch',
					'gf',
					'pf',
					'mf',
					'pm',
					'gp',
					'mq',
					're',
					'yt',
					'bl',
					'wf',
					'nc'
				])
			)
			.nullable()
	})
	.partial()
	.passthrough();

export const CampaignSms_CampaignSmsCountCustomersOutputDto_campaign_read = z
	.object({
		customerCount: z.number().int(),
		frenchCustomerCount: z.number().int(),
		otherCustomerCount: z.number().int(),
		consumedCredit: z.number().int()
	})
	.partial()
	.passthrough();

export const SecuredArea_read = z
	.object({
		companyUpdateConfig: z.boolean(),
		companyStartPaymentSMS: z.boolean(),
		companySetReminder: z.boolean(),
		campaignSmsPost: z.boolean(),
		campaignAnniversairePatch: z.boolean(),
		campaignBienvenuePatch: z.boolean(),
		campaignDormantPatch: z.boolean(),
		campaignExpirationChequePatch: z.boolean(),
		campaignFidelitePatch: z.boolean(),
		campaignGoogleReviewPatch: z.boolean(),
		customerDelete: z.boolean(),
		customerSetLoyaltyPoints: z.boolean()
	})
	.passthrough();

export const Subscription_read = z
	.object({
		smsCredit: z.number().int(),
		fiducialSocieteId: z.string().max(8).nullable(),
		expirationDate: z.string().datetime({ offset: true }),
		active: z.boolean(),
		cancelled: z.boolean()
	})
	.partial()
	.passthrough();

export const Company_read = z
	.object({
		id: z.number().int().optional(),
		email: z.string().max(255).email(),
		firstname: z
			.string()
			.max(255)
			.regex(/^(((?!\d).)*)$/),
		lastname: z
			.string()
			.max(255)
			.regex(/^(((?!\d).)*)$/),
		phoneNumber: z
			.string()
			.min(10)
			.max(10)
			.regex(/^(0[67]\d{8})$/),
		name: z.string().max(255),
		senderId: z
			.string()
			.max(11)
			.regex(/^(((?!^[0-9]*$).)*)$/),
		senderIdVerified: z.boolean().nullish(),
		activityArea: z.enum([
			'Bijouterie',
			'Camping',
			'Chaussures',
			'Coiffure',
			'Commerce alimentaire',
			'Commerce de détail',
			'Fleuriste',
			'Garagiste',
			'Hôtel',
			'Institut',
			'Jardinerie',
			'Lingerie',
			'Parfumerie',
			'Prêt à porter',
			'Restauration',
			'Autre'
		]),
		customActivityArea: z.string().max(75).nullish(),
		postalCode: z
			.string()
			.min(5)
			.max(5)
			.regex(/^(\d{5})$/),
		city: z.string().max(255),
		companyType: z.enum(['default', 'fiducial']).nullish(),
		reminder: z.string().max(180).nullish(),
		stats: z.array(z.string()).nullish(),
		securedAreas: SecuredArea_read.optional(),
		registrationCompleted: z.boolean().optional(),
		googleReviewUrl: z
			.string()
			.max(255)
			.regex(
				/^(.*(^(https:\/\/g\.page\/r\/[\w-]+\/review|https:\/\/g\.co\/kgs\/[\w-]+|https:\/\/search\.google\.com\/local\/writereview\?placeid=[\w-]+)$).*)$/
			)
			.url()
			.nullish(),
		subscription: z.union([Subscription_read, z.null()]).optional(),
		campaignFidelite: CampaignFidelite_read.optional(),
		campaignAnniversaire: CampaignAnniversaire_read.optional(),
		campaignBienvenue: CampaignBienvenue_read.optional(),
		campaignExpirationCheque: CampaignExpirationCheque_read.optional(),
		campaignDormant: CampaignDormant_read.optional(),
		campaignGoogleReview: CampaignGoogleReview_read.optional(),
		archived: z.boolean().optional()
	})
	.passthrough();

export const Company_CompanyCheckPinInputDto_write = z
	.object({ pin: z.string() })
	.partial()
	.passthrough();

export const Company_CompanyCheckPinOutputDto_read = z
	.object({ token: z.string() })
	.partial()
	.passthrough();

export const Company_CompanyGetSubscriptionOutput_read = z
	.object({
		status: z.enum([
			'active',
			'canceled',
			'incomplete',
			'incomplete_expired',
			'past_due',
			'paused',
			'trialing',
			'unpaid'
		])
	})
	.partial()
	.passthrough();

export const Company_CompanyStatsDto_read = z
	.object({
		companyStats: z.array(
			z
				.object({
					id: z.enum([
						'new_clients',
						'total_ca',
						'nb_transactions',
						'avg_ca',
						'nb_voucher',
						'total_voucher_amount',
						'sms_auto_sent',
						'sms_campaign_sent'
					]),
					value: z.number()
				})
				.partial()
				.passthrough()
		)
	})
	.partial()
	.passthrough();

export const Company_CompanyStripePortalDto_read = z
	.object({ url: z.string() })
	.partial()
	.passthrough();

export const Company_EmptyInputDto_write = z.object({}).partial().passthrough();

export const Company_EmptyOutputDto_read = z.object({}).partial().passthrough();

export const Company_PaymentSessionStatusDto_read = z
	.object({ status: z.enum(['paid', 'processing', 'error']) })
	.partial()
	.passthrough();

export const Company_PaymentStartDto_read = z
	.object({ link: z.string().url() })
	.partial()
	.passthrough();

export const Company_SubscribeWithFiducialInputDto_write = z
	.object({ societeId: z.string().max(8) })
	.partial()
	.passthrough();

export const Company_SubscribeWithFiducialOutputDto_read = z
	.object({ isSubscribed: z.boolean() })
	.partial()
	.passthrough();

export const CompanyDraft_read = z.object({ id: z.number().int() }).partial().passthrough();

export const CompanyDraft_write = z
	.object({
		pin: z
			.string()
			.min(4)
			.max(4)
			.regex(/^(.*\d{4}.*)$/),
		email: z.string().max(255).email(),
		firstname: z
			.string()
			.max(255)
			.regex(/^(((?!\d).)*)$/),
		lastname: z
			.string()
			.max(255)
			.regex(/^(((?!\d).)*)$/),
		phoneNumber: z
			.string()
			.min(10)
			.max(10)
			.regex(/^(0[67]\d{8})$/),
		name: z.string().max(255),
		senderId: z
			.string()
			.max(11)
			.regex(/^(((?!^[0-9]*$).)*)$/),
		activityArea: z.enum([
			'Bijouterie',
			'Camping',
			'Chaussures',
			'Coiffure',
			'Commerce alimentaire',
			'Commerce de détail',
			'Fleuriste',
			'Garagiste',
			'Hôtel',
			'Institut',
			'Jardinerie',
			'Lingerie',
			'Parfumerie',
			'Prêt à porter',
			'Restauration',
			'Autre'
		]),
		postalCode: z
			.string()
			.min(5)
			.max(5)
			.regex(/^(\d{5})$/),
		city: z.string().max(255),
		cguAccepted: z.boolean(),
		plainPassword: z
			.string()
			.min(12)
			.max(255)
			.regex(/^((?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{12,255}.*)$/)
			.nullable()
	})
	.passthrough();

export const CompanyDraft_CompanyDraftCheckEmailAvailabilityInputDto_write = z
	.object({ email: z.string().email() })
	.partial()
	.passthrough();

export const CompanyDraft_CompanyDraftCheckEmailAvailabilityOutputDto_read = z
	.object({ valid: z.boolean() })
	.partial()
	.passthrough();

export const CompanyDraft_CompanyDraftValidateDto_read = z
	.object({ token: z.string() })
	.partial()
	.passthrough();

export const Customer_customer_read = z
	.object({
		id: z.number().int().optional(),
		firstname: z
			.string()
			.max(255)
			.regex(/^(((?!\d).)*)$/),
		lastname: z
			.string()
			.max(255)
			.regex(/^(((?!\d).)*)$/),
		civility: z.enum(['Monsieur', 'Madame', 'Non genré']),
		phoneNumber: z.string().max(35).nullable(),
		phoneNumberCountryCode: z
			.enum([
				'de',
				'at',
				'be',
				'bg',
				'cy',
				'hr',
				'dk',
				'es',
				'ee',
				'fi',
				'fr',
				'gb',
				'gr',
				'hu',
				'ie',
				'it',
				'lv',
				'lt',
				'lu',
				'mt',
				'nl',
				'pl',
				'pt',
				'cz',
				'ro',
				'sk',
				'si',
				'se',
				'ch',
				'gf',
				'pf',
				'mf',
				'pm',
				'gp',
				'mq',
				're',
				'yt',
				'bl',
				'wf',
				'nc'
			])
			.nullable()
			.default('fr'),
		phoneNumberVerified: z.boolean().nullish(),
		stopSmsReceived: z.boolean().optional(),
		email: z.string().max(255).email().nullish(),
		birthDate: z.string().nullish(),
		postalCode: z.string().min(5).max(5).nullish(),
		city: z.string().max(255).nullish(),
		note: z.string().max(255).nullish(),
		comment: z.string().max(500).nullish(),
		commentDate: z.string().datetime({ offset: true }).nullish(),
		loyaltyPoints: z.number().int().optional(),
		vouchers: z.array(Voucher_jsonld_customer_read).optional(),
		createdAt: z.string().datetime({ offset: true }).optional()
	})
	.passthrough();

export const Sms_history_read = z
	.object({
		campaign: Campaign_history_read,
		sentDate: z.string().datetime({ offset: true }),
		status: z.string().nullable(),
		sent: z.boolean().nullable()
	})
	.partial()
	.passthrough();

export const CustomerTransaction_history_read = z
	.object({ id: z.number().int() })
	.partial()
	.passthrough();

export const CustomerHistory_history_read = z
	.object({
		type: z.enum([
			'create',
			'edit',
			'add_points',
			'edit_points',
			'sms',
			'stop_sms_form',
			'add_voucher',
			'use_voucher'
		]),
		vars: z.array(z.string()),
		relatedVoucher: z.string().nullable(),
		relatedSms: z.union([Sms_history_read, z.null()]),
		relatedTransaction: z.union([CustomerTransaction_history_read, z.null()]),
		createdAt: z.string().datetime({ offset: true }),
		removed: z.boolean()
	})
	.partial()
	.passthrough();

export const CustomerTransaction_read = z
	.object({
		id: z.number().int().optional(),
		amount: z.number().int().gte(1).lte(1000000),
		vouchers: z.array(z.string()).optional()
	})
	.passthrough();

export const CustomerTransaction_transaction_write = z
	.object({
		customer: z.string(),
		amount: z.number().int().gte(1).lte(1000000),
		vouchers: z.array(z.string()).optional()
	})
	.passthrough();

export const Faq_faq_read = z
	.object({ title: z.string(), content: z.string(), position: z.number().int() })
	.partial()
	.passthrough();

export const Tutorial_tutorial_read = z
	.object({
		title: z.string(),
		youtubeUrl: z.string().regex(/^(https:\/\/www\.youtube\.com\/embed\/.*.*)$/),
		position: z.number().int()
	})
	.partial()
	.passthrough();

export const Voucher_customer_read = z
	.object({
		id: z.number().int(),
		type: z.enum(['fidelite', 'anniversaire', 'bienvenue', 'dormant']),
		amount: z.number().int(),
		burnt: z.boolean(),
		burntDate: z.string().datetime({ offset: true }).nullable(),
		expirationDate: z.string().datetime({ offset: true }).nullable(),
		availabilityDate: z.string().datetime({ offset: true }).nullable(),
		available: z.boolean()
	})
	.partial()
	.passthrough();

export const ForgotPassword_validate = z.union([z.object({}).partial().passthrough(), z.null()]);

export const ValidationError = z
	.object({
		violations: z.array(
			z.object({ propertyPath: z.string(), message: z.string() }).partial().passthrough()
		)
	})
	.passthrough();

/*export const schemas = {
	CampaignAnniversaire_campaign_write_campaign_write_enabled_campaign_write_voucherEnabled,
	CampaignAnniversaire_jsonld_campaign_read,
	ValidationError_jsonld,
	CampaignBienvenue_campaign_write_campaign_write_enabled_campaign_write_voucherEnabled,
	CampaignBienvenue_jsonld_campaign_read,
	CampaignDormant_campaign_write_campaign_write_enabled_campaign_write_voucherEnabled,
	CampaignDormant_jsonld_campaign_read,
	CampaignExpirationCheque_campaign_write_campaign_write_enabled,
	CampaignExpirationCheque_jsonld_campaign_read,
	CampaignFidelite_campaign_write_campaign_write_voucherEnabled,
	CampaignFidelite_jsonld_campaign_read,
	CampaignGoogleReview_campaign_write_campaign_write_enabled_campaign_write_enabled,
	CampaignGoogleReview_jsonld_campaign_read,
	CampaignSmsFilter_jsonld_campaign_read,
	CampaignSms_jsonld_campaign_read,
	CampaignSmsFilter_jsonld_campaign_write_campaign_write_sms,
	CampaignSms_jsonld_campaign_write_campaign_write_sms,
	CampaignSms_CampaignSmsCountCustomersInputDto_jsonld_campaign_write_campaign_write_sms,
	CampaignSms_CampaignSmsCountCustomersOutputDto_jsonld_campaign_read,
	Company_EmptyInputDto_jsonld_write,
	Company_EmptyOutputDto_jsonld_read,
	Company_PaymentSessionStatusDto_jsonld_read,
	Company_CompanyCheckPinInputDto_jsonld_write,
	Company_CompanyCheckPinOutputDto_jsonld_read,
	SecuredArea_company_write_config,
	Company_company_write_config,
	SecuredArea_jsonld_read,
	Subscription_jsonld_read,
	CampaignFidelite_jsonld_read,
	CampaignAnniversaire_jsonld_read,
	CampaignBienvenue_jsonld_read,
	CampaignExpirationCheque_jsonld_read,
	CampaignDormant_jsonld_read,
	CampaignGoogleReview_jsonld_read,
	Company_jsonld_read,
	Company_CompanyStripePortalDto_jsonld_read,
	Company_company_write_reminder,
	Company_PaymentStartDto_jsonld_read,
	Company_CompanyStatsDto_jsonld_read,
	Company_company_write_stats,
	Company_SubscribeWithFiducialInputDto_jsonld_write,
	Company_SubscribeWithFiducialOutputDto_jsonld_read,
	Company_CompanyGetSubscriptionOutput_jsonld_read,
	CompanyDraft_jsonld_write,
	CompanyDraft_jsonld_read,
	CompanyDraft_CompanyDraftCheckEmailAvailabilityInputDto_jsonld_write,
	CompanyDraft_CompanyDraftCheckEmailAvailabilityOutputDto_jsonld_read,
	CompanyDraft_CompanyDraftValidateDto_jsonld_read,
	Customer_jsonld_customer_write,
	Voucher_jsonld_customer_read,
	Customer_jsonld_customer_read,
	Customer_customer_write,
	Customer_customer_write_loyalty_points,
	Campaign_jsonld_history_read,
	Sms_jsonld_history_read,
	CustomerTransaction_jsonld_history_read,
	CustomerHistory_jsonld_history_read,
	CustomerTransaction_jsonld_transaction_write,
	CustomerTransaction_jsonld_read,
	Faq_jsonld_faq_read,
	ForgotPassword_request,
	ForgotPassword_reset,
	getOpenAiRecommandationsLanding_Body,
	getOpenAiRecommandationsSms_Body,
	auth_Body,
	Tutorial_jsonld_tutorial_read,
	Campaign_history_read,
	CampaignAnniversaire_campaign_read,
	CampaignAnniversaire_read,
	CampaignBienvenue_campaign_read,
	CampaignBienvenue_read,
	CampaignDormant_campaign_read,
	CampaignDormant_read,
	CampaignExpirationCheque_campaign_read,
	CampaignExpirationCheque_read,
	CampaignFidelite_campaign_read,
	CampaignFidelite_read,
	CampaignGoogleReview_campaign_read,
	CampaignGoogleReview_read,
	CampaignSmsFilter_campaign_read,
	CampaignSms_campaign_read,
	CampaignSmsFilter_campaign_write_campaign_write_sms,
	CampaignSms_campaign_write_campaign_write_sms,
	CampaignSms_CampaignSmsCountCustomersInputDto_campaign_write_campaign_write_sms,
	CampaignSms_CampaignSmsCountCustomersOutputDto_campaign_read,
	SecuredArea_read,
	Subscription_read,
	Company_read,
	Company_CompanyCheckPinInputDto_write,
	Company_CompanyCheckPinOutputDto_read,
	Company_CompanyGetSubscriptionOutput_read,
	Company_CompanyStatsDto_read,
	Company_CompanyStripePortalDto_read,
	Company_EmptyInputDto_write,
	Company_EmptyOutputDto_read,
	Company_PaymentSessionStatusDto_read,
	Company_PaymentStartDto_read,
	Company_SubscribeWithFiducialInputDto_write,
	Company_SubscribeWithFiducialOutputDto_read,
	CompanyDraft_read,
	CompanyDraft_write,
	CompanyDraft_CompanyDraftCheckEmailAvailabilityInputDto_write,
	CompanyDraft_CompanyDraftCheckEmailAvailabilityOutputDto_read,
	CompanyDraft_CompanyDraftValidateDto_read,
	Customer_customer_read,
	Sms_history_read,
	CustomerTransaction_history_read,
	CustomerHistory_history_read,
	CustomerTransaction_read,
	CustomerTransaction_transaction_write,
	Faq_faq_read,
	Tutorial_tutorial_read,
	Voucher_customer_read,
	ForgotPassword_validate,
	ValidationError,
};*/

export type CampaignAnniversaire_campaign_write_campaign_write_enabled_campaign_write_voucherEnabled =
	z.infer<
		typeof CampaignAnniversaire_campaign_write_campaign_write_enabled_campaign_write_voucherEnabled
	>;
export type CampaignAnniversaire_jsonld_campaign_read = z.infer<
	typeof CampaignAnniversaire_jsonld_campaign_read
>;
export type ValidationError_jsonld = z.infer<typeof ValidationError_jsonld>;
export type CampaignBienvenue_campaign_write_campaign_write_enabled_campaign_write_voucherEnabled =
	z.infer<
		typeof CampaignBienvenue_campaign_write_campaign_write_enabled_campaign_write_voucherEnabled
	>;
export type CampaignBienvenue_jsonld_campaign_read = z.infer<
	typeof CampaignBienvenue_jsonld_campaign_read
>;
export type CampaignDormant_campaign_write_campaign_write_enabled_campaign_write_voucherEnabled =
	z.infer<
		typeof CampaignDormant_campaign_write_campaign_write_enabled_campaign_write_voucherEnabled
	>;
export type CampaignDormant_jsonld_campaign_read = z.infer<
	typeof CampaignDormant_jsonld_campaign_read
>;
export type CampaignExpirationCheque_campaign_write_campaign_write_enabled = z.infer<
	typeof CampaignExpirationCheque_campaign_write_campaign_write_enabled
>;
export type CampaignExpirationCheque_jsonld_campaign_read = z.infer<
	typeof CampaignExpirationCheque_jsonld_campaign_read
>;
export type CampaignFidelite_campaign_write_campaign_write_voucherEnabled = z.infer<
	typeof CampaignFidelite_campaign_write_campaign_write_voucherEnabled
>;
export type CampaignFidelite_jsonld_campaign_read = z.infer<
	typeof CampaignFidelite_jsonld_campaign_read
>;
export type CampaignGoogleReview_campaign_write_campaign_write_enabled_campaign_write_enabled =
	z.infer<typeof CampaignGoogleReview_campaign_write_campaign_write_enabled_campaign_write_enabled>;
export type CampaignGoogleReview_jsonld_campaign_read = z.infer<
	typeof CampaignGoogleReview_jsonld_campaign_read
>;
export type CampaignSmsFilter_jsonld_campaign_read = z.infer<
	typeof CampaignSmsFilter_jsonld_campaign_read
>;
export type CampaignSms_jsonld_campaign_read = z.infer<typeof CampaignSms_jsonld_campaign_read>;
export type CampaignSmsFilter_jsonld_campaign_write_campaign_write_sms = z.infer<
	typeof CampaignSmsFilter_jsonld_campaign_write_campaign_write_sms
>;
export type CampaignSms_jsonld_campaign_write_campaign_write_sms = z.infer<
	typeof CampaignSms_jsonld_campaign_write_campaign_write_sms
>;
export type CampaignSms_CampaignSmsCountCustomersInputDto_jsonld_campaign_write_campaign_write_sms =
	z.infer<
		typeof CampaignSms_CampaignSmsCountCustomersInputDto_jsonld_campaign_write_campaign_write_sms
	>;
export type CampaignSms_CampaignSmsCountCustomersOutputDto_jsonld_campaign_read = z.infer<
	typeof CampaignSms_CampaignSmsCountCustomersOutputDto_jsonld_campaign_read
>;
export type Company_EmptyInputDto_jsonld_write = z.infer<typeof Company_EmptyInputDto_jsonld_write>;
export type Company_EmptyOutputDto_jsonld_read = z.infer<typeof Company_EmptyOutputDto_jsonld_read>;
export type Company_PaymentSessionStatusDto_jsonld_read = z.infer<
	typeof Company_PaymentSessionStatusDto_jsonld_read
>;
export type Company_CompanyCheckPinInputDto_jsonld_write = z.infer<
	typeof Company_CompanyCheckPinInputDto_jsonld_write
>;
export type Company_CompanyCheckPinOutputDto_jsonld_read = z.infer<
	typeof Company_CompanyCheckPinOutputDto_jsonld_read
>;
export type SecuredArea_company_write_config = z.infer<typeof SecuredArea_company_write_config>;
export type Company_company_write_config = z.infer<typeof Company_company_write_config>;
export type SecuredArea_jsonld_read = z.infer<typeof SecuredArea_jsonld_read>;
export type Subscription_jsonld_read = z.infer<typeof Subscription_jsonld_read>;
export type CampaignFidelite_jsonld_read = z.infer<typeof CampaignFidelite_jsonld_read>;
export type CampaignAnniversaire_jsonld_read = z.infer<typeof CampaignAnniversaire_jsonld_read>;
export type CampaignBienvenue_jsonld_read = z.infer<typeof CampaignBienvenue_jsonld_read>;
export type CampaignExpirationCheque_jsonld_read = z.infer<
	typeof CampaignExpirationCheque_jsonld_read
>;
export type CampaignDormant_jsonld_read = z.infer<typeof CampaignDormant_jsonld_read>;
export type CampaignGoogleReview_jsonld_read = z.infer<typeof CampaignGoogleReview_jsonld_read>;
export type Company_jsonld_read = z.infer<typeof Company_jsonld_read>;
export type Company_CompanyStripePortalDto_jsonld_read = z.infer<
	typeof Company_CompanyStripePortalDto_jsonld_read
>;
export type Company_company_write_reminder = z.infer<typeof Company_company_write_reminder>;
export type Company_PaymentStartDto_jsonld_read = z.infer<
	typeof Company_PaymentStartDto_jsonld_read
>;
export type Company_CompanyStatsDto_jsonld_read = z.infer<
	typeof Company_CompanyStatsDto_jsonld_read
>;
export type Company_company_write_stats = z.infer<typeof Company_company_write_stats>;
export type Company_SubscribeWithFiducialInputDto_jsonld_write = z.infer<
	typeof Company_SubscribeWithFiducialInputDto_jsonld_write
>;
export type Company_SubscribeWithFiducialOutputDto_jsonld_read = z.infer<
	typeof Company_SubscribeWithFiducialOutputDto_jsonld_read
>;
export type Company_CompanyGetSubscriptionOutput_jsonld_read = z.infer<
	typeof Company_CompanyGetSubscriptionOutput_jsonld_read
>;
export type CompanyDraft_jsonld_write = z.infer<typeof CompanyDraft_jsonld_write>;
export type CompanyDraft_jsonld_read = z.infer<typeof CompanyDraft_jsonld_read>;
export type CompanyDraft_CompanyDraftCheckEmailAvailabilityInputDto_jsonld_write = z.infer<
	typeof CompanyDraft_CompanyDraftCheckEmailAvailabilityInputDto_jsonld_write
>;
export type CompanyDraft_CompanyDraftCheckEmailAvailabilityOutputDto_jsonld_read = z.infer<
	typeof CompanyDraft_CompanyDraftCheckEmailAvailabilityOutputDto_jsonld_read
>;
export type CompanyDraft_CompanyDraftValidateDto_jsonld_read = z.infer<
	typeof CompanyDraft_CompanyDraftValidateDto_jsonld_read
>;
export type Customer_jsonld_customer_write = z.infer<typeof Customer_jsonld_customer_write>;
export type Voucher_jsonld_customer_read = z.infer<typeof Voucher_jsonld_customer_read>;
export type Customer_jsonld_customer_read = z.infer<typeof Customer_jsonld_customer_read>;
export type Customer_customer_write = z.infer<typeof Customer_customer_write>;
export type Customer_customer_write_loyalty_points = z.infer<
	typeof Customer_customer_write_loyalty_points
>;
export type Campaign_jsonld_history_read = z.infer<typeof Campaign_jsonld_history_read>;
export type Sms_jsonld_history_read = z.infer<typeof Sms_jsonld_history_read>;
export type CustomerTransaction_jsonld_history_read = z.infer<
	typeof CustomerTransaction_jsonld_history_read
>;
export type CustomerHistory_jsonld_history_read = z.infer<
	typeof CustomerHistory_jsonld_history_read
>;
export type CustomerTransaction_jsonld_transaction_write = z.infer<
	typeof CustomerTransaction_jsonld_transaction_write
>;
export type CustomerTransaction_jsonld_read = z.infer<typeof CustomerTransaction_jsonld_read>;
export type Faq_jsonld_faq_read = z.infer<typeof Faq_jsonld_faq_read>;
export type ForgotPassword_request = z.infer<typeof ForgotPassword_request>;
export type ForgotPassword_reset = z.infer<typeof ForgotPassword_reset>;
export type getOpenAiRecommandationsLanding_Body = z.infer<
	typeof getOpenAiRecommandationsLanding_Body
>;
export type getOpenAiRecommandationsSms_Body = z.infer<typeof getOpenAiRecommandationsSms_Body>;
export type auth_Body = z.infer<typeof auth_Body>;
export type Tutorial_jsonld_tutorial_read = z.infer<typeof Tutorial_jsonld_tutorial_read>;
export type Campaign_history_read = z.infer<typeof Campaign_history_read>;
export type CampaignAnniversaire_campaign_read = z.infer<typeof CampaignAnniversaire_campaign_read>;
export type CampaignAnniversaire_read = z.infer<typeof CampaignAnniversaire_read>;
export type CampaignBienvenue_campaign_read = z.infer<typeof CampaignBienvenue_campaign_read>;
export type CampaignBienvenue_read = z.infer<typeof CampaignBienvenue_read>;
export type CampaignDormant_campaign_read = z.infer<typeof CampaignDormant_campaign_read>;
export type CampaignDormant_read = z.infer<typeof CampaignDormant_read>;
export type CampaignExpirationCheque_campaign_read = z.infer<
	typeof CampaignExpirationCheque_campaign_read
>;
export type CampaignExpirationCheque_read = z.infer<typeof CampaignExpirationCheque_read>;
export type CampaignFidelite_campaign_read = z.infer<typeof CampaignFidelite_campaign_read>;
export type CampaignFidelite_read = z.infer<typeof CampaignFidelite_read>;
export type CampaignGoogleReview_campaign_read = z.infer<typeof CampaignGoogleReview_campaign_read>;
export type CampaignGoogleReview_read = z.infer<typeof CampaignGoogleReview_read>;
export type CampaignSmsFilter_campaign_read = z.infer<typeof CampaignSmsFilter_campaign_read>;
export type CampaignSms_campaign_read = z.infer<typeof CampaignSms_campaign_read>;
export type CampaignSmsFilter_campaign_write_campaign_write_sms = z.infer<
	typeof CampaignSmsFilter_campaign_write_campaign_write_sms
>;
export type CampaignSms_campaign_write_campaign_write_sms = z.infer<
	typeof CampaignSms_campaign_write_campaign_write_sms
>;
export type CampaignSms_CampaignSmsCountCustomersInputDto_campaign_write_campaign_write_sms =
	z.infer<typeof CampaignSms_CampaignSmsCountCustomersInputDto_campaign_write_campaign_write_sms>;
export type CampaignSms_CampaignSmsCountCustomersOutputDto_campaign_read = z.infer<
	typeof CampaignSms_CampaignSmsCountCustomersOutputDto_campaign_read
>;
export type SecuredArea_read = z.infer<typeof SecuredArea_read>;
export type Subscription_read = z.infer<typeof Subscription_read>;
export type Company_read = z.infer<typeof Company_read>;
export type Company_CompanyCheckPinInputDto_write = z.infer<
	typeof Company_CompanyCheckPinInputDto_write
>;
export type Company_CompanyCheckPinOutputDto_read = z.infer<
	typeof Company_CompanyCheckPinOutputDto_read
>;
export type Company_CompanyGetSubscriptionOutput_read = z.infer<
	typeof Company_CompanyGetSubscriptionOutput_read
>;
export type Company_CompanyStatsDto_read = z.infer<typeof Company_CompanyStatsDto_read>;
export type Company_CompanyStripePortalDto_read = z.infer<
	typeof Company_CompanyStripePortalDto_read
>;
export type Company_EmptyInputDto_write = z.infer<typeof Company_EmptyInputDto_write>;
export type Company_EmptyOutputDto_read = z.infer<typeof Company_EmptyOutputDto_read>;
export type Company_PaymentSessionStatusDto_read = z.infer<
	typeof Company_PaymentSessionStatusDto_read
>;
export type Company_PaymentStartDto_read = z.infer<typeof Company_PaymentStartDto_read>;
export type Company_SubscribeWithFiducialInputDto_write = z.infer<
	typeof Company_SubscribeWithFiducialInputDto_write
>;
export type Company_SubscribeWithFiducialOutputDto_read = z.infer<
	typeof Company_SubscribeWithFiducialOutputDto_read
>;
export type CompanyDraft_read = z.infer<typeof CompanyDraft_read>;
export type CompanyDraft_write = z.infer<typeof CompanyDraft_write>;
export type CompanyDraft_CompanyDraftCheckEmailAvailabilityInputDto_write = z.infer<
	typeof CompanyDraft_CompanyDraftCheckEmailAvailabilityInputDto_write
>;
export type CompanyDraft_CompanyDraftCheckEmailAvailabilityOutputDto_read = z.infer<
	typeof CompanyDraft_CompanyDraftCheckEmailAvailabilityOutputDto_read
>;
export type CompanyDraft_CompanyDraftValidateDto_read = z.infer<
	typeof CompanyDraft_CompanyDraftValidateDto_read
>;
export type Customer_customer_read = z.infer<typeof Customer_customer_read>;
export type Sms_history_read = z.infer<typeof Sms_history_read>;
export type CustomerTransaction_history_read = z.infer<typeof CustomerTransaction_history_read>;
export type CustomerHistory_history_read = z.infer<typeof CustomerHistory_history_read>;
export type CustomerTransaction_read = z.infer<typeof CustomerTransaction_read>;
export type CustomerTransaction_transaction_write = z.infer<
	typeof CustomerTransaction_transaction_write
>;
export type Faq_faq_read = z.infer<typeof Faq_faq_read>;
export type Tutorial_tutorial_read = z.infer<typeof Tutorial_tutorial_read>;
export type Voucher_customer_read = z.infer<typeof Voucher_customer_read>;
export type ForgotPassword_validate = z.infer<typeof ForgotPassword_validate>;
export type ValidationError = z.infer<typeof ValidationError>;

export const endpoints = makeApi([
	{
		method: 'get',
		path: '/api/check-payment-session',
		alias: 'checkPaymentSession',
		description: `Permet de vérifier l&#x27;état d&#x27;une session de paiement pour savoir si elle le paiement est en cours ou terminé`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'sessionId',
				type: 'Query',
				schema: z.string()
			}
		],
		response: z
			.object({ status: z.enum(['paid', 'processing', 'error']) })
			.partial()
			.passthrough()
	},
	{
		method: 'patch',
		path: '/api/companies/:companyId/campaign_anniversaires/:id',
		alias: 'campaignAnniversairePatch',
		description: `Updates the CampaignAnniversaire resource.`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The updated CampaignAnniversaire resource`,
				type: 'Body',
				schema:
					CampaignAnniversaire_campaign_write_campaign_write_enabled_campaign_write_voucherEnabled
			},
			{
				name: 'companyId',
				type: 'Path',
				schema: z.string()
			},
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: CampaignAnniversaire_jsonld_campaign_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'patch',
		path: '/api/companies/:companyId/campaign_bienvenues/:id',
		alias: 'campaignBienvenuePatch',
		description: `Updates the CampaignBienvenue resource.`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The updated CampaignBienvenue resource`,
				type: 'Body',
				schema:
					CampaignBienvenue_campaign_write_campaign_write_enabled_campaign_write_voucherEnabled
			},
			{
				name: 'companyId',
				type: 'Path',
				schema: z.string()
			},
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: CampaignBienvenue_jsonld_campaign_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'patch',
		path: '/api/companies/:companyId/campaign_dormants/:id',
		alias: 'campaignDormantPatch',
		description: `Updates the CampaignDormant resource.`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The updated CampaignDormant resource`,
				type: 'Body',
				schema: CampaignDormant_campaign_write_campaign_write_enabled_campaign_write_voucherEnabled
			},
			{
				name: 'companyId',
				type: 'Path',
				schema: z.string()
			},
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: CampaignDormant_jsonld_campaign_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'patch',
		path: '/api/companies/:companyId/campaign_expiration_cheques/:id',
		alias: 'campaignExpirationChequePatch',
		description: `Updates the CampaignExpirationCheque resource.`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The updated CampaignExpirationCheque resource`,
				type: 'Body',
				schema: CampaignExpirationCheque_campaign_write_campaign_write_enabled
			},
			{
				name: 'companyId',
				type: 'Path',
				schema: z.string()
			},
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: CampaignExpirationCheque_jsonld_campaign_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'patch',
		path: '/api/companies/:companyId/campaign_fidelites/:id',
		alias: 'campaignFidelitePatch',
		description: `Updates the CampaignFidelite resource.`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The updated CampaignFidelite resource`,
				type: 'Body',
				schema: CampaignFidelite_campaign_write_campaign_write_voucherEnabled
			},
			{
				name: 'companyId',
				type: 'Path',
				schema: z.string()
			},
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: CampaignFidelite_jsonld_campaign_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'patch',
		path: '/api/companies/:companyId/campaign_google_reviews/:id',
		alias: 'campaignGoogleReviewPatch',
		description: `Updates the CampaignGoogleReview resource.`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The updated CampaignGoogleReview resource`,
				type: 'Body',
				schema: CampaignGoogleReview_campaign_write_campaign_write_enabled_campaign_write_enabled
			},
			{
				name: 'companyId',
				type: 'Path',
				schema: z.string()
			},
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: CampaignGoogleReview_jsonld_campaign_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'get',
		path: '/api/companies/:companyId/campaign_sms',
		alias: 'campaignSmsGetCollection',
		description: `Retrieves the collection of CampaignSms resources.`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'companyId',
				type: 'Path',
				schema: z.string()
			},
			{
				name: 'page',
				type: 'Query',
				schema: z.number().int().optional().default(1)
			}
		],
		response: z
			.object({
				'hydra:member': z.array(CampaignSms_jsonld_campaign_read),
				'hydra:totalItems': z.number().int().gte(0).optional(),
				'hydra:view': z
					.object({
						'@id': z.string(),
						'@type': z.string(),
						'hydra:first': z.string(),
						'hydra:last': z.string(),
						'hydra:previous': z.string(),
						'hydra:next': z.string()
					})
					.partial()
					.passthrough()
					.optional(),
				'hydra:search': z
					.object({
						'@type': z.string(),
						'hydra:template': z.string(),
						'hydra:variableRepresentation': z.string(),
						'hydra:mapping': z.array(
							z
								.object({
									'@type': z.string(),
									variable: z.string(),
									property: z.union([z.string(), z.null()]),
									required: z.boolean()
								})
								.partial()
								.passthrough()
						)
					})
					.partial()
					.passthrough()
					.optional()
			})
			.passthrough(),
		errors: [
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			}
		]
	},
	{
		method: 'post',
		path: '/api/companies/:companyId/campaign_sms',
		alias: 'campaignSmsPost',
		description: `Creates a CampaignSms resource.`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The new CampaignSms resource`,
				type: 'Body',
				schema: CampaignSms_jsonld_campaign_write_campaign_write_sms
			},
			{
				name: 'companyId',
				type: 'Path',
				schema: z.string()
			}
		],
		response: CampaignSms_jsonld_campaign_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'delete',
		path: '/api/companies/:companyId/campaign_sms/:id',
		alias: 'campaignSmsDelete',
		description: `Annulation d&#x27;une campagne SMS et de tout ce qu&#x27;elle a engendrée en Base de données :

* Annulation de la campagne SMS Link Mobility

* Recréditation du crédit sms utilisés
`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'companyId',
				type: 'Path',
				schema: z.string()
			},
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: z.void(),
		errors: [
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			}
		]
	},
	{
		method: 'post',
		path: '/api/companies/:companyId/campaign_sms/count-customers',
		alias: 'campaignSmsCountCustomers',
		description: `Permet de récupérer le nombre prévisionnel de clients qui seront contactés par une campagne SMS ainsi que le nombre de crédits SMS qui seront consommés si la campagne est envoyée`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The new CampaignSms resource`,
				type: 'Body',
				schema:
					CampaignSms_CampaignSmsCountCustomersInputDto_jsonld_campaign_write_campaign_write_sms
			},
			{
				name: 'companyId',
				type: 'Path',
				schema: z.string()
			}
		],
		response: CampaignSms_CampaignSmsCountCustomersOutputDto_jsonld_campaign_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'post',
		path: '/api/companies/:companyId/create-customer-account',
		alias: 'customerPostAnonymous',
		description: `Permet de créer un nouveau client sans JWT`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The new Customer resource`,
				type: 'Body',
				schema: Customer_jsonld_customer_write
			},
			{
				name: 'companyId',
				type: 'Path',
				schema: z.string()
			}
		],
		response: Customer_jsonld_customer_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'get',
		path: '/api/companies/:companyId/customers',
		alias: 'customerGetCollection',
		description: `Permet de lister ou de rechercher les clients de l&#x27;enseigne`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'companyId',
				type: 'Path',
				schema: z.string()
			},
			{
				name: 'page',
				type: 'Query',
				schema: z.number().int().optional().default(1)
			},
			{
				name: 'search',
				type: 'Query',
				schema: z.string().optional()
			}
		],
		response: z
			.object({
				'hydra:member': z.array(Customer_jsonld_customer_read),
				'hydra:totalItems': z.number().int().gte(0).optional(),
				'hydra:view': z
					.object({
						'@id': z.string(),
						'@type': z.string(),
						'hydra:first': z.string(),
						'hydra:last': z.string(),
						'hydra:previous': z.string(),
						'hydra:next': z.string()
					})
					.partial()
					.passthrough()
					.optional(),
				'hydra:search': z
					.object({
						'@type': z.string(),
						'hydra:template': z.string(),
						'hydra:variableRepresentation': z.string(),
						'hydra:mapping': z.array(
							z
								.object({
									'@type': z.string(),
									variable: z.string(),
									property: z.union([z.string(), z.null()]),
									required: z.boolean()
								})
								.partial()
								.passthrough()
						)
					})
					.partial()
					.passthrough()
					.optional()
			})
			.passthrough(),
		errors: [
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			}
		]
	},
	{
		method: 'post',
		path: '/api/companies/:companyId/customers',
		alias: 'customerPost',
		description: `Permet de créer un nouveau client`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The new Customer resource`,
				type: 'Body',
				schema: Customer_jsonld_customer_write
			},
			{
				name: 'companyId',
				type: 'Path',
				schema: z.string()
			}
		],
		response: Customer_jsonld_customer_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'get',
		path: '/api/companies/:companyId/customers/:id',
		alias: 'customerGet',
		description: `Permet de récupérer les informations d&#x27;un client ainsi que ses chèques`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'companyId',
				type: 'Path',
				schema: z.string()
			},
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: Customer_jsonld_customer_read,
		errors: [
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			}
		]
	},
	{
		method: 'delete',
		path: '/api/companies/:companyId/customers/:id',
		alias: 'customerDelete',
		description: `Permet de supprimer un client`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'companyId',
				type: 'Path',
				schema: z.string()
			},
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: z.void(),
		errors: [
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			}
		]
	},
	{
		method: 'patch',
		path: '/api/companies/:companyId/customers/:id',
		alias: 'customerPatch',
		description: `Permet de mettre à jour les informations d&#x27;un client`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The updated Customer resource`,
				type: 'Body',
				schema: Customer_customer_write
			},
			{
				name: 'companyId',
				type: 'Path',
				schema: z.string()
			},
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: Customer_jsonld_customer_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'patch',
		path: '/api/companies/:companyId/customers/:id/loyalty-points',
		alias: 'customerSetLoyaltyPoints',
		description: `Permet de mettre à jour le nombre de points de fidélité d&#x27;un client`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The updated Customer resource`,
				type: 'Body',
				schema: z.object({ loyaltyPoints: z.number().int() }).partial().passthrough()
			},
			{
				name: 'companyId',
				type: 'Path',
				schema: z.string()
			},
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: Customer_jsonld_customer_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'delete',
		path: '/api/companies/:id',
		alias: 'companyDelete',
		description: `Permet d&#x27;archiver l&#x27;enseigne, son abonnement sera automatiquement annulé`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: z.void(),
		errors: [
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			}
		]
	},
	{
		method: 'post',
		path: '/api/companies/:id/cancel-subscription',
		alias: 'companyCancelSubscription',
		description: `Permet d&#x27;annuler l&#x27;abonnement de l&#x27;enseigne. L&#x27;abonnement restera actif jusqu&#x27;à la fin de la période en cours`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The new Company resource`,
				type: 'Body',
				schema: z.object({}).partial().passthrough()
			},
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: Company_EmptyOutputDto_jsonld_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'get',
		path: '/api/companies/:id/check-payment-subscription',
		alias: 'companyCheckPaymentSubscription',
		description: `Permet de vérifier l&#x27;état de l&#x27;abonnement de l&#x27;enseigne`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: Company_PaymentSessionStatusDto_jsonld_read,
		errors: [
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			}
		]
	},
	{
		method: 'post',
		path: '/api/companies/:id/check-pin',
		alias: 'companyCheckPin',
		description: `Permet de faire une vérification du code PIN de l&#x27;enseigne afin d&#x27;obtenir un token qu&#x27;il faudra passer en paramètre des appels qui ont besoin d&#x27;avoir cette confirmation`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The new Company resource`,
				type: 'Body',
				schema: z.object({ pin: z.string() }).partial().passthrough()
			},
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: Company_CompanyCheckPinOutputDto_jsonld_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'patch',
		path: '/api/companies/:id/config',
		alias: 'companyUpdateConfig',
		description: `Permet de mettre à jour la configuration de l&#x27;enseigne`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The updated Company resource`,
				type: 'Body',
				schema: Company_company_write_config
			},
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: Company_jsonld_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'post',
		path: '/api/companies/:id/forgot-pin',
		alias: 'companyForgotPin',
		description: `Permet d&#x27;envoyer un SMS au numéro de téléphone configuré au niveau de l&#x27;enseigne pour lui rappeler son code PIN`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The new Company resource`,
				type: 'Body',
				schema: z.object({}).partial().passthrough()
			},
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: Company_EmptyOutputDto_jsonld_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'get',
		path: '/api/companies/:id/generate-qr-code',
		alias: 'companyGenerateQrCode',
		description: `Permet de générer un QR code pointant vers la page de création de client de l&#x27;enseigne`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: z.void(),
		errors: [
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			}
		]
	},
	{
		method: 'get',
		path: '/api/companies/:id/get-stripe-portal-url',
		alias: 'companyGetStripePortalUrl',
		description: `Permet d&#x27;obtenir l&#x27;URL du portail de facturation Stripe de l&#x27;enseigne`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: Company_CompanyStripePortalDto_jsonld_read,
		errors: [
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			}
		]
	},
	{
		method: 'patch',
		path: '/api/companies/:id/reminder',
		alias: 'companySetReminder',
		description: `Permet de mettre à jour le rappel de l&#x27;enseigne`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The updated Company resource`,
				type: 'Body',
				schema: z
					.object({ reminder: z.string().max(180).nullable() })
					.partial()
					.passthrough()
			},
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: Company_jsonld_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'get',
		path: '/api/companies/:id/start-payment-sms',
		alias: 'companyStartPaymentSMS',
		description: `Permet d&#x27;acheter du crédit SMS en étant rediriger vers une page de paiement Stripe`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			},
			{
				name: 'referer',
				type: 'Query',
				schema: z.string().optional()
			}
		],
		response: Company_PaymentStartDto_jsonld_read,
		errors: [
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			}
		]
	},
	{
		method: 'get',
		path: '/api/companies/:id/start-payment-subscription',
		alias: 'companyStartPaymentSubscription',
		description: `Permet de s&#x27;abonner à MaCarteFid en étant rediriger vers une page de paiement Stripe`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: Company_PaymentStartDto_jsonld_read,
		errors: [
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			}
		]
	},
	{
		method: 'get',
		path: '/api/companies/:id/stats',
		alias: 'companyGetStats',
		description: `Permet de récupérer diverses statistiques à propos de l&#x27;enseigne et de ses clients`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: Company_CompanyStatsDto_jsonld_read,
		errors: [
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			}
		]
	},
	{
		method: 'patch',
		path: '/api/companies/:id/stats',
		alias: 'companyPatchStats',
		description: `Permet de mettre à jour l&#x27;ordre d&#x27;affichage des stats de l&#x27;enseigne`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The updated Company resource`,
				type: 'Body',
				schema: Company_company_write_stats
			},
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: Company_jsonld_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'get',
		path: '/api/companies/:id/stats/export',
		alias: 'companyGetStatsExport',
		description: `Permet de récupérer diverses statistiques à propos de l&#x27;enseigne et de ses clients au format Excel, une ligne par mois`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: z.void()
	},
	{
		method: 'post',
		path: '/api/companies/:id/subscribe-with-fiducial',
		alias: 'companySubscribeWithFiducial',
		description: `Permet de s&#x27;abonner à MaCarteFid via un code de société Fiducial`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The new Company resource`,
				type: 'Body',
				schema: z
					.object({ societeId: z.string().max(8) })
					.partial()
					.passthrough()
			},
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: Company_SubscribeWithFiducialOutputDto_jsonld_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'get',
		path: '/api/companies/:id/subscription',
		alias: 'companyGetSubscription',
		description: `Permet de récupérer l&#x27;état de l&#x27;abonnement de l&#x27;enseigne`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: Company_CompanyGetSubscriptionOutput_jsonld_read,
		errors: [
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			}
		]
	},
	{
		method: 'post',
		path: '/api/company_drafts',
		alias: 'companyDraftPost',
		description: `Permet d&#x27;inscrire une nouvelle enseigne à MaCarteFid`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The new CompanyDraft resource`,
				type: 'Body',
				schema: CompanyDraft_jsonld_write
			}
		],
		response: CompanyDraft_jsonld_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'get',
		path: '/api/company_drafts/:token/validate-email',
		alias: 'companyDraftValidateEmail',
		description: `Permet de vérifier que l&#x27;enseigne qui s&#x27;est inscrite possède bien cette adresse e-mail en validant qu&#x27;elle a bien reçu le token passé en paramètre par email`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'token',
				type: 'Path',
				schema: z.string()
			}
		],
		response: CompanyDraft_CompanyDraftValidateDto_jsonld_read,
		errors: [
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			}
		]
	},
	{
		method: 'post',
		path: '/api/company_drafts/check-email-availability',
		alias: 'CompanyDraftCheckEmailAvailability',
		description: `Permet de vérifier que l&#x27;adresse e-mail d&#x27;une nouvelle enseigne qui veut s&#x27;inscrire n&#x27;est pas déjà utilisée`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The new CompanyDraft resource`,
				type: 'Body',
				schema: z.object({ email: z.string().email() }).partial().passthrough()
			}
		],
		response: CompanyDraft_CompanyDraftCheckEmailAvailabilityOutputDto_jsonld_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'get',
		path: '/api/create-customer-account/:id',
		alias: 'getCreateCustomerAccountPage',
		description: `Permet de récupérer les informations nécessaire pour afficher le formulaire de création de compte client autonome`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			},
			{
				name: 'token',
				type: 'Query',
				schema: z.string()
			}
		],
		response: z.object({ company: z.string() }).partial().passthrough()
	},
	{
		method: 'get',
		path: '/api/customers/:customerId/customer_histories',
		alias: 'customerHistoryGetCollection',
		description: `Retrieves the collection of CustomerHistory resources.`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'customerId',
				type: 'Path',
				schema: z.string()
			},
			{
				name: 'page',
				type: 'Query',
				schema: z.number().int().optional().default(1)
			}
		],
		response: z
			.object({
				'hydra:member': z.array(CustomerHistory_jsonld_history_read),
				'hydra:totalItems': z.number().int().gte(0).optional(),
				'hydra:view': z
					.object({
						'@id': z.string(),
						'@type': z.string(),
						'hydra:first': z.string(),
						'hydra:last': z.string(),
						'hydra:previous': z.string(),
						'hydra:next': z.string()
					})
					.partial()
					.passthrough()
					.optional(),
				'hydra:search': z
					.object({
						'@type': z.string(),
						'hydra:template': z.string(),
						'hydra:variableRepresentation': z.string(),
						'hydra:mapping': z.array(
							z
								.object({
									'@type': z.string(),
									variable: z.string(),
									property: z.union([z.string(), z.null()]),
									required: z.boolean()
								})
								.partial()
								.passthrough()
						)
					})
					.partial()
					.passthrough()
					.optional()
			})
			.passthrough(),
		errors: [
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			}
		]
	},
	{
		method: 'post',
		path: '/api/customers/:customerId/customer_transactions',
		alias: 'customerTransactionPost',
		description: `Creates a CustomerTransaction resource.`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `The new CustomerTransaction resource`,
				type: 'Body',
				schema: CustomerTransaction_jsonld_transaction_write
			},
			{
				name: 'customerId',
				type: 'Path',
				schema: z.string()
			}
		],
		response: CustomerTransaction_jsonld_read,
		errors: [
			{
				status: 400,
				description: `Invalid input`,
				schema: z.void()
			},
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 422,
				description: `Validation error`,
				schema: ValidationError_jsonld
			}
		]
	},
	{
		method: 'delete',
		path: '/api/customers/:customerId/customer_transactions/:id',
		alias: 'customerTransactionDelete',
		description: `Une transaction peut être supprimée si c&#x27;est la dernière du client.
Cette action a pour effet :
- suppression des points gagnés via les euros dépensés
- recréditation des points utilisés lors de la génération du chèque
- si des chèques ont été utilisé lors de la transaction ils redeviennent valides
- annulation du SMS linkmobility si possible
- recréditation du crédit SMS utilisé si l&#x27;annulation a été possible`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'customerId',
				type: 'Path',
				schema: z.string()
			},
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			}
		],
		response: z.void(),
		errors: [
			{
				status: 403,
				description: `Forbidden`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Resource not found`,
				schema: z.void()
			}
		]
	},
	{
		method: 'get',
		path: '/api/faqs',
		alias: 'faqGetCollection',
		description: `Retrieves the collection of Faq resources.`,
		requestFormat: 'json',
		response: z
			.object({
				'hydra:member': z.array(Faq_jsonld_faq_read),
				'hydra:totalItems': z.number().int().gte(0).optional(),
				'hydra:view': z
					.object({
						'@id': z.string(),
						'@type': z.string(),
						'hydra:first': z.string(),
						'hydra:last': z.string(),
						'hydra:previous': z.string(),
						'hydra:next': z.string()
					})
					.partial()
					.passthrough()
					.optional(),
				'hydra:search': z
					.object({
						'@type': z.string(),
						'hydra:template': z.string(),
						'hydra:variableRepresentation': z.string(),
						'hydra:mapping': z.array(
							z
								.object({
									'@type': z.string(),
									variable: z.string(),
									property: z.union([z.string(), z.null()]),
									required: z.boolean()
								})
								.partial()
								.passthrough()
						)
					})
					.partial()
					.passthrough()
					.optional()
			})
			.passthrough()
	},
	{
		method: 'get',
		path: '/api/openai/recommendations/landing',
		alias: 'getOpenAiRecommandationsLanding',
		description: `Permet de faire générer un contenu de page HTML à OpenAI pour pouvoir l&#x27;utiliser dans une campagne`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				type: 'Body',
				schema: getOpenAiRecommandationsLanding_Body.optional()
			}
		],
		response: z.void()
	},
	{
		method: 'get',
		path: '/api/openai/recommendations/sms',
		alias: 'getOpenAiRecommandationsSms',
		description: `Permet de faire générer un contenu de SMS à OpenAI pour pouvoir l&#x27;utiliser dans une campagne`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				type: 'Body',
				schema: getOpenAiRecommandationsSms_Body.optional()
			}
		],
		response: z.void()
	},
	{
		method: 'get',
		path: '/api/payment/subscription-price',
		alias: 'getSubscriptionPrice',
		description: `Permet de récupérer le prix de l&#x27;abonnement à MaCarteFid tel qu&#x27;il est défini dans la configuration de Stripe`,
		requestFormat: 'json',
		response: z.object({ price: z.number().int() }).partial().passthrough()
	},
	{
		method: 'get',
		path: '/api/profile',
		alias: 'getProfile',
		description: `Permet de récupérer les informations de l&#x27;utilisateur connecté et de son enseigne, ainsi que la configuration de l&#x27;application`,
		requestFormat: 'json',
		response: z
			.object({
				company: Company_jsonld_read,
				config: z
					.object({
						INFORMATION_BANNER: z
							.object({ message: z.string().nullable(), date: z.string().nullable() })
							.partial()
							.passthrough()
							.nullable()
					})
					.partial()
					.passthrough(),
				smsCreditStatus: z.enum(['stop', 'danger', 'warning', 'ok']),
				customerCountries: z.array(
					z.enum([
						'de',
						'at',
						'be',
						'bg',
						'cy',
						'hr',
						'dk',
						'es',
						'ee',
						'fi',
						'fr',
						'gb',
						'gr',
						'hu',
						'ie',
						'it',
						'lv',
						'lt',
						'lu',
						'mt',
						'nl',
						'pl',
						'pt',
						'cz',
						'ro',
						'sk',
						'si',
						'se',
						'ch',
						'gf',
						'pf',
						'mf',
						'pm',
						'gp',
						'mq',
						're',
						'yt',
						'bl',
						'wf',
						'nc'
					])
				)
			})
			.partial()
			.passthrough()
	},
	{
		method: 'get',
		path: '/api/rich_sms/:id',
		alias: 'getRichSms',
		description: `Permet de récupérérer les informations d&#x27;un SMS Rich pour les afficher sur une page web`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'id',
				type: 'Path',
				schema: z.string()
			},
			{
				name: 'token',
				type: 'Query',
				schema: z.string()
			}
		],
		response: z
			.object({
				content: z.string(),
				company: z.string(),
				sms: z
					.object({
						customer: z
							.object({
								firstname: z.string(),
								lastname: z.string(),
								loyaltyPoints: z.number().int()
							})
							.partial()
							.passthrough(),
						voucher: z
							.object({
								id: z.number().int(),
								type: z.enum(['fidelite', 'anniversaire', 'bienvenue', 'dormant']),
								amount: z.number().int(),
								burnt: z.boolean(),
								expirationDate: z.string().datetime({ offset: true }).nullable(),
								availabilityDate: z.string().datetime({ offset: true })
							})
							.partial()
							.passthrough()
							.nullable()
					})
					.partial()
					.passthrough()
			})
			.partial()
			.passthrough()
	},
	{
		method: 'post',
		path: '/api/token/refresh',
		alias: 'authRefresh',
		description: `Permet de recevoir un nouveau token JWT afin de ne pas avoir à renseigner les identifiants de connexion à chaque fois`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				type: 'Body',
				schema: z.object({ refreshToken: z.string() }).partial().passthrough().optional()
			}
		],
		response: z
			.object({
				token: z.string(),
				refreshToken: z.string(),
				refreshTokenExpiration: z.number().int()
			})
			.passthrough()
	},
	{
		method: 'get',
		path: '/api/tutorials',
		alias: 'tutorialGetCollection',
		description: `Retrieves the collection of Tutorial resources.`,
		requestFormat: 'json',
		response: z
			.object({
				'hydra:member': z.array(Tutorial_jsonld_tutorial_read),
				'hydra:totalItems': z.number().int().gte(0).optional(),
				'hydra:view': z
					.object({
						'@id': z.string(),
						'@type': z.string(),
						'hydra:first': z.string(),
						'hydra:last': z.string(),
						'hydra:previous': z.string(),
						'hydra:next': z.string()
					})
					.partial()
					.passthrough()
					.optional(),
				'hydra:search': z
					.object({
						'@type': z.string(),
						'hydra:template': z.string(),
						'hydra:variableRepresentation': z.string(),
						'hydra:mapping': z.array(
							z
								.object({
									'@type': z.string(),
									variable: z.string(),
									property: z.union([z.string(), z.null()]),
									required: z.boolean()
								})
								.partial()
								.passthrough()
						)
					})
					.partial()
					.passthrough()
					.optional()
			})
			.passthrough()
	},
	{
		method: 'post',
		path: '/auth',
		alias: 'auth',
		description: `Permet de récupérer un token d&#x27;authentification JWT qui servira à s&#x27;authentifier sur les autres routes, ainsi qu&#x27;un refresh token qui permettra de renouveler le token d&#x27;authentification`,
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				type: 'Body',
				schema: auth_Body.optional()
			}
		],
		response: z
			.object({
				token: z.string(),
				refreshToken: z.string(),
				refreshTokenExpiration: z.number().int()
			})
			.passthrough()
	},
	{
		method: 'post',
		path: '/forgot-password/',
		alias: 'postForgotPassword',
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `Request a new password`,
				type: 'Body',
				schema: ForgotPassword_request
			},
			{
				name: 'FP-provider',
				type: 'Header',
				schema: z.string().optional()
			}
		],
		response: z.void(),
		errors: [
			{
				status: 400,
				description: `Missing email parameter or invalid format`,
				schema: z.void()
			}
		]
	},
	{
		method: 'get',
		path: '/forgot-password/:tokenValue',
		alias: 'getForgotPassword',
		requestFormat: 'json',
		parameters: [
			{
				name: 'tokenValue',
				type: 'Path',
				schema: z.string()
			},
			{
				name: 'FP-provider',
				type: 'Header',
				schema: z.string().optional()
			}
		],
		response: z.void(),
		errors: [
			{
				status: 404,
				description: `Token not found or expired`,
				schema: z.void()
			}
		]
	},
	{
		method: 'post',
		path: '/forgot-password/:tokenValue',
		alias: 'postForgotPasswordToken',
		requestFormat: 'json',
		parameters: [
			{
				name: 'body',
				description: `Reset password`,
				type: 'Body',
				schema: z
					.object({
						password: z
							.string()
							.min(12)
							.max(255)
							.regex(/^((?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{12,255}.*)$/)
							.nullable()
					})
					.passthrough()
			},
			{
				name: 'tokenValue',
				type: 'Path',
				schema: z.string()
			},
			{
				name: 'FP-provider',
				type: 'Header',
				schema: z.string().optional()
			}
		],
		response: z.void(),
		errors: [
			{
				status: 400,
				description: `Missing password parameter`,
				schema: z.void()
			},
			{
				status: 404,
				description: `Token not found`,
				schema: z.void()
			}
		]
	}
]);

export const api = new Zodios(endpoints);

export function createApiClient(baseUrl: string, options?: ZodiosOptions) {
	return new Zodios(baseUrl, endpoints, options);
}
