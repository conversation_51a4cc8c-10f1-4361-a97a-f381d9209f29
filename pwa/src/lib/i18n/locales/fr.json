{"banner": {"sender-invalid": {"1": "Votre nom d'expéditeur de SMS a été refusé par nos prestataires, par conséquent vos SMS n'arrivent pas à destination mais sont tout de même décomptés de votre crédit.", "2": "Pour remédier a cette situation veuillez", "3": "changer votre nom d'expéditeur"}}, "campaign": {"anniversaire": {"configuration": "Paramètrage*", "description": "Cette page permet de paramétrer automatiquement un SMS le jour de l'anniversaire du client, incluant éventuellement un chèque anniversaire.", "disabled": "Les SMS automatiques Anniversaire sont inactifs", "enabled": "Les SMS automatiques Anniversaire sont activés", "error-message": "Une erreur s'est produite lors de l'enregistrement", "form": {"sms-placeholder": "Parce que chaque client est unique nous vous offrons un chèque de {voucherAmount}€ pour célébrer votre anniversaire. A bientôt chez {company}."}, "success-message": "Offre Anniversaire mise à jour avec succès", "title": "SMS AUTOMATIQUE : ANNIVERSAIRE", "voucher-rule": {"1": "Le client reçoit le jour de son anniversaire un chèque de", "2": "€,", "3": "il sera alors valable pendant", "4": "mois."}, "voucherEnabled": "Offrir un chèque anniversaire aux clients le jour leur anniversaire"}, "bienvenue": {"configuration": "Paramètrage*", "description": "Cette page permet de paramétrer automatiquement un SMS de bienvenue à chaque client nouvellement enregistré, incluant éventuellement un chèque bienvenue.", "disabled": "Les SMS automatiques Bienvenue sont inactifs", "enabled": "Les SMS automatiques Bienvenue sont activés", "error-message": "Une erreur s'est produite lors de l'enregistrement", "form": {"errors": {"voucherAmount": "La valeur du chèque doit être comprise entre 1 et 999", "voucherValidityMonth": "Le nombre de mois doit être compris entre 1 et 99"}, "sms-placeholder": "Pour vous souhaiter la Bienvenue nous vous offrons un chèque de {voucherAmount}€. A bientôt chez {company}."}, "success-message": "Offre Bienvenue mise à jour avec succès", "title": "SMS AUTOMATIQUE : BIENVENUE", "voucher-rule": {"1": "Offrir un chèque bienvenue aux clients lors de l'ouverture de leur compte.", "2": "€.", "3": "Il sera alors valable pendant", "4": "mois."}, "voucherEnabled": "Offrir un chèque aux clients"}, "dormant": {"configuration": "Paramètrage*", "description": "Cette page permet de paramétrer automatiquement un SMS lorsqu’un client est inactif pendant un certain temps. Ce client est considéré comme dormant.", "disabled": "Les SMS automatiques Dormant sont inactifs", "enabled": "Les SMS automatiques Dormant sont activés", "error-message": "Une erreur s'est produite lors de l'enregistrement", "form": {"errors": {"customerInactiveSinceMonth": "Le nombre de mois doit être compris entre 1 et 99", "voucherAmount": "La valeur du chèque doit être comprise entre 1 et 999", "voucherValidityMonth": "La durée de validité doit être comprise entre 1 et 99"}, "sms-placeholder": "Nous aimerions vous revoir et pour vous le prouver voici un chèque de {voucherAmount}€."}, "success-message": "Réveil Clients Dormants mis à jour avec succès", "title": "SMS AUTOMATIQUE : DORMANT", "voucher-rule": {"1": "Un client est considéré comme dormant s'il n'a pas effectué d'achat depuis", "2": "mois.", "3": "L'offre envoyée aux clients dormants est un chèque cadeau d'une valeur de", "4": "€.", "5": "Ce chèque cadeau sera valable pendant", "6": "mois."}, "voucherEnabled": "Offrir un chèque aux clients"}, "expiration-cheque": {"configuration": "Paramètrage*", "description": "Cette page permet de paramétrer automatiquement un SMS lorsque le chèque d’un client arrive à expiration. Cela vous permet de relancer un client qui aurait oublié sa récompense !", "disabled": "Les SMS automatiques Expiration Chèque sont inactifs", "enabled": "Les SMS automatiques Expiration Chèque sont activés", "error-message": "Une erreur s'est produite lors de l'enregistrement", "form": {"errors": "Le nombre de jours doit être compris entre 1 et 9", "sms-placeholder": "Vite ! votre chèque fidélité expire bientôt. Venez nous rendre visite chez {company}."}, "success-error": "Rappel fin de validité chèque mis à jour avec succès", "title": "SMS AUTOMATIQUE : EXPIRATION CHÈQUE", "voucher-rule": {"1": "Lorsq<PERSON>'un chèque arrive à", "2": "jour", "3": "de son expiration, un message est envoyé."}}, "google-review": {"configuration": "Paramètrage", "description": "Cette page permet de paramétrer automatiquement un SMS d’avis Google qui va\nsolliciter vos clients à laisser un avis positif sur votre enseigne.", "disabled": "Les SMS automatiques Avis Google sont inactifs", "enabled": "Les SMS automatiques Avis Google sont activés", "error-message": "Une erreur s'est produite lors de l'enregistrement", "form": {"sms-placeholder": "Vite ! votre chèque fidélité expire bientôt. Venez nous rendre visite chez {company}."}, "success-error": "Google mis à jour avec succès", "enabled-error": "Me<PERSON>i de renseigner votre URL avis Google pour activer cette campagne", "title": "SMS AUTOMATIQUE : AVIS GOOGLE", "rules": {"1": "1 - Pour faire apparaître le lien avis Google dans le message client, suivez cette", "1-link": "vidéo", "2": "2 - Une fois le lien copié, dirigez vous sur la page", "2-link": "<PERSON><PERSON><PERSON><PERSON>", "3": "3 - <PERSON><PERSON><PERSON> coller le lien dans l'espace \"URL avis Google\"", "4": "Ce SMS se déclenche à partir du deuxième achat client. Il est ensuite renouvelé tous les\n6 mois, afin d'éviter d'être trop invasif avec vos clients. Cette campagne fait grandir votre\nnotoriété et visibilité !"}}, "fidelite": {"form": {"configuration": {"title": "Paramètrage*", "voucher-rule": {"1": "Lorsqu'un client atteint le seuil de", "2": "un chèque-cadeau d'une valeur de", "3": "est généré.", "4": "Il sera alors valable pendant", "5": "mois.", "6": "Le montant maximum des chèques est de"}}, "description": {"1": "A vous de jouer ! ", "2": "Personnalisez votre programme de fidélité : comment le client cumule-t-il ses points ? Quel sera le seuil pour obtenir une récompense ? ", "3": "Vous êtes aux commandes de ces paramètres et pourrez les modifier à tout moment sur cette page.", "4": "C'est le moment de créer un programme qui vous ressemble !"}, "error-message": "Une erreur s'est produite lors de l'enregistrement", "errors": {"earnedPoint": "Le nombre de points doit être compris entre 1 et 999", "spentAmount": "Le montant encaissé doit être compris entre 1 et 999", "thresholdPoint": "Le seuil de points doit être compris entre 1 et 9999", "voucherAmount": "La valeur du chèque-cadeau doit être comprise entre 1 et 999", "voucherMaxAmount": "Le montant max doit être compris entre 1 et 999", "voucherValidityMonth": "Le nombre de mois de validité doit être compris entre 1 et 99"}, "save": "J'enregistre et j'accède à mon tableau de bord", "save-redirect": "J'enregistre", "sms-placeholder": "Toute notre équipe vous remercie pour votre fidélité, voici un chèque cadeau valable 2 mois ! Profitez-en vite en magasin.", "success-message": "Offre Chèque Fidélité mise à jour avec succès", "title": {"rules": "Mécanisme du programme*"}, "voucher": {"rule": {"1": "Le client gagne", "2": "tous les"}}}, "title": "SMS AUTOMATIQUE : FIDÉLITÉ"}, "form": {"save": "J'enregistre", "tainted-message": "Êtes vous sur de vouloir quitter la page ? Des modifications ont été effectuées et ne seront pas enregistrées."}, "offer-label": {"campaign_anniversaire": "Offre Anniversaire", "campaign_bienvenue": "Offre Bienvenue", "campaign_dormant": "Réveil Clients <PERSON>", "campaign_expiration_cheque": "Rappel fin de validité chèque", "campaign_fidelite": "Offre Chèque Fidélité", "campaign_google_review": "Avis <PERSON>"}, "rich-sms": {"error": {"empty": "Veuillez saisir le contenu de la page web ou désactiver l'option correspondante.", "invalid-char": "Le message contient un caractère non autorisé.", "length": "Le message dépasse la limite autorisée de {maxLength} caractères."}, "form": {"enabled": "Activer la landing page", "preview": "Prévisualiser"}, "link": {"add": "Ajouter un lien", "enter": "Entrez ici l'adresse du lien (commençant par http:// ou https://)", "select-error": "Veuillez sélectionner le texte à transformer en lien.", "url-error": "L'URL saisie n'est pas valide."}}, "sms": {"create": {"filters": {"activity-period": "Période d'activité :", "avg-cart": "<PERSON><PERSON> moy<PERSON> :", "nb-visit": "Nombre de passage :", "visits": "passage(s)", "countries": "Pays de résidence :", "no-countries-found": "Aucun pays correspondant parmis vos clients"}, "form": {"civilities": "Sexe :", "default-sms-content": "A l'occasion de [nom évènement], profitez de notre offre exclusive de [réduction/avantage] valable jusqu'au [date].", "enough-sms-credit": "Après cette opération <strong>il vous restera {smsCreditAfterCampaign} SMS</strong>", "name": "Nom de la campagne* :", "name-help": "Le nom de la campagne n'est pas visible par vos clients", "nb-customers": "{count} {count, plural, =0 {client destinataire} =1 {client destinataire} other {clients destinataires}}.", "nb-customers-details": "({frenchCustomerCount} en France et {otherCustomerCount} à l'étranger)", "consumed-credit": " soit un total de <strong>{consumedCredit} SMS</strong> décomptés pour cette campagne.", "not-enough-credit": "Votre crédit n'est pas suffisant pour envoyer votre campagne.", "save": "Initier la campagne", "send-all": "J'envoie ma campagne à tous mes clients", "send-filter": "Je filtre les clients à qui j'envoie la campagne", "startDate": "Date et heure d'envoi :", "success-message": {"1": "Votre campagne SMS a été crée avec succès, elle va être envoyée", "2": {"at": "le", "now": "dès maintenant"}}, "error-sunday": "L'envoi de SMS le dimanche n'est pas autorisé par la législation Française. Veuillez sélectionner une autre date.", "title": {"filters": "Je définis les destinataires de ma campagne", "page": "CAMPAGNE \"SMS\"", "schedule": "Programmation de ma campagne", "sms-content": "Je rédige le contenu de ma campagne"}}, "list-link": "Historique de mes campagnes", "refund-sms-credit": "Pour acheter des SMS c'est par ici !"}, "list": {"ajax-error": "Une erreur s'est produite lors du chargement de l'historique", "create-link": "<PERSON><PERSON>er une campagne SMS", "load-more": "Charger plus ...", "loading": "Chargement", "table": {"campaign-name": "Nom de la campagne", "nb-customers": "Nb de clients contactés", "sms-content-label": "SMS envoyé :", "startDate": "Date de début d'envoi", "actions": "Actions"}, "title": "HISTORIQUE DE MES CAMPAGNES SMS", "remove-rule": "L'annulation d'une campagne est possible jusqu'à 1h30 avant la date et heure programmée."}, "cancel": {"btn": "Annuler la campagne", "confirm": "Êtes vous sûr de vouloir annuler cette campagne ?", "error": "Une erreur s'est produite lors de l'annulation de la campagne"}}, "sms-content": {"fake-date": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 11:35", "nb-characters": "{count} {count, plural, =0 {caractère restant} =1 {caractère restant} other {caractères restants}}.", "preview": "Prévisualisation", "title": "Texte du SMS*"}}, "city-search": {"add-city": "Ville non trouvée, valider quand même ?", "load-error": "Impossible de charger la liste des villes.", "no-result": "Aucune ville ne correspond au code postal {postalCode}.", "no-result-select": "Aucun résultat trouvé", "verify-input": "Veuillez vérifier votre saisie"}, "config": {"cgu": {"link": {"1": "Accès aux CGU", "2": "de la plateforme MaCarteFid"}}, "errors": {"firstname": "Le prénom est incorrect. Il ne doit pas contenir de chiffres.", "information-message": "<PERSON><PERSON><PERSON> de renseigner les informations suivantes pour la création de votre compte MaCarteFid'.", "lastname": "Le nom est incorrect. Il ne doit pas contenir de chiffres.", "phoneNumber": "Le numéro de mobile doit être composé de 10 chiffres et commencer par 06 ou 07.", "pin": "Veuillez respecter le format suivant : 4 chiffres. Example : 1337.", "plainPassword": "Veuillez respecter les règles suivantes :", "postalCode": "Le code postal est incorrect. Il doit contenir 5 chiffres.", "senderId": {"alpha": "Le nom d'expéditeur doit contenir uniquement des caractères alphanumériques (A-Za-z0-9). Les espaces et les underscores sont autorisés.", "not-only-integers": "Le nom d'expéditeur doit contenir au moins un caractère qui n'est pas un chiffre"}, "stripe-portal": "Une erreur est survenue lors de la récupération du lien vers votre portail de gestion d'abonnement", "googleReviewUrl": "L'URL spécifiée ne respecte pas le format demandé"}, "form": {"activityArea": "Activité* :", "city": "Ville* :", "customActivityArea": "Précision de l'activité :", "email": "Email", "firstname": "Prénom* :", "help": {"customActivityArea": "Précisez ici votre activité en donnant des informations plus détaillées sur votre business ! Plus vous êtes précis plus les suggestions seront pertinentes !", "googleReviewUrl": " Pour trouver votre lien Google, rendez-vous sur notre", "googleReviewUrl-link": "tutoriel en vidéo.", "googleReviewUrl-formats": "Format des urls acceptés :"}, "lastname": "Nom* :", "name": "Nom de l'enseigne* :", "phoneNumber": "Numéro de mobile* :", "pinCode": "Votre code pin* :", "plainPassword": "Votre mot de passe* :", "postalCode": "Code postal* :", "senderId": "Nom d'expéditeur des SMS* :", "googleReviewUrl": "URL avis Google :"}, "pin-code-info": "Le code PIN vous sera demandé pour accéder à des actions sensibles comme le lancement d'une campagne SMS, la modification des points d'un client.", "remove": {"button": "Supprimer mon compte", "confirm": "Attention cette action est irreversible.\nÊtes-vous sûr de vouloir supprimer votre compte ?\nEntrer le nom renseigné lors de la création de votre compte MaCarteFid' dans la cellule ci-dessous pour valider la suppression."}, "cancel-subscription": {"confirm": "Confirmez-vous votre souhait de résilier votre abonnement MaCarteFid' ?", "button": "Résilier mon abonnement", "success": "Vous abonnement est maintenant résilié", "error": "Une erreur s'est produite. Veuillez réessayer.", "fiducial-success": "Vous serez contacté sous peu par nos équipes Fiducial pour la prise en compte de votre résiliation.", "cancelled-info": "Votre demande de résiliation a été prise en compte et sera effective le {date}.", "cancelled-info-fiducial": "Vous serez contacté sous peu par nos équipes Fiducial pour la prise en compte de votre résiliation."}, "senderIdVerified": {"invalid": {"1": "Ce nom d'expéditeur a été refusé par nos prestataires d'envoi de SMS, aucun clients ne reçoit vos notifications mais les SMS sont décomptés.", "2": "<PERSON><PERSON><PERSON>z envoyer en saisir un nouveau."}, "waiting": {"1": "Ce nom d'expéditeur n'a pas encore été vérifié.", "2": "Veuillez envoyer au moins 1 SMS après avoir enregistré ce formulaire et revenir ici pour voir si celui-ci a été accepté par nos prestataires d'envoi de SMS."}}, "stripe-portal-subscription-link": "Mon abonnement & mes factures", "stripe-portal-invoice-link": "Mes factures", "success-message": "Modifications enregistrées avec succès", "title": {"account-security": "Sécurité de votre compte", "information": "1. Informations", "manager-identification": "Identification du responsable", "my-store": "<PERSON><PERSON><PERSON> mon enseigne", "pin": "Code PIN", "your-store": "<PERSON><PERSON><PERSON> magasin"}, "qr-code": {"label": "QR code", "generate": "Télécharger mon QR code", "text": "Mettez à la disposition de vos clients un QR code pour qu'ils puissent se créer un compte fidélité en toute autonomie ! Votre temps est précieux."}}, "customer": {"born": "Né(e) le", "history": {"delivered": "SMS délivré", "error": "Une erreur s'est produite lors du chargement de l'historique", "error-code": "Code erreur : ", "load-more": "Charger plus ...", "loading": "Chargement", "sending": "SMS en cours d'envoi", "sending-at": "Prévu le", "sent-at": "<PERSON><PERSON><PERSON>", "title": "Historique", "remove": "Annuler l'encaissement", "remove_confirm": "Êtes-vous sûr de vouloir annuler cet encaissement ?"}, "joined": "Membre depuis", "loyaltyPoints": {"before": "Avant encaissement :", "new-voucher": "Cet achat débloquera un chèque de", "points": "{points, plural, =0 {point} =1 {point} other {points}}", "pts": "{points, plural, =0 {PT} =1 {PT} other {PTS}}", "rule": {"1": "Plus que", "2": "avant un chèque de"}, "title": "Points"}, "no-data": "Non renseigné", "stop-sms-received": "Le client ne souhaite plus recevoir de SMS", "post-it": {"empty": "Pas de post-it actuellement", "last-update": "Dernière modification :", "title": "Post-it"}, "transaction": {"amount": {"help": {}, "unit": "€"}, "error": "Une erreur s'est produite lors de l'encaissement", "help": {"amount": {"1": "Taper le montant total de la vente en incluant le chèque de fidélité.", "2": "Si le montant est de 100 euros avec un chèque fidélité de 5 euros, saisir 100, seuls 95 seront comptés."}}, "loyaltypoint": {"rule": "<PERSON><PERSON> {nbLoyaltyPoints} {nbLoyaltyPoints, plural, =0 {point} =1 {point} other {points}} de fidélité"}, "require-phone": "Fiche client invalide, merci de renseigner le numéro de téléphone", "success": "Encaissement enregistré avec succès", "title": "Encaissement", "total-paid": "Total payé", "validate": "Valider encaissement"}}, "dashboard": {"campaigns": {"title": "Mes SMS automatiques"}, "create-customer": "<PERSON><PERSON>er un nouveau client", "create-sms-campaign": "Lancer une campagne SMS", "reminder": {"empty": "Ajouter un message", "remindme": "Rappel :"}, "ressources": {"help": {"1": "<PERSON><PERSON><PERSON>", "2": "d'assistance"}, "questions": "F.A.Q", "questions-description": "Des solutions pour toutes les entreprises", "title": "<PERSON><PERSON> ressources", "tutorial": "<PERSON><PERSON><PERSON>"}, "sms": {"current-amount": "SMS restants", "status": {"danger": "Urgent rechargez avant arrêt des campagnes", "stop": "Crédit SMS épuisé, campagnes arrêtées", "warning": "<PERSON><PERSON><PERSON> faible, risque d'arrêt des campagnes"}}, "stats": {"items": {"avg_ca": "<PERSON><PERSON> panier moyen", "nb_transactions": "Nombre encaissements", "nb_voucher": "Chèques en circulation", "new_clients": "Nouveaux clients fidélisés", "sms_auto_sent": "SMS auto envoyés", "sms_campaign_sent": "SMS campagnes envoyés", "total_ca": "Montant total transactions", "total_voucher_amount": "Montant chèques en circulation"}, "save-error": "Une erreur s'est produite lors de l'enregistrement de la posision des statistiques", "title": "Mes statistiques du mois", "download-success": "Vos statistiques ont été téléchargées", "download-error": "Une erreur s'est produite lors du téléchargement des statistiques"}}, "faq": {"title": "FAQ", "description": "Découvrez dans notre rubrique FAQ les réponses à vos questions les plus fréquentes. Et si vous ne trouvez pas, contactez nous depuis la rubrique \"demande d'assistance\"."}, "forgot-password": {"check-your-mailbox": "Veuillez vérifier vos e-mails", "log-in": "Je m'identifie", "text": "Mot de passe oublié ? Restez zen ! Entrez votre adresse mail afin de recevoir un lien pour réinitialiser votre mot de passe.", "title": "Mot de passe oublié", "validate": "Valider"}, "general": {"and": "et", "between": "entre", "cashed": "{amount, plural, =0 {encaissé} =1 {encaissé} other {encaissés}}", "copyright": "© Aquitem - Tous droits réservés", "currency": "€", "france": "France", "from": "du", "loading": "Chargement...", "or": "Ou", "points": "{points, plural, =0 {point} =1 {point} other {points}}", "required-fields": "*Champs obligatoires", "save": "Enregistrer", "search": "<PERSON><PERSON><PERSON>", "secured-area": {"automatic-campaign": "Mes SMS automatiques", "change-loyalty-points": "Ajuster les points d'un client", "create-sms-campaign": "Lancer une campagne SMS", "modify-company-post-it": "Post-it magasin", "refund-sms": "Recharger le solde SMS", "remove-customer": "Supprimer un client"}, "to": "au"}, "help": {"title": "<PERSON><PERSON><PERSON> d'assistance", "description": {"1": "La team MaCarteFid' by Fiducial est toujours prête à booster votre expérience.", "2": "Une question ? Besoin d'accompagnement ? Contactez-nous par mail", "email": "<EMAIL>", "fiducial": {"1": "La team MaCarteFid' by Fiducial est toujours prête à booster votre expérience.", "2": "Une question ? Besoin d'accompagnement ? Contactez-nous par téléphone au numéro suivant :", "phone": "04 37 64 16 22", "3": "Nous sommes joignable du lundi au vendredi de 9h à 13h et de 14h à 18h"}}}, "history": {"customer": {"add_points": "+ {points} {points, plural, =0 {point} =1 {point} other {points}} (encaissement de {amount} €)", "add_voucher": "Génération d'un chèque {type} de {amount} € (-{usedPoints} {usedPoints, plural, =0 {point} =1 {point} other {points}})", "create": "Création du client", "edit": "Modification de la fiche client", "edit_points": "Ajustement du nombre de points : {points} {points, plural, =0 {point} =1 {point} other {points}} (anciennement {previousPoints} {previousPoints, plural, =0 {point} =1 {point} other {points}})", "sms": "Envoi d'un SMS {type, select, campaign_bienvenue {Bienvenue} campaign_anniversaire {Anniversaire} campaign_dormant {<PERSON><PERSON><PERSON>} campaign_expiration_cheque {Expiration Chèque} campaign_fidelite {Fidélité} campaign_google_review {Avis Google} campaign_sms {Campagne SMS} other {}}", "use_voucher": "Utilisation d'un chèque {type} de {amount} €", "stop_sms_form": "Le client a demandé à ne plus recevoir de SMS"}}, "http_errors": {"404": "Page non trouvée", "500": "Une erreur s'est produite", "error": "<PERSON><PERSON><PERSON>"}, "interface-utilisateur-intuitive-et-conviviale-maca": "interface utilisateur intuitive et conviviale, MaCarteFID rend la gestion de vos clients", "login": {"button": "Se connecter", "email": "Adresse e-mail :", "email-placeholder": "<EMAIL>", "error": "Une erreur s'est produite", "forgot-password": "Mot de passe oublié", "password": "Mot de passe :", "register-link": "Je crée mon compte", "text": "Accédez à votre espace dédié sur MaCarteFid’ et offrez à vos clients une expérience optimale.", "title": "Connexion", "welcome-message": {"1": "Bienvenue sur MaCarteFid’ by Fiducial, notre solution 100% web pour gérer", "2": "et animer votre programme de fidélité. ", "3": "Grâce à son interface innovante et intuitive, MaCarteFid’ rend la gestion", "4": "de vos clients et de l’animation de leurs offres plus fun."}, "archived": "Votre compte MaCarteFid’ a été archivé. Veuillez nous contacter pour obtenir plus d’informations."}, "logo-macartefid": "Logo MaCarteFID", "maintenance": {"title": "Site en maintenance", "text": "Veuillez nous excuser pour la gêne occassionée. MaCarteFid' est actuellement en maintenance et sera disponible dans quelques instants. Merci de votre compréhension."}, "menu": {"mobile": {"ajouter-un-client": "Ajouter un client", "configuration": "Configuration", "deconnexion": "Déconnexion", "rechercher": "<PERSON><PERSON><PERSON>", "tableau-de-bord": "Tableau de bord"}}, "modal": {"company-reminder": {"form": {"reminder": "Rappel :"}, "title": "Modifier le rappel", "toast-success": "Rappel mis à jour avec succès"}, "customer": {"errors": {"email": "L'email est invalide", "firstname": "Le prénom est incorrect. Il ne doit pas contenir de chiffres.", "lastname": "Le nom est incorrect. Il ne doit pas contenir de chiffres.", "phoneNumber": "Le numéro de mobile doit être composé de 10 chiffres et commencer par 06 ou 07."}, "form": {"birthDate": "Date de naissance :", "city": "Ville :", "country": "Pays :", "email": "Email :", "firstname": "Prénom* :", "nom": "Nom* :", "phoneNumber": "Mobile* :", "postalCode": "Code postal :", "note": "Note Client :", "remove": "Supprimer le client"}, "remove": {"alert": "Etes-vous sûr de vouloir supprimer ce client ?", "toast-error": "Une erreur s'est produite lors de la <PERSON>", "toast-success": "Le client a été supprimé avec succès"}, "title": "Édition fiche client", "title-edit": "<PERSON><PERSON>́er un nouveau client", "toast-success": "Client modifié avec succès", "toast-success-create": "Client créé avec succès"}, "customer-comment": {"form": {"comment": "Commentaire :"}, "title": "Modifier le commentaire", "toast-success": "Post-it modifié avec succès"}, "customer-points": {"form": {"loyaltyPoints": "Nombre de points* :"}, "title": "Modifier le nombre de points", "toast-success": "Nombre de points modifiés avec succès"}, "openai": {"acceptGenerateLanding": "Oui je veux une suggestion", "acceptSmsOnly": "Non, utiliser la suggestion de SMS uniquement", "activityArea": {"info": {"1": "Dites-nous en plus sur votre activité ! En remplissant le champ \"", "2": "\" dans vos paramètres, les suggestions seront plus pertinentes. Et oui, l’objectif est de personnaliser le plus possible vos sms pour susciter l’émotion auprès de vos clients."}, "link": "précision de l'activité"}, "askForLanding": "Vou<PERSON>z-vous générer le contenu de la page web liée au SMS ?", "askForSmsOrLandingOrBoth": "Que voulez-vous utiliser ?", "choose": {"proposals": "Choisissez parmi nos propositions :"}, "chooseBoth": "Les deux !", "chooseLanding": "La suggestion de page web", "chooseSms": "La suggestion de SMS", "error": {"themeRequired": "<PERSON>tte valeur ne doit pas être vide.", "tooManyRequest": "Vous avez un peu trop utilisé ce service, afin de réguler son utilisation vous allez devoir patienter jusqu'à {date} afin de pouvoir l'utiliser de nouveau !"}, "intro": "Voilà 5 propositions de textes pour votre SMS ! Sélectionnez celle qui vous correspond le plus et apportez-y, selon vos envies, vos modifications perso ! Rappelez-vous que cela remplacera votre texte précédemment rédigé.", "landingResult": "Suggestion de contenu pour la page web liée au SMS :", "retry": "<PERSON><PERSON><PERSON><PERSON>", "selected": {"proposal": "Suggestion de contenu SMS sélectionnée :"}, "theme": "Thème de la campagne* :", "title": "En manque d'inspiration ?"}, "pincode": {"label-post": "Vous avez oublié votre code PIN ?", "label-pre": "Merci de rentrer votre code PIN pour accéder à cette rubrique :", "link": "Le recevoir au {phoneNumber}", "send": "So<PERSON><PERSON><PERSON>", "title": "Code PIN"}, "unsubscribe": {"title": "Résilier mon abonnement", "content": {"1": "Vous nous quittez déjà ? Si vous souhaitez résilier votre abonnement deux options s'offrent à vous :", "2": "En <b>r<PERSON><PERSON>t votre abonnement</b> vous n'aurez plus accès à vos données clients et aux fonctionnalités de l'outils MaCarteFid'. Il vous sera possible de réactiver votre abonnement prochainement et retrouver ici toutes les informations précédemment renseignées.", "3": "En choisissant de <b>r<PERSON><PERSON>er et supprimer votre compte</b>, votre abonnement sera interrompu et toutes vos données seront définitivement effacées."}, "unsubscribe": {"button": "Résilier mon abonnement"}, "remove": {"button": "Résilier mon abonnement & supprimer mon compte", "button-tunnel": "Supprimer mon compte"}}}, "payment-sms": {"status": {"error": "Une erreur s'est produite.", "paid": "Paiement réussi ! Redirection ...", "processing": "En attente du paiement..."}}, "register": {"1": {"account-security": "Sécurité de votre compte", "cgu": {"1": "J'accepte les", "2": "conditions générales de ventes et d'utilisation."}, "create-my-account": "<PERSON><PERSON><PERSON> mon compte", "errors": {"firstname": "Le prénom est incorrect. Il ne doit pas contenir de chiffres.", "information-message": "<PERSON><PERSON><PERSON> de renseigner les informations suivantes pour la création de votre compte MaCarteFid'.", "lastname": "Le nom est incorrect. Il ne doit pas contenir de chiffres.", "phoneNumber": "Le numéro de mobile doit être composé de 10 chiffres et commencer par 06 ou 07.", "pin": "Veuillez respecter le format suivant : 4 chiffres. Example : 1337.", "plainPassword": "Veuillez respecter les règles suivantes :", "postalCode": "Le code postal est incorrect. Il doit contenir 5 chiffres."}, "form": {"activityArea": "Activité* :", "city": "Ville* :", "email": "Email", "firstname": "Prénom* :", "lastname": "Nom* :", "name": "Nom de l'enseigne* :", "phoneNumber": "Numéro de mobile* :", "pinCode": "Créer votre code pin* :", "plainPassword": "Votre mot de passe* :", "postalCode": "Code postal* :"}, "greetings": {"1": "Merci de vous être inscrit sur MaCarteFID' !", "2": "Un e-mail de confirmation vient d'être envoyé à l'adresse e-mail que vous avez fournie lors de l'inscription.", "3": "Veuillez consulter votre boîte de réception (ou votre dossier de courriers indésirables si vous ne trouvez pas l'e-mail) et cliquer sur le lien de confirmation pour valider votre compte et activer votre abonnement."}, "pin-code-info": "Le code PIN vous sera demandé pour accéder à des actions sensibles comme le lancement d'une campagne SMS, la modification des points d'un client.", "secured-area-info": "Vous pourrez modifier la sécurisation des rubriques dans le paramétrage de votre compte.", "senderId": {"1": "11 caractères maximum. Les espaces et les underscores sont autorisés.", "2": "Ce nom d'expéditeur n'a pas encore été vérifié.", "3": "Veuillez envoyer au moins 1 SMS après avoir enregistré ce formulaire et revenir ici pour voir si celui-ci a été accepté par nos prestataires d'envoi de SMS."}, "title": {"information": "1. Informations", "manager-identification": "Identification du responsable"}, "your-store": "<PERSON><PERSON><PERSON> magasin"}, "2": {"change-company-type": "Retour", "create-subscription": "Ouv<PERSON>r un abonnement :", "fiducial-customer": "Je suis client Fiducial", "month": "mois", "my-subscription": "Mon abonnement", "not-fiducial-customer": "Je ne suis pas client Fiducial", "resub": "Je me réabonne", "update-payment": "Mettre à jour mes coordonnées bancaires.", "subscription": {"expired": "Votre abonnement est arrivé à expiration le", "past_due": "Nous rencontrons un problème avec le paiement de votre abonnement mensuel. Veuillez renseigner vos coordonnées bancaires via le bouton ci-dessous.", "sms-credit": "Il vous reste {smsCredit} SMS à utiliser.", "will-expire": "Votre abonnement arrivera a expiration le"}, "subscription-description": {"1": "Abonnement mensuel à tacite reconduction.", "2": "Sans engagement."}, "title": {"subscription": "2. ABONNEMENT"}, "validate": "Je me lance", "welcome": "Bienvenue", "fiducial": {"content": "Veuillez remplir votre N° Fiducial. Celui-ci est composé de 8 chiffres.", "societeId": "N° de client :", "validate": "Valider", "rate-limit": "Vous avez fait trop de tentatives différentes, ve<PERSON><PERSON><PERSON> patienter jusqu'à {date} avant de pouvoir réessayer.", "loading": "Veuillez patienter, nous interrogeons nos services pour connaître le statut de votre abonnement. Cela peut prendre jusqu'à 1 minute. Merci.", "expired-nodetail": "Si vous souhaitez renouveler votre abonnement, rapprochez vous de votre agence Fiducial.", "expired": "Contactez-nous par téléphone au {phone} ou par email à l'adresse suivante : {email}", "expired-phone": "***********.00", "expired-email": "<EMAIL>"}}, "3": {"min-sms": "Minimum 100 SMS soit 6€ HT.", "my-subscription": "Mon abonnement est actif", "purchase-sms": "Acheter plus de SMS", "redirect": "Paie<PERSON> r<PERSON>. Redirection ...", "skip": {"1": "<PERSON>er cette <PERSON>"}, "sms-credit": "Crédit <PERSON>", "sms-credit-welcome-offer": "Pour l'ouverture d'un compte nous vous offrons 50 SMS.", "sms-quantity": "La quantité est modifiable sur la page de paiement.", "waiting": "En attente de validation du paiement ..."}, "4": {"campaign-message": {"1": "Vous venez de configurer l'offre chèque fidélité, retrouvez son paramétrage dans la section", "2": "\"Offre chèque fidélité\"", "3": "de votre tableau de bord. D'autres offres à personnaliser vous attendent sur votre tableau de bord :"}, "smsCreditStatus": "Mon solde de SMS est de {smsCredit}.", "title": {"config": "4. <PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "adresse-e-mail": "Adresse e-mail :", "create-account": "Je crée un compte", "email-address": "Adresse e-mail :", "password": {"rules": {"length": "12 caractères minimum", "lowercase": "une minuscule", "number": "un chiffre", "special": "un caractère spécial", "uppercase": "une majuscule"}}, "sign-in": "Je m'identifie", "steps": {"infos": "Informations", "params": "Paramètrage", "sms": "SMS", "subscription": "Abonnement"}, "welcome-message": "Entrez votre adresse e-mail pour créer et paramétrer votre compte !  Sans installation de matériel ou logiciel, MaCarteFid’ permet de mettre en place un programme de fidélisation fun, simple et innovant dans votre commerce !"}, "rich-sms-preview": {"points": "Au {date} à {time}, votre solde de point est de {points} {points, plural, =0 {point} =1 {point} other {points}}.", "voucher": {"amount-suffix": "de remise", "availability": "Valable jusqu'au {date}", "created-at": "<PERSON><PERSON> le {date}", "labels": {"anniversaire": "Chèque Anniversaire", "bienvenue": "<PERSON><PERSON>que <PERSON>", "dormant": "<PERSON><PERSON><PERSON>", "fidelite": "<PERSON><PERSON><PERSON>"}, "number": "Chèque N°{number}"}, "warning": "Notre environnement est fragile, l'impression de ce chèque n'est pas nécessaire"}, "search": {"help": "Rechercher parmi vos clients.", "loading": "Chargement...", "no-result": "Aucun résultat", "placeholder": "Rechercher un client"}, "tutorial": {"title": "Tutoriels vidéo", "description": "Vous trouverez sur cette rubrique des vidéos pour vous expliquer pas à pas comment utiliser et configurer MaCarteFid'"}, "validate-email": {"error": "Ce lien de validation n'est pas valide.", "error2": "Votre compte a déjà été validé ou le lien a expiré."}, "voucher": {"from": "à partir du", "label": "Chèque {type, select, bienvenue {Bienvenue} anniversaire {Anniversaire} dormant {<PERSON><PERSON><PERSON>} fidelite {<PERSON>d<PERSON>lit<PERSON>} other {}}", "validity": "expiration"}, "phone-number": {"invalid": "Le numéro de téléphone mobile est invalide", "invalid-dial": "L'indicatif pays du numéro de téléphone mobile est invalide", "too-short": "Le numéro de téléphone mobile est trop court", "too-long": "Le numéro de téléphone mobile est trop long"}, "create-customer-account": {"company-name-suffix": "Création d'un compte pour", "text": "Bonjour et bienvenue sur notre programme fidélité ! Profitez d'offres exclusives en vous créant un compte ci-dessous :", "submitted": "Félicitations ! C’est officiel, vous faites partie de notre programme fidélité."}}