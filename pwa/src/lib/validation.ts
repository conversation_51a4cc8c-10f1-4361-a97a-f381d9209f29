import { z } from 'zod';
import { unwrapFunctionStore, t } from 'svelte-i18n';

const $t = unwrapFunctionStore(t);

/*****
 * Placer dans ce fichier les règles de validation zod qui ont besoin d'être réutilisées
 *****/

export function getSenderIdRule() {
	return z
		.string()
		.max(11)
		.refine((value) => /^([a-zA-Z0-9_ ]*)$/.test(value), $t('config.errors.senderId.alpha'))
		.refine(
			(value) => /^(((?!^[0-9]*$).)*)$/.test(value),
			$t('config.errors.senderId.not-only-integers')
		);
}
