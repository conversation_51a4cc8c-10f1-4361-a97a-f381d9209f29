<script lang="ts">
    import {t} from 'svelte-i18n';
    import {createEventDispatcher} from 'svelte';
    import {createZodiosForm, type CustomErrorMap} from '$lib/form';
    import {zodiosClient, profile} from '$lib/stores';
    import {Customer_jsonld_customer_write, Customer_jsonld_customer_read} from '$lib/zodios';

    import Modal from './Modal.svelte';
    import Radio from '$lib/components/form/Radio.svelte';
    import Input from '$lib/components/form/Input.svelte';
    import {goto} from '$app/navigation';
    import ButtonIcon from '../ui/ButtonIcon.svelte';
    import {faCircleCheck, faTrash} from '@fortawesome/free-solid-svg-icons';
    import CityAutocomplete from '../form/CityAutocomplete.svelte';
    import PhoneNumber from '../form/PhoneNumber.svelte';
    import {execIfIsGrantedOrAskForPinCode} from '$lib/auth';
    import toast from 'svelte-french-toast';
    import {z} from 'zod';
    import CustomerForm from "$lib/components/customer/CustomerForm.svelte";

    export let customer: Customer_jsonld_customer_read | null = null;
    export let showModal = false;
    export let edition = false;

    const dispatch = createEventDispatcher<{
        created: { customer: Customer_jsonld_customer_read };
        saved: { customer: Customer_jsonld_customer_read };
    }>();

    let modal: Modal;
    let company = $profile.company;

    function onSubmitted(customer: Customer_jsonld_customer_read) {
        modal?.close();

        if (edition) {
            dispatch('saved', {customer});
        } else if (customer) {
            dispatch('created', {customer});
        }
    }

    function onRemoved() {
        modal?.close();
        goto(`/dashboard`);
    }

    function onClose() {
        console.log('todo')
        // if (submitted) {
        // 	if (edition) {
        // 		dispatch('saved', { customer: savedCustomer });
        // 	} else if (customer) {
        // 		dispatch('created', { customer });
        // 	}
        // }
        // form.reset();
        // if (submitted && edition) {
        // 	// si on sauvegarde en édition on met les nouvelles données dans le formulaire
        // 	$_form = savedCustomer ? Object.assign({}, savedCustomer) : initialData;
        // } else if (edition) {
        // 	// Si on ferme la fenètre en édition on remet les valeurs initiales
        // 	$_form = customer ? Object.assign({}, customer) : initialData;
        // } else {
        // 	// En création on efface les valeurs du formulaire
        // 	$_form = initialData;
        // }
        // submitted = false;
    }
</script>

<Modal bind:this={modal} bind:showModal on:close={onClose}>
    <h4 slot="header" class="fw-bold">
        {#if edition}
            {$t('modal.customer.title')}
        {:else}
            {$t('modal.customer.title-edit')}
        {/if}
    </h4>

    {#if showModal}
        <CustomerForm
            {edition}
            {customer}
            {company}
            {modal}
            {onSubmitted}
            {onRemoved}
        />
    {/if}
</Modal>
