import { AxiosError } from 'axios';
import type { PageLoad } from './$types';

export const load = (async ({ parent, url, params }) => {
	const { zodiosClient } = await parent();

	const token = url.searchParams.get('token');

	let sms = null;
	let error = null;

	if (token === null) {
		error = 'Le token de sécurité est invalide';
	} else {
		try {
			sms = await zodiosClient.getRichSms({ params: { id: params.id }, queries: { token } });
		} catch (e) {
			if (e instanceof AxiosError) {
				if (e.code === '403') {
					error = 'Le token de sécurité est invalide';
				} else {
					error = "Une erreur s'est produite";
				}
			}
		}
	}

	return {
		error,
		sms
	};
}) satisfies PageLoad;
