<script lang="ts">
	import type { PageData } from './$types';
    import CustomerForm from "$lib/components/customer/CustomerForm.svelte";
    import { t } from 'svelte-i18n';

	export let data: PageData;

    let localStorageKey = 'customer-account-created';
    let submitted = localStorage.getItem(localStorageKey) === 'true';
    export let onSubmitted = () => {
        submitted = true;
        localStorage.setItem(localStorageKey, 'true');
    };

    let onReset = (e: Event) => {
        e.preventDefault()
        localStorage.removeItem(localStorageKey);
        submitted = false;
    };
</script>

<div
    class="d-flex flex-column justify-content-center align-items-center m-auto"
    style="max-width: 1024px;"
>
    {#if data.error}
        <div class="bg-white text-dark py-5 w-100 fs-6 fw-medium rounded-4 px-4 px-lg-6">
            <div class="flex-center">
                <div>{data.error}</div>
            </div>
        </div>
    {:else}
        <div
            id="company-name"
            class="flex-center w-100 fw-bold mb-0 mb-lg-3 text-center px-3"
            style="font-size: min(4rem, max(1.5rem, 2vw)); line-height: 1.5;"
        >
            {data.company.name}
        </div>
        <div class="flex-center mb-3">
            <div>{$t('create-customer-account.text')}</div>
        </div>
        <div class="separator mb-3 mb-lg-5"></div>
        <div class="bg-white text-dark py-5 w-100 fs-6 fw-medium rounded-4 px-4 px-lg-6">

            {#if submitted}
                <div class="flex-center">
                    <div>{$t('create-customer-account.submitted')}</div>
                </div>
                <div class="flex-center mt-4 fs-sm">
                    <a class="text-mcf-dark" href="#!" on:click={onReset}>Retour à la création de compte</a>
                </div>
            {:else if data}
                <CustomerForm company={data.company} {onSubmitted} anonymousToken="{data.token}" anonymousPost/>
            {/if}
        </div>
    {/if}
</div>
