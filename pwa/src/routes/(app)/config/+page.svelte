<script lang="ts">
	import { t } from 'svelte-i18n';
	import ButtonIcon from '$lib/components/ui/ButtonIcon.svelte';
    import {
        faBan,
        faCheckCircle,
        faCircleCheck,
        faFileLines, faQrcode,
        faQuestionCircle,
        faTimesCircle,
        faTrash,
        faWarning
    } from '@fortawesome/free-solid-svg-icons';
	import type { PageData } from './$types';
	import TitleDashed from '$lib/components/ui/TitleDashed.svelte';
	import { Company_company_write_config as WriteSchema } from '$lib/zodios';
	import { createZodiosForm, type CustomErrorMap } from '$lib/form';
	import { zodiosClient } from '$lib/stores';
	import Input from '$lib/components/form/Input.svelte';
	import Password from '$lib/components/form/Password.svelte';
	import PasswordValidator from '$lib/components/register/PasswordValidator.svelte';
	import PinCode from '$lib/components/register/PinCode.svelte';
	import Select from '$lib/components/form/Select.svelte';
	import CityAutocomplete from '$lib/components/form/CityAutocomplete.svelte';
	import { z } from 'zod';
	import type { Profile } from '$lib/api.client';
	import { onMount } from 'svelte';
	import toast from 'svelte-french-toast';
	import Wrapper from '$lib/components/form/Wrapper.svelte';
	import Label from '$lib/components/form/Label.svelte';
	import ConditionalWrapper from '$lib/components/ui/ConditionalWrapper.svelte';
	import CancelSubscriptionModal from '$lib/components/modal/CancelSubscriptionModal.svelte';
	import Fa from 'svelte-fa';
	import { openInNewTab } from '$lib/utils';
	import { getSenderIdRule } from '$lib/validation';
	import { removeCompany } from '$lib/company';

	export let data: PageData;

	let company = data.profile.company;
	$: company = data.profile.company;
	let campaignAuto = false;
	let stripePortalUrl: string | null = null;
	let showCancelSubscriptionModal = false;

	onMount(() => {
		// scéanrio ou on arrive sur la page via la fonction goto
		if (window.location.hash.substring(1) !== '') {
			setTimeout(() => {
				document.getElementById(`#${window.location.hash.substring(1)}`)?.scrollIntoView();
			}, 10);
		}
		campaignAuto =
			company.securedAreas.campaignAnniversairePatch &&
			company.securedAreas.campaignBienvenuePatch &&
			company.securedAreas.campaignDormantPatch &&
			company.securedAreas.campaignExpirationChequePatch &&
			company.securedAreas.campaignFidelitePatch &&
            company.securedAreas.campaignGoogleReviewPatch;
	});

	const messages: CustomErrorMap<typeof WriteSchema> = {
		firstname: $t('config.errors.firstname'),
		lastname: $t('config.errors.lastname'),
		pin: $t('config.errors.pin'),
		plainPassword: $t('config.errors.plainPassword'),
		phoneNumber: $t('config.errors.phoneNumber'),
		postalCode: $t('config.errors.postalCode'),
        googleReviewUrl: $t('config.errors.googleReviewUrl')
	};

	const FormSchema = WriteSchema.extend({
		pin: z
			.string()
			.min(4)
			.max(4)
			.regex(/^(.*\d{4}.*)$/)
			.nullish(),
		senderId: getSenderIdRule()
	});

	let savedCompany: typeof company;

	const form = createZodiosForm({
		schema: FormSchema,
		async onSubmitted(data) {
			if (data.pin === '' || data.pin === null) {
				delete data.pin;
			}
			if (data.plainPassword === '' || data.plainPassword === null) {
				delete data.plainPassword;
			}

			data.securedAreas = {
				...company.securedAreas,
				campaignAnniversairePatch: campaignAuto,
				campaignBienvenuePatch: campaignAuto,
				campaignDormantPatch: campaignAuto,
				campaignExpirationChequePatch: campaignAuto,
				campaignFidelitePatch: campaignAuto,
                campaignGoogleReviewPatch: campaignAuto
			};

			savedCompany = (await $zodiosClient.companyUpdateConfig(data, {
				params: {
					id: company.id.toString()
				}
			})) as Profile['company'];
			toast.success($t('config.success-message'));
			return savedCompany;
		},
		initialData: {
			pin: null,
			email: company.email,
			plainPassword: '',
			firstname: company.firstname,
			lastname: company.lastname,
			phoneNumber: company.phoneNumber,
			name: company.name,
			senderId: company.senderId,
			activityArea: company.activityArea,
			customActivityArea: company.customActivityArea,
			postalCode: company.postalCode,
			city: company.city,
            googleReviewUrl: company.googleReviewUrl
		},
		options: {
			taintedMessage: null,
			dataType: 'json'
		},
		messages
	});

	const { form: _form, errors, enhance, delayed } = form;


    $: {
        // On transforme l'url vide en null car zodios est relou
        if ($_form.googleReviewUrl === '') {
            $_form.googleReviewUrl = null
        }
    }

	const openStripePortal = async (event) => {
		event.preventDefault();

		if (stripePortalUrl === null) {
			const response = await $zodiosClient
				.companyGetStripePortalUrl({
					params: { id: company.id.toString() }
				})
				.catch(() => {
					stripePortalUrl = null;
				});
			stripePortalUrl = response?.url ?? null;
		}

		if (stripePortalUrl !== null) {
			openInNewTab('stripe-portal', stripePortalUrl, false);
		} else {
			toast.error($t('config.errors.stripe-portal'));
		}
	};

    const downloadQrCode = async (event) => {
        event.preventDefault();

        await $zodiosClient.companyGenerateQrCode({
            responseType: 'blob',
            params: { id: company.id.toString() }
        });
    };

	async function onUnsubscribe() {
		data.profile = (await data.zodiosClient.getProfile()) as Profile;
	}

	async function handleRemoveCompany(event: any) {
		event.preventDefault();
		await removeCompany(company);
	}

	const dateFormatter = new Intl.DateTimeFormat('fr-FR');
</script>

<div class="w-100 d-flex justify-content-center">
	<form
		method="POST"
		use:enhance
		class="bg-mcf-light text-dark py-5 w-100 fs-6 fw-medium rounded-4"
		style="max-width: 1024px;"
		data-testid="config-form"
	>
		<div class="px-5">
			<TitleDashed title={$t('config.title.my-store')} class="text-mcf-blue" responsive />
		</div>
		<div
			class="form-padding d-flex flex-column flex-md-row flex-wrap justify-content-end pt-3 mb-6 mt-6 mt-md-0 gap-3"
		>
			<ButtonIcon
				on:click={openStripePortal}
				theme="dark"
				size="medium"
				icon={faFileLines}
				iconSize={'sm'}
				label={company.companyType === 'fiducial'
					? $t('config.stripe-portal-invoice-link')
					: $t('config.stripe-portal-subscription-link')}
				customClasses="btn flex-center rounded-3 gap-2 fw-bold btn-mcf-dark"
			/>
		</div>

		<div class="form-section-title">{$t('config.title.manager-identification')}</div>

		<div class="form-padding mb-6">
			<Input
				{form}
				field="firstname"
				label={$t('config.form.firstname')}
				horizontal
				autocomplete="given-name"
			/>
			<Input
				{form}
				field="lastname"
				label={$t('config.form.lastname')}
				horizontal
				autocomplete="family-name"
			/>
			<Input
				{form}
				field="phoneNumber"
				label={$t('config.form.phoneNumber')}
				horizontal
				autocomplete="tel"
			/>
			<Wrapper horizontal>
				<Label label="Adresse mail :" horizontal autocomplete="username" />
				<ConditionalWrapper condition={true} class="col-lg-8">
					<input
						type="email"
						class="form-control"
						value={company.email}
						readonly
						disabled
					/>
				</ConditionalWrapper>
			</Wrapper>
		</div>

		<div class="form-section-title" id="your-store">{$t('config.title.your-store')}</div>

		<div class="form-padding mb-6">
			<Input
				{form}
				field="name"
				label={$t('config.form.name')}
				horizontal
				autocomplete="organization"
			/>
			<Input
				{form}
				field="senderId"
				label={$t('config.form.senderId')}
				horizontal
				autocomplete="sender-id"
			>
				<span slot="help">
					<div class="form-text text-secondary">
						{#if company.senderIdVerified === null || $_form.senderId !== company.senderId}
							{$t('config.senderIdVerified.waiting.1')}<br />
							{$t('config.senderIdVerified.waiting.2')}
						{:else if company.senderIdVerified === false}
							<Fa icon={faWarning} class="text-warning" />
							{$t('config.senderIdVerified.invalid.1')}<br />
							{$t('config.senderIdVerified.invalid.2')}
						{/if}
					</div>
				</span>
				<span slot="afterInput" class="input-group-text">
					{#if company.senderIdVerified === null || $_form.senderId !== company.senderId}
						<Fa icon={faQuestionCircle} class="text-secondary" />
					{:else if company.senderIdVerified === false}
						<Fa icon={faTimesCircle} class="text-danger" />
					{:else}
						<Fa icon={faCheckCircle} class="text-success" />
					{/if}
				</span>
			</Input>
			<Select {form} field="activityArea" label={$t('config.form.activityArea')} horizontal />
			<Input
				{form}
				id="customActivityArea"
				type="text"
				field="customActivityArea"
				label={$t('config.form.customActivityArea')}
				horizontal
			>
				<span slot="help">
					<div class="form-text text-secondary">
						{$t('config.form.help.customActivityArea')}
					</div>
				</span>
			</Input>
			<Input
				{form}
				id="googleReviewUrl"
				type="text"
				field="googleReviewUrl"
				label={$t('config.form.googleReviewUrl')}
                placeholder="https://g.page/r/XXXXXXXXXXXXXXX/review"
				horizontal
			>
				<span slot="help">
					<div class="form-text text-secondary">
						{$t('config.form.help.googleReviewUrl')} <a href="/tutorial" target=_blank class="text-dark">{$t('config.form.help.googleReviewUrl-link')}</a>
                        <br />
                        {$t('config.form.help.googleReviewUrl-formats')}
                        <ul>
                            <li>https://g.page/r/XXXXXXXXXXXXXXX/review</li>
                            <li>https://g.co/kgs/XXXXXXXXXXXXXXX</li>
                            <li>https://search.google.com/local/writereview?placeid=XXXXXXXXXXXXXXX</li>
                        </ul>
					</div>
				</span>
			</Input>
			<Input
				{form}
				field="postalCode"
				label={$t('config.form.postalCode')}
				horizontal
				autocomplete="postal-code"
			/>
			<CityAutocomplete
				{form}
				field="city"
				label={$t('config.form.city')}
				postalCodeField="postalCode"
				horizontal
				autocomplete="city"
			/>

            <div class="mb-3 row">
                <div class="form-label col-form-label col-lg-4">
                    {$t('config.qr-code.label')} :
                </div>
                <div class="col-lg-8">
                    <ButtonIcon
                        on:click={downloadQrCode}
                        theme="dark"
                        size="medium"
                        icon={faQrcode}
                        iconSize={'sm'}
                        label={$t('config.qr-code.generate')}
                        customClasses="btn flex-center rounded-3 gap-2 fw-bold btn-mcf-dark"
                    />
                    <div class="form-text text-secondary s-b-_NMToNEpRf">
                        {$t('config.qr-code.text')}
                    </div>
                </div>
            </div>
		</div>

		<div class="form-section-title">{$t('config.title.account-security')}</div>

		<div class="form-padding">
			<Password
				{form}
				field="plainPassword"
				label={$t('config.form.plainPassword')}
				horizontal
				disableAutocomplete
				allowNull
			/>
			<div class="flex-center">
				<PasswordValidator password={$_form.plainPassword} />
			</div>
		</div>

		<div class="form-section-title">{$t('config.title.pin')}</div>

		<div class="form-padding">
			<div class="mb-3 row">
				<label class="form-label col-form-label col-lg-4">
					{$t('config.form.pinCode')}
				</label>
				<div class="col-lg-8">
					<PinCode bind:value={$_form.pin} errors={$errors.pin} />
				</div>
			</div>
			<p class="mb-5">
				{$t('config.pin-code-info')}
			</p>
			<div class="mb-6">
				<div class="form-custom-checkbox mb-1">
					<input
						bind:checked={company.securedAreas.campaignSmsPost}
						type="checkbox"
						id="pinCampaignSmsPost"
					/>
					<label for="pinCampaignSmsPost"
						>{$t('general.secured-area.create-sms-campaign')}</label
					>
				</div>
				<div class="form-custom-checkbox mb-1">
					<input bind:checked={campaignAuto} type="checkbox" id="pinCampaignAuto" />
					<label for="pinCampaignAuto"
						>{$t('general.secured-area.automatic-campaign')}</label
					>
				</div>
				<div class="form-custom-checkbox mb-1">
					<input
						bind:checked={company.securedAreas.customerDelete}
						type="checkbox"
						id="pinCustomerDelete"
					/>
					<label for="pinCustomerDelete"
						>{$t('general.secured-area.remove-customer')}</label
					>
				</div>
				<div class="form-custom-checkbox mb-1">
					<input
						bind:checked={company.securedAreas.customerSetLoyaltyPoints}
						type="checkbox"
						id="pinCustomerSetLoyaltyPoints"
					/>
					<label for="pinCustomerSetLoyaltyPoints"
						>{$t('general.secured-area.change-loyalty-points')}</label
					>
				</div>
				<div class="form-custom-checkbox mb-1">
					<input
						bind:checked={company.securedAreas.companyStartPaymentSMS}
						type="checkbox"
						id="pinCompanyStartPaymentSMS"
					/>
					<label for="pinCompanyStartPaymentSMS"
						>{$t('general.secured-area.refund-sms')}</label
					>
				</div>
				<div class="form-custom-checkbox mb-1">
					<input
						bind:checked={company.securedAreas.companySetReminder}
						type="checkbox"
						id="pinCompanyUpdateReminder"
					/>
					<label for="pinCompanyUpdateReminder"
						>{$t('general.secured-area.modify-company-post-it')}</label
					>
				</div>
			</div>
			<div class="d-flex justify-content-between flex-column flex-md-row mb-5">
				{#if company?.subscription?.cancelled}
					<ButtonIcon
						theme="dark"
						size="large"
						icon={faTrash}
						label={$t('modal.unsubscribe.remove.button-tunnel')}
						aditionnalClasses="mb-2 mb-md-0"
						on:click={handleRemoveCompany}
					/>
				{:else}
					<ButtonIcon
						theme="dark"
						size="large"
						icon={faBan}
						label={$t('config.cancel-subscription.button')}
						aditionnalClasses="mb-2 mb-md-0"
						on:click={(e) => (e.preventDefault(), (showCancelSubscriptionModal = true))}
					/>
				{/if}
				<ButtonIcon
					type="submit"
					theme="pink"
					size="large"
					icon={faCircleCheck}
					label="Enregistrer"
					loader={$delayed}
				/>
			</div>
			{#if company?.subscription?.cancelled && company?.subscription?.expirationDate}
				<p class="mb-5 text-mcf-red">
					{#if company.companyType === 'fiducial'}
						{$t('config.cancel-subscription.cancelled-info-fiducial')}
					{:else}
						{$t('config.cancel-subscription.cancelled-info', {
							values: {
								date: dateFormatter.format(
									new Date(company.subscription.expirationDate)
								)
							}
						})}
					{/if}
				</p>
			{/if}
			<div class="d-flex flex-column flex-lg-row fs-6">
				<span>{$t('general.required-fields')}</span>
				<span class="ms-0 ms-lg-6"
					><a href="/files/CGU.pdf" class="text-dark" target="_blank"
						>{$t('config.cgu.link.1')}</a
					>
					{$t('config.cgu.link.2')}</span
				>
			</div>
		</div>
	</form>
</div>

<CancelSubscriptionModal
	bind:showModal={showCancelSubscriptionModal}
	{company}
	on:close={onUnsubscribe}
/>

<style lang="scss">
	.form-section-title {
		background: $mcf-blue;
		color: white;
		padding: 0.75rem 0.75rem 0.75rem 0.5rem;
		margin-bottom: 2rem;
		font-size: 1.1rem;
		display: inline-block;
		border-top-right-radius: 3rem;
		border-bottom-right-radius: 3rem;
		white-space: nowrap;
	}

	.form-padding {
		padding: 0 1rem !important;
	}

	@include media-breakpoint-up(sm) {
		.form-section-title {
			padding: 0.75rem 2.75rem 0.75rem 3rem;
			font-size: 1.25rem;
		}

		.form-padding {
			padding: 0 5rem !important;
		}
	}
</style>
