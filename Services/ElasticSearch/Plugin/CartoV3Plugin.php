<?php

namespace Alienor\ElasticBundle\Services\ElasticSearch\Plugin;

use Alienor\ElasticBundle\Model\Interfaces\SourceInterface;
use Alienor\ElasticBundle\Services\ElasticSearch\ElasticRequestBuilder;
use Alienor\ElasticBundle\Services\DataLoader\HereIsochrone;
use Alienor\ElasticBundle\Services\DataLoader\IntersectPolygone;

class CartoV3Plugin extends CartoV2Plugin
{
    public const ALLOWED_FOR_EVERYBODY = false;

    protected $cartoPointSecondairePlugin;

    /**
     * AbstractPlugin constructor.
     * @param ElasticRequestBuilder $elasticRequestBuilder
     * @param MagasinConcurrent $magasinConcurrent
     * @param HereIsochrone $hereIsochrone
     * @param IntersectPolygone $intersectPolygone
     * @param CartoPointSecondairePlugin $cartoPointSecondairePlugin
     */
    public function __construct(ElasticRequestBuilder $elasticRequestBuilder, HereIsochrone $hereIsochrone, IntersectPolygone $intersectPolygone, CartoPointSecondairePlugin $cartoPointSecondairePlugin)
    {
        parent::__construct($elasticRequestBuilder, $hereIsochrone, $intersectPolygone);
        $this->cartoPointSecondairePlugin = $cartoPointSecondairePlugin;

    }

    public function isValid(SourceInterface $source, $params, $filters = null, $state = null)
    {
        return true;
        // return isset($params['options']['isochrone']);
    }

    public function execute(SourceInterface $source, $params, $filters = null, $state = null)
    {
        $res = parent::execute($source, $params, $filters, $state);
        $res['otherDatas']['resPointSecondaire'] = $this->cartoPointSecondairePlugin->execute($source, $params, $filters, $state);
        return $res;
    }
}