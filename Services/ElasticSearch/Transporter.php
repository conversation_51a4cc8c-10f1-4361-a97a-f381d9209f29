<?php

namespace Alienor\ElasticBundle\Services\ElasticSearch;

use Alienor\ElasticBundle\Model\ElasticableInterface;
use Alienor\ElasticBundle\Model\MappingConfig;
use Alienor\ElasticBundle\Services\Serializer;
use Doctrine\Common\Collections\ArrayCollection;
use Elastica\Document;
use Elastica\Index;
use Symfony\Component\Serializer\SerializerInterface;

/**
 * Transfert des données de l'application vers elasticsearch
 */
class Transporter
{

    /** @var MappingConfig[]|ArrayCollection */
    private readonly \Doctrine\Common\Collections\ArrayCollection $mappings;

    /**
     * Transporter constructor.
     */
    public function __construct(
        private readonly SerializerInterface $serializer,
        private readonly Administrator $administrator,
        array $mappings
    ) {
        $this->mappings = new ArrayCollection(array_map(function($m) { return MappingConfig::fromArray($m); }, $mappings));
    }

    /**
     * @param $entity
     * @return MappingConfig|false
     */
    public function findConfigByClass($entity)
    {
        return $this->mappings->filter(function(MappingConfig $m) use ($entity) {
            $classname = $m->getClass();

            if ($entity instanceof ElasticableInterface) {
                return $entity->getElasticIndex() === $m->getIndex();
            }

            return is_subclass_of($entity, $classname) || $entity instanceof $classname || $entity === $classname || $m->getIdentifier() === $entity;
        })->first();
    }

    /**
     * @param $entity
     * @return \Elastica\Index|null
     */
    public function getIndexType($entity, $lang = null) {
        if ($config = $this->findConfigByClass($entity)) {
            return $this->administrator->getCurrentTypeAlias($config, $lang);
        }
        return null;
    }

    /**
     * @param                    $entity
     * @param null               $lang
     * @return \Elastica\Index|null
     */
    public function getTimestampedIndexType($entity, \DateTimeInterface $now, $lang = null, $configFile = null) {
        if ($config = $this->findConfigByClass($entity)) {
            return $this->administrator->getTimestampedIndex($config, $now, $lang, $configFile);
        }
        return null;
    }

    /**
     * @return \Elastica\Document
     */
    public function getDocument(ElasticableInterface $entity) {
        $serialized = $this->serializer->serializeGroup($entity);
        return new Document($entity->getId(), $serialized);
    }

    /**
     * @return Document
     */
    public function createDocument(array $data, $config) {
        if (isset($config['fieldId']) && isset($data[$config['fieldId']])) {
            $document = new Document($data[$config['fieldId']], $data);
        } else {
            $document = new Document(null, $data);
        }
        if (isset($config["upsert"]) && $config["upsert"]) {
            $document->setDocAsUpsert(true);
        }
        return $document;
    }

    /**
     *
     * @return string
     */
    public function addDocument(ElasticableInterface $entity, Index $indexType)
    {
        $document = $this->getDocument($entity);
        $indexType->addDocument($document);
        return $document->getData();
    }

    /**
     *
     * @return string
     */
    public function updateDocument(ElasticableInterface $entity, Index $indexType)
    {
        $document = $this->getDocument($entity);
        $indexType->updateDocument($document);
        return $document->getData();
    }

    /**
     * @param string $id
     * @return \Elastica\Response
     */
    public function deleteDocument($id, Index $indexType)
    {
        return $indexType->deleteById($id);
    }

    /**
     * @return Administrator
     */
    public function getAdministrator()
    {
        return $this->administrator;
    }

}