<?php
namespace Alienor\ElasticBundle\Services\Normalizer;

use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

/**
 * Normalizes an object implementing the {@see \DateTimeInterface} to a date string.
 * Denormalizes a date string to an instance of {@see \DateTime} or {@see \DateTimeImmutable}.
 *
 * <AUTHOR> <<EMAIL>>
 */
class DateTimeNormalizer implements NormalizerInterface, DenormalizerInterface
{
    public const FORMAT_KEY = 'datetime_format';

    /**
     * @param string $format
     */
    public function __construct(private $format = 'Y-m-d H:i:s')
    {
    }

    /**
     * {@inheritdoc}
     *
     * @throws \InvalidArgumentException
     */
    public function normalize($object, $format = null, array $context = array()): array|string|int|float|bool|\ArrayObject|null
    {
        if (!$object instanceof \DateTimeInterface) {
            throw new \InvalidArgumentException('The object must implement the "\DateTimeInterface".');
        }
        $format = $context[self::FORMAT_KEY] ?? $this->format;
        return $object->format($format);
    }

    /**
     * {@inheritdoc}
     */
    public function supportsNormalization($data, $format = null, array $context = []): bool
    {
        return $data instanceof \DateTimeInterface;
    }

    /**
     * {@inheritdoc}
     *
     * @throws \UnexpectedValueException
     */
    public function denormalize($data, $type, $format = null, array $context = array()): mixed
    {
        $dateTimeFormat = $context[self::FORMAT_KEY] ?? null;
        if (null !== $dateTimeFormat) {
            $object = \DateTime::class === $type ? \DateTime::createFromFormat($dateTimeFormat, $data) : \DateTimeImmutable::createFromFormat($dateTimeFormat, $data);
            if (false !== $object) {
                return $object;
            }
            $dateTimeErrors = \DateTime::class === $type ? \DateTime::getLastErrors() : \DateTimeImmutable::getLastErrors();
            throw new \UnexpectedValueException(sprintf(
                'Parsing datetime string "%s" using format "%s" resulted in %d errors:'."\n".'%s',
                $data,
                $dateTimeFormat,
                $dateTimeErrors['error_count'],
                implode("\n", $this->formatDateTimeErrors($dateTimeErrors['errors']))
            ));
        }
        try {
            return \DateTime::class === $type ? new \DateTime($data) : new \DateTimeImmutable($data);
        } catch (\Exception $e) {
            throw new \UnexpectedValueException($e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function supportsDenormalization($data, $type, $format = null, array $context = []): bool
    {
        $supportedTypes = array(
            \DateTimeInterface::class => true,
            \DateTimeImmutable::class => true,
            \DateTime::class => true,
        );
        return isset($supportedTypes[$type]);
    }

    /**
     * Formats datetime errors.
     *
     *
     * @return string[]
     */
    private function formatDateTimeErrors(array $errors): array
    {
        $formattedErrors = array();
        foreach ($errors as $pos => $message) {
            $formattedErrors[] = sprintf('at position %d: %s', $pos, $message);
        }
        return $formattedErrors;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            '\DateTime' => false,
        ];
    }
}
