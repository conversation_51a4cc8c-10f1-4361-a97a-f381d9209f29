<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241204160422 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE answer ALTER question_id TYPE VARCHAR(255)');
        $this->addSql('ALTER TABLE participant ADD completed_cold BOOLEAN NOT NULL');
        $this->addSql('ALTER TABLE participant RENAME COLUMN completed TO completed_hot');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE participant ADD completed BOOLEAN NOT NULL');
        $this->addSql('ALTER TABLE participant DROP completed_hot');
        $this->addSql('ALTER TABLE participant DROP completed_cold');
        $this->addSql('ALTER TABLE answer ALTER question_id TYPE INT');
    }
}
