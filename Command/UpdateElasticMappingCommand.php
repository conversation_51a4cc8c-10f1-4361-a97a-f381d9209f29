<?php

namespace Alienor\ElasticBundle\Command;

use Alienor\ElasticBundle\Services\PopulatorChain;
use Alienor\ElasticBundle\Services\PopulatorInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManager;
use Elastica\Client;
use Alienor\ElasticBundle\Services\ElasticSearch\Transporter;
use Alienor\ElasticBundle\Services\ElasticSearch\Administrator;
use Alienor\ElasticBundle\Services\ElasticSearch\IndexConfigurationLoader;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Form\Exception\LogicException;

#[AsCommand(name: 'alienor:elastic:es_update')]
class UpdateElasticMappingCommand extends AbstractUpdateElasticMappingCommand
{
    /**
     * {@inheritdoc}
     */
    protected function configure(): void
    {
        parent::configure();
        $this
            ->setDescription('Commande de création et mise à jour des index elasticsearch')
            ->setHelp(<<<EOT

La commande <comment>%command.name%</comment> migre les alias du projet vers un nouvel index :

    <info>php %command.full_name%</info>

Pour limiter les index à migrer, il est possible de préciser ceux-ci en argument, séparés par des espaces :

    <info>php %command.full_name% account ticket product</info>

EOT
            )
        ;
    }

    /**
     * @param OutputInterface $output
     * @param array $types
     * @param \DateTimeInterface $now
     */
    public function populateDatas(OutputInterface $output, array $types, \DateTimeInterface $now, InputInterface $input)
    {
        $output->writeln('');
        $output->writeln("Synchronisation des données ElasticSearch");

        $populators = $this->populators->getPopulators();

        foreach ($types as $type) {

            if (isset($config["username"]) && isset($config["password"])) {
                $elasticaConfig = $this->elasticAdministrator->getClient()->getConfig();
                $elasticaConfig["headers"] = array_merge($elasticaConfig["headers"] ?? array(), array(
                    "Authorization" => "Basic " . base64_encode($config["username"] . ':' . $config["password"])
                ));
                $this->elasticAdministrator->getClient()->setConfig($elasticaConfig);
            }
            
            $datas = null;
            $populated = false;
            if (sizeof($populators)) {
                foreach ($populators as $populator) {
                    if ($populator->hasCustomFetch($type)) {
                        if ($populator->hasBulkFetch()) {
                            $count = $populator->countResults($type);
                            $this->initProgress($output, $count);

                            $populator->fetchDatasBulk($type, $this->bulkSize, function(array $evaluations, $count) use ($output, $type, $now, $populator) {
                                $this->pushDatas($evaluations, $output, $type, $now, $populator->hasCustomProcessAvance());
                                if ($populator->hasCustomProcessAvance()) {
                                    $this->bulkProcess->setProgress($count);
                                }
                            });
                            $this->bulkProcess->finish();
                            $populated = true;
                        } else {
                            $datas = $populator->fetchDatas($type);
                        }
                    }
                }
            }

            if ($datas === null && $populated === false) {
                $datas = $this->findAllDatas($type);
            }

            if ($datas != null && count($datas)) {
                $this->initProgress($output, count($datas));
                $bulkDatas = array_chunk($datas, $this->bulkSize);
                foreach ($bulkDatas as $bulkData) {
                    $this->pushDatas($bulkData, $output, $type, $now);
                }
                $this->bulkProcess->finish();
            } else if ($populated === false) {
                $output->writeln('');
                $output->writeln("Aucune donnée à envoyer à ElasticSearch");
            }
        }
    }

    public function findAllDatas($type) {
        if ($config = $this->transporter->findConfigByClass($type)) {
            return $this->entityManager->getRepository($config->getClass())->findBy(array(), null, $this->limit);
        }
        throw new LogicException(sprintf("Type introuvable: %s", $type));
    }
}
