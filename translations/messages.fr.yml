menu:
  dashboard:
    title: Tableaux de bord
    list: Liste des tableaux de bord
    add: Créer un tableau de bord
    help: Télécharger l'aide
  visualisation:
    title: Visualisations
    list: Liste des visualisations
    add: Créer une visualisation
  source:
    title: Sources
    list: Liste des sources
    add: Créer une source
  config:
    title: <PERSON>ccès
    user:
      title: Utilisateurs
      list: Liste
      add: Ajouter
    client:
      title: Clients
      list: Liste
      add: Ajouter
    group:
      title: Groupes
      list: Liste
      add: Ajouter
    csv:
      title: Gestion des CSV
      list: Liste
    profil:
      title: Profils
      list: Liste
      add: Ajouter
    permission:
      title: Permissions
      list: Liste
      add: Ajouter
    server:
      title: Serveurs
      list: Liste des serveurs
      add: Créer un serveur
    alert:
      title: Notifications
      list: Liste des notifications
      add: Créer une notification
    export:
      title: Exports
      list: Liste des exports
      add: Créer un export
  enseigne:
    title: Mon enseigne
  dropdowns:
    failed: Échec lors du chargement
  bookmark:
    title: Mes tableaux de bord favoris
  user:
    preferences:
      title: Mes préférences
      links:
        welcome_dashboard: Gérer mon tableau de bord d'accueil
        favorite_dashboards: Gérer mes tableaux de bord favoris
    profile:
      title: Mon profil
      links:
        update_profile: Modifier mon profil
        update_password: Modifier mon mot de passe
    version: Version
  alert:
    title: Notifications
    empty: Pas de notification pour le moment...
  filters:
    title: Filtres actifs
  dashboard_list:
    title: Mes tableaux de bord
    nothing: Pas de tableau de bord
    no_bookmarked_dashboard: Vous n'avez pas de tableau de bord favori pour le moment.
    show_dashboard: Voir
    filter:
      all: Tous
      bookmarked: Mes favoris
      search: Rechercher un tableau de bord
  collapse: Réduire le menu
dashboard:
  lists: Liste des tableaux de bord
  title: Titre (50 caractères maximum)
  description: Description
  short_description: Description courte
  icon: Icône
  save.add: Ajouter le tableau de bord
  add:
    title: Créer un tableau de bord
    filter: Ajouter un filtre
    filter_validate: Ajouter le filtre
    bloc: Ajouter un bloc
  new: Nouveau tableau de bord
  edit: Modifier le tableau de bord
  groupe: Groupe
  groupe_placeholder: Veuillez sélectionner un groupe
  client: Client
  filters: Filtres
  filters_source_help: Les filtres en gris sont les filtres initialement autorisés dans les paramètres de la source
  plugins: Plugins
  private: Tableau de bord privé ?
  nestedFilters: Filtres imbriqués ?
  picture: Image
  helpDocument: Document d'aide (fichier PDF)
  type: "Type de tableau de bord"
  saveas:
    new: Titre du nouveau tableau de bord
    description: Description du nouveau tableau de bord
    filters: Inclure les filtres en cours ?
  save:
    success: >-
      Les modifications apportées au tableau de bord ont été enregistrées
      avec succès
  list:
    title_page: Liste des tableaux de bord
    title_ux: Mes tableaux de bord
    title: Tableau de bord
    dashboard: Tableau de bord
    name: Nom
    desc: Description
    public: Publique
    groupe: Groupe
    createdOn: Date de création
    edit: Modifier
    delete: Supprimer
    default: Accueil
    bookmarks: Favoris
    my_bookmarks: Mes favoris
    all_my_dashboards: Tous mes tableaux de bord
    search_dashboard: Rechercher un tableau de bord
    read_more: lire la suite
    card_subtitle: Tableau de bord
    share: Partager
    dates:
      expiration: Date d'expiration
      never: Jamais
      day: 1 jour
      week: 1 semaine
      month: 1 mois
      custom: Personnalisée
      custom_date: Date personalisée
  search: 'Recherche parmis les champs suivants : Nom, Description, Groupe.'
  search_ux: Rechercher (nom et description)
  bookmark:
    save:
      success: Le tableau de bord a été ajouté aux favoris avec succès
    remove:
      success: Le tableau de bord a été retiré des favoris avec succès
  dashboard_default:
    save:
      success: Le tableau de bord par defaut a été activé avec succès
    remove:
      success: Le tableau de bord par defaut a été désactivé avec succès
  filter:
    save:
      success: Le filtre a été enregistré avec succès
    update:
      success: Le filtre a été mis à jour avec succès
    remove:
      success: Le filtre a été supprimé avec succès
    sidebar:
      title: '{0,1}%count% filtre actif|]1,Inf[ %count% filtres actifs'
      no_active_filter: Aucun filtre actif
      save_bookmark: Enregistrer en favoris
    mobile_dropdown:
      list:
        title: Filtres actifs
        add: Ajouter un filtre
      field_list:
        title: Ajouter un filtre
      config:
        title: Filtre par
        create: Appliquer le filtre
        update: Appliquer les modifications
        remove: Supprimer le filtre
        dynamic: Dynamique
        fixed: Fixe
    modal:
      duplicate_filter_warning: Vous avez déjà paramétré un filtre utilisant le champ %field%
      tabs:
        bookmarks:
          title: Mes filtres favoris
        data_filters:
          title: Données
          source: Source de données
          filter_choice: Sélection d'un filtre
        period_filters:
          title: Périodes
  btn:
    actions:
      mobile:
        help_document: Comprendre le tableau de bord
        save_on_plateforme: Enregistrer la sélection dans la Plateforme
        save_as_on_plateforme: Créer une nouvelle sélection vers la Plateforme
        export_pdf: Exporter en PDF
        share: Partager le tableau de bord
        auto_export_pdf: Créer un PDF automatisé
      desktop:
        help_document: Comprendre le tableau de bord
        visualisation: Visualisations
        group_save: Enregistrer
        save: Enregistrer
        save_as: Enregistrer sous
        save_on_plateforme: Enregistrer la sélection sur la Plateforme
        save_as_on_plateforme: Créer une nouvelle sélection vers la Plateforme
        group_action: Actions
        print: Imprimer
        export_pdf: Exporter au format PDF
        share: Partager
        auto_export_pdf: Créer un export PDF automatisé
        define_as_home_dashboard: Définir comme tableau de bord d'accueil
        remove_home_dashboard: Désactiver le tableau de bord d'accueil
        fullscreen_enter: Plein écran
        fullscreen_leave: Quitter le plein écran
    visualisation: Ajouter une visualisation
    filter: Ajouter un filtre
    bloc: Ajouter un bloc
    save: Enregistrer
    save-as: Enregistrer-sous
    print: Imprimer
    export: Exporter
    bookmark: Mes tableaux de bord favoris
    bookmark_add: Ajouter ce tableau de bord à mes favoris
    bookmark_remove: Retirer ce tableau de bord de mes favoris
    bookmark_edit: Gérer mes favoris
    pdf: Télécharger au format PDF
    external: Générer un lien de partage avec les filtres actuels
    schedule: Créer un export PDF automatisé
    requeteur:
      back: Retour sur la Plateforme
      export: Nouvelle sélection
      update: Mise à jour de la sélection
    grow: Agrandir la hauteur du tableau de bord
    shrink: Réduire la hauteur du tableau de bord
    add_row: Ajouter une ligne vide
  tooltips:
    home: Mettre en tableau de bord d'accueil
    bookmark: Ajouter aux tableaux de bord favoris
    share: Créer un lien de partage
  annotation:
    color: Couleur
    desc: Description
    title: Titre
  pdf:
    error:
      casper: Le serveur n'est pas configuré pour générer des fichiers PDF
      timeout: Erreur lors de la génération du fichier PDF
  schedule:
    success: L'export a été enregistré avec succès
    error: Une erreur s'est produite lors de la création de l'export
  dropdown:
    sort_by: Trier par
    default_filter: Choisir comme filtre par défaut
    number: Nombre
    direction: Direction
    last: Dernier(ères)s
    future: Prochain(e)s
    periode: Période
    input: Input
    record: Démarrer l'enregistrement
  filters_bookmark:
    name: Nom
    add_to: Ajouter aux filtres favoris
    update: Mettre à jour un favori
    create: Créer un nouveau filtre favori
    include: Inclure le filtre sur la date
    title: Mes filtres favoris
    add: Ajouter un filtre
    source: Source de données
  actions:
    request: Voir la requête
    legend: Afficher la légende
    fullscreen: Afficher en plein écran
    png: Télécharger au format png
    csv: Télécharger au format csv
    csv_email: Envoyer le csv par email
    csv_disabled: Un téléchargement est déjà en cours
    edit: Modifier la visualisation
    annotation: Ajouter une annotation
    lock: Bloquer les données
    filters: Voir les filtres
    remove: Supprimer du tableau de bord
  requeteurReference: Métric de référence pour la Plateforme
  requeteurReference_none: 'Aucun (comportement par défaut : comptage sur le CODECLIENT)'
  fieldToggle: Toggle Champ
  fieldToggle_none: 'Aucun'
  requeteurEnabled: Autoriser l'export dans la Plateforme
  comparisonEnabled: Activer la comparaison ?
  comparisonMagasinEnabled: Activer la comparaison magasins identiques ?
  aquitem: Aquitem
  import:
    title_page: Importer un dashboard
    error_json: La configuration JSON est invalide
    error: "Erreur : %error%"
  export:
    title_page: Exporter un dashboard
general:
  title_suffix: MyDataviz'
  equal: Egal
  different: Différent
  dashboard: tableau de bord
  source: source
  data_source: source de données
  visualisation: visualisation
  next: suivant
  prev: précédent
  search: Rechercher
  continue: continuer
  close: Fermer
  back: retour
  step_next: Étape suivante
  step_back: Étape précédente
  colon_punctuation: '&nbsp;:'
  'no': Non
  'yes': Oui
  edit: Modifier
  delete: Supprimer
  remove: Supprimer
  update: Mise à jour
  copy.btn: Copier
  copy.link: Copier le lien
  copy.success: Copié !
  save.changes: Enregistrer les modifications
  select.file: Sélectionner un fichier
  analyze: Analyser
  save: Enregistrer
  saveas: Enregistrer-sous
  duplicate: Dupliquer
  interpersonate: Simuler
  reset: Réinitialiser
  activate: Activer
  desactivate: Désactiver
  share: Partager
  exemple: Exemple
  validate: Valider
  send: Envoyer
  choose: choisir
  hide: cacher
  import: Importer
  export: Exporter
  paste_here: Collez ici
  unknow: inconnu
  upload: Uploader
  download: Télécharger
  reload: Actualiser
  preview: Prévisualiser
  cancel: Annuler
  apply: Appliquer
  actions: Actions
  select: Sélectionner
  select_all: Tout sélectionner
  unselect_all: Tout désélectionner
  consult: Consulter
  loading: Chargement en cours...
  ascending: Ascendant
  descending: Descendant
  link: Lien
  url: URL
  qr_code: QR Code
  scan: Scannez
  params: Paramètres
  none: Aucun
  default: Par défaut
  or: ou
  or_to: ou de
  to: au
  add: Ajouter
  config: Configuration
  infos: Informations
  color: Couleur
  colors: Couleurs
  color_scheme:
    __color_dark: Gris
    __color_white: Blanc
    __color_transparent: Transparent
  lang:
    en: Anglais
    fr: Français
  calcul:
    label: Calcul
    count: Comptage
    sum: Somme
    avg: Moyenne
    max: Maximum
    min: Minimum
    cardinality: Comptage unique
    std_deviation: Écart type
    reverse_nested: Comptage document
    moving_fn: Agrégation glissante
  regroup:
    date_histogram: Histogramme date
    histogram: Histogramme
    terms: Termes
    range_field: Range
    date_range: Range date
    none: Aucun
    geo_bounding_box: Geo Bounding Box
  file:
    invalid: Fichier invalide
    error:
      load: Erreur lors du chargement du fichier
    processing: Traitement des données ...
  form:
    error:
      empty: Cette valeur ne peut pas être vide
    required: Ce champ est obligatoire
    html5_validation_error: Ce champ n'est pas valide
  date:
    from: Du
    to: au
    day: jour
    month: mois
    year: année
    hour: heure
    minute: minute
    second: seconde
    days: jours
    months: mois
    years: années
    hours: heures
    minutes: minutes
    seconds: secondes
    startDate: Date de début
    startHour: Heure de début
    endDate: Date de fin
    endHour: Heure de fin
  confirm:
    delete: Voulez-vous vraiment supprimer <b>%label%</b> ?
    duplicate: Voulez-vous vraiment dupliquer <b>%label%</b> ?
  months:
    '1': Janvier
    '2': Février
    '3': Mars
    '4': Avril
    '5': Mai
    '6': Juin
    '7': Juillet
    '8': Août
    '9': Septembre
    '10': Octobre
    '11': Novembre
    '12': Décembre
  days:
    '1': Lundi
    '2': Mardi
    '3': Mercredi
    '4': Jeudi
    '5': Vendredi
    '6': Samedi
    '7': Dimanche
  filter:
    equal: Égal
    different: Différent
    periods: Périodes
    datas: Données
    my_favorite_filters: Mes filtres favoris
    label_separator:
      from: du
      to: au
      of: de
      at: à
      from_alone: à partir du
      to_alone: jusqu'au
      uc_first:
        from: Du
source:
  lists: Liste des sources
  add: Ajouter une source
  new: Nouvelle source
  edit: Modifier une source
  name: Nom de la source
  message.saving: 'Veuillez patienter, l''enregistrement de vos données est en cours...'
  tab:
    config: Configuration
    fields: Champs personnalisés
    mapping: Configuration des relations
    preview: Prévisualisation des données
    label: Personnalisation des libellés
    filters: Configuration des filtres
    update_date: Configuration de la date de mise à jour
    field-toggle: Toggle champ
  form:
    name: Nom
    method: Méthode d'importation
    import.file: Importer un fichier
    import.url: Importer via une url
    delete_type: Effacer les données sur le serveur Elasticsearch ?
    paste: Coller le contenu
    paste_csv: Coller le contenu de votre fichier CSV dans cette zone de texte
    format: format
    format.english: 'Format anglais : données séparées par des ,'
    format.french: 'Format français : données séparées par des ;'
    firstline: La première ligne contient le nom des colonnes
    first_records: 10 premiers enregistrements
    analyze.result: Résultats de l'analyse
    select_source: Selectionnez une source
    select_field: Selectionnez un champ
    default_filters:
      label: Filtres par défaut
      help: Les filtres par défaut correspondent aux filtres paramétrés par défaut lors de la mise en place des tableaux de bords
    request:
      help: Coller votre requête ici
      label: 'Requête :'
    table:
      label: Label
      type: Type
      custom_label: Label personnalisé
      custom_type: Type d'affichage
    choose.type: Choisissez un type
    source_type:
      empty: Type de source
      es: Elasticsearch
      csv: Fichier CSV
      request: Requête ES
    groupe:
      label: Client
      empty: Choisissez un groupe
    server:
      label: Serveur
      empty: Choisissez un serveur
    ip: Adresse IP
    port: Port
    index: Index
    private: Source privée
    type:
      string: Chaine de charactère
      keyword: Chaine de charactère
      integer: Nombre entier
      float: Nombre à virgule
      date: Date
      money: Monétaire
      percent: Pourcentage
      boolean: Booléen
      bytes: Bytes
      geo_point: Geo Point
      geo_shape: Geo Shape
      keywords: Mots-clés
    relation:
      add: Ajouter une relation
      count: Nombre de résultats
      column: Colonne de tri
      order: Ordre de tri
    update_date_field: Champ de date de mise à jour
    error:
      loadIndexes: Impossible de charger la liste des index
  field-toggle:
    field1: Champ 1
    field2: Champ 2
    field1Label: Label champ 1
    field2Label: Label champ 2
    add: Ajouter un toggle
    action: Actions
  list:
    search: Rechercher une source
    all: Toutes les sources
    nom: Source
    groupe: Groupe
    ip: IP
    type: Type d'import
    public: Publique
    server: Serveur
    elasticIndex: Index
    es_type: Type
    createdOn: Date de création
    edit: Modifier
    delete: Supprimer
    default: par défaut
  mapping:
    field: Champ
    source: Source
    mapping: Relation
    action: Actions
server:
  add: Ajouter un serveur
  new: Nouveau serveur
  lists: Liste des serveurs
  list:
    search: Rechercher un serveur
    name: Nom
    hosts: Adresse(s) IP
    reindexHosts: Adresse(s) IP interne pour la réindexation (optionnel)
    version: Version
    createdOn: Date de création
    edit: Modifier
    delete: Supprimer
  recap:
    reindex: Réindexer
    list:
      title: Récapitulatif des indexes
      dashboard: Tableaux de bord
      indexes: Indexes
      servers: Serveurs
  migrate:
    title: Migration source
  save:
    add: Ajouter le serveur
    help:
      hosts: >-
        Adresses IP avec port, séparées par des virgules (ex:
        "***********:9200,***********:9200")
alert:
  display_params: Paramètres d'affichage
  alert_group: Notification
  category: Catégorie
  locale: Langue
  categories:
    maintenance: Maintenance
    news: Nouveautés
  lead: Chapeau
  alert_details_group: Détail de la notification
  heading: En-tête
  video_link: Lien Youtube
  content: Contenu
  add: Ajouter une notification
  edit: Modifier une notification
  new: Nouvelle notification
  preview: Prévisualiser le détail
  lists: Liste des notifications
  read_more: Lire la suite
  form:
    title: Titre (50 caractères max)
  list:
    search: Rechercher une notification
    title: Titre
    category: Catégorie
    locale: Langue
    startDate: Date de début
    endDate: Date de fin
    createdAt: Date de création
    edit: Modifier
    delete: Supprimer
  save:
    add: Ajouter la notification
alert_user:
  export_async:
    pending: Téléchargement en cours
    started: Téléchargement en cours
    finished: Votre fichier est prêt
    error: Une erreur s'est produite, veuillez recommencer
    download: Télécharger
export:
  lists: Liste des exports
  add: Ajouter un export
  edit: Modifier un export
  form:
    activeFilters: 'Filtre actif|]1,Inf[ Filtres actifs'
    title: Libellé
    createdAt: Date de création
    filters: Filtres
    emails: Emails (séparés par des virgules)
    frequency: Fréquence d'envoi
    frequencyChoice: Nombre de jours d'intervalle personnalisé
    active: Actif
    frequencies:
      day: 1 fois par jour
      week: 1 fois par semaine
      month: 1 fois par mois
      custom: Personnalisée
    frequencies_by_value: >-
      {1}1 fois par jour|{7}1 fois par semaine|{30}1 fois par mois|]0,Inf[
      Tous les %count% jours
    retries: Tentative
    edit: Modifier
    delete: Supprimer
  list:
    search: Rechercher un export
    all: Tous les exports
    title: Libellé
    dashboard: Tableau de bord
    createdAt: Date de création
    filters: Filtres
    emails: Emails
    frequency: Fréquence d'envoi
    active: Actif
    nextExecution: Prochain envoi
  save:
    edit: Modifier l'export
visualisation:
  label: Visualisation
  lists: Liste des visualisations
  list:
    all: Toutes mes visualisations
    title: Nom
    dashboards: Tableaux de bord
    type: Type
    source: Source
    groupe: Groupe
    createdOn: Date de création
  name: Nom
  type: Type
  public: Publique
  source: Source
  createdOn: Date de création
  updatedOn: Date de modification
  search: Rechercher une visualisation
  search_placeholder: Entrer le(s) terme(s) à rechercher
  nb: Nb
  description: Description
  examples: Exemples
  add: Créer une visualisation
  add.filter: Ajouter un filtre
  adds: Ajouter des visualisations
  edit: Modifier une visualisation
  group: Groupement de visualisation
  new: Nouvelle visualisation
  form:
    success: Votre visualisation a été sauvegardée avec succès
    error: Erreur lors de l'enregistrement de votre visualisation
    message: >-
      Vous pouvez dès maintenant l'ajouter dans un tableau de bord déjà
      existant ou en créer un nouveau.
    steps: Étape %begin% sur %end%
    custom: Données & Personnalisation
    has_error: Votre formulaire contient des erreurs
    no_error: Votre formulaire ne contient pas d'erreurs
    select_source: Sélectionnez une source de données
    source_empty: Aucune source sélectionnée
    source_error: Veuillez sélectionner une source de données
    select_datas: Choisissez les données à afficher
    select_visu: Choisissez un type de visualisation
    select_calcul: Choix du calcul
    select_icons: Choix des icônes
    customize: Personnalisez votre visualisation
    drop: Déposer ici
    impossible: Impossible
    preview: 'Prévisualisation :'
    group: Visualisations à grouper
    datas: Données
    options: Personnaliser la visualisation
    firstOptionsBlock: Nom et description
    markdown: Stylisez votre texte avec le Markdown
    switch: Autres visualisations possibles
    schemes:
      no_result: Aucun résultat
      dashboardsUsed: 'Visualisation présente sur :'
      dashboards: Tableaux de bords
      clients: Palettes clients
      help_dashboard: Seuls les %maxShow% premiers tableaux de bord sont affichés
      help_client: Seuls les %maxShow% premières palettes clients sont affichées
      help_both: >-
        Seuls les %maxShow% premiers tableaux de bord et les %maxShow%
        premières palettes clients sont affichés
    filter:
      label: Filtre
      keep: Je garde
      exclude: J'exclus
      add_1: Ajouter un filtre
      add_2: en glissant un champ ici
      operators:
        =: Égal à
        lt: Inférieur à
        lte: Inférieur ou égal à
        gt: Supérieur à
        gte: Supérieur ou égal à
      summary:
        must: Je garde
        must_not: J'exclus
        values: les valeurs du champ %field%
        nullValues: les valeurs NULL du champ
      operatorsSummary:
        =: égales à
        lt: inférieures à
        lte: inférieures ou égales à
        gt: supérieures à
        gte: supérieures ou égales à
    icons:
      choose: Choisir les icônes
      type: Type d'icones
      single: Simple
      multiple: Multiple
      placeholder: Label personnalisé...
      options:
        label: Options des labels
        custom: Personnalisés ?
        id: Id
        value: Valeur
        percent: Pourcentage
    save:
      title:
        label: Titre
        help: Titre de la visualisation
      displayName:
        label: Nom du graphique
        help: Nom du graphique affiché dans le tableau de bord
        auto: Le titre sera automatique selon le paramétrage
      group: Groupe
      description:
        label: Aide
        help: Description de la visualisation
      private: Cette visualisation est privée ?
  debug:
    title: Requête Elasticsearch
    copy: Copier la requête
    copied: Requête copiée !
    filters: Filtres actuellement appliqués à la visualisation
  sample:
    title: Créer un sample
    copy: Copier le sample
    copied: Sample copié !
  types:
    lineChart:
      label: Ligne
      description: >-
        Cette visualisation représente un ensemble de points (visibles ou
        non), d'un ou plusieurs groupe de valeurs distincts, sur une grille
        X-Y, reliés par un segment (ligne qui va d'un point à un autre).
        L'ordonnée (axe des Y - vertical) représente les variations de
        valeurs de la données, l'abscisse (axe des X - horizontal)
        représente le plus souvent le temps.
         Cette visualisation est souvent utilisée pour faire apparaître des tendances au cours du temps.
      example: >-
        Nombre de ventes par mois ; Chiffre d’affaires par an ; Nombre de
        clients journalier
    pieChart:
      label: Camembert
      description: >-
        Cette visualisation permet de montrer la part d’un élément au sein
        d’un ensemble. Le total des parts d’un camembert est toujours égal à
        100%.
         Il est possible d'éxaminer plusieurs ensemble en même temps.
         Par exemple il est possible de visualiser une famille de poduits et ses sous produits.
      example: >-
        Répartition du total des ventes par secteur/rayons/magasins ;
        Proportion d’hommes/femmes ; Répartition des dépenses par pôle
    barChart:
      label: Barre
      description: >-
        Cette visualisation vous permet de comparer les performances d'un
        groupe de donnée.
         Un diagramme en bâtons représente un ensemble de données de même nature ; et donc qui peuvent être comparées. À chaque valeur est associée un segment vertical ou un rectangle dont la hauteur est proportionnelle à la valeur connue. Le rapport des hauteurs fournit le rapport des quantités correspondantes.
         Les données peuvent être facilement comparées à l'œil nu.
      example: 'Ventes par canal ; Ventes effectuées par secteur, par mois'
    bubbleChart:
      label: Bulles
      description: >-
        Cette visualisation permet une représentation des données en trois
        dimensions (axe x, axe y, taille de la bulle).
         Les graphiques à bulles peuvent être considérés comme une variante du graphique à nuage de points, dans lequel les points de données sont remplacés par des bulles. Si vos données comportent 3 dimensions.
         Les graphiques à bulles peuvent faciliter la compréhension des interactions sociales, économiques, médicales et autres interactions scientifiques.
         Par exemple dans un magasin proposant plusieurs gammes de produits, il sera possible de regarder le chiffre d'affaire réalisé (axe y) par jour (axe x) avec le nombre de produits vendus (taille de la bulle). Cela permet de visualiser un éventuel changement de comportement d'achat (moins de produit achetés mais plus de chiffre d'affaire indique une tendance d'achat de produit de luxe. A l'inverse plus de produits achetés mais une baisse de chiffre d'affaire indique une tendance d'achat de produits d'entrée de gamme.
      example: >-
        Taux de satisfaction par produits indépendamment du prix (Rapport
        qualité prix) ; Délai de paiement de clients, en fonction du prix dû
        ; Chiffres d’affaires par magasins en tenant compte des clients
        fidélisés
    tableChart:
      label: Tableau Top
      description: >-
        Cette visualisation permet d'affficher les valeurs de plusieurs
        données sous forme de top.
      example: >-
        Connaitre le top 1 des produits pour le top 20 des familles de produits rapportant le plus CA ;
        Connaitre la ville la plus peuplée pour le top 3 des régions les plus peuplées
    tableSimpleChart:
      label: Tableau
      description: >-
        Cette visualisation permet d'affficher les valeurs de plusieurs
        données sous forme de tableau.
      example: >-
        Nombre d’articles restants par rayon ; Récapitulatif du nombre
        d’employés par magasin ; Récapitulatif des salaires versés
    treemapChart:
      label: Treemap
      description: >-
        Les cartes proportionnelles montrent les parties d'un tout. Elles
        affichent l'information hiérarchique comme un emboitement de
        rectangles dont la taille et la couleur varie en fonction de la
        valeur associée.
         La taille de chaque rectangle représente une quantité et donc proportionnelle à la valeur qu'elle représente, alors que la couleur représente une catégorie de la donnée.
         Les cartes proportionnelles permettent d'identifier les tendances et faire des comparaisons rapidement.
         Il est possible de visualiser plusieurs données (groupe de catégories et sous-groupe de chaque catégorie).
      example: >-
        Vente par gammes de produits (en %) ; Ventes par canal ; Classement
        des performances effectuées
    heatmapChart:
      label: Heat Calendar
      description: >-
        Cette visualisation permet sous forme de calendrier, de faire
        correspondre chaque jour l'intensité de la valeur d'une donnée à un
        nuancier de couleurs.
         Ainsi vous repérez facillement les journées exeptionnelles tant positives que négatives.
      example: Nombre de visites par jour ; Nombre de réclamations par jour
    iconColorChart:
      label: Icônes
      description: >-
        Cette visualisation permet de comparer sous forme de remplissage
        d'icône les propotions des valeurs d'une donnée.
         Par exemple on pourra comparer la répartition des notes données lors d'une enquête.
         Il est possible de comparer X valeurs et pour chaque valeur il est possible de choisir l'icône et la couleur de remplissage.
    metricColorChart:
      label: Metric Couleur
      description: >-
        Cette visualisation permet d'afficher des valeurs clés d'une donnée
        (un chiffre d'affaire, le panier moyen des ventes ou encore le
        record du plus gros panier de vente)
      example: Chiffre d'affaires journalier ; Prix d’un panier moyen
    gaugeLiquidChart:
      label: Jauge liquide
      description: >-
        Cette visualisation permet de comparer une valeur à sa valeur
        maximal et minimal d'atteinte. Le remplissage de la jauge correspond
        au taux de la valeur.
         Par exemple on pourra visualiser la moyenne des notes obtenues lors d'une enquête par rapport à la note maximale.
      example: Taux de fidélité ; Taux de conversion
    streamgraphChart:
      label: Streamgraph
      description: >-
        Cette visualisation est une variante du graphique "Aire" standard.
        Avec un graphique en flux vous pouvez visualiser les valeurs
        cumulées de données au cours du temps. La ligne de base est décalée
        au centre des valeurs. Chaque couleur indique l'évolution d'une
        valeur au cours du temps.
      example: Performance de ventes d’articles au cours du temps
    clockChart:
      label: Horloge
      description: >-
        Cette visualisation permet d'afficher la répartition horaire d’une
        donnée sur une plage horaire définie. Il n'est cependant pas
        possible de définir plusieurs plages horaires
      example: >-
        Nombre de ventes par tranche horaire ; Nombre de visites par tranche
        horaire ; Temps d’attente par heure
    iconGaugeChart:
      label: icône Jauge
      description: |-
        Cette visualisation permet de comparer 2 valeurs d'une donnée.
         Par exemple on pourra comparer la répartition des genres.
         Il est possible de comparer X valeurs et pour chaque valeur il est possible de choisir l'icône et la couleur de la jauge pour chacune des valeurs.
    mapChart:
      label: Carte
      description: >-
        Cette visualisation vous permet de localiser visuellement les points
        stratégiques de vos données. Selon le zoom les points sont regroupés
        pour une meilleure interprétation, la taille du point vous permet
        d'identifier facilement les zones influentes.
         Il est necessaire que vos données à intégrer sur la carte soit localisées avec les coordonées GPS.
      example: Répartition des clients en France ; Répartition des magasins
    sankeyChart:
      label: Sankey
      description: >-
        Cette visulalisation est un type de diagramme de flux qui permet de
        visualiser l’association des éléments de données.
         Les associations des éléments sont représentées par des flèches dirigées, dont la largeur est proportionnelle à la quantité de flux visualisée. Si un flux est deux fois plus large, il représente le double de la quantité.
    pyramidChart:
      label: Pyramid
      description: >-
        Cette visualisation permet de représenter la répartition de deux
        valeurs d'une donnée selon une seconde donnée. Elle est constituée
        de deux histogrammes juxtaposés, un pour chaque valeur de la
        première donnée, où la seconde donnée est portée horizontalement et
        les valeurs de la première données verticalement.
         Le plus souvant cette visualisation sera utilisée pour représenter la répartition par sexe et âge d'une population où les effectifs sont portés horizontalement et les âges verticalement.
    wordcloudChart:
      label: Nuage de mots
      description: >-
        Le nuage de mot permet de mettre en exergue des mots selon une
        valeur.
         Cela permet par exemple de mettre en avant les mots les plus fréquement rencontrés dans les commentaires d'une enquête ou bien de mettre en avant les marques selon le chiffre d'affaire réalisé sur chacune d'entre elle.
    heatmapAxisChart:
      label: Heatmap
      description: >-
        Cette visualisation permet de faire  correspondre l'intensité d'une
        donnée à un nuancier de couleurs sur une matrice à deux dimensions.
         Ce procédé permet de donner à des données un aspect visuel plus facile à interpréter qu'un tableau de statistiques.
    listChart:
      label: Liste
      description: >-
        Cette visualisation permet d'affficher les valeurs de plusieurs
        données ayant des relations entre elles. Par exemple il est possible
        d'afficher le titre des parcours de randonnées et le temps moyen
        necessaire pour effectuer le parcours.
    textZone:
      label: Zone de texte
      description: >-
        Cela permet de mettre en place du texte et ou images sur votre
        tableau de bord afin de fournir une explication ou séparer
        différents thèmes de votre tableau de bord.
         Le format d'écriture utilisé est le Markdown (lien https://github.com/adam-p/markdown-here/wiki/Markdown-Cheatsheet)
    tableCrossedChart:
      label: Tableau croisé
      description: >-
        Un tableau croisé vous perment de regrouper des données selon leurs
        valeurs et faire une opération de calul entre elles.
         Par exemple il est possible de visualiser  la répartition du budget de plusieurs pôles d'activité d'une entreprise, selon l'allocation de ce budget (formation, employé, matériel...)
    interactiveBubbleChart:
      label: Bulles dynamiques
      description: Description de la visualisation Bulles dynamiques
    timeDotChart:
      label: Time Dot
      description: >-
        Cette visualisation permet une représentation des données en trois
        dimensions (axe heures, axe date, taille du point).Cette
        visualisation nécessite l'utilisation d'un champ date complet avec
        la date et l'heure.
         Cette visualisation vous permet de mettre en évidence la répartition d'une donnée sous forme de points en fonction de l'heure et d'une unité de temps choisie. La valeur est affichée sous forme de points ayant une taille proportionnelle à cette dernière.
         Par exemple vous pouvez visualiser le chiffre d'affaire réalisé selon l'heure et le mois de vente.
    selectorChart:
      label: Selecteur
      description: >-
        Cette visualisation vous permet d'afficher les valeurs d'une donnée
        sous forme de bouton.
         Cela permet d'intégrer des filtres rapidement acessibles, n'ayant pas besoin de mise en situation, mais qui sont souvent utilisés pour étudier les autres données
    barHChart:
      label: Barre Horizontale
      description: >-
        Contrairement à un diagramme en barre classique cette visualisation
        permet d'étudier la ventilation des valeurs de chaque composant.
         Par exemple sur une question nous allons étudier la ventilation des réponses selon le genre. On aura ainsi par exemple 75% oui et 25% nom pour les femmes et 63% oui et 37% non pour les hommes.
    radarChart:
      label: Radar
      description: >-
        Le radar montre des données multidimensionnelles sous la forme d'un
        graphique à deux dimensions de plus de trois variables représentées
        sur l'axe à partir du même point.
         Cette visualisation permet de tracer une ou plusieurs séries de valeur sur un axe gradué partant du point central pour chaque valeur. Ces axes sont disposés de façon équiangles les uns des autres.
    correlationMatrixChart:
      label: Matrice de correlation
      description: Description de la visualisation matrice de corrélation
    gaugeComparaisonChart:
      label: Jauge de comparaison
      description: >-
        Jauge permettant de comparer une valeur d'une période avec la valeur
        de la période précédente de même durée
    dotRepartitionChart:
      label: Répartition de points
      description: 100 points permettent de comprendre la répartition des valeurs
  import:
    title_page: Importer une visualisation
    error: "Erreur : %error%"
    error_json: La configuration JSON est invalide
  export:
    title_page: Exporter une visualisation
  samples:
    title: Données statiques
    description: Description

roles:
  admin: Super Administrateur
  cc: Chargé de clientèle
  enseigne: Admin client
  structure: Groupe
  magasin: Utilisateur
user:
  edit: Modifier un utilisateur
  add: Ajouter un utilisateur
  new: Nouvel utilisateur
  profile:
    form_field: Profil
    title: Mon profil
    id: Identifiant
    created: Créé le
    roles: Rôles
    groupes: Mes Groupes
    client: Enseigne
    edit:
      title: Modifier mon profil
      infos: Modifier mes infos personnelles
      password: Modifier mon mot de passe
      password_rules: >-
        Le nouveau mot de passe doit comporter au moins 8 caractères, une
        majuscule, une minuscule, un chiffre et un caractère spécial.
  lists: Liste des utilisateurs
  list:
    search: Rechercher un utilisateur
    all: Tous les utilisateurs
    username: Login
    firstname: Prénom
    lastname: Nom
    client: Client
    createdOn: Date de création
  email: Email
  username: Login
  firstname: Prénom
  lastname: Nom
  password: Mot de passe
  confirm: Confirmation
  primary_group: Client principal
  clients: Clients
  client: Client
  client_empty: Sélectionnez un client
  roles: Rôles
  groupes: Groupes
  createdOn: Date de création
  ccClient: Clients du chargé de clientèle
  interpersonate:
    begin: Simuler
    exit: Quitter la simulation
  concurrentLogin: Nombre de connexions simultanées
  desactivatedOn: Date de fin de validité du compte
  dashboardListingType: Type d'affichage du listing des tableaux de bord
  infos: Informations personnelles
  login_params: Paramètres de connexion
  permissions: Groupes et permissions
  edit_password: Modifier le mot de passe
  login: Connexion
  login_title: My Dataviz' - Groupe Aquitem
  logout: Déconnexion
  profil:
    edit: Modifier mon profil
  form:
    current_password: Mot de passe actuel
    new_password: Nouveau mot de passe
    new_password_confirmation: Répéter le nouveau mot de passe
    filters:
      label: Filtres
      source: Source
      select_source: Sélectionner une source
      selected: Filtres sélectionnés
      availability: L'ajout de filtre sur un utilisateur sera accessible dès que l'enregistrement sera réalisé une première fois.
      need_reload: Les groupes de l'utilisateurs ont été modifiés. Veuillez enregistrer l'utilisateur pour que la liste des sources disponibles soit actualisée.
  login_form:
    welcome: Bienvenue sur MyDataviz'
    login: Connectez-vous
    username: Identifiant
    password: Mot de passe
    email: Email
    remember: Se souvenir de moi
    forgotten: Mot de passe oublié ?
    click_here:
      click: Cliquez
      here: ici
      for: pour le réinitialiser
    forget:
      message: Entrez votre adresse e-mail pour réinitialiser votre mot de passe
    error:
      username: Le nom d'utilisateur est obligatoire
      password: Le mot de passe est obligatoire
      missing: Entrez un nom d'utilisateur et un mot de passe
    reset:
      title: Réinitialisation du mot de passe
      password: Choisissez un nouveau mot de passe
      sent: >-
        Un e-mail a été envoyé à votre adresse. Il contient un lien sur
        lequel il vous faudra cliquer afin de réinitialiser votre mot de
        passe.
      form_error: Veuillez renseigner votre identifiant et votre adresse e-mail.
    logged_out: Vous avez été déconnecté, veuillez vous authentifier à nouveau.
  defaut: 'Défaut : '
  lang:
    en: Anglais
    fr: Français
groupe:
  lists: Liste des groupes
  list:
    all: Tous les groupes
    search: Rechercher un groupe
    name: Nom
    type: Type
    createdOn: Date de création
  add: Ajouter un groupe
  new: Nouveau groupe
  edit: Modifier le groupe
  name: Nom
  type: Type
  client: Client
  createdOn: Date de création
  form:
    custom: Personnalisation
    custom_colors: Personnalisation de l'interface
    custom_dashboard: Personnalisation des tableaux de bord
    config: Configuration
    aquitem: Aquitem
    elastic: Elasticsearch
    idEnseigne: ID Enseigne
    requeteurUrl: URL Plateforme
    esLogin: Login Elasticsearch
    esPass: Mot de passe Elasticsearch
    concurrentLogin: Nombre de connexions simultanées (par défaut)
    concurrentDashboard: Nombre de tableaux de bord maximum
    client: Client
    members: Membres
    type: Type
    types:
      structure: Structure
      magasin: Magasin
    parent: Enseigne parente
    parent_empty: Choisissez une enseigne parente
    structure_parent: Structure Parente
    name: Nom
    csv: Dossier contenant les fichiers CSV
    salt: Sel
    prefix: Préfixe stockage CSV
    logo: 'Logo (format : png ou svg | hauteur : 55px)'
    theme: Thème
    colors:
      label: Couleurs
      btn: Modifier la palette de couleurs des visualisations
      from: Couleurs du client
      title: Palette de couleurs
      add: Ajouter une couleur
      preset: Mes palettes personnalisées
      name: Nom de la palette
      import: Importer une palette
      export: Exporter la palette
      save: Enregistrer la palette
      save_edit: Enregistrer les modifications
      remove_alerte: Êtes-vous sûr de vouloir supprimer la palette de couleur %name% ?
      generate: Générer la palette de couleur
      back_1: Couleur de fond 1
      back_2: Couleur de fond 2
      back_3: Couleur de fond 3
      front_1: Couleur de contraste 1
      front_2: Couleur de contraste 2
      front_3: Couleur de contraste 3
      dashboards_1: Couleur 1
      dashboards_2: Couleur 2
      dashboards_3: Couleur 3
      algo_1: Algorithme 1
      algo_2: Algorithme 2
      from_client: Palette de couleurs du client
      from_custom: Générer une palette
      save_scheme: Enregistrer dans mes palettes personnalisées
      auto: Automatique
      error: Veuillez choisir 9 couleurs pour pouvoir enregistrer la palette
      custom: Personnalisée
    icons:
      message: >-
        Choisissez une/des icône(s) au format .svg via le formulaire ou
        glissez les directement ici
      btn: Modifier les icônes personnalisées
      title: Icônes personnalisées
      file: Sélectionner un fichier
    images:
      message: >-
        Choisissez une/des image(s) au format .png/.jpg via le formulaire ou
        glissez les directement ici
      btn: Modifier les images personnalisées
      title: Images personnalisées
      file: Sélectionner un fichier
    can_csv: Droit de créer des sources à partir de fichier CSV
    can_include_current_day_filter: Droit d'inclure aujourd'hui dans les filtres dates dynamique
    has_async_table_export: Téléchargement asynchrone des tableaux CSV
    help:
      sel: TODO REMOVE
      prefix: TODO REMOVE
    user:
      list: Liste des utilisateurs dans le groupe
      add: Ajouter des utilisateurs au groupe
    plugin: Plugins autorisés
    pluginHorsAllowedByDefault: Plugins autorisés (hors plugins autorisés par défaut)
    lang: Langue
client:
  lists: Liste des clients
  add: Ajouter un client
  new: Nouveau client
  edit: Modifier le client
  list:
    search: Rechercher un client
    name: Nom
    createdOn: Date de création
  form:
    name: Nom (25 caractères maximum)
widgets: widgets
permission:
  name: Permissions
  list: Liste des permissions
  key: Clé
  libelle: Libellé
  defaultAccess: Activé par défaut
  parent: Groupe de permission
profil:
  lists: Liste des profils
  new: Nouveau profil
  list:
    search: Rechercher un profil
    libelle: Libellé
  libelle: Libellé
  type: Type
  client: Client
  add: Ajouter un profil
  edit: Modifier un profil
  form:
    auth: Autorisation
    client_empty: Tous les clients
    duplicate:
      placeholder: Libellé du nouveau profil
csv:
  list: Liste des CSV
  filename: Nom du fichier
  createdOn: Date de création
  lastUpdate: Dernière MAJ
  frequency: Fréquence
  source: Source
plugin:
  lists: Liste des plugins
  show: Consultation plugin
  config: Configuration plugins
  list:
    search: Rechercher un plugin
    name: Nom
    displayName: Nom
    installed: Installé
    uptodate: A jour
    usedby: Utilisations
    code: Code
  actions:
    show: Voir
    download: Installer / Mettre à jour
    update: Sauvegarder les modifications
    uninstall: Désinstaller
js:
  dashboard:
    bloc:
      title: Titre
    visualisations:
      title: Visualisations
      new: Nouvelle visualisation
      add: Ajouter au tableau de bord
      remove: Supprimer du tableau de bord
      error: Une erreur s'est produite
      fields:
        type: Type
        name: Nom
        source: Source
        updateDate: Modifié le
      loading:
        error: Erreur lors du chargement de la liste des visualisations
    loading:
      error: Erreur lors du chargement du tableau de bord
    colors:
      error: Ce nom est déjà pris
    pdf:
      generating: 'Votre PDF est en cours de création, veuillez patienter...'
      wait: Veuillez patienter
    capture_visualisation:
      generating: Votre capture est en cours de création, veuillez patienter...
    requeteur:
      exporting: Export des clients selectionnés vers la Plateforme...
      back: Retour sur la Plateforme
      back_operation: Afficher la sélection dans mon opération
      back_selection: Voir la sélection dans la Plateforme
      selection: Sélection
    filters:
      title: Filtrer
      empty: Aucun filtre n'est disponible sur ce tableau de bord
      none: Aucun filtre appliqué
      saved: Aucun filtre enregistré
      data: données
      period: période
      or: ou de
      bookmark:
        name: Nom du filtre favori
        none:
          no_filter_saved: Aucun filtre enregistré.
          create_or_save: Créez et enregistrez vos filtres de
          win_time: et gagnez du temps dans vos analyses.
          filters: ''

  modal:
    confirm: Confirmation
    message:
      delete: Voulez-vous vraiment supprimer
      delete_filters: Voulez-vous vraiment supprimer tous les filtres ?
  schedule:
    title: Créer un export PDF automatisé
  csv-export:
    title: Générer un export CSV
    sendByEmail: Envoyer par email
    error: Une erreur s'est produite. Veuillez réessayer.
    successEmail: Votre export vous sera envoyé par email une fois prêt.
    invalidEmails: Email(s) invalide(s).
  copy:
    link: Copier le lien de partage
    clipboard: Copier dans le presse papier
    done: Lien copié !
  visualisation:
    comptage: Comptage
    error: Erreur(s) lors de la création de la visualisation
    empty: Aucunes données correspondantes
    referrer: Retour au tableau de bord "%title%"
    remove: 'La visualisation sera supprimée des tableaux de bords suivants :'
    select: Veuillez sélectionner un type de visualisation
    step: Étape
    step_of: sur
    dashboards:
      title: Tableaux de bord utilisant ce type de visualisation
      clic: >-
        Cliquez ici pour afficher des exemples de tableaux de bord utilisant
        ce type de visualisation
      none: Aucun tableau de bord n'utilise ce type de visualisation
    popover:
      type: Type de champ
      method: Méthode de calcul
      choice: Choix du calcul
      buckets: 'Attention, seuls les %size% premiers résultats sont renvoyés.'
      regroup: 'Type de regroupement :'
      reverse_path: 'Niveau ciblé :'
      zone:
        add: Ajouter une donnée
    dropzone:
      data_required: Données requises
    save:
      message_1: Vous pouvez dès maintenant l'ajouter dans un
      message_2: tableau de bord déjà existant
      create_dashboard: créer un dashboard
      create_visu: créer une nouvelle visualisation
      back: Retour à la liste des visualisations
      list: Tableaux de bord utilisant cette visualisation
    form:
      colorpicker:
        label: Type de couleur
      color_palette_reference: Référence palette
      color_custom: Couleur personnalisée
      size: Nombre de résultats
      min_doc_count: Comptage minimum
      min_count: Comptage minimum
      plugin_not_allowed: >-
        Le plugin associé à cette visualisation n'est pas autorisé pour le
        client de l'utilisateur
      sort: Ordre de tri
      order: Sens de tri
      sort_agg: Colonne de tri
      _count: Valeur
      _term: Alphabétique
      _custom: Personnalisé
      desc: Décroissant
      asc: Croissant
      interval: Intervalle
      range: range
      nb_range: "Nombre de périodes"
      period: "Type de période"
      range_size: "Taille des périodes"
      custom_format: "Format d'affichage des libellés"
      shift: "Décalage par rapport à la date du jour"
      range_label: "Label des périodes"
      range_labels:
        from: "Début"
        to: "Fin"
        key: "Début - Fin"
      date_histogram:
        s: Secondes
        m: Minutes
        mm: Minutes seules
        H: Heures
        HH: Heures seules
        d: Jours
        E: Jours de la semaine
        w: Semaines
        M: Mois
        'y': Années
    comparison:
      title: Comparaison
      enable: Activer la comparaison
      simulate: Simuler la comparaison
  user:
    reconnect: >-
      Un autre appareil s'est connecté avec votre nom d'utilisateur et votre
      mot de passe. Vous allez être redirigé vers la page
      d'authentification.
    role: Veuillez sélectionner au moins un rôle.
  source:
    form:
      index: Choisissez un index
      type: Choisissez un type
      fill: Au moins un champ n'a pas été renseigné
    message:
      datas: >-
        Les données actuellement stockées seront remplacées par les
        nouvelles lors de la sauvegarde
    csv:
      error: Erreur lors de la lecture du CSV
      form:
        fill: Veuillez renseigner tous les champs du formulaire
        format: Format de stockage
      file: Fichier CSV
      syntax: Syntaxe invalide
      format: Format d'origine
    date_syntax: >-
      Veuillez respecter la syntaxe suivant pour renseigner le format de
      votre champ date :
    date_format: Formatage des dates
    success: Enregistrement réussi
    error: Erreur lors de l'enregistrement
    field_toggle:
      error_field: Veuillez choisir deux champs différents.
    index:
      creating: Création de l'index en cours ...
      created: Création de l'index
      done: Création de l'index réussi
      exist: l'index existe déjà
      exist_error: 'Erreur lors de la création de l''index : l''index %index% existe déjà'
      right: Vous n'avez pas les droits nécessaire
      deleted: Suppression de l'ancien index
      deleting: Suppression de l'ancien index en cours ...
      delete.error: Erreur lors de la suppression de l'ancien index
      delete.success: Suppression de l'ancien index
      insert:
        inserting: Insertion des données en cours ...
        upload: Envoi de votre fichier ...
        process: Analyse de votre fichier ...
        done: Insertion des données
        error: ERREUR
        errors: Insertion terminée avec %nb% erreur(s) sur %total% ligne(s)
      processing: Traitement des données ...
      ending: Finalisation...
    type:
      deleted: Suppression de l'ancien type
    list:
      message:
        delete: Les visualisations suivantes seront supprimées
  filters:
    hidden: Filtres masqués
    visible: Filtres visibles
    active: "Filtres actifs :"
    available: Aucun filtre disponible
    empty: Champ vide
    search_list:
      input: Rechercher une valeur
      value_equal: Valeur égale à
      value_different: Valeur différente de
      all_selected: Toutes les valeurs ont déjà été sélectionnées
      paste: Coller ici les valeurs recherchées les unes sous les autres sans séparateur
      reset: Réinitialiser
      no_result: Aucun résultat
      force: Elargir la recherche à toute la base
    geo:
      department: departement
      region: region
    dates:
      today: Aujourd'hui
      yesterday: Hier
      last_7_days: 7 derniers jours
      last_30_days: 30 derniers jours
      next_7_days: 7 prochains jours
      next_30_days: 30 prochains jours
      current_month: Mois en cours
      last_month: Mois précédent
      last_12_months: 12 derniers mois
      current_year: Année en cours
      last_year: Année précédente
      custom: Période personnalisée
      dynamic: Période dynamique
      fixed:
        year: Année
        semester: Semestre
        trimester: Trimestre
        month: Mois
        week: Semaine
        custom: Période personnalisée
        startDate: Date de début
        startTime: Heure de début
        endDate: Date de fin
        endTime: Heure de fin
        from_date: À partir de [DATE]
        to_date: Jusqu'à [DATE]
      from_date_today: La date de fin sera celle du jour
      to_date_today: La date de début sera celle du jour
    days:
      '1': L
      '2': M
      '3': M
      '4': J
      '5': V
      '6': S
      '7': D
    type:
      dynamic: Dynamique
      fixed: Fixe
    comparison:
      comparison: Comparaison
      compare: Comparer
      previous_year: année précédente
      previous_period: période précédente
      custom: période personnalisée
      disable: Désactiver
      enable: Activer
      warning_message: Une comparaison est déjà en cours, elle sera remplacée.
      comparison_magasin: Comparaison magasins identiques
  map:
    choose:
      departement: Choisissez vos départements
      region: Choisissez vos régions
  zoom:
    download: Télécharger au format image
    legend: Afficher/Masquer la légende
  annotation:
    select: Sélectionner un point sur la courbe.
grid:
  drop:
    here: Déposer ici
filter:
  form:
    keep: Je garde ...
    exclude: J'exclus ...
form:
  dropzone:
    here: Déposer votre fichier ou parcourir
  toastui:
    max_linebreaks: >-
      {0,1}%value% saut de ligne restant|]1,Inf[ %value% sauts de ligne
      restants
    max_characters: '{0,1}%value% car. restant|]1,Inf[ %value% car. restants'
flashbag:
  dashboard:
    create: Le tableau de bord a été créé avec succès
    edit: Le tableau de bord a été modifié avec succès
  server:
    create: Le serveur a été créé avec succès
    edit: Le serveur a été modifié avec succès
    delete: Le serveur a bien été supprimée
  alert:
    create: La notification a été créée avec succès
    edit: La notification a été modifiée avec succès
    delete: La notification a été supprimée avec succès
  export:
    create: L'export a été créé avec succès
    success: L'export a été enregistré avec succès
    edit: L'export a été modifié avec succès
    errorEmailInexistantInExport: L'email que vous avez tenté de supprimer n'est pas lié à cet export
    error: Une erreur s'est produite lors de la création de l'export
    deleteUser: Votre desinscription a bien été prise en compte
    delete: L'export a été supprimé avec succès
    unexist: L'export n'existe plus
  group:
    create: Le groupe a été ajouté avec succès
    edit: Le groupe a été modifié avec succès
  client:
    create: Le compte Client a été ajouté avec succès
    edit: Le compte Client a été modifié avec succès
    visulieespluginsupprime: Visualisation du client rattaché au(x) plugin(s) supprimé(s)
  user:
    create: L'utilisateur a été ajouté avec succès
    edit: L'utilisateur a été modifié avec succès
    infos: Vos informations personnelles ont été modifiées avec succès
    password: Votre mot de passe a été modifié avec succès
    exist: Ce nom d'utilisateur est déjà utilisé
    validGroup: Le(s) groupe(s) choisis ne sont pas valides
    reconnect: >-
      Un autre appareil s'est connecté avec votre nom d'utilisateur et votre
      mot de passe. Pour vous reconnecter à nouveau, veuillez entrer vos
      informations d'identification ci-dessous. Veuillez noter que l'autre
      appareil sera déconnecté.
  permission:
    create: La permission a été créée avec succès
    edit: La permission a été modifiée avec succès
  profil:
    create: Le profil a été créé avec succès
    edit: Le profil a été modifié avec succès
  source:
    delete: La source a bien été supprimée
    es: Les données ont bien été supprimées du serveur Elasticsearch
    error:
      delete: >-
        Erreur lors de la suppression des données sur le serveur
        Elasticsearch
      es: Erreur lors de la suppression de la source
requeteur:
  export:
    saveas_title: Créer une nouvelle sélection vers la Plateforme
    title: Enregistrer la sélection sur la Plateforme
    success: La sélection a été enregistrée avec succès
    error: Erreur lors de l'enregistrement de la sélection
    form:
      title: Titre de la sélection
      desc: Description de la sélection
Bad credentials.: Identifiants invalides
User account has expired.: Votre compte utilisateur a expiré
Bad credentials: Identifiants incorrects
Invalid CSRF token.: Erreur d'authentification
User account is disabled.: Votre compte est désactivé
resetting:
  check_email: >
    Un e-mail a été envoyé. Il contient un lien sur lequel il vous faudra
    cliquer pour réinitialiser votre mot de passe.

    Remarque : Vous ne pouvez demander un nouveau mot de passe que toutes
    les %tokenLifetime% heures.


    Si vous ne recevez pas un email, vérifiez votre dossier spam ou essayez
    à nouveau.
  request:
    username: Nom d'utilisateur ou adresse e-mail
    submit: Réinitialiser le mot de passe
  reset:
    submit: Modifier le mot de passe
  flash:
    success: Le mot de passe a été réinitialisé avec succès.
  email:
    subject: Réinitialisation de votre mot de passe
    message: >
      Bonjour %username% !


      Pour réinitialiser votre mot de passe, merci de vous rendre sur
      %confirmationUrl%


      Cordialement,

      L'équipe
permissions:
  Dashboard:
    dashboard_list: Lister les tableaux de bord
    dashboard_view: Voir des tableaux de bord
    dashboard_edit: Modifier des tableaux de bord
    dashboard_delete: Supprimer des tableaux de bord
    dashboard_share: Partager des tableaux de bord
    dashboard_create: Ajouter des tableaux de bord
    dashboard_add_widget: Ajouter des visualisations sur le tableau de bord
    dashboard_export_requeteur: Enregistrer une sélection sur la Plateforme
    dashboard_create_filter: Créer des filtres manuellement sur un tableau de bord
    export_manage: Créer des exports PDF automatisés
    dashboard_view_filters: Voir les filtres sur les visualisations
  Visualisation:
    visualisation_list: Lister les visualisations
    visualisation_create: Créer des visualisations
    visualisation_view: Voir des visualisations
    visualisation_edit: Modifier des visualisations
    visualisation_delete: Supprimer des visualisations
    visualisation_share: Partager des visualisations
  Source:
    source_list: Lister les sources
    source_create: Créer des sources
    source_edit: Modifier des sources
    source_view: Voir des sources
    source_delete: Supprimer des sources
  Utilisateur:
    user_list: Lister les utilisateurs
    user_view: Voir les utilisateurs
    user_edit: Modifier des utilisateurs
    user_desactivate: Désactiver des utilisateurs
    user_create: Créer des utilisateurs
    user_delete: Supprimer des utilisateurs
    user_edit_self: Modifier son propre profil
    user_edit_password: Modifier son mot de passe
    user_logout: Se déconnecter
    user_interpersonate: Simuler des utilisateurs
    user_add_home_dashboard: Ajouter un tableau de bord d'accueil
  Groupe:
    groupe_list: Lister les groupes
    groupe_edit: Modifier des groupes
    groupe_create: Créer des groupes
    groupe_delete: Supprimer des groupes
    groupe_view: Voir des groupes
  Client:
    client_view: Voir des clients
    client_create: Ajouter des clients
    client_edit: Modifier des clients
    client_list: Lister les clients
    client_delete: Supprimer des clients
  Profil:
    profil_list: Lister les profils
    profil_create: Créer des profils
    profil_edit: Modifier des profils
    profil_delete: Supprimer des profils
  Alert:
    alert_manage: Gestion des notifications
  groupsTitle:
    Dashboard: Tableaux de bord
    Visualisation: Visualisation
    Utilisateur: Utilisateur
    Groupe: Groupe
    Client: Client
    Profil: Profil
    Source: Source
    Alert: Notification
errors:
  '403': Vous n'êtes pas autorisé à consulter cette page
  '404': La page que vous demandez n'existe pas
  '500': Le serveur a rencontré une erreur lors du traitement de votre demande
  '503': Service indisponible
  error: Erreur
  maintenance: >-
    Nous améliorons votre expérience sur MyDataviz',<br>nous serons de
    retour dans peu de temps.<br><br>We are improving your experience on
    MyDataviz',<br>we will be back shortly.
  maintenance_title: Opération de maintenance
