<?php

namespace Alienor\ElasticBundle\DependencyInjection\Compiler;

use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\Reference;

class ElasticPopulatorPass implements CompilerPassInterface
{
    public function process(ContainerBuilder $container): void
    {
        // always first check if the primary service is defined
        if (!$container->has('alienor.elastic.populator_chain')) {
            return;
        }

        $definition = $container->findDefinition('alienor.elastic.populator_chain');

        // find all service IDs with the alienor.elastic.populator tag
        $taggedServices = $container->findTaggedServiceIds('alienor.elastic.populator');

        foreach ($taggedServices as $id => $tags) {
            // add the transport service to the ChainPopulator service
            $definition->addMethodCall('addPopulator', array(new Reference($id)));
        }
    }
}