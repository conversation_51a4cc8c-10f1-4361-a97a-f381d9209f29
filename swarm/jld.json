{"parser": "XMLParser", "nom": "<PERSON>", "server": "https://192.168.13.60:9200", "parameters": {"login": "jld", "pass": "OTE3YTU5"}, "alias": "jld_clients", "type": "_doc", "index_basename": "jld_clients", "period": "s", "filepath": "", "settings": {"number_of_shards": 1}, "mapping": {"properties": {"ACTIVITE": {"type": "keyword"}, "AGE": {"type": "integer"}, "SITE": {"type": "keyword"}, "CHEQUES_ENCAISSES": {"type": "nested", "include_in_parent": true, "properties": {"CANAL": {"type": "keyword"}, "CODECLIENT": {"type": "keyword"}, "DATEEMISSION": {"type": "date", "format": "strict_date"}, "DATEENCAISSEMENT": {"type": "date", "format": "strict_date"}, "DATEFINVALIDITE": {"type": "date", "format": "strict_date"}, "ENCAISSEMENT": {"type": "boolean"}, "MONTANT": {"type": "float"}, "NBJOUR_ENC": {"type": "long"}, "NBSEM_ENC": {"type": "float"}, "NUMEROCHEQUE": {"type": "keyword"}, "TYPECHEQUE": {"type": "keyword"}, "VALIDE": {"type": "keyword"}}}, "CHEQUES_NON_ENCAISSES": {"type": "nested", "include_in_parent": true, "properties": {"CANAL": {"type": "keyword"}, "CODECLIENT": {"type": "keyword"}, "DATEEMISSION": {"type": "date", "format": "strict_date"}, "DATEFINVALIDITE": {"type": "date", "format": "strict_date"}, "ENCAISSEMENT": {"type": "boolean"}, "MONTANT": {"type": "float"}, "NUMEROCHEQUE": {"type": "keyword"}, "TYPECHEQUE": {"type": "keyword"}, "VALIDE": {"type": "keyword"}}}, "CIVILITE": {"type": "keyword"}, "CLIENTMAGASIN": {"type": "keyword"}, "LIBELLEMAGASIN": {"type": "keyword"}, "MAGASIN": {"type": "keyword"}, "CLIENTNPAI": {"type": "boolean"}, "CLIENTZZZ": {"type": "boolean"}, "CODECLIENT": {"type": "keyword"}, "CODEPARETO_MG": {"type": "keyword"}, "CODEPOSTAL": {"type": "keyword"}, "REGION": {"type": "keyword"}, "REGION_CODE": {"type": "keyword"}, "DEPARTEMENT": {"type": "keyword"}, "DEPARTEMENT_CODE": {"type": "keyword"}, "CONTACT_COURRIER": {"type": "boolean"}, "CONTACT_EMAIL": {"type": "boolean"}, "CONTACT_SMS": {"type": "boolean"}, "DATECREATION": {"type": "date", "format": "basic_date"}, "DATENAISSANCE": {"type": "date", "format": "basic_date"}, "ENCARTE": {"type": "boolean"}, "MOIS_NAISSANCE": {"type": "integer"}, "NPAI_EMAIL": {"type": "boolean"}, "NPAI_SMS": {"type": "boolean"}, "SEGMENTATION_ACTUELLE": {"type": "keyword"}, "SEGMENTATION_N1": {"type": "keyword"}, "SEGMENTATION_N2": {"type": "keyword"}, "SEGMENTATION_N3": {"type": "keyword"}, "POSITION_GEO": {"type": "geo_point"}, "SEXE": {"type": "keyword"}, "STATUT_INSEE": {"type": "keyword"}, "VENTES": {"type": "nested", "include_in_parent": true, "properties": {"ANNIVERSAIRE": {"type": "boolean"}, "BIENVENUE": {"type": "boolean"}, "CHEQUE_FID": {"type": "boolean"}, "CODECLIENT": {"type": "keyword"}, "DATEVENTE": {"type": "date", "format": "basic_date"}, "ENCARTEE": {"type": "boolean"}, "HEUREVENTE": {"type": "date", "format": "HHmm"}, "IDVENTE": {"type": "keyword"}, "JOURVENTE": {"type": "keyword"}, "LIBELLEMAGASIN": {"type": "keyword"}, "LIGNES": {"type": "nested", "include_in_parent": true, "properties": {"CA": {"type": "float"}, "CODECLIENT": {"type": "keyword"}, "CODEFAMILLE": {"type": "keyword"}, "CODEGROUPE": {"type": "keyword"}, "CODEMARQUE": {"type": "keyword"}, "CODESOUSFAMILLE": {"type": "keyword"}, "CODETYPESERVICE": {"type": "keyword"}, "CODEZEFID": {"type": "keyword"}, "IDVENTE": {"type": "keyword"}, "LIBELLEARTICLE": {"type": "keyword"}, "LIBELLEFAMILLE": {"type": "keyword"}, "LIBELLEGROUPE": {"type": "keyword"}, "LIBELLEMARQUE": {"type": "keyword"}, "LIBELLESOUSFAMILLE": {"type": "keyword"}, "NB_PRODUITS": {"type": "long"}, "NUMERO_LIGNE": {"type": "keyword"}, "QTT": {"type": "long"}}}, "MAGASIN": {"type": "keyword"}, "NUMEROTICKET": {"type": "keyword"}, "REGLEMENTS": {"type": "nested", "include_in_parent": true, "properties": {"CHEQUEFIDELITE": {"type": "keyword"}, "CODECLIENT": {"type": "keyword"}, "CODEREGLEMENT": {"type": "keyword"}, "IDVENTE": {"type": "keyword"}, "NUMEROCHEQUE": {"type": "keyword"}, "TTC": {"type": "float"}}}, "SEXE": {"type": "keyword"}, "SEXE_CODECLIENT": {"type": "keyword"}, "SEGMENTATION_ACTUELLE_CODECLIENT": {"type": "keyword"}, "SITE": {"type": "keyword"}, "TTC": {"type": "float"}}}, "VILLE": {"type": "keyword"}}}}