{"parser": "XMLParser", "nom": "Saint Algue", "server": "https://192.168.13.60:9200", "parameters": {"login": "<PERSON><PERSON><PERSON>", "pass": "MmNlNmQy"}, "alias": "saint<PERSON>gue_clients", "type": "_doc", "index_basename": "saint<PERSON>gue_clients", "period": "s", "filepath": "", "settings": {"number_of_shards": 1}, "mapping": {"properties": {"ACTIVITE": {"type": "keyword"}, "AGE": {"type": "integer"}, "SITE": {"type": "keyword"}, "CODEENSEIGNE": {"type": "keyword"}, "CHEQUES": {"include_in_parent": true, "type": "nested", "properties": {"DATEFINVALIDITE": {"format": "strict_date", "type": "date"}, "TYPECHEQUE": {"type": "keyword"}, "VALIDE": {"type": "keyword"}, "CODECLIENT": {"type": "keyword"}, "DATEEMISSION": {"format": "strict_date", "type": "date"}, "CANAL": {"type": "keyword"}, "NBJOUR_ENC": {"type": "long"}, "NBSEM_ENC": {"type": "float"}, "ENCAISSEMENT": {"type": "boolean"}, "NUMEROCHEQUE": {"type": "keyword"}, "DATEENCAISSEMENT": {"format": "strict_date", "type": "date"}, "MONTANT": {"type": "float"}, "PERIODEANTERIEUR": {"type": "integer"}}}, "CIVILITE": {"type": "keyword"}, "CLIENTMAGASIN": {"type": "keyword"}, "LIBELLEMAGASIN": {"type": "keyword"}, "MAGASIN": {"type": "keyword"}, "CLIENTNPAI": {"type": "boolean"}, "CLIENTZZZ": {"type": "boolean"}, "CODECLIENT": {"type": "keyword"}, "CODEPARETO_MG": {"type": "keyword"}, "CODEPOSTAL": {"type": "keyword"}, "REGION": {"type": "keyword"}, "REGION_CODE": {"type": "keyword"}, "DEPARTEMENT": {"type": "keyword"}, "DEPARTEMENT_CODE": {"type": "keyword"}, "CONTACT_COURRIER": {"type": "boolean"}, "CONTACT_EMAIL": {"type": "boolean"}, "CONTACT_SMS": {"type": "boolean"}, "DATECREATION": {"type": "date", "format": "basic_date"}, "DATENAISSANCE": {"type": "date", "format": "basic_date"}, "ENCARTE": {"type": "boolean"}, "MOIS_NAISSANCE": {"type": "integer"}, "NPAI_EMAIL": {"type": "boolean"}, "NPAI_SMS": {"type": "boolean"}, "SEGMENTATION_ACTUELLE": {"type": "keyword"}, "SEGMENTATION_N1": {"type": "keyword"}, "SEGMENTATION_N2": {"type": "keyword"}, "SEGMENTATION_N3": {"type": "keyword"}, "POSITION_GEO": {"type": "geo_point"}, "SEXE": {"type": "keyword"}, "STATUT_INSEE": {"type": "keyword"}, "VENTES": {"type": "nested", "include_in_parent": true, "properties": {"EXCLUSION": {"type": "boolean"}, "ANNIVERSAIRE": {"type": "boolean"}, "BIENVENUE": {"type": "boolean"}, "CHEQUE_FID": {"type": "boolean"}, "CODECLIENT": {"type": "keyword"}, "CODEENSEIGNE": {"type": "keyword"}, "DATEVENTE": {"type": "date", "format": "basic_date"}, "ENCARTEE": {"type": "boolean"}, "HEUREVENTE": {"type": "date", "format": "HHmm"}, "IDVENTE": {"type": "keyword"}, "JOURVENTE": {"type": "keyword"}, "LIBELLEMAGASIN": {"type": "keyword"}, "LIGNES": {"type": "nested", "include_in_parent": true, "properties": {"CA": {"type": "float"}, "CODECLIENT": {"type": "keyword"}, "CODEFAMILLE": {"type": "keyword"}, "CODEGROUPE": {"type": "keyword"}, "CODEMARQUE": {"type": "keyword"}, "CODESOUSFAMILLE": {"type": "keyword"}, "CODETYPESERVICE": {"type": "keyword"}, "CODEZEFID": {"type": "keyword"}, "IDVENTE": {"type": "keyword"}, "LIBELLEARTICLE": {"type": "keyword"}, "LIBELLEFAMILLE": {"type": "keyword"}, "LIBELLEGROUPE": {"type": "keyword"}, "LIBELLEMARQUE": {"type": "keyword"}, "LIBELLESOUSFAMILLE": {"type": "keyword"}, "NB_PRODUITS": {"type": "long"}, "NUMERO_LIGNE": {"type": "keyword"}, "QTT": {"type": "long"}}}, "MAGASIN": {"type": "keyword"}, "NUMEROTICKET": {"type": "keyword"}, "REGLEMENTS": {"type": "nested", "include_in_parent": true, "properties": {"CODEREGLEMENT": {"type": "keyword"}, "CODECLIENT": {"type": "keyword"}, "TTC": {"type": "float"}, "CHEQUEFIDELITE": {"type": "keyword"}, "IDVENTE": {"type": "keyword"}, "NUMEROCHEQUE": {"type": "keyword"}, "DATEFINVALIDITE": {"format": "strict_date", "type": "date"}, "TYPECHEQUE": {"type": "keyword"}, "VALIDE": {"type": "keyword"}, "DATEEMISSION": {"format": "strict_date", "type": "date"}, "CANAL": {"type": "keyword"}, "NBJOUR_ENC": {"type": "long"}, "NBSEM_ENC": {"type": "float"}, "ENCAISSEMENT": {"type": "boolean"}, "DATEENCAISSEMENT": {"format": "strict_date", "type": "date"}, "MONTANT": {"type": "float"}, "PERIODEANTERIEUR": {"type": "integer"}}}, "SEXE": {"type": "keyword"}, "SEXE_CODECLIENT": {"type": "keyword"}, "SEGMENTATION_ACTUELLE_CODECLIENT": {"type": "keyword"}, "SITE": {"type": "keyword"}, "TTC": {"type": "float"}}}, "VILLE": {"type": "keyword"}}}}