const Encore = require('@symfony/webpack-encore');
const CopyPlugin = require("copy-webpack-plugin");
const dotenv = require("dotenv");
const TerserPlugin = require("terser-webpack-plugin");
const { ESBuildMinifyPlugin } = require('esbuild-loader')
const { NormalModuleReplacementPlugin } = require('webpack')
const SpeedMeasurePlugin = require("speed-measure-webpack-plugin");
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

// Manually configure the runtime environment if not already configured yet by the "encore" command.
// It's useful when you use tools that rely on webpack.config.js file.
if (!Encore.isRuntimeEnvironmentConfigured()) {
    Encore.configureRuntimeEnvironment(process.env.NODE_ENV || 'dev');
}

Encore
    // directory where compiled assets will be stored
    .setOutputPath('public/build/')
    // public path used by the web server to access the output path
    .setPublicPath('/build')
    // only needed for CDN's or subdirectory deploy
    //.setManifestKeyPrefix('build/')

    /*
     * ENTRY CONFIG
     *
     * Each entry will result in one JavaScript file (e.g. app.js)
     * and one CSS file (e.g. app.css) if your JavaScript imports CSS.
     */
    .addEntry('app', './assets/app.ts')
	.addEntry('fonts', './assets/styles/fonts.scss')
	.addEntry('bootstrap', './assets/styles/bootstrap.scss')
	.addEntry('pdf', './assets/styles/pdf.scss')

    // enables the Symfony UX Stimulus bridge (used in assets/bootstrap.js)
    .enableStimulusBridge('./assets/controllers.json')

    // When enabled, Webpack "splits" your files into smaller pieces for greater optimization.
    .splitEntryChunks()

    // enables the Symfony UX Stimulus bridge (used in assets/bootstrap.js)
    .enableStimulusBridge('./assets/controllers.json')

    // will require an extra script tag for runtime.js
    // but, you probably want this, unless you're building a single-page app
    .enableSingleRuntimeChunk()

    /*
     * FEATURE CONFIG
     *
     * Enable & configure other features below. For a full
     * list of features, see:
     * https://symfony.com/doc/current/frontend.html#adding-more-features
     */
    .cleanupOutputBeforeBuild()
    .enableBuildNotifications()
    .enableSourceMaps(!Encore.isProduction())
    // enables hashed filenames (e.g. app.abc123.css)
    .enableVersioning(Encore.isProduction())

    // configure Babel
    // .configureBabel((config) => {
    //     config.plugins.push('@babel/a-babel-plugin');
    // })

    // enables and configure @babel/preset-env polyfills
    .configureBabelPresetEnv((config) => {
        config.useBuiltIns = 'usage';
        config.corejs = '3.23';
    })

    // enables Sass/SCSS support
    .enableSassLoader((options) => {
	    options.warnRuleAsWarning = true;
	    options.sassOptions.quietDeps = true;
    })

    // uncomment if you use TypeScript
    .enableTypeScriptLoader()
	.enableForkedTypeScriptTypesChecking()


    // uncomment to get integrity="..." attributes on your script & link tags
    // requires WebpackEncoreBundle 1.4 or higher
    //.enableIntegrityHashes(Encore.isProduction())

	.addPlugin(new CopyPlugin(
		{
			patterns: [
				{
					from: './node_modules/@pdftron/pdfjs-express-viewer/public',
					to: './pdfjsexpress'
				}
			]
		}
	))

	.configureTerserPlugin((options) => {
		options.minify = TerserPlugin.esbuildMinify;
	})
	.configureLoaderRule('typescript', (options) => {
		options.use[0].loader = 'esbuild-loader';
		options.use[0].options = {
			loader: 'ts',
		};
	})
	.configureLoaderRule('javascript', (options) => {
		options.use[0].loader = 'esbuild-loader';
		options.use[0].options = {
			loader: 'js'
		};
	})


	.configureDefinePlugin(options => {
		const env = dotenv.config({ path: '.env.local', silent: true });

		if (env.error) {
			throw env.error;
		}

		options['process.env.PDFJS_LICENSE_KEY'] = JSON.stringify(env.parsed.PDFJS_LICENSE_KEY);
	})
;

let config = Encore.getWebpackConfig();

// webpackConfig.optimization.minimize = false;

const options = config.plugins[0].options;
const runtimeOptions = config.plugins[0].runtimeOptions;
config.plugins.splice(0, 1);

const configWithTimeMeasures = new SpeedMeasurePlugin().wrap(config);
configWithTimeMeasures.plugins.push(new MiniCssExtractPlugin(options, runtimeOptions));

module.exports = configWithTimeMeasures;
