<?php

namespace App\Tests\Entity;

use App\Entity\Company;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Zenstruck\Foundry\Test\Factories;

class CompanyTest extends WebTestCase
{
    use Factories;

    #[DataProvider('googleReviewUrlProvider')]
    public function testGoogleReviewUrlValidation(?string $url = null, bool $expected = true): void
    {
        $company = new Company();

        $company->setGoogleReviewUrl($url);

        $errors = self::getContainer()->get(ValidatorInterface::class)->validateProperty($company, 'googleReviewUrl');
        if ($expected) {
            $this->assertEquals(0, $errors->count());
        } else {
            $this->assertGreaterThan(0, $errors->count());
        }
    }

    public static function googleReviewUrlProvider(): array
    {
        return [
            ['https://g.page/r/XXXXXXXXXXXXXXX/review', true],
            ['https://g.pageZ/r/XXXXXXXXXXXXXXX/review', false],
            ['https://g.page/r/ABC123DEF456GHI/review', true],
            ['https://g.page/review', false],
            ['https://g.page/r//review', false],
            ['http://g.page/r/XXXXXXXXXXXXXXX/review', false],
            ['https://g.page/XXXXXXXXXXXXXXX/review', false],
            ['https://g.co/kgs/XXXXXXXXXXXXXXX', true],
            ['https://g.co/kgs/ABC123DEF456GHI', true],
            ['https://g.co/abc/ABC123DEF456GHI', false],
            ['https://search.google.com/local/writereview?placeid=XXXXXXXXXXXXXXX', true],
            ['https://search.google.com/local/writereview?placeid=ABC123DEF456GHI', true],
            ['https://search.google.com/local/wrongparam?placeid=ABC123DEF456GHI', false],
            ['https://search.google.com/local/writereview?wrongparam=ABC123DEF456GHI', false],
            ['', false],
            ['invalid-url', false],
            [null, true],
        ];
    }
}
