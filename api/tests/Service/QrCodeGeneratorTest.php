<?php

namespace App\Tests\Service;

use App\Factory\CompanyFactory;
use App\Service\QrCodeGenerator;
use Endroid\QrCode\Writer\Result\ResultInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Zenst<PERSON>ck\Foundry\Test\Factories;
use Zenst<PERSON>ck\Foundry\Test\ResetDatabase;

class QrCodeGeneratorTest extends KernelTestCase
{
    use Factories;
    use ResetDatabase;

    private QrCodeGenerator $qrCodeGenerator;

    protected function setUp(): void
    {
        $this->qrCodeGenerator = self::getContainer()->get(QrCodeGenerator::class);
    }

    public function testGenerateCompanyQrCode(): void
    {
        $company = CompanyFactory::createOne();

        $result = $this->qrCodeGenerator->generateCompanyQrCode($company);

        $this->assertEquals(sprintf('http://localhost:3000/create-customer-account/%s/token', $company->getId()), $this->qrCodeGenerator->getQrCodeTargetUrl($company));
        $this->assertInstanceOf(ResultInterface::class, $result);
        $this->assertStringContainsString('data:image/png;', $result->getDataUri());
    }
}
