<?php

namespace App\Tests\Service;

use App\Factory\CampaignAnniversaireFactory;
use App\Factory\CampaignBienvenueFactory;
use App\Factory\CampaignDormantFactory;
use App\Factory\CampaignExpirationChequeFactory;
use App\Factory\CampaignFideliteFactory;
use App\Factory\CampaignGoogleReviewFactory;
use App\Factory\CompanyFactory;
use App\Factory\CustomerFactory;
use App\Factory\CustomerTransactionFactory;
use App\Factory\SubscriptionFactory;
use App\Factory\VoucherFactory;
use App\Service\CampaignManager;
use App\Service\CustomerNotifier\LinkMobilityClient;
use App\Tests\Utils\MockFactory;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Clock\Clock;
use Symfony\Component\Clock\MockClock;
use Symfony\Component\Clock\Test\ClockSensitiveTrait;
use Zenstruck\Foundry\Test\Factories;

class CampaignManagerTest extends TestCase
{
    use Factories;
    use ClockSensitiveTrait;

    private CampaignManager $campaignManager;
    private \PHPUnit\Framework\MockObject\MockObject|LinkMobilityClient $linkMobilityMock;

    protected function setUp(): void
    {
        parent::setUp();

        $mockFactory = new MockFactory('');

        $this->linkMobilityMock = $mockFactory->getLinkMobilityMock();
        $this->campaignManager = $mockFactory->getCampaignManagerMock($this->linkMobilityMock);
        Clock::set(new MockClock('2023-07-01 00:00:00', 'Europe/Paris'));
    }

    public function testSendFideliteCampaign(): void
    {
        $customer = CustomerFactory::new()->create([
            'company' => CompanyFactory::new()->empty()->create([
                'subscription' => SubscriptionFactory::new([
                    'smsCredit' => 100,
                ]),
                'campaignFidelite' => CampaignFideliteFactory::createOne([
                    'earnedPoint' => 1,
                    'enabled' => true,
                    'spentAmount' => 1,
                    'thresholdPoint' => 100,
                    'voucherAmount' => 37,
                    'voucherEnabled' => true,
                    'voucherMaxAmount' => 100,
                    'voucherValidityMonth' => 3,
                ]),
            ]),
            'vouchers' => VoucherFactory::createSequence([
                [...VoucherFactory::AVAILABLE, 'amount' => 12],
                [...VoucherFactory::AVAILABLE, 'amount' => 9],
                [...VoucherFactory::AVAILABLE, 'amount' => 7],
            ]),
            'loyaltyPoints' => 100,
        ]);

        $this->linkMobilityMock->expects($this->once())->method('createIndividualCampaign');

        $this->campaignManager->sendFideliteCampaign($customer->_real(), Clock::get()->now());

        $this->assertEquals(99, $customer->getCompany()->getSubscription()->getSmsCredit());

        // Un sms est envoyé
        $this->assertEquals(1, $customer->getSms()->count());
        // On ne vérifie pas le chèque car ce n'est pas le notifier qui le créé
    }

    public function testSendFideliteCampaignButNotEnoughCredit(): void
    {
        $customer = CustomerFactory::new()->create([
            'company' => CompanyFactory::new()->empty()->create([
                'subscription' => SubscriptionFactory::new([
                    'smsCredit' => 0,
                ]),
                'campaignFidelite' => CampaignFideliteFactory::createOne([
                    'earnedPoint' => 1,
                    'enabled' => true,
                    'spentAmount' => 1,
                    'thresholdPoint' => 100,
                    'voucherAmount' => 37,
                    'voucherEnabled' => true,
                    'voucherMaxAmount' => 100,
                    'voucherValidityMonth' => 3,
                ]),
            ]),
            'vouchers' => [],
            'loyaltyPoints' => 100,
        ]);

        // Le sms n'est pas envoyé
        $this->linkMobilityMock->expects($this->never())->method('createIndividualCampaign');

        $this->campaignManager->sendFideliteCampaign($customer->_real(), Clock::get()->now());

        $this->assertEquals(0, $customer->getCompany()->getSubscription()->getSmsCredit());

        // Le sms n'est pas envoyé
        $this->assertEquals(0, $customer->getSms()->count());
        // On ne vérifie pas le chèque car ce n'est pas le notifier qui le créé
    }

    public function testSendFideliteCampaignDisabled(): void
    {
        $customer = CustomerFactory::new()->create([
            'company' => CompanyFactory::new()->empty()->create([
                'subscription' => SubscriptionFactory::new([
                    'smsCredit' => 100,
                ]),
                'campaignFidelite' => CampaignFideliteFactory::createOne([
                    'earnedPoint' => 1,
                    'enabled' => false,
                    'spentAmount' => 1,
                    'thresholdPoint' => 100,
                    'voucherAmount' => 37,
                    'voucherEnabled' => true,
                    'voucherMaxAmount' => 100,
                    'voucherValidityMonth' => 3,
                ]),
            ]),
            'vouchers' => [],
            'loyaltyPoints' => 100,
        ]);

        $this->linkMobilityMock->expects($this->never())->method('createIndividualCampaign');

        $this->campaignManager->sendFideliteCampaign($customer->_real(), Clock::get()->now());

        // Le sms n'est pas envoyé
        $this->assertEquals(0, $customer->getSms()->count());
        // On ne vérifie pas le chèque car ce n'est pas le notifier qui le créé
    }

    public function testSendBienvenueCampaign(): void
    {
        $customer = CustomerFactory::new()->create([
            'company' => CompanyFactory::new()->empty()->create([
                'subscription' => SubscriptionFactory::new([
                    'smsCredit' => 100,
                ]),
                'campaignBienvenue' => CampaignBienvenueFactory::createOne([
                    'enabled' => true,
                    'voucherAmount' => 15,
                    'voucherEnabled' => true,
                    'voucherValidityMonth' => 1,
                ]),
            ]),
            'vouchers' => VoucherFactory::createSequence([
                [...VoucherFactory::AVAILABLE, 'amount' => 12],
                [...VoucherFactory::AVAILABLE, 'amount' => 9],
                [...VoucherFactory::AVAILABLE, 'amount' => 7],
            ]),
            'loyaltyPoints' => 100,
        ]);

        $this->linkMobilityMock->expects($this->once())->method('createIndividualCampaign');

        $this->campaignManager->sendBienvenueCampaign($customer->_real(), Clock::get()->now());

        // Un chèque a été ajouté
        $this->assertEquals(15, $customer->getVouchers()[3]->getAmount());
        // Valable à partir du lendemain
        $this->assertEquals(new \DateTimeImmutable('2023-07-02 00:00:00'), $customer->getVouchers()[3]->getAvailabilityDate());
        // Valable pendant 1 mois
        $this->assertEquals(new \DateTimeImmutable('2023-08-01 00:00:00'), $customer->getVouchers()[3]->getExpirationDate());
        $this->assertEquals(99, $customer->getCompany()->getSubscription()->getSmsCredit());

        // Le sms est envoyé
        $this->assertEquals(1, $customer->getSms()->count());
        $this->assertEquals($customer->getVouchers()[3], $customer->getSms()->first()->getVoucher());
    }

    public function testSendGoogleReviewCampaign(): void
    {
        $customer = CustomerFactory::new()->create([
            'company' => CompanyFactory::new()->empty()->create([
                'subscription' => SubscriptionFactory::new([
                    'smsCredit' => 100,
                ]),
                'campaignGoogleReview' => CampaignGoogleReviewFactory::createOne([
                    'enabled' => true,
                ]),
            ]),
            'transactions' => CustomerTransactionFactory::new()->withoutPersisting()->sequence([
                ['amount' => 100, 'createdAt' => new \DateTime('2023-01-02 00:00:00')],
                ['amount' => 100, 'createdAt' => new \DateTime('2023-01-03 00:00:00')],
            ]),
        ]);

        $this->linkMobilityMock->expects($this->once())->method('createIndividualCampaign');

        $this->campaignManager->sendGoogleReviewCampaign($customer->_real(), Clock::get()->now());

        $this->assertEquals(99, $customer->getCompany()->getSubscription()->getSmsCredit());

        // Le sms est envoyé
        $this->assertEquals(1, $customer->getSms()->count());
    }

    public function testSendBienvenueCampaignButNotEnoughCredit(): void
    {
        $customer = CustomerFactory::new()->create([
            'company' => CompanyFactory::new()->empty()->create([
                'subscription' => SubscriptionFactory::new([
                    'smsCredit' => 0,
                ]),
                'campaignBienvenue' => CampaignBienvenueFactory::createOne([
                    'enabled' => true,
                    'voucherAmount' => 16,
                    'voucherEnabled' => true,
                ]),
            ]),
            'vouchers' => [],
            'loyaltyPoints' => 100,
        ]);

        // Le sms n'est pas envoyé
        $this->linkMobilityMock->expects($this->never())->method('createIndividualCampaign');

        $this->campaignManager->sendBienvenueCampaign($customer->_real(), Clock::get()->now());

        // Un chèque a été ajouté
        $this->assertEquals(16, $customer->getVouchers()[0]->getAmount());
        $this->assertEquals(0, $customer->getCompany()->getSubscription()->getSmsCredit());

        // Le sms n'est pas envoyé
        $this->assertEquals(0, $customer->getSms()->count());
    }

    public function testSendBienvenueCampaignDisabled(): void
    {
        $customer = CustomerFactory::new()->create([
            'company' => CompanyFactory::new()->empty()->create([
                'subscription' => SubscriptionFactory::new([
                    'smsCredit' => 100,
                ]),
                'campaignBienvenue' => CampaignBienvenueFactory::createOne([
                    'enabled' => false,
                    'voucherAmount' => 17,
                    'voucherEnabled' => true,
                ]),
            ]),
            'vouchers' => [],
            'loyaltyPoints' => 100,
        ]);

        $this->linkMobilityMock->expects($this->never())->method('createIndividualCampaign');

        $this->campaignManager->sendBienvenueCampaign($customer->_real(), Clock::get()->now());

        // Aucun chèque n'a été ajouté
        $this->assertEquals(0, count($customer->getVouchers()));
        $this->assertEquals(100, $customer->getCompany()->getSubscription()->getSmsCredit());

        // Le sms n'est pas envoyé
        $this->assertEquals(0, $customer->getSms()->count());
    }

    public function testSendBienvenueCampaignEnabledVoucherDisabled(): void
    {
        $customer = CustomerFactory::new()->create([
            'company' => CompanyFactory::new()->empty()->create([
                'subscription' => SubscriptionFactory::new([
                    'smsCredit' => 100,
                ]),
                'campaignBienvenue' => CampaignBienvenueFactory::createOne([
                    'enabled' => true,
                    'voucherAmount' => 18,
                    'voucherEnabled' => false,
                ]),
            ]),
            'vouchers' => [],
            'loyaltyPoints' => 100,
        ]);

        $this->linkMobilityMock->expects($this->once())->method('createIndividualCampaign');

        $this->campaignManager->sendBienvenueCampaign($customer->_real(), Clock::get()->now());

        // Aucun chèque n'a été ajouté
        $this->assertEquals(0, count($customer->getVouchers()));
        $this->assertEquals(99, $customer->getCompany()->getSubscription()->getSmsCredit());

        // Le sms est envoyé
        $this->assertEquals(1, $customer->getSms()->count());
        $this->assertNull($customer->getSms()->first()->getVoucher());
    }

    public function testSendAnniversaireCampaign(): void
    {
        $customer = CustomerFactory::new()->create([
            'company' => CompanyFactory::new()->empty()->create([
                'subscription' => SubscriptionFactory::new([
                    'smsCredit' => 100,
                ]),
                'campaignAnniversaire' => CampaignAnniversaireFactory::createOne([
                    'enabled' => true,
                    'voucherAmount' => 15,
                    'voucherEnabled' => true,
                    'voucherValidityMonth' => 1,
                ]),
            ]),
            'vouchers' => VoucherFactory::createSequence([
                [...VoucherFactory::AVAILABLE, 'amount' => 12],
                [...VoucherFactory::AVAILABLE, 'amount' => 9],
                [...VoucherFactory::AVAILABLE, 'amount' => 7],
            ]),
        ]);

        $anotherCustomer = CustomerFactory::new()->create([
            'company' => $customer->getCompany(),
            'vouchers' => VoucherFactory::createSequence([
                [...VoucherFactory::AVAILABLE, 'amount' => 12],
                [...VoucherFactory::AVAILABLE, 'amount' => 9],
                [...VoucherFactory::AVAILABLE, 'amount' => 7],
            ]),
        ]);

        $this->linkMobilityMock->expects($this->once())->method('createBatchCampaign');

        $this->campaignManager->sendAnniversaireCampaigns([$customer->_real(), $anotherCustomer->_real()], $customer->getCompany()->getCampaignAnniversaire(), Clock::get()->now());

        // Un chèque a été ajouté à chacun des customers
        $this->assertEquals(15, $customer->getVouchers()[3]->getAmount());
        $this->assertEquals(15, $anotherCustomer->getVouchers()[3]->getAmount());
        // Valable pendant 1 mois
        $this->assertEquals(new \DateTimeImmutable('2023-08-01 00:00:00'), $customer->getVouchers()[3]->getExpirationDate());
        $this->assertEquals(98, $customer->getCompany()->getSubscription()->getSmsCredit());

        // Le sms est envoyé
        $this->assertEquals(1, $customer->getSms()->count());
        $this->assertEquals(1, $anotherCustomer->getSms()->count());
        $this->assertEquals($customer->getVouchers()[3], $customer->getSms()->first()->getVoucher());
    }

    public function testSendAnniversaireCampaignButNotEnoughCredit(): void
    {
        $customer = CustomerFactory::new()->create([
            'company' => CompanyFactory::new()->empty()->create([
                'subscription' => SubscriptionFactory::new([
                    'smsCredit' => 1,
                ]),
                'campaignAnniversaire' => CampaignAnniversaireFactory::createOne([
                    'enabled' => true,
                    'voucherAmount' => 16,
                    'voucherEnabled' => true,
                ]),
            ]),
            'vouchers' => [],
            'loyaltyPoints' => 100,
        ]);

        $anotherCustomer = CustomerFactory::new()->create([
            'company' => $customer->getCompany(),
            'vouchers' => [],
        ]);

        // Le sms n'est pas envoyé
        $this->linkMobilityMock->expects($this->never())->method('createBatchCampaign');

        $this->campaignManager->sendAnniversaireCampaigns([$customer->_real(), $anotherCustomer->_real()], $customer->getCompany()->getCampaignAnniversaire(), Clock::get()->now());

        // Un chèque a été ajouté
        $this->assertEquals(16, $customer->getVouchers()[0]->getAmount());
        $this->assertEquals(16, $anotherCustomer->getVouchers()[0]->getAmount());
        $this->assertEquals(1, $customer->getCompany()->getSubscription()->getSmsCredit());

        // Le sms n'est pas envoyé
        $this->assertEquals(0, $customer->getSms()->count());
        $this->assertEquals(0, $anotherCustomer->getSms()->count());
    }

    public function testSendAnniversaireCampaignDisabled(): void
    {
        $customer = CustomerFactory::new()->create([
            'company' => CompanyFactory::new()->empty()->create([
                'subscription' => SubscriptionFactory::new([
                    'smsCredit' => 100,
                ]),
                'campaignAnniversaire' => CampaignAnniversaireFactory::createOne([
                    'enabled' => false,
                    'voucherAmount' => 17,
                    'voucherEnabled' => true,
                ]),
            ]),
            'vouchers' => [],
            'loyaltyPoints' => 100,
        ]);

        $anotherCustomer = CustomerFactory::new()->create([
            'company' => $customer->getCompany(),
            'vouchers' => [],
        ]);

        $this->linkMobilityMock->expects($this->never())->method('createBatchCampaign');

        $this->campaignManager->sendAnniversaireCampaigns([$customer->_real(), $anotherCustomer->_real()], $customer->getCompany()->getCampaignAnniversaire(), Clock::get()->now());

        // Aucun chèque n'a été ajouté
        $this->assertEquals(0, count($customer->getVouchers()));
        $this->assertEquals(0, count($anotherCustomer->getVouchers()));
        $this->assertEquals(100, $customer->getCompany()->getSubscription()->getSmsCredit());

        // Le sms n'est pas envoyé
        $this->assertEquals(0, $customer->getSms()->count());
        $this->assertEquals(0, $anotherCustomer->getSms()->count());
    }

    public function testSendAnniversaireCampaignEnabledVoucherDisabled(): void
    {
        $customer = CustomerFactory::new()->create([
            'company' => CompanyFactory::new()->empty()->create([
                'subscription' => SubscriptionFactory::new([
                    'smsCredit' => 100,
                ]),
                'campaignAnniversaire' => CampaignAnniversaireFactory::createOne([
                    'enabled' => true,
                    'voucherAmount' => 18,
                    'voucherEnabled' => false,
                ]),
            ]),
            'vouchers' => [],
            'loyaltyPoints' => 100,
        ]);

        $anotherCustomer = CustomerFactory::new()->create([
            'company' => $customer->getCompany(),
            'vouchers' => [],
        ]);

        $this->linkMobilityMock->expects($this->once())->method('createBatchCampaign');

        $this->campaignManager->sendAnniversaireCampaigns([$customer->_real(), $anotherCustomer->_real()], $customer->getCompany()->getCampaignAnniversaire(), Clock::get()->now());

        // Aucun chèque n'a été ajouté
        $this->assertEquals(0, count($customer->getVouchers()));
        $this->assertEquals(0, count($anotherCustomer->getVouchers()));
        $this->assertEquals(98, $customer->getCompany()->getSubscription()->getSmsCredit());

        // Le sms est envoyé
        $this->assertEquals(1, $customer->getSms()->count());
        $this->assertEquals(1, $anotherCustomer->getSms()->count());
        $this->assertNull($customer->getSms()->first()->getVoucher());
    }

    public function testSendDormantCampaign(): void
    {
        $customer = CustomerFactory::new()->create([
            'company' => CompanyFactory::new()->empty()->create([
                'subscription' => SubscriptionFactory::new([
                    'smsCredit' => 100,
                ]),
                'campaignDormant' => CampaignDormantFactory::createOne([
                    'enabled' => true,
                    'voucherAmount' => 25,
                    'voucherEnabled' => true,
                    'voucherValidityMonth' => 2,
                ]),
            ]),
            'vouchers' => VoucherFactory::createSequence([
                [...VoucherFactory::AVAILABLE, 'amount' => 12],
                [...VoucherFactory::AVAILABLE, 'amount' => 9],
                [...VoucherFactory::AVAILABLE, 'amount' => 7],
            ]),
        ]);

        $anotherCustomer = CustomerFactory::new()->create([
            'company' => $customer->getCompany(),
            'vouchers' => VoucherFactory::createSequence([
                [...VoucherFactory::AVAILABLE, 'amount' => 12],
                [...VoucherFactory::AVAILABLE, 'amount' => 9],
                [...VoucherFactory::AVAILABLE, 'amount' => 7],
            ]),
        ]);

        $this->linkMobilityMock->expects($this->once())->method('createBatchCampaign');

        $this->campaignManager->sendDormantCampaigns([$customer->_real(), $anotherCustomer->_real()], $customer->getCompany()->getCampaignDormant(), Clock::get()->now());

        // Un chèque a été ajouté à chacun des customers
        $this->assertEquals(25, $customer->getVouchers()[3]->getAmount());
        $this->assertEquals(new \DateTimeImmutable('2023-09-01 00:00:00'), $customer->getVouchers()[3]->getExpirationDate());
        // Valable pendant 2 mois
        $this->assertEquals(25, $anotherCustomer->getVouchers()[3]->getAmount());
        $this->assertEquals(98, $customer->getCompany()->getSubscription()->getSmsCredit());

        // Le sms est envoyé
        $this->assertEquals(1, $customer->getSms()->count());
        $this->assertEquals(1, $anotherCustomer->getSms()->count());
        $this->assertEquals($customer->getVouchers()[3], $customer->getSms()->first()->getVoucher());
    }

    public function testSendDormantCampaignButNotEnoughCredit(): void
    {
        $customer = CustomerFactory::new()->create([
            'company' => CompanyFactory::new()->empty()->create([
                'subscription' => SubscriptionFactory::new([
                    'smsCredit' => 1,
                ]),
                'campaignDormant' => CampaignDormantFactory::createOne([
                    'enabled' => true,
                    'voucherAmount' => 26,
                    'voucherEnabled' => true,
                ]),
            ]),
            'vouchers' => [],
            'loyaltyPoints' => 100,
        ]);

        $anotherCustomer = CustomerFactory::new()->create([
            'company' => $customer->getCompany(),
            'vouchers' => [],
        ]);

        // Le sms n'est pas envoyé
        $this->linkMobilityMock->expects($this->never())->method('createBatchCampaign');

        $this->campaignManager->sendDormantCampaigns([$customer->_real(), $anotherCustomer->_real()], $customer->getCompany()->getCampaignDormant(), Clock::get()->now());

        // Un chèque a été ajouté
        $this->assertEquals(26, $customer->getVouchers()[0]->getAmount());
        $this->assertEquals(26, $anotherCustomer->getVouchers()[0]->getAmount());
        $this->assertEquals(1, $customer->getCompany()->getSubscription()->getSmsCredit());

        // Le sms n'est pas envoyé
        $this->assertEquals(0, $customer->getSms()->count());
        $this->assertEquals(0, $anotherCustomer->getSms()->count());
    }

    public function testSendDormantCampaignDisabled(): void
    {
        $customer = CustomerFactory::new()->create([
            'company' => CompanyFactory::new()->empty()->create([
                'subscription' => SubscriptionFactory::new([
                    'smsCredit' => 100,
                ]),
                'campaignDormant' => CampaignDormantFactory::createOne([
                    'enabled' => false,
                    'voucherAmount' => 27,
                    'voucherEnabled' => true,
                ]),
            ]),
            'vouchers' => [],
            'loyaltyPoints' => 100,
        ]);

        $anotherCustomer = CustomerFactory::new()->create([
            'company' => $customer->getCompany(),
            'vouchers' => [],
        ]);

        $this->linkMobilityMock->expects($this->never())->method('createBatchCampaign');

        $this->campaignManager->sendDormantCampaigns([$customer->_real(), $anotherCustomer->_real()], $customer->getCompany()->getCampaignDormant(), Clock::get()->now());

        // Aucun chèque n'a été ajouté
        $this->assertEquals(0, count($customer->getVouchers()));
        $this->assertEquals(0, count($anotherCustomer->getVouchers()));
        $this->assertEquals(100, $customer->getCompany()->getSubscription()->getSmsCredit());

        // Le sms n'est pas envoyé
        $this->assertEquals(0, $customer->getSms()->count());
        $this->assertEquals(0, $anotherCustomer->getSms()->count());
    }

    public function testSendDormantCampaignEnabledVoucherDisabled(): void
    {
        $customer = CustomerFactory::new()->create([
            'company' => CompanyFactory::new()->empty()->create([
                'subscription' => SubscriptionFactory::new([
                    'smsCredit' => 100,
                ]),
                'campaignDormant' => CampaignDormantFactory::createOne([
                    'enabled' => true,
                    'voucherAmount' => 28,
                    'voucherEnabled' => false,
                ]),
            ]),
            'vouchers' => [],
            'loyaltyPoints' => 100,
        ]);

        $anotherCustomer = CustomerFactory::new()->create([
            'company' => $customer->getCompany(),
            'vouchers' => [],
        ]);

        $this->linkMobilityMock->expects($this->once())->method('createBatchCampaign');

        $this->campaignManager->sendDormantCampaigns([$customer->_real(), $anotherCustomer->_real()], $customer->getCompany()->getCampaignDormant(), Clock::get()->now());

        // Aucun chèque n'a été ajouté
        $this->assertEquals(0, count($customer->getVouchers()));
        $this->assertEquals(0, count($anotherCustomer->getVouchers()));
        $this->assertEquals(98, $customer->getCompany()->getSubscription()->getSmsCredit());

        // Le sms est envoyé
        $this->assertEquals(1, $customer->getSms()->count());
        $this->assertEquals(1, $anotherCustomer->getSms()->count());
        $this->assertNull($customer->getSms()->first()->getVoucher());
    }

    public function testSendExpirationChequeCampaign(): void
    {
        $customer = CustomerFactory::new()->create([
            'company' => CompanyFactory::new()->empty()->create([
                'subscription' => SubscriptionFactory::new([
                    'smsCredit' => 100,
                ]),
                'campaignExpirationCheque' => CampaignExpirationChequeFactory::createOne([
                    'enabled' => true,
                ]),
            ]),
            'vouchers' => VoucherFactory::createSequence([
                [...VoucherFactory::AVAILABLE, 'amount' => 12],
                [...VoucherFactory::AVAILABLE, 'amount' => 9],
                [...VoucherFactory::AVAILABLE, 'amount' => 7],
            ]),
        ]);

        $anotherCustomer = CustomerFactory::new()->create([
            'company' => $customer->getCompany(),
            'vouchers' => VoucherFactory::createSequence([
                [...VoucherFactory::AVAILABLE, 'amount' => 12],
                [...VoucherFactory::AVAILABLE, 'amount' => 9],
                [...VoucherFactory::AVAILABLE, 'amount' => 7],
            ]),
        ]);

        $this->linkMobilityMock->expects($this->once())->method('createBatchCampaign');

        $this->campaignManager->sendExpirationChequeCampaigns([$customer->_real(), $anotherCustomer->_real()], $customer->getCompany()->getCampaignExpirationCheque(), Clock::get()->now());

        $this->assertEquals(98, $customer->getCompany()->getSubscription()->getSmsCredit());

        // Le sms est envoyé
        $this->assertEquals(1, $customer->getSms()->count());
        $this->assertEquals(1, $anotherCustomer->getSms()->count());
        $this->assertNull($customer->getSms()->first()->getVoucher());
    }

    public function testSendExpirationChequeCampaignButNotEnoughCredit(): void
    {
        $customer = CustomerFactory::new()->create([
            'company' => CompanyFactory::new()->empty()->create([
                'subscription' => SubscriptionFactory::new([
                    'smsCredit' => 1,
                ]),
                'campaignExpirationCheque' => CampaignExpirationChequeFactory::createOne([
                    'enabled' => true,
                ]),
            ]),
            'vouchers' => [],
            'loyaltyPoints' => 100,
        ]);

        $anotherCustomer = CustomerFactory::new()->create([
            'company' => $customer->getCompany(),
            'vouchers' => [],
        ]);

        // Le sms n'est pas envoyé
        $this->linkMobilityMock->expects($this->never())->method('createBatchCampaign');

        $this->campaignManager->sendExpirationChequeCampaigns([$customer->_real(), $anotherCustomer->_real()], $customer->getCompany()->getCampaignExpirationCheque(), Clock::get()->now());

        $this->assertEquals(1, $customer->getCompany()->getSubscription()->getSmsCredit());

        // Le sms n'est pas envoyé
        $this->assertEquals(0, $customer->getSms()->count());
        $this->assertEquals(0, $anotherCustomer->getSms()->count());
    }

    public function testSendExpirationChequeCampaignDisabled(): void
    {
        $customer = CustomerFactory::new()->create([
            'company' => CompanyFactory::new()->empty()->create([
                'subscription' => SubscriptionFactory::new([
                    'smsCredit' => 100,
                ]),
                'campaignExpirationCheque' => CampaignExpirationChequeFactory::createOne([
                    'enabled' => false,
                ]),
            ]),
            'vouchers' => [],
            'loyaltyPoints' => 100,
        ]);

        $anotherCustomer = CustomerFactory::new()->create([
            'company' => $customer->getCompany(),
            'vouchers' => [],
        ]);

        $this->linkMobilityMock->expects($this->never())->method('createBatchCampaign');

        $this->campaignManager->sendExpirationChequeCampaigns([$customer->_real(), $anotherCustomer->_real()], $customer->getCompany()->getCampaignExpirationCheque(), Clock::get()->now());

        $this->assertEquals(100, $customer->getCompany()->getSubscription()->getSmsCredit());

        // Le sms n'est pas envoyé
        $this->assertEquals(0, $customer->getSms()->count());
        $this->assertEquals(0, $anotherCustomer->getSms()->count());
    }

    public function testSendIndividualCampaignToInternationalCustomer(): void
    {
        $customer = CustomerFactory::new()->create([
            'phoneNumber' => '1522 343333',
            'phoneNumberCountryCode' => '49',
            'company' => CompanyFactory::new()->empty()->create([
                'subscription' => SubscriptionFactory::new([
                    'smsCredit' => 100,
                ]),
                'campaignFidelite' => CampaignFideliteFactory::createOne([
                    'earnedPoint' => 1,
                    'enabled' => true,
                    'thresholdPoint' => 100,
                    'voucherAmount' => 37,
                    'voucherEnabled' => false,
                    'voucherMaxAmount' => 100,
                    'voucherValidityMonth' => 3,
                ]),
            ]),
            'loyaltyPoints' => 100,
        ]);

        $this->campaignManager->sendFideliteCampaign($customer->_real(), Clock::get()->now());

        // 2 crédits consommé pour les numéros de téléphone non français
        $this->assertEquals(98, $customer->getCompany()->getSubscription()->getSmsCredit());

        // Un sms est envoyé
        $this->assertEquals(1, $customer->getSms()->count());
    }

    public function testSendBatchCampaignToInternationalCustomers(): void
    {
        $customer = CustomerFactory::new()->create([
            'phoneNumber' => '1522343333',
            'phoneNumberCountryCode' => 'de',
            'company' => CompanyFactory::new()->empty()->create([
                'subscription' => SubscriptionFactory::new([
                    'smsCredit' => 100,
                ]),
                'campaignAnniversaire' => CampaignAnniversaireFactory::createOne([
                    'enabled' => true,
                    'voucherAmount' => 15,
                    'voucherEnabled' => true,
                    'voucherValidityMonth' => 1,
                ]),
            ]),
        ]);

        $anotherCustomer = CustomerFactory::new()->create([
            'phoneNumber' => '0400000000',
            'phoneNumberCountryCode' => 'be',
            'company' => $customer->getCompany(),
        ]);

        $anotherCustomerButFrench = CustomerFactory::new()->create([
            'phoneNumber' => '0600000000',
            'phoneNumberCountryCode' => 'fr',
            'company' => $customer->getCompany(),
        ]);

        $this->linkMobilityMock->expects($this->once())->method('createBatchCampaign');

        $this->campaignManager->sendAnniversaireCampaigns(
            [$customer->_real(), $anotherCustomer->_real(), $anotherCustomerButFrench->_real()],
            $customer->getCompany()->getCampaignAnniversaire(), Clock::get()->now()
        );

        // 1 crédit FR + (2 crédits consommés * 2 customers) = 5 crédits
        $this->assertEquals(95, $customer->getCompany()->getSubscription()->getSmsCredit());

        // Le sms est envoyé
        $this->assertEquals(1, $customer->getSms()->count());
        $this->assertEquals(1, $anotherCustomer->getSms()->count());
        $this->assertEquals(1, $anotherCustomerButFrench->getSms()->count());
    }
}
