<?php

namespace App\Tests\Api;

use App\Entity\Company;
use App\Factory\CompanyFactory;
use App\Factory\SubscriptionFactory;
use App\Security\Voter\CompanyActiveVoter;

class CompanyInactiveSecurityTest extends AbstractTestCase
{
    /**
     * Liste des operations sur lesquels il faut tester que la route n'est pas accessible si l'abonnement est expiré.
     * On teste toutes les operations dans un seul test (sans dataProvider) pour gagner
     * en rapidité d'exécution.
     *
     * /!\ Nous n'essayons pas d'obtenir un code 200 sur chaque route, car ces cas sont déjà testé ailleurs.
     * Le but est uniquement de s'assurer que la sécurité est en place.
     */
    protected static function getPaths(): array
    {
        return [
            // Payment ❌
            [404, 'GET', '/api/check-payment-session', []],
            // Campaign ❌
            [403, 'PATCH', '/api/companies/{companyId}/campaign_anniversaires/{id}', []],
            [403, 'PATCH', '/api/companies/{companyId}/campaign_bienvenues/{id}', []],
            [403, 'PATCH', '/api/companies/{companyId}/campaign_dormants/{id}', []],
            [403, 'PATCH', '/api/companies/{companyId}/campaign_expiration_cheques/{id}', []],
            [403, 'PATCH', '/api/companies/{companyId}/campaign_fidelites/{id}', []],
            [403, 'PATCH', '/api/companies/{companyId}/campaign_google_reviews/{id}', []],
            // CampaignSmS ❌
            [403, 'GET', '/api/companies/{companyId}/campaign_sms', []],
            [403, 'POST', '/api/companies/{companyId}/campaign_sms', []],
            [403, 'POST', '/api/companies/{companyId}/campaign_sms/count-customers', []],
            // Customer ❌
            [403, 'GET', '/api/companies/{companyId}/customers', []],
            [403, 'POST', '/api/companies/{companyId}/customers', []],
            [403, 'GET', '/api/companies/{companyId}/customers/{id}', []],
            [403, 'PATCH', '/api/companies/{companyId}/customers/{id}', []],
            [403, 'PATCH', '/api/companies/{companyId}/customers/{id}/loyalty-points', []],
            [403, 'DELETE', '/api/companies/{companyId}/customers/{id}', []],
            // Company ❌✅
            [200, 'GET', '/api/companies/{companyId}/check-payment-subscription', []],
            [403, 'POST', '/api/companies/{companyId}/check-pin', []],
            [403, 'PATCH', '/api/companies/{companyId}/config', []],
            [403, 'POST', '/api/companies/{companyId}/forgot-pin', []],
            [403, 'GET', '/api/companies/{companyId}/generate-qr-code', []],
            //            [200, 'GET', '/api/companies/{companyId}/get-stripe-portal-url', []], Pas testable appel Stripe
            [403, 'PATCH', '/api/companies/{companyId}/reminder', []],
            //            [200, 'GET', '/api/companies/{companyId}/start-payment-sms', []], Pas testable appel Stripe
            //            [200, 'GET', '/api/companies/{companyId}/start-payment-subscription', []], Pas testable appel Stripe
            [403, 'GET', '/api/companies/{companyId}/stats', []],
            [403, 'PATCH', '/api/companies/{companyId}/stats', []],
            // CompanyDraft ✅
            [400, 'POST', '/api/company_drafts', []],
            [404, 'GET', '/api/company_drafts/{token}/validate-email', []],
            [400, 'POST', '/api/company_drafts/check-email-availability', []],
            // CustomerHistory ❌
            [403, 'GET', '/api/customers/{customerId}/customer_histories', []],
            // CustomerTransaction ❌
            [403, 'POST', '/api/customers/{customerId}/customer_transactions', []],
            // Profile ✅
            [200, 'GET', '/api/profile', []],
            // RichSms ✅
            [404, 'GET', '/api/rich_sms/{smsId}', ['{smsId}' => 1]],
            // Security ✅
            [400, 'POST', '/auth', []],
            // ForgotPassword ✅
            [400, 'POST', '/forgot-password/', []],
            [404, 'GET', '/forgot-password/{tokenValue}', []],
            [400, 'POST', '/forgot-password/{tokenValue}', []],
        ];
    }

    public function testInactiveSubscriptionSecurity(): void
    {
        $company = self::createInactiveCompany();
        $paths = self::getPaths();

        foreach ($paths as $path) {
            [$status, $method, $pattern, $params] = $path;

            $url = self::buildUrl($company->_real(), $pattern, $params ?? []);
            $headers = ['Content-Type' => 'PATCH' === $method ? 'application/merge-patch+json' : 'application/json'];
            $body = [];

            $this->browser()
                ->actingAs($company)
                ->request($method, $url, [
                    ...$body,
                    'headers' => $headers,
                ])
                ->assertStatus($status)
                ->assertJsonContains(403 === $status ? [
                    'hydra:title' => 'An error occurred',
                    'hydra:description' => CompanyActiveVoter::ERROR_MESSAGE_INACTIVE,
                ] : [])
            ;
        }
    }

    public function testArchivedSecurity(): void
    {
        $company = self::createArchivedCompany();
        $paths = self::getPaths();

        foreach ($paths as $path) {
            [$status, $method, $pattern, $params] = $path;

            $url = self::buildUrl($company->_real(), $pattern, $params ?? []);
            $headers = ['Content-Type' => 'PATCH' === $method ? 'application/merge-patch+json' : 'application/json'];
            $body = [];

            $this->browser()
                ->actingAs($company)
                ->request($method, $url, [
                    ...$body,
                    'headers' => $headers,
                ])
                ->assertStatus($status)
                ->assertJsonContains(403 === $status ? [
                    'hydra:title' => 'An error occurred',
                    'hydra:description' => CompanyActiveVoter::ERROR_MESSAGE_ARCHIVED,
                ] : [])
            ;
        }
    }

    /**
     * Retourne une Company avec un abonnement inactif.
     */
    protected static function createInactiveCompany()
    {
        $company = CompanyFactory::new([
            'subscription' => SubscriptionFactory::new([
                'active' => false,
            ]),
        ])->empty()->create();

        return $company;
    }

    /**
     * Retourne une Company archivée depuis le BO.
     */
    protected static function createArchivedCompany()
    {
        return CompanyFactory::new()->archived()->empty()->create();
    }

    /**
     * Construction de l'URL de l'opération.
     */
    protected static function buildUrl(Company $company, string $pattern, array $params)
    {
        $params['{companyId}'] = $company->getId();
        $url = $pattern;
        foreach ($params as $key => $param) {
            $url = str_replace($key, $param, $url);
        }

        return $url;
    }
}
