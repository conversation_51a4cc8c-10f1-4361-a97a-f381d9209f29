<?php

namespace App\Tests\Api;

use App\Entity\Company;
use App\Enum\CompanyHistoryType;
use App\Factory\CompanyFactory;
use App\Factory\CompanyHistoryFactory;
use App\Service\FrontEndRouter;
use App\Service\StripeClient;
use PHPUnit\Framework\Attributes\Group;
use PHPUnit\Framework\MockObject\MockObject;
use Stripe\BillingPortal\Session;
use Stripe\Stripe;
use Symfony\Component\Clock\Clock;
use Symfony\Component\Clock\MockClock;
use Symfony\Component\Clock\Test\ClockSensitiveTrait;
use Symfony\Contracts\Cache\CacheInterface;

class CompanyTest extends AbstractTestCase
{
    use ClockSensitiveTrait;

    public function testUpdateConfig(): void
    {
        $company = CompanyFactory::createOne(['senderId' => 'Alienor', 'senderIdVerified' => true]);
        $anotherCompany = CompanyFactory::createOne();
        $companyEmail = $company->getEmail();

        // Fonctionne
        $this->browser()
            ->actingAs($company)
            ->patch('/api/companies/'.$company->getId().'/config', [
                'json' => [
                    'pin' => '1111',
                    'email' => '<EMAIL>',
                    'plainPassword' => 'Azerty123!!!',
                    'firstname' => 'Paul',
                    'lastname' => 'Pierre',
                    'phoneNumber' => '0789545045',
                    'name' => 'Ma super entreprise',
                    'senderId' => 'Alienor',
                    'activityArea' => Company::ACTIVITY_AREAS[0],
                    'customActivityArea' => 'Boulangerie Patisserie',
                    'postalCode' => '33110',
                    'city' => 'Le Bouscat',
                    'googleReviewUrl' => 'https://g.page/r/XXXXXXXXXXXXXXX/review',
                ],
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
            ])
            ->assertSuccessful()
            ->assertMatchesResourceOutputJsonSchema(Company::class, Company::OPERATION_UPDATE_CONFIG);

        // l'email ne doit pas être modifié
        $this->assertEquals($company->getEmail(), $companyEmail);
        // senderIdVerified reste à true puisque le champ name n'a pas été modifié
        $this->assertTrue($company->isSenderIdVerified());

        // senderIdVerified est égale à null puisque le champ name a été modifié
        $this->browser()
            ->actingAs($company)
            ->patch('/api/companies/'.$company->getId().'/config', [
                'json' => [
                    'senderId' => 'DunderMiff',
                ],
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
            ])
            ->assertSuccessful()
            ->assertMatchesResourceOutputJsonSchema(Company::class, Company::OPERATION_UPDATE_CONFIG);
        $this->assertNull($company->isSenderIdVerified());

        // Vérification des droits
        $this->browser()
            ->actingAs($anotherCompany)
            ->patch('/api/companies/'.$company->getId().'/config', [
                'json' => [],
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
            ])
            ->assertStatus(403);
    }

    public function testUpdateReminder(): void
    {
        $company = CompanyFactory::createOne();
        $anotherCompany = CompanyFactory::createOne();

        // Fonctionne
        $this->browser()
            ->actingAs($company)
            ->patch('/api/companies/'.$company->getId().'/reminder', [
                'json' => ['reminder' => 'Ne pas oublier'],
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
            ])
            ->assertSuccessful()
            ->assertMatchesResourceJsonSchemas(Company::class, Company::OPERATION_SET_REMINDER);

        $this->assertEquals('Ne pas oublier', $company->getReminder());

        // Chaine de caractère trop longue
        $this->browser()
            ->actingAs($company)
            ->patch('/api/companies/'.$company->getId().'/reminder', [
                'json' => ['reminder' => str_repeat('abcdefghijklmnopqrstuvwxyz', 50)],
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
            ])
            ->assertViolations([
                [
                    'propertyPath' => 'reminder',
                    'message' => 'Cette chaîne est trop longue. Elle doit avoir au maximum 180 caractères.',
                ],
            ]);

        // Vérification des droits
        $this->browser()
            ->actingAs($anotherCompany)
            ->patch('/api/companies/'.$company->getId().'/reminder', [
                'json' => ['reminder' => 'Normalement bloqué'],
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
            ])
            ->assertStatus(403);
    }

    public function testGetStripePortalUrl(): void
    {
        $company = CompanyFactory::createOne();
        $anotherCompany = CompanyFactory::createOne();

        // construction de l'objet que createPortal doit retourner
        $stripeSession = new Session();
        $stripeSession->url = 'https://billing.stripe.com/xxxxx';

        // création du mock
        $mock = $this->getMockedStripeClient();
        $mock->method('createPortal')->willReturn($stripeSession);

        $useuse = function () use ($mock) {
            // remplacement du service par un mock dans le container
            self::getContainer()->set(StripeClient::class, $mock);
        };

        // Une company peut récupérer le lien vers son portail Stripe
        $this->browser()
            ->use($useuse)
            ->actingAs($company)
            ->get('/api/companies/'.$company->getId().'/get-stripe-portal-url', [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
            ])
            ->assertSuccessful()
            ->assertJsonContains([
                'url' => 'https://billing.stripe.com/xxxxx',
            ])
            ->assertMatchesResourceOutputJsonSchema(Company::class, Company::OPERATION_GET_STRIPE_PORTAL_URL);

        // Une company ne peut pas récupérer le lien vers le portail Stripe d'une autre company
        $this->browser()
            ->use($useuse)
            ->actingAs($company)
            ->get('/api/companies/'.$anotherCompany->getId().'/get-stripe-portal-url', [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
            ])
            ->assertStatus(403);
    }

    public function testDeleteCompany(): void
    {
        $mock = $this->getMockedStripeClient();

        $useuse = function () use ($mock) {
            // remplacement du service par un mock dans le container
            self::getContainer()->set(StripeClient::class, $mock);
        };

        $company = CompanyFactory::createOne(['name' => 'entreprise avec un nom unique', 'companyType' => Company::COMPANY_TYPE_DEFAULT]);
        $anotherCompany = CompanyFactory::createOne(['name' => 'entreprise avec un autre nom unique', 'companyType' => Company::COMPANY_TYPE_DEFAULT]);

        // Une company n'a pas le droit de supprimer une autre company
        $this->browser()
            ->actingAs($company)
            ->use($useuse)
            ->delete('/api/companies/'.$anotherCompany->getId())
            ->assertStatus(403);

        CompanyFactory::repository()->assert()->exists(['name' => 'entreprise avec un autre nom unique']);

        // Une company peut se supprimer elle même
        $this->browser()
            ->actingAs($company)
            ->use($useuse)
            ->delete('/api/companies/'.$company->getId())
            ->assertStatus(204);

        CompanyFactory::repository()->assert()->notExists(['name' => 'entreprise avec un nom unique']);

        // On vérifie que la company est bien archivée en bdd avec l'extension softdeleteable
        $em = self::getContainer()->get('doctrine')->getManager();
        $em->getFilters()->disable('softdeleteable');
        $archivedCompany = CompanyFactory::repository()->findOneBy(['name' => 'entreprise avec un nom unique'])->_disableAutoRefresh();
        $this->assertNotNull($archivedCompany->getDeletedAt());
        $this->assertTrue($archivedCompany->isDeleted());
        $this->assertFalse($archivedCompany->getSubscription()->isActive());
        $this->assertTrue($archivedCompany->getSubscription()->isCancelled());
        $em->getFilters()->enable('softdeleteable');
    }

    public function testUnsubscribeDefaultCompany(): void
    {
        $mock = $this->getMockedStripeClient();

        $useuse = function () use ($mock) {
            // remplacement du service par un mock dans le container
            self::getContainer()->set(StripeClient::class, $mock);
        };

        $company = CompanyFactory::createOne(['name' => 'entreprise avec un nom unique', 'companyType' => Company::COMPANY_TYPE_DEFAULT]);
        $anotherCompany = CompanyFactory::createOne(['name' => 'entreprise avec un autre nom unique', 'companyType' => Company::COMPANY_TYPE_DEFAULT]);

        // Une company n'a pas le droit de désinscrire une autre company
        $this->browser()
            ->actingAs($company)
            ->use($useuse)
            ->post('/api/companies/'.$anotherCompany->getId().'/cancel-subscription', [
                'json' => [],
            ])
            ->assertStatus(403);

        // Une company peut se désinscrire elle même
        $this->browser()
            ->actingAs($company)
            ->use($useuse)
            ->post('/api/companies/'.$company->getId().'/cancel-subscription', [
                'json' => [],
            ])
            ->assertStatus(200);

        $this->assertTrue($company->getSubscription()->isCancelled());
    }

    public function testCheckPin(): void
    {
        $company = CompanyFactory::createOne();
        $anotherCompany = CompanyFactory::createOne();

        // Code PIN valide
        $this->browser()
            ->actingAs($company)
            ->post('/api/companies/'.$company->getId().'/check-pin', [
                'json' => [
                    'pin' => '0000',
                ],
            ])
            ->assertSuccessful()
            ->assertMatchesJsonSchema(['token' => 'string'])
            ->assertMatchesResourceJsonSchemas(Company::class, Company::OPERATION_CHECK_PIN);

        // Code PIN invalide
        $this->browser()
            ->actingAs($company)
            ->post('/api/companies/'.$company->getId().'/check-pin', [
                'json' => [
                    'pin' => '1111',
                ],
            ])
            ->assertStatus(401);

        // Vérification des droits
        $this->browser()
            ->actingAs($anotherCompany)
            ->post('/api/companies/'.$company->getId().'/check-pin', [
                'json' => [
                    'pin' => '0000',
                ],
            ])
            ->assertStatus(403);
    }

    #[Group('CustomerHistory')]
    public function testForgotPin(): void
    {
        $this->markTestSkipped('En mode test on reçoit plus de la 429 au bout de 3 appels, à voir.');

        $company = CompanyFactory::createOne();
        $anotherCompany = CompanyFactory::createOne();

        // faire une boucle d'appels ne semble pas impacter le temps d'exécution du test
        for ($i = 1; $i <= 4; ++$i) {
            $browser = $this->browser()
                ->actingAs($company)
                ->post('/api/companies/'.$company->getId().'/forgot-pin', [
                    'json' => [],
                ]);
            if ($i <= 3) {
                $browser->assertSuccessful();
                $this->assertNotificationCount(1);
            } else {
                // le composant rate_limiter est censé rejeter la requête
                // au dela de 3 appels d'affilé par le même utilisateur, il doit attendre 15 minutes
                $browser->assertStatus(429);
                $this->assertNotificationCount(0);
            }
        }

        // Vérification que cela a créé 3 lignes dans l'historique
        CompanyHistoryFactory::assert()->count(3, ['company' => $company->_real(), 'type' => CompanyHistoryType::TYPE_REQUEST_PIN]);

        // Vérification des droits
        $this->browser()
            ->actingAs($anotherCompany)
            ->post('/api/companies/'.$company->getId().'/forgot-pin', [
                'json' => [],
            ])
            ->assertStatus(403);
        $this->assertNotificationCount(0);
    }

    public function testStats(): void
    {
        $company = CompanyFactory::createOne();
        $anotherCompany = CompanyFactory::createOne();

        $this->browser()
            ->actingAs($company)
            ->get('/api/companies/'.$company->getId().'/stats')
            ->assertJsonContains([
                '@type' => 'CompanyStatsDto',
                'companyStats' => [
                    ['@type' => 'CompanyStatDto'],
                ],
            ])
            ->assertMatchesResourceOutputJsonSchema(Company::class, Company::OPERATION_GET_STATS);

        $this->browser()
            ->actingAs($company)
            ->get('/api/companies/'.$anotherCompany->getId().'/stats')
            ->assertStatus(403);
    }

    public function testSetStatsPositions(): void
    {
        $company = CompanyFactory::createOne();
        $anotherCompany = CompanyFactory::createOne();

        $this->browser()
            ->actingAs($company)
            ->patch('/api/companies/'.$company->getId().'/stats', [
                'json' => [
                    'stats' => ['total_ca', 'new_clients'],
                ],
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
            ])
            ->assertJsonContains(
                [
                    'stats' => ['total_ca', 'new_clients'],
                ]
            )
            ->assertMatchesResourceJsonSchemas(Company::class, Company::OPERATION_PATCH_STATS);

        $this->browser()
            ->actingAs($company)
            ->patch('/api/companies/'.$company->getId().'/stats', [
                'json' => [
                    'stats' => ['invalid_name', 'new_clients'],
                ],
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
            ])
            ->assertViolations([
                [
                    'propertyPath' => 'stats',
                    'message' => 'Une ou plusieurs des valeurs soumises sont invalides.',
                ],
            ]);

        $this->browser()
            ->actingAs($anotherCompany)
            ->patch('/api/companies/'.$company->getId().'/stats', [
                'json' => [
                    'stats' => ['total_ca', 'new_clients'],
                ],
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
            ])
            ->assertStatus(403);
    }

    public function testGetStatsExport(): void
    {
        Clock::set(new MockClock('2025-01-29 00:00:00', 'Europe/Paris'));

        $company = CompanyFactory::createOne(
            ['name' => 'My Company']
        );

        $this->browser()
            ->actingAs($company)
            ->get('/api/companies/'.$company->getId().'/stats/export')
            ->assertSuccessful()
            ->assertHeaderEquals('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            ->assertHeaderEquals('Content-Disposition', 'attachment;filename=MY-COMPANY_STATISTIQUES_20250129.xlsx')
        ;
    }

    public function testGenerateQrCode(): void
    {
        $company = CompanyFactory::createOne();
        $anotherCompany = CompanyFactory::createOne();

        // Test de génération du QR code pour l'enseigne propriétaire
        $this->browser()
            ->actingAs($company)
            ->get('/api/companies/'.$company->getId().'/generate-qr-code')
            ->assertSuccessful()
            ->assertHeaderEquals('Content-Type', 'image/png')
            ->assertHeaderEquals('Content-Disposition', 'attachment;filename=macartefid-qrcode.png')
        ;

        // Vérification des droits - une autre enseigne ne peut pas générer le QR code
        $this->browser()
            ->actingAs($anotherCompany)
            ->get('/api/companies/'.$company->getId().'/generate-qr-code')
            ->assertStatus(403);
    }

    public function getMockedStripeClient(): StripeClient&MockObject
    {
        $mock = $this
            ->getMockBuilder(StripeClient::class)
            ->setConstructorArgs([
                'xxx',
                self::getContainer()->get(CacheInterface::class),
                self::getContainer()->get(FrontEndRouter::class),
            ])
            ->disableOriginalClone()
            ->onlyMethods(['createPortal', 'cancelSubscription'])
            ->getMock();

        return $mock;
    }
}
