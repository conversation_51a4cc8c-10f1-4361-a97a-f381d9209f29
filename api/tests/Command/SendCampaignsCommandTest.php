<?php

namespace App\Tests\Command;

use App\Factory\CampaignAnniversaireFactory;
use App\Factory\CampaignDormantFactory;
use App\Factory\CampaignExpirationChequeFactory;
use App\Factory\CompanyFactory;
use App\Factory\SubscriptionFactory;
use App\Message\BatchCampaign;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Clock\Test\ClockSensitiveTrait;
use Symfony\Component\Console\Tester\CommandTester;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\ResetDatabase;
use Zenstruck\Messenger\Test\InteractsWithMessenger;

use function Zenstruck\Foundry\Persistence\flush_after;

class SendCampaignsCommandTest extends KernelTestCase
{
    // This trait provided by <PERSON>ry will take care of refreshing the database content to a known state before each test
    use ResetDatabase;
    use Factories;
    use ClockSensitiveTrait;
    use InteractsWithMessenger;

    private Application $application;

    protected function setUp(): void
    {
        $kernel = self::bootKernel();
        $this->application = new Application($kernel);
        $this->transport('sync')->intercept();
    }

    public function testExecuteWithAnniversaire(): void
    {
        flush_after(function () {
            CompanyFactory::createOne([
                'name' => 'Company 1',
                'campaignAnniversaire' => CampaignAnniversaireFactory::new()->withoutPersisting()->create([
                    'enabled' => true,
                ]),
                'customers' => [],
            ]);
            CompanyFactory::createOne([
                'name' => 'Disabled',
                'campaignAnniversaire' => CampaignAnniversaireFactory::new()->withoutPersisting()->create([
                    'enabled' => false,
                ]),
                'customers' => [],
            ]);
            CompanyFactory::createOne([
                'name' => 'Inactive Company',
                'subscription' => SubscriptionFactory::new()->withoutPersisting()->create([
                    'active' => false,
                ]),
                'campaignAnniversaire' => CampaignAnniversaireFactory::new()->withoutPersisting()->create([
                    'enabled' => true,
                ]),
                'customers' => [],
            ]);
            CompanyFactory::new()->archived()->create([
                'name' => 'Archived Company',
                'campaignAnniversaire' => CampaignAnniversaireFactory::new()->withoutPersisting()->create([
                    'enabled' => true,
                ]),
                'customers' => [],
            ]);
        });

        $command = $this->application->find('app:send-campaigns');
        $commandTester = new CommandTester($command);
        $commandTester->execute([
            'type' => 'anniversaire',
            '--force' => false,
        ]);

        $commandTester->assertCommandIsSuccessful();

        $output = $commandTester->getDisplay();
        $this->assertStringContainsString('Company 1', $output);
        $this->assertStringNotContainsString('Disabled', $output);
        $this->assertStringNotContainsString('Inactive Company', $output);
        $this->assertStringNotContainsString('Archived Company', $output);

        $queue = $this->transport('sync')->queue();
        $queue->assertCount(1);
        $queue->assertContains(BatchCampaign::class);
    }

    public function testExecuteWithDormant(): void
    {
        flush_after(function () {
            CompanyFactory::createOne([
                'name' => 'Company 1',
                'campaignDormant' => CampaignDormantFactory::new()->withoutPersisting()->create([
                    'enabled' => true,
                ]),
                'customers' => [],
            ]);
            CompanyFactory::createOne([
                'name' => 'Disabled',
                'campaignDormant' => CampaignDormantFactory::new()->withoutPersisting()->create([
                    'enabled' => false,
                ]),
                'customers' => [],
            ]);
            CompanyFactory::createOne([
                'name' => 'Inactive Company',
                'subscription' => SubscriptionFactory::new()->withoutPersisting()->create([
                    'active' => false,
                ]),
                'campaignDormant' => CampaignDormantFactory::new()->withoutPersisting()->create([
                    'enabled' => true,
                ]),
                'customers' => [],
            ]);
            CompanyFactory::new()->archived()->create([
                'name' => 'Archived Company',
                'campaignDormant' => CampaignDormantFactory::new()->withoutPersisting()->create([
                    'enabled' => true,
                ]),
                'customers' => [],
            ]);
        });

        $command = $this->application->find('app:send-campaigns');
        $commandTester = new CommandTester($command);
        $commandTester->execute([
            'type' => 'dormant',
            '--force' => false,
        ]);

        $commandTester->assertCommandIsSuccessful();

        $output = $commandTester->getDisplay();
        $this->assertStringContainsString('Company 1', $output);
        $this->assertStringNotContainsString('Disabled', $output);
        $this->assertStringNotContainsString('Inactive Company', $output);
        $this->assertStringNotContainsString('Archived Company', $output);

        $queue = $this->transport('sync')->queue();
        $queue->assertCount(1);
        $queue->assertContains(BatchCampaign::class);
    }

    public function testExecuteWithExpirationCheque(): void
    {
        flush_after(function () {
            CompanyFactory::createOne([
                'name' => 'Company 1',
                'campaignExpirationCheque' => CampaignExpirationChequeFactory::new()->withoutPersisting()->create([
                    'enabled' => true,
                ]),
                'customers' => [],
            ]);
            CompanyFactory::createOne([
                'name' => 'Disabled',
                'campaignExpirationCheque' => CampaignExpirationChequeFactory::new()->withoutPersisting()->create([
                    'enabled' => false,
                ]),
                'customers' => [],
            ]);
            CompanyFactory::createOne([
                'name' => 'Inactive Company',
                'subscription' => SubscriptionFactory::new()->withoutPersisting()->create([
                    'active' => false,
                ]),
                'campaignExpirationCheque' => CampaignExpirationChequeFactory::new()->withoutPersisting()->create([
                    'enabled' => false,
                ]),
                'customers' => [],
            ]);
            CompanyFactory::new()->archived()->create([
                'name' => 'Archived Company',
                'campaignExpirationCheque' => CampaignExpirationChequeFactory::new()->withoutPersisting()->create([
                    'enabled' => true,
                ]),
                'customers' => [],
            ]);
        });

        $command = $this->application->find('app:send-campaigns');
        $commandTester = new CommandTester($command);
        $commandTester->execute([
            'type' => 'expiration_cheque',
            '--force' => false,
        ]);

        $commandTester->assertCommandIsSuccessful();

        $output = $commandTester->getDisplay();
        $this->assertStringContainsString('Company 1', $output);
        $this->assertStringNotContainsString('Disabled', $output);
        $this->assertStringNotContainsString('Inactive Company', $output);
        $this->assertStringNotContainsString('Archived Company', $output);

        $queue = $this->transport('sync')->queue();
        $queue->assertCount(1);
        $queue->assertContains(BatchCampaign::class);
    }

    public function testWithError(): void
    {
        $command = $this->application->find('app:send-campaigns');
        $commandTester = new CommandTester($command);
        $commandTester->execute([
            'type' => 'invalid',
            '--force' => false,
        ]);

        $this->assertStringContainsString('Invalid type', $commandTester->getDisplay());
        $this->assertEquals(1, $commandTester->getStatusCode());
    }
}
