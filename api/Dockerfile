#syntax=docker/dockerfile:1.4

# Base FrankenPHP image
FROM gitlab.alienor.net:5050/dev-docker/web_php/frankenphp_base:8.4-frankenphp AS frankenphp_base

RUN set -eux; install-php-extensions bcmath amqp redis;

RUN apt-get update && apt-get install -y --no-install-recommends \
    exim4 \
    libnss3-tools \
    && rm -rf /var/lib/apt/lists/*

# ~~~~~ CONFIGURE EXIM4 ~~~~~~~

RUN echo "dc_eximconfig_configtype='smarthost'\n\
dc_local_interfaces='127.0.0.1 ; ::1'\n\
dc_smarthost='smtp.alienor.net'\n\
CFILEMODE='644'" > /etc/exim4/update-exim4.conf.conf

RUN service exim4 restart

COPY --link frankenphp/conf.d/10-app.ini $PHP_INI_DIR/conf.d/
COPY --link --chmod=755 frankenphp/docker-entrypoint.sh /usr/local/bin/docker-entrypoint
COPY --link frankenphp/Caddyfile /etc/caddy/Caddyfile

ENTRYPOINT ["docker-entrypoint"]

HEALTHCHECK --start-period=60s CMD echo "healthcheck" || exit 1
CMD [ "frankenphp", "run", "--config", "/etc/caddy/Caddyfile" ]

FROM gitlab.alienor.net:5050/dev-docker/web_php/frankenphp_dev:8.4-frankenphp AS frankenphp_dev

# ~~~~~~ STRIPE ~~~~~~
# @TODO trouver pourquoi ça ne s'installe pas
#RUN curl -s https://packages.stripe.dev/api/security/keypair/stripe-cli-gpg/public | gpg --dearmor | tee /usr/share/keyrings/stripe.gpg \
#  	&& echo "deb [signed-by=/usr/share/keyrings/stripe.gpg] https://packages.stripe.dev/stripe-cli-debian-local stable main" | tee -a /etc/apt/sources.list.d/stripe.list \
#    && apt update \
#    && apt install stripe

RUN set -eux; install-php-extensions bcmath amqp redis;

RUN apt-get update && apt-get install -y --no-install-recommends \
    exim4 \
    libnss3-tools \
    && rm -rf /var/lib/apt/lists/*

# ~~~~~ CONFIGURE EXIM4 ~~~~~~~

RUN echo "dc_eximconfig_configtype='smarthost'\n\
dc_local_interfaces='127.0.0.1 ; ::1'\n\
dc_smarthost='smtp.alienor.net'\n\
CFILEMODE='644'" > /etc/exim4/update-exim4.conf.conf

RUN service exim4 restart

COPY --link frankenphp/conf.d/10-app.ini $PHP_INI_DIR/conf.d/
COPY --link --chmod=755 frankenphp/docker-entrypoint.sh /usr/local/bin/docker-entrypoint
COPY --link frankenphp/Caddyfile /etc/caddy/Caddyfile
COPY --link frankenphp/conf.d/20-app.dev.ini $PHP_INI_DIR/conf.d/

RUN mkdir -p /var/www/.cache/composer/ && chown -R www-data:www-data /var/www/.cache/composer/

ENTRYPOINT ["docker-entrypoint"]

HEALTHCHECK --start-period=60s CMD echo "healthcheck" || exit 1
CMD [ "frankenphp", "run", "--config", "/etc/caddy/Caddyfile", "--watch" ]

FROM frankenphp_base AS frankenphp_prod

ENV APP_ENV=prod
#ENV FRANKENPHP_CONFIG="import worker.Caddyfile"

RUN mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini"

COPY --link frankenphp/conf.d/20-app.prod.ini $PHP_INI_DIR/conf.d/
COPY --link frankenphp/worker.Caddyfile /etc/caddy/worker.Caddyfile

RUN mkdir -p /var/www/.cache/composer/ && chown -R www-data:www-data /var/www/.cache/composer/

# prevent the reinstallation of vendors at every changes in the source code
COPY --link composer.* symfony.* ./
RUN set -eux; \
    composer install --no-cache --prefer-dist --no-dev --no-autoloader --no-scripts --no-progress

# copy sources
COPY --link . ./
RUN rm -Rf frankenphp/

RUN set -eux; \
    mkdir -p var/cache var/log; \
    composer dump-autoload --classmap-authoritative --no-dev; \
    composer dump-env prod; \
    composer run-script  --no-dev post-install-cmd; \
    chmod +x bin/console; sync;
