{% extends '@EasyAdmin/page/content.html.twig' %}

{% block content_title %}Statistiques{% endblock %}
{% block page_actions %}
{% endblock %}

{% macro stat_block(title, value) %}
    <div class="card h-100">
        <div class="card-body d-flex flex-column justify-content-around pb-1">
            <h5 class="card-title">{{ title }}</h5>
            <p class="text-end card-text fs-2">{{ value }}</p>
        </div>
    </div>
{% endmacro %}

{% macro table_fiducial_block(title, value, money = false) %}
    {% embed 'admin/stats_table.html.twig' with { 'title': title } %}
        {% block table %}
            <table class="table">
                {% if is_granted('ROLE_SUPER_ADMIN') and value.fiducial is defined %}
                    {% set fiducial = value.fiducial %}
                    {% if money %}
                        {% set fiducial = fiducial|number_format(2, '.', ' ') ~ ' €'  %}
                    {% endif %}
                    <tr>
                        <td><span class="badge badge-danger">Fiducial</span></td>
                        <td class="text-end">{{ fiducial }}</td>
                    </tr>
                {% endif %}
                {% set default = value.default %}
                {% if money %}
                    {% set default = default|number_format(2, '.', ' ') ~ ' €'  %}
                {% endif %}
                <tr>
                    <td><span class="badge badge-info">Non fiducial</span></td>
                    <td class="text-end">{{ default }}</td>
                </tr>
            </table>
        {% endblock %}
    {% endembed  %}
{% endmacro %}

{% block main %}

    {{ form_start(form) }}
    <div class="row row-cols-lg-auto g-3 align-items-center">
        <div class="col-lg-3">
            {{ form_row(form.startDate) }}
        </div>
        <div class="col-lg-3">
            {{ form_row(form.endDate) }}
        </div>
        <div class="col align-items-end">
            <button class="btn btn-primary">Filtrer</button>
        </div>
    </div>
    {{ form_end(form) }}

    <div class="row row-cols-1 row-cols-md-3 g-4">
        <div class="col col-lg-4">
            {{ _self.stat_block("Nombre total d'enseignes inscrites", stats.companyCount) }}
        </div>
        {% if is_granted('ROLE_SUPER_ADMIN') %}
            <div class="col col-lg-4">
                {{ _self.table_fiducial_block("Répartition des types d'abonnement", stats.companyType) }}
            </div>
        {% endif %}
        <div class="col col-lg-4">
            {{ _self.stat_block("Nombre de clients finaux inscrits", stats.customerCount) }}
        </div>
        <div class="col col-lg-4">
            {{ _self.stat_block("Nombre moyen de clients par enseigne", stats.avgCustomerCount) }}
        </div>
        <div class="col col-lg-4">
            {% embed 'admin/stats_table.html.twig' with { 'title': 'Nombre de SMS envoyés par les enseignes' } %}
                {% block table %}
                    <table class="table">
                        <tr>
                            <td>Campagnes manuelles</td>
                            <td class="text-end">{{ stats.smsCount.sms }}</td>
                        </tr>
                        <tr>
                            <td>Campagnes automatisées</td>
                            <td class="text-end">{{ stats.smsCount.auto }}</td>
                        </tr>
                    </table>
                {% endblock %}
            {% endembed  %}
        </div>
        <div class="col col-lg-4">
            {{ _self.stat_block("Nombre de SMS envoyés par la plateforme", stats.alertSmsCount) }}
        </div>
        <div class="col col-lg-4">
            {{ _self.stat_block("Taux de conversion des offres", stats.conversionRate|number_format(2, '.', ' ') ~ '%') }}
        </div>
        {% if is_granted('ROLE_ADMIN_AQUITEM') %}
            <div class="col col-lg-4">
                {{ _self.table_fiducial_block("CA abonnements", stats.subscriptionCA, true) }}
            </div>
            <div class="col col-lg-4">
                {{ _self.table_fiducial_block("CA SMS vendus", stats.smsCA, true) }}
            </div>
        {% endif %}
    </div>
    <hr class="mt-5 mb-3">
    <p class="mb-3 fst-italic">Les statistiques ci-dessous sont globales et ne peuvent être filtrées par date</p>
    <div class="row row-cols-1 row-cols-md-3 g-4">
        <div class="col col-lg-4">
            {% embed 'admin/stats_table.html.twig' with { 'title': 'Utilisation des campaignes' } %}
                {% block table %}
                    <table class="table">
                        {% for key, data in stats.campaignsUsage %}
                            <tr>
                                <td>{{ "campaign.#{key}"|trans }}</td>
                                <td class="text-end">{{ data.enabled }}</td>
                                <td class="text-end">{{ data.percent|number_format(0, '.', ' ') ~ '%' }}</td>
                            </tr>
                        {% endfor %}
                    </table>
                {% endblock %}
            {% endembed  %}
        </div>
    </div>
{% endblock %}
