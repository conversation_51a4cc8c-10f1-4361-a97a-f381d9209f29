{% if field.value is not empty %}
    {% set manyToManySuffix = field.getCustomOption('manyToMany') ? '[]' : '' %}
    {% set filteredIndexUrl = ea_url()
        .removeReferrer()
        .setController(field.customOptions.get('crudControllerFqcn'))
        .setAction(constant('EasyCorp\\Bundle\\EasyAdminBundle\\Config\\Action::INDEX'))
        .set("filters[" ~ field.getCustomOption('field') ~ "][comparison]", '=')
        .set("filters[" ~ field.getCustomOption('field') ~ "][value]" ~ manyToManySuffix, attribute(entity.instance, field.getCustomOption('entityFieldId')|default('id')))
        .unset('entityId')
    %}
    <a href="{{ filteredIndexUrl }}" id="load-customer-{{ entity.instance.id }}" style="display: block" class="collection-link-load" data-url="{{ ea_url().setRoute('admin_company_count_clients', { id: entity.instance.id }).generateUrl() }}">
        <span class="badge badge-secondary"><i class="fa-solid fa-up-right-from-square me-1"></i> <span class="count-placeholder">...</span></span>
    </a>
{% else %}
    <span class="badge badge-secondary">0</span>
{% endif %}

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const element = document.getElementById('load-customer-{{ entity.instance.id }}')
        fetch(element.dataset.url)
            .then(response => response.text())
            .then(data => {
                element.querySelector('.count-placeholder').innerHTML = data;
            });
    });
</script>
