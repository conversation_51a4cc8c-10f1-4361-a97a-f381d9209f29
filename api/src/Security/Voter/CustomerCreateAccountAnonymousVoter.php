<?php

namespace App\Security\Voter;

use App\Controller\CreateCustomerAccountController;
use App\Entity\Customer;
use App\Service\QrCodeGenerator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

class CustomerCreateAccountAnonymousVoter extends Voter
{
    public const string RULE_POST_DENORMALIZE = "is_granted('CUSTOMER_CREATE_ACCOUNT_ANONYMOUS', {customer: object, request: request})";
    public const string CREATE = 'CUSTOMER_CREATE_ACCOUNT_ANONYMOUS';

    protected function supports(string $attribute, mixed $subject): bool
    {
        return $attribute == self::CREATE
            && \is_array($subject)
            && $subject['request'] instanceof Request
            && $subject['customer'] instanceof Customer;
    }

    /** @param string $subject */
    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token): bool
    {
        assert($subject['request'] instanceof Request);
        assert($subject['customer'] instanceof Customer);

        $token = $subject['request']->query->get('token');
        $companyId = $subject['request']->attributes->get('companyId');

        $company = $subject['customer']->getCompany();
        if (
            (string) $company->getId() !== (string) $companyId
            || !CreateCustomerAccountController::validateToken($token, $company)
        ) {
            return false;
        }

        return true;
    }
}
