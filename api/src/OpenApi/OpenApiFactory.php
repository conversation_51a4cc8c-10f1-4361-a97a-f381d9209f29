<?php

declare(strict_types=1);

namespace App\OpenApi;

use ApiPlatform\Metadata\Resource\ResourceMetadataCollection;
use ApiPlatform\OpenApi\Factory\OpenApiFactoryInterface;
use ApiPlatform\OpenApi\Model;
use ApiPlatform\OpenApi\OpenApi;
use App\Controller\CustomControllerInterface;
use App\Entity\Campaign;
use App\Entity\SecuredArea;
use Symfony\Component\DependencyInjection\Attribute\AsDecorator;
use Symfony\Component\DependencyInjection\Attribute\AutowireIterator;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\RouterInterface;

#[AsDecorator(decorates: 'coop_tilleuls_forgot_password.openapi.factory')]
final readonly class OpenApiFactory implements OpenApiFactoryInterface
{
    /**
     * @param iterable<CustomControllerInterface> $handlers
     */
    public function __construct(
        private OpenApiFactoryInterface $decorated,
        #[AutowireIterator('app.custom_controller')] private iterable $handlers,
        private RouterInterface $router
    ) {
    }

    public function __invoke(array $context = []): OpenApi
    {
        $authPath = '/auth';
        $jwtOpenApi = new \Lexik\Bundle\JWTAuthenticationBundle\OpenApi\OpenApiFactory($this->decorated, $authPath, 'email', 'password');
        $openApi = $jwtOpenApi();

        $openApi = $openApi->withServers([
            [
                'url' => sprintf('%s://%s', $this->router->getContext()->getScheme(), $this->router->getContext()->getHost()),
            ],
        ]);

        $paths = $openApi->getPaths();

        foreach ($this->handlers as $handler) {
            $paths->addPath(
                $handler->getOpenApiPath(),
                $handler->getOpenApiOperation()
            );
        }

        $this->addSecurityPaths($paths, $authPath);
        $this->addCompanyStatsExportPath($paths);

        // Surcharge du schema de reset de mot de passe pour inclure la validation
        $schemas = $openApi->getComponents()->getSchemas();
        $schemas['ForgotPassword:reset']['properties']['password'] = $schemas['CompanyDraft-write']['properties']['plainPassword'];

        // Ajout du booleen removable pour les encaissement dans l'appel à GET CustomerHistories
        /* @see CustomerHistoryNormalizer */
        $schemas['CustomerTransaction.jsonld-history.read']['properties']['removable'] = [
            'type' => 'boolean',
            'description' => 'Indique si la transaction a la possiblité d\'être supprimée',
            'readOnly' => true,
        ];

        $components = $openApi->getComponents();

        $securitySchema = $components->getSecuritySchemes();

        $securitySchema['JWT'] = [
            'type' => 'http',
            'scheme' => 'bearer',
            'bearerFormat' => 'JWT',
            'description' => "Token d'authentification JWT nécessaire pour accéder à toutes les routes qui nécessitent d'être connecté",
        ];

        $securitySchema['X-PIN'] = new \ArrayObject([
            'type' => 'apiKey',
            'in' => 'header',
            'name' => 'X-PIN',
            'description' => 'Header de sécurité pour accéder aux routes protégées par la configuration `securedAreas` de l\'enseigne. Doit correspondre au code PIN de l\'enseigne. Obligatoire uniquement si la configuration le demande.',
        ]);

        $securitySchema['X-API-KEY'] = new \ArrayObject([
            'type' => 'apiKey',
            'in' => 'header',
            'name' => 'X-API-KEY',
            'description' => "Header de sécurité à renseigner lors de l'authentification pour obtenir un JWT ayant des droits supplémentaires. A utiliser quand le consommateur de l'API est un serveur pour lui permettre de ne pas avoir à renseigner le code PIN de l'enseigne sur les routes sécurisées.",
        ]);

        $openApi = $this->addDescriptionToSchemas($openApi);
        $openApi = $this->addTagsDescriptions($openApi);
        $openApi = $this->mergeInheritedCampaignSchemas($openApi);
        // $openApi = $this->updateForgotPasswordDescription($openApi);

        $openApi = $this->addPinCodeSecurityConfig($openApi);
        $openApi = $this->addValidationErrorSchema($openApi);
        $openApi = $this->updateHydraExamples($openApi);
        $openApi = $this->removeRequiredFromPatchRequestBody($openApi);

        $schemas = $this->fixNullableStringFormatForZodios($schemas);

        $filteredPaths = new Model\Paths();
        foreach ($paths->getPaths() as $path => $pathItem) {
            if (in_array($path, [
                '/api/subscriptions/{id}',
                '/api/configs/{id}',
                '/api/companies/{id}',
                '/api/company_drafts/{id}',
            ])) {
                $filteredPaths->addPath($path, $pathItem->withGet(null));
            } else {
                $filteredPaths->addPath($path, $pathItem);
            }
        }

        return $openApi
            ->withPaths($filteredPaths)
            ->withComponents($components)
        ;
    }

    public function addDescriptionToSchemas(OpenApi $openApi): OpenApi
    {
        $schemas = $openApi->getComponents()->getSchemas();

        $mapping = [
            'Company.jsonld-read' => 'Enseigne',
            'Campaign.jsonld-history.read' => 'Campagne',
            'CampaignFidelite.jsonld-read' => 'Campagne Fidélité',
            'CampaignAnniversaire.jsonld-read' => 'Campagne Anniversaire',
            'CampaignBienvenue.jsonld-read' => 'Campagne Bienvenue',
            'CampaignDormant.jsonld-read' => 'Campagne Dormant',
            'CampaignExpirationCheque.jsonld-read' => 'Campagne Expiration Chèque',
            'SecuredArea-company.write.config' => "Configuration des actions nécessitant de taper le code PIN de l'enseigne",
            'SecuredArea-read' => "Configuration des actions nécessitant de taper le code PIN de l'enseigne",
            'SecuredArea.jsonld-read' => "Configuration des actions nécessitant de taper le code PIN de l'enseigne",
            'CampaignSmsFilter.jsonld-campaign.write_campaign.write.sms' => 'Filtres utilisés pour sélectionner les clients qui recevront la campagne SMS',
            'Voucher.jsonld-customer.read' => 'Chèques appartenant au client',
            'CampaignSmsFilter.jsonld-campaign.read' => 'Filtres utilisés pour sélectionner les clients qui recevront la campagne SMS',
        ];

        foreach ($mapping as $schema => $description) {
            $schemas[$schema]['description'] = $description;
        }

        return $openApi;
    }

    public function addTagsDescriptions(OpenApi $openApi): OpenApi
    {
        $newTags = [
            [
                'name' => 'Security',
                'description' => "Authentification à l'API",
            ],
            [
                'name' => 'Profile',
                'description' => 'Profil utilisateur',
            ],
            [
                'name' => 'Payment',
                'description' => 'Gestion des paiements',
            ],
            [
                'name' => 'CompanyDraft',
                'description' => "Processus d'inscription d'une nouvelle enseigne",
            ],
            [
                'name' => 'Company',
                'description' => 'Gestion des enseignes',
            ],
            [
                'name' => 'Customer',
                'description' => "Gestion des clients d'une enseigne",
            ],
            [
                'name' => 'CustomerHistory',
                'description' => "Récupération de l'historique des actions des clients",
            ],
            [
                'name' => 'CustomerTransaction',
                'description' => 'Gestion des transactions des clients',
            ],
            [
                'name' => 'CampaignAnniversaire',
                'description' => 'Gestion de la campagne Anniversaire',
            ],
            [
                'name' => 'CampaignBienvenue',
                'description' => 'Gestion de la campagne Bienvenue',
            ],
            [
                'name' => 'CampaignDormant',
                'description' => 'Gestion de la campagne Réveil Clients Dormants',
            ],
            [
                'name' => 'CampaignExpirationCheque',
                'description' => 'Gestion de la campagne Expiration Cheque',
            ],
            [
                'name' => 'CampaignGoogleReview',
                'description' => 'Gestion de la campagne Avis Google',
            ],
            [
                'name' => 'CampaignFidelite',
                'description' => 'Gestion de la campagne Fidélité',
            ],
            [
                'name' => 'CampaignGoogleReview',
                'description' => 'Gestion de la campagne Avis Google',
            ],
            [
                'name' => 'CampaignSms',
                'description' => 'Gestion des campagnes SMS',
            ],
            [
                'name' => 'RichSms',
                'description' => 'Récupération des Rich SMS',
            ],
            [
                'name' => 'Forgot password',
                'description' => 'Oubli de mot de passe',
            ],
            [
                'name' => 'OpenAiRecommandations',
                'description' => 'Génération de recommandations via OpenAI',
            ],
            [
                'name' => 'Tutorial',
                'description' => 'Récupération des tutoriels',
            ],
            [
                'name' => 'Faq',
                'description' => 'Récupération de la FAQ',
            ],
        ];

        return $openApi->withTags(array_merge($openApi->getTags(), $newTags));
    }

    /**
     * Rajoute les propriétés communes dans les schémas de campagnes
     * Car ApiPlatform ne fait pas automatiquement la liaison avec les propriétés déclarées dans l'entity parente.
     */
    private function mergeInheritedCampaignSchemas(OpenApi $openApi)
    {
        $schemas = $openApi->getComponents()->getSchemas();

        $mapping = [
            // write
            'CampaignAnniversaire-campaign.write_campaign.write.enabled_campaign.write.voucherEnabled',
            'CampaignBienvenue-campaign.write_campaign.write.enabled_campaign.write.voucherEnabled',
            'CampaignDormant-campaign.write_campaign.write.enabled_campaign.write.voucherEnabled',
            'CampaignExpirationCheque-campaign.write_campaign.write.enabled',
            'CampaignFidelite-campaign.write_campaign.write.voucherEnabled',
            'CampaignGoogleReview-campaign.write_campaign.write.enabled_campaign.write.enabled',
            'CampaignSms.jsonld-campaign.write_campaign.write.sms',
            'CampaignSms-campaign.write_campaign.write.sms',
            // read
            'CampaignAnniversaire-campaign.read',
            'CampaignAnniversaire-read',
            'CampaignAnniversaire.jsonld-campaign.read',
            'CampaignAnniversaire.jsonld-read',

            'CampaignBienvenue-campaign.read',
            'CampaignBienvenue-read',
            'CampaignBienvenue.jsonld-campaign.read',
            'CampaignBienvenue.jsonld-read',

            'CampaignDormant-campaign.read',
            'CampaignDormant-read',
            'CampaignDormant.jsonld-campaign.read',
            'CampaignDormant.jsonld-read',

            'CampaignExpirationCheque-campaign.read',
            'CampaignExpirationCheque-read',
            'CampaignExpirationCheque.jsonld-campaign.read',
            'CampaignExpirationCheque.jsonld-read',

            'CampaignFidelite-campaign.read',
            'CampaignFidelite-read',
            'CampaignFidelite.jsonld-campaign.read',
            'CampaignFidelite.jsonld-read',

            'CampaignGoogleReview-campaign.read',
            'CampaignGoogleReview-read',
            'CampaignGoogleReview.jsonld-campaign.read',
            'CampaignGoogleReview.jsonld-read',

            'CampaignSms-campaign.read',
            'CampaignSms.jsonld-campaign.read',
        ];

        foreach ($mapping as $schema) {
            if (isset($schemas[$schema]['properties']['id'])) {
                $schemas[$schema]['properties']['id']['description'] = 'Identifiant unique de la campagne';
                $schemas[$schema]['properties']['id']['example'] = 98;
            }
            if (isset($schemas[$schema]['properties']['type'])) {
                $schemas[$schema]['properties']['type']['description'] = 'Type de campagne';
                $schemas[$schema]['properties']['type']['example'] = 'campaign_anniversaire';
                $schemas[$schema]['properties']['type']['enum'] = Campaign::TYPES;
            }
            if (isset($schemas[$schema]['properties']['company'])) {
                $schemas[$schema]['properties']['company']['description'] = 'Enseigne à laquelle la campagne est rattaché - format IRI';
                $schemas[$schema]['properties']['company']['example'] = '/api/companies/12';
            }
            if (isset($schemas[$schema]['properties']['enabled'])) {
                $schemas[$schema]['properties']['enabled']['description'] = '`true` si la campagne est activée';
            }
            if (isset($schemas[$schema]['properties']['smsContent'])) {
                $schemas[$schema]['properties']['smsContent']['description'] = 'Contenu du SMS qui sera envoyé aux clients par la campagne';
                $schemas[$schema]['properties']['smsContent']['example'] = 'Toute notre équipe vous remercie pour votre fidélité, voici un chèque cadeau valable 3 mois ! Profitez-en vite en magasin.';
            }
            if (isset($schemas[$schema]['properties']['voucherEnabled'])) {
                $schemas[$schema]['properties']['voucherEnabled']['description'] = '`true` si la campagne doit génèrer des chèques';
            }
            if (isset($schemas[$schema]['properties']['richSmsEnabled'])) {
                $schemas[$schema]['properties']['richSmsEnabled']['description'] = '`true` si le SMS doit contenir un lien vers une page affichant le message contenu dans `richSmsContent` ainsi que les détails du chèque';
            }
            if (isset($schemas[$schema]['properties']['richSmsContent'])) {
                $schemas[$schema]['properties']['richSmsContent']['example'] = '<h1>Enseigne</h1><b>Bonjour</b>, vous avez reçu un chèque cadeau valable 3 mois ! <a href="###">Profitez-en vite en magasin</a>.';
                $schemas[$schema]['properties']['richSmsContent']['description'] = <<<EOF
Contenu HTML de la page sur laquelle le lien contenu dans le SMS doit rediriger. Uniquement si `richSmsEnabled` est activé\n
Balises autorisées : `<h1>`, `<b>`, `<i>`, `<a>`, `<div>`, `<br>`
EOF;
            }
        }

        return $openApi;
    }

    /*
     * @TODO
     * N'a pas l'air de s'appliquer car le bundle ForgotPassword doit passer après la surcharge faite ici
    private function updateForgotPasswordDescription(OpenApi $openApi)
    {
        $paths = $openApi->getPaths();

        $forgotPasswordPath = '/forgot-password/';
        $forgotPasswordPost = $paths->getPath($forgotPasswordPath)->getPost();
        $paths->addPath($forgotPasswordPath, $paths->getPath($forgotPasswordPath)
            ->withPost(
                $forgotPasswordPost
                    ->withSummary('Génère un token et envoie un e-mail')
                    ->withDescription("Permet de réinitialiser le mot de passe d'un utilisateur en générant un token qui sera envoyé par email")
            )
        );

        return $openApi->withPaths($paths);
    }
    */
    private function addPinCodeSecurityConfig(OpenApi $openApi)
    {
        $paths = $openApi->getPaths();

        // récupère les noms des opérations à sécuriser à partir des noms des propriétés de la classe SecuredArea
        $reflection = new \ReflectionClass(SecuredArea::class);
        $operationsToSecure = array_map(fn ($property) => $property->getName(), $reflection->getProperties());

        foreach ($paths->getPaths() as $path => $pathItem) {
            $operations = [
                'get' => $pathItem->getGet(),
                'post' => $pathItem->getPost(),
                'patch' => $pathItem->getPatch(),
                'delete' => $pathItem->getDelete(),
                'put' => $pathItem->getPut(),
            ];

            foreach ($operations as $method => $operation) {
                if (!$operation) {
                    continue;
                }
                if (in_array($operation->getOperationId(), $operationsToSecure)) {
                    $openApi = $this->updatePathMethod($openApi, $path, $method, 'security', [['X-PIN' => [], 'JWT' => []]]);
                }
            }
        }

        return $openApi;
    }

    /**
     * Pitié pourquoi on peut pas modifier les éléments par référence.
     */
    private function updatePathMethod(OpenApi $openApi, string $path, string $method, string $property, mixed $value): OpenApi
    {
        $paths = $openApi->getPaths();
        $pathItem = $paths->getPath($path);

        $operationGetter = 'get'.ucfirst($method);
        $operationSetter = 'with'.ucfirst($method);
        /* @var Model\Operation $operation */
        $operation = $pathItem->{$operationGetter}();

        $setter = 'with'.ucfirst($property);
        $operation = $operation->{$setter}($value);

        $pathItem = $pathItem->{$operationSetter}($operation);

        $paths->addPath($path, $pathItem);

        return $openApi->withPaths($paths);
    }

    private function addValidationErrorSchema(OpenApi $openApi)
    {
        $schemas = $openApi->getComponents()->getSchemas();

        $violationsSchema = [
            'type' => 'array',
            'description' => 'Liste des erreurs de validation',
            'items' => [
                'type' => 'object',
                'properties' => [
                    'propertyPath' => [
                        'type' => 'string',
                        'description' => 'Chemin de la propriété en erreur',
                        'example' => 'firstname',
                    ],
                    'message' => [
                        'type' => 'string',
                        'description' => 'Message d\'erreur',
                        'example' => 'Le prénom est obligatoire',
                    ],
                ],
            ],
        ];

        $schemas['ValidationError'] = new \ArrayObject([
            'type' => 'object',
            'properties' => [
                'violations' => $violationsSchema,
            ],
            'required' => [
                'violations',
                'message',
                'propertyPath',
            ],
        ]);

        $schemas['ValidationError.jsonld'] = new \ArrayObject([
            'type' => 'object',
            'properties' => [
                '@context' => [
                    'type' => 'string',
                    'description' => 'URL du schéma JSON-LD',
                    'example' => '/contexts/ConstraintViolationList',
                ],
                '@type' => [
                    'type' => 'string',
                    'description' => 'Type du schéma JSON-LD',
                    'example' => 'ConstraintViolationList',
                ],
                'hydra:title' => [
                    'type' => 'string',
                    'description' => 'Titre du schéma JSON-LD',
                    'example' => 'An error occurred',
                ],
                'hydra:description' => [
                    'type' => 'string',
                    'description' => 'Description du schéma JSON-LD',
                    'example' => 'firstname: Le prénom est obligatoire',
                ],
                'violations' => $violationsSchema,
            ],
            'required' => [
                'violations',
                'message',
                'propertyPath',
            ],
        ]);

        $paths = $openApi->getPaths();

        foreach ($paths->getPaths() as $path => &$pathItem) {
            $operations = [
                'get' => $pathItem->getGet(),
                'post' => $pathItem->getPost(),
                'patch' => $pathItem->getPatch(),
                'delete' => $pathItem->getDelete(),
                'put' => $pathItem->getPut(),
            ];

            foreach ($operations as $method => &$operation) {
                if (!$operation) {
                    continue;
                }
                $existingResponses = $operation?->getResponses() ?: [];
                if (isset($existingResponses['422'])) {
                    $operation = $this->buildOpenApiResponse('422', 'Validation error', $operation, [
                        'application/ld+json' => 'ld+json',
                        'application/json' => 'json',
                    ], [
                        'ld+json' => [
                            '$ref' => '#/components/schemas/ValidationError.jsonld',
                        ],
                        'json' => [
                            '$ref' => '#/components/schemas/ValidationError',
                        ],
                    ]);
                    $setter = 'with'.ucfirst($method);
                    $pathItem = $pathItem->{$setter}($operation);
                }
            }
            $paths->addPath($path, $pathItem);
        }

        return $openApi;
    }

    private function buildOpenApiResponse(int|string $status, string $description, ?Model\Operation $openapiOperation = null, ?array $responseMimeTypes = null, ?array $operationOutputSchemas = null, ?ResourceMetadataCollection $resourceMetadataCollection = null): Model\Operation
    {
        $responseLinks = $responseContent = null;
        if ($responseMimeTypes && $operationOutputSchemas) {
            $responseContent = $this->buildContent($responseMimeTypes, $operationOutputSchemas);
        }

        return $openapiOperation->withResponse($status, new Model\Response($description, $responseContent, null, $responseLinks));
    }

    /**
     * @return \ArrayObject<Model\MediaType>
     */
    private function buildContent(array $responseMimeTypes, array $operationSchemas): \ArrayObject
    {
        /** @var \ArrayObject<Model\MediaType> $content */
        $content = new \ArrayObject();

        foreach ($responseMimeTypes as $mimeType => $format) {
            $content[$mimeType] = new Model\MediaType(new \ArrayObject($operationSchemas[$format]));
        }

        return $content;
    }

    private function updateHydraExamples(OpenApi $openApi)
    {
        $schemas = $openApi->getComponents()->getSchemas();

        foreach ($schemas as $schemaName => $schema) {
            if (isset($schema['properties'])) {
                if (isset($schemas[$schemaName]['properties']['@context'])) {
                    $schemas[$schemaName]['properties']['@context']['description'] = 'Contexte JSON-LD';
                    $schemas[$schemaName]['properties']['@context']['example'] = '/contexts/MySchema';
                }
                if (isset($schemas[$schemaName]['properties']['@id'])) {
                    $schemas[$schemaName]['properties']['@id']['description'] = 'IRI de la ressource';
                    $schemas[$schemaName]['properties']['@id']['example'] = '/api/entity/1';
                }
                if (isset($schemas[$schemaName]['properties']['@type'])) {
                    $schemas[$schemaName]['properties']['@type']['description'] = 'Type de la ressource';
                    $schemas[$schemaName]['properties']['@type']['example'] = 'MySchema';
                }
            }
        }

        return $openApi;
    }

    private function removeRequiredFromPatchRequestBody(OpenApi $openApi): OpenApi
    {
        return $openApi;
    }

    public function addSecurityPaths(Model\Paths $paths, string $authPath): void
    {
        $authPost = $paths->getPath($authPath)->getPost();
        $authResponse = $authPost->getResponses()['200'];
        $authResponse['content']['application/json']['schema']['properties']['token'] += [
            'description' => 'Token JWT',
            'example' => 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0Ijox',
        ];

        $authResponse['content']['application/json']['schema']['properties']['refreshToken'] = [
            'description' => 'Refresh token à utiliser avec la route `/api/token/refresh` pour renouveler le token d\'authentification',
            'example' => 'ebe915c36a268de60b463d5094becdeafc4a92fd9113992b0f12735965d170bab71dc897b5947346db2c7166a8ca8b616db0d3df35ef3456d262ec5b0b1c13c9',
            'type' => 'string',
        ];

        $authResponse['content']['application/json']['schema']['properties']['refreshTokenExpiration'] = [
            'description' => 'Date au format timestamp à laquelle le refresh token va expirer',
            'example' => 1739960329,
            'type' => 'integer',
        ];

        $authResponse['content']['application/json']['schema']['required'][] = 'refreshToken';
        $authResponse['content']['application/json']['schema']['required'][] = 'refreshTokenExpiration';

        // Surcharge de la route d'authentification généré par LexikJWT
        $paths->addPath($authPath, $paths->getPath($authPath)
            ->withPost(
                $authPost
                    ->withSummary("Récupération d'un token d'authentification JWT")
                    ->withDescription("Permet de récupérer un token d'authentification JWT qui servira à s'authentifier sur les autres routes, ainsi qu'un refresh token qui permettra de renouveler le token d'authentification")
                    ->withTags(['Security'])
                    ->withOperationId('auth')
                    ->withSecurity([['X-API-KEY' => []]])
                    ->withRequestBody(new Model\RequestBody(
                        content: new \ArrayObject([
                            'application/json' => [
                                'schema' => [
                                    'type' => 'object',
                                    'properties' => [
                                        'email' => [
                                            'type' => 'string',
                                            'example' => '<EMAIL>',
                                            'description' => 'Adresse e-mail de l\'utilisateur',
                                        ],
                                        'password' => [
                                            'type' => 'string',
                                            'example' => 'MyS3cr€tP@$$w0rd',
                                            'description' => 'Mot de passe de l\'utilisateur',
                                        ],
                                    ],
                                ],
                            ],
                        ])
                    ))
                    ->withResponses([
                        Response::HTTP_OK => [
                            'description' => 'Création d\'un token JWT',
                            'content' => [
                                'application/ld+json' => $authResponse['content']['application/json'],
                            ],
                        ]])
            )
        );

        $paths->addPath('/api/token/refresh', (new Model\PathItem())
            ->withPost(
                $authPost
                    ->withSummary("Renouvellement d'un token JWT")
                    ->withDescription('Permet de recevoir un nouveau token JWT afin de ne pas avoir à renseigner les identifiants de connexion à chaque fois')
                    ->withTags(['Security'])
                    ->withOperationId('authRefresh')
                    ->withSecurity([['X-API-KEY' => []]])
                    ->withRequestBody(new Model\RequestBody(
                        content: new \ArrayObject([
                            'application/json' => [
                                'schema' => [
                                    'type' => 'object',
                                    'properties' => [
                                        'refreshToken' => [
                                            'type' => 'string',
                                            'example' => 'ebe915c36a268de60b463d5094becdeafc4a92fd9113992b0f12735965d170bab71dc897b5947346db2c7166a8ca8b616db0d3df35ef3456d262ec5b0b1c13c9',
                                            'description' => "Refresh token provenant d'une authentification précédente",
                                        ],
                                    ],
                                ],
                            ],
                        ])
                    ))
                    ->withResponses([
                        Response::HTTP_OK => [
                            'description' => 'Création d\'un nouveau token JWT',
                            'content' => [
                                'application/ld+json' => $authResponse['content']['application/json'],
                            ],
                        ]])
            )
        );
    }

    private function addCompanyStatsExportPath(Model\Paths $paths): void
    {
        $path = '/api/companies/{id}/stats/export';

        $get = $paths->getPath($path)->getGet();

        $get = $get->withResponses([
            Response::HTTP_OK => [
                'description' => 'Fichier Excel',
                'content' => [
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => [
                        'schema' => [
                            'type' => 'string',
                            'format' => 'binary',
                        ],
                    ],
                ],
            ]]);

        $paths->addPath($path, $paths->getPath($path)
            ->withGet($get)
        );
    }

    /**
     * Transforme les propriétés de type
     * "type" => [
     *   "string"
     *   "null"
     * ]
     * en :
     * "type" => "string"
     * "nullable" => true.
     *
     * Pour que zodios soit en capacité de générer de les interpréter correctement
     */
    private function fixNullableStringFormatForZodios(\ArrayObject $schemas): \ArrayObject
    {
        foreach ($schemas as $key => $schema) {
            if (!isset($schema['properties'])) {
                continue;
            }
            foreach ($schema['properties'] as $property => $propertySchema) {
                if (
                    isset($propertySchema['type'])
                    && is_array($propertySchema['type'])
                    && 2 === count($propertySchema['type'])
                    && 'null' === $propertySchema['type'][1]
                ) {
                    $schemas[$key]['properties'][$property]['nullable'] = true;
                    $schemas[$key]['properties'][$property]['type'] = $propertySchema['type'][0];
                }
            }
        }

        return $schemas;
    }
}
