<?php

namespace App\Repository;

use App\Entity\CampaignDormant;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<CampaignDormant>
 *
 * @method CampaignDormant|null find($id, $lockMode = null, $lockVersion = null)
 * @method CampaignDormant|null findOneBy(array $criteria, array $orderBy = null)
 * @method CampaignDormant[]    findAll()
 * @method CampaignDormant[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CampaignDormantRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CampaignDormant::class);
    }

    public function findCampaignsToSend()
    {
        return $this->createQueryBuilder('c')
            ->innerJoin('c.company', 'co')
            ->innerJoin('co.subscription', 'sub')
            ->andWhere('c.enabled = true')
            ->andWhere('co.archivedAt IS NULL')
            ->andWhere('sub.active = true')
            ->getQuery()
            ->getResult();
    }
}
