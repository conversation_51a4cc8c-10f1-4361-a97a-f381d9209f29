<?php

namespace App\Repository;

use App\Entity\CampaignAnniversaire;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<CampaignAnniversaire>
 *
 * @method CampaignAnniversaire|null find($id, $lockMode = null, $lockVersion = null)
 * @method CampaignAnniversaire|null findOneBy(array $criteria, array $orderBy = null)
 * @method CampaignAnniversaire[]    findAll()
 * @method CampaignAnniversaire[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CampaignAnniversaireRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CampaignAnniversaire::class);
    }

    public function findCampaignsToSend()
    {
        return $this->createQueryBuilder('c')
            ->innerJoin('c.company', 'co')
            ->innerJoin('co.subscription', 'sub')
            ->andWhere('c.enabled = true')
            ->andWhere('co.archivedAt IS NULL')
            ->andWhere('sub.active = true')
            ->getQuery()
            ->getResult();
    }
}
