<?php

namespace App\Repository;

use App\Entity\CampaignExpirationCheque;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<CampaignExpirationCheque>
 *
 * @method CampaignExpirationCheque|null find($id, $lockMode = null, $lockVersion = null)
 * @method CampaignExpirationCheque|null findOneBy(array $criteria, array $orderBy = null)
 * @method CampaignExpirationCheque[]    findAll()
 * @method CampaignExpirationCheque[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CampaignExpirationChequeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CampaignExpirationCheque::class);
    }

    public function findCampaignsToSend()
    {
        return $this->createQueryBuilder('c')
            ->innerJoin('c.company', 'co')
            ->innerJoin('co.subscription', 'sub')
            ->andWhere('c.enabled = true')
            ->andWhere('co.archivedAt IS NULL')
            ->andWhere('sub.active = true')
            ->getQuery()
            ->getResult();
    }
}
