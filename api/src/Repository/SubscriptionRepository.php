<?php

namespace App\Repository;

use App\Entity\Company;
use App\Entity\Subscription;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Clock\ClockAwareTrait;
use Symfony\Component\Clock\DatePoint;

/**
 * @extends ServiceEntityRepository<Subscription>
 *
 * @method Subscription|null find($id, $lockMode = null, $lockVersion = null)
 * @method Subscription|null findOneBy(array $criteria, array $orderBy = null)
 * @method Subscription[]    findAll()
 * @method Subscription[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SubscriptionRepository extends ServiceEntityRepository
{
    use ClockAwareTrait;

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Subscription::class);
    }

    /**
     * Retourne la liste des Subscriptions dont la date d'abonnement a expirée afin de mettre à jour leur statut.
     *
     * @return Subscription[]
     */
    public function findOutdatedSubscriptions(): array
    {
        return $this->createQueryBuilder('s')
            ->where('s.active = true')
            ->andWhere('s.expirationDate < :now')
            ->setParameter('now', $this->now()->setTime(0, 0, 0)->format('Y-m-d H:i:s'))
            ->getQuery()
            ->getResult();
    }

    /**
     * Retourne la liste des Subscriptions Fiducial dont la date d'abonnement expire le lendemain.
     *
     * @return Subscription[]
     */
    public function findFiducialSubscriptionsToRefresh(): array
    {
        return $this->createQueryBuilder('s')
            ->where('s.active = true')
            ->innerJoin('s.company', 'c')
            ->andWhere('c.companyType = :fiducial')
            ->setParameter('fiducial', Company::COMPANY_TYPE_FIDUCIAL)
            ->andWhere("TO_CHAR(s.expirationDate, 'yyyy-mm-dd') = :tomorrow")
            ->setParameter('tomorrow', (new DatePoint('tomorrow'))->format('Y-m-d'))
            ->getQuery()
            ->getResult();
    }
}
