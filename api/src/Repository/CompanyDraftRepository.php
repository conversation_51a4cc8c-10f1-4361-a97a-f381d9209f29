<?php

namespace App\Repository;

use App\Entity\CompanyDraft;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<CompanyDraft>
 *
 * @method CompanyDraft|null find($id, $lockMode = null, $lockVersion = null)
 * @method CompanyDraft|null findOneBy(array $criteria, array $orderBy = null)
 * @method CompanyDraft[]    findAll()
 * @method CompanyDraft[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CompanyDraftRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CompanyDraft::class);
    }
}
