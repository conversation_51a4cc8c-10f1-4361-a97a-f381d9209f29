<?php

namespace App\Factory;

use App\Entity\Faq;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<Faq>
 */
final class FaqFactory extends PersistentProxyObjectFactory
{
    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories
     */
    protected function defaults(): array
    {
        return [
            'active' => self::faker()->boolean(),
            'content' => self::faker()->sentence(20),
            'title' => self::faker()->sentence(10).' ?',
            'createdAt' => self::faker()->dateTimeThisDecade(),
        ];
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
     */
    protected function initialize(): static
    {
        return $this
            // ->afterInstantiate(function(Faq $faq): void {})
        ;
    }

    public static function class(): string
    {
        return Faq::class;
    }
}
