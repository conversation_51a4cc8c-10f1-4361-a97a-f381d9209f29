<?php

namespace App\Factory;

use App\Entity\CustomerHistory;
use App\Enum\CustomerHistoryType;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<CustomerHistory>
 */
final class CustomerHistoryFactory extends PersistentProxyObjectFactory
{
    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories
     */
    protected function defaults(): array
    {
        return [
            'createdAt' => self::faker()->dateTime(),
            'type' => self::faker()->randomElement(CustomerHistoryType::values()),
            'removed' => false,
        ];
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
     */
    protected function initialize(): static
    {
        return $this
            // ->afterInstantiate(function(CustomerHistory $customerHistory): void {})
        ;
    }

    public static function class(): string
    {
        return CustomerHistory::class;
    }
}
