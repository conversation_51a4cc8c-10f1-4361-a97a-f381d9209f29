<?php

namespace App\Factory;

use App\Entity\Customer;
use Doctrine\Common\Collections\ArrayCollection;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<Customer>
 */
final class CustomerFactory extends PersistentProxyObjectFactory
{
    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories
     */
    protected function defaults(): array
    {
        return [
            'civility' => self::faker()->randomElement(Customer::CIVILITIES),
            'firstname' => self::faker()->firstName(),
            'lastname' => self::faker()->lastName(),
            'loyaltyPoints' => self::faker()->numberBetween(0, 1000),
            'phoneNumber' => '6'.self::faker()->unique()->numerify('########'),
            'phoneNumberCountryCode' => 'fr',
            'postalCode' => str_replace(' ', '', self::faker()->postcode()),
            'city' => self::faker()->city(),
            'birthDate' => self::faker()->dateTimeBetween('-60 years', '-20 years'),
            'vouchers' => VoucherFactory::new()->many(3, 5),
            'transactions' => CustomerTransactionFactory::new()->many(1, 6),
        ];
    }

    public function withoutTransactions(): self
    {
        return $this->with(['transactions' => new ArrayCollection()]);
    }

    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
     */
    protected function initialize(): static
    {
        return $this
            // ->afterInstantiate(function(Customer $customer): void {})
        ;
    }

    public static function class(): string
    {
        return Customer::class;
    }
}
