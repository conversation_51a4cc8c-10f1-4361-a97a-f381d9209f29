<?php

namespace App\Entity;

use ApiPlatform\Metadata\ApiProperty;
use App\Entity\Trait\Timestampable;
use App\Repository\CampaignRepository;
use App\Validator\SmsMessage;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: CampaignRepository::class)]
#[ORM\InheritanceType('JOINED')]
#[ORM\HasLifecycleCallbacks()]
#[ORM\DiscriminatorColumn(name: 'type', type: 'string')]
#[ORM\DiscriminatorMap([
    'campaign_anniversaire' => 'CampaignAnniversaire',
    'campaign_bienvenue' => 'CampaignBienvenue',
    'campaign_dormant' => 'CampaignDormant',
    'campaign_expiration_cheque' => 'CampaignExpirationCheque',
    'campaign_fidelite' => 'CampaignFidelite',
    'campaign_google_review' => 'CampaignGoogleReview',
    'campaign_sms' => 'CampaignSms',
])]
#[SmsMessage]
abstract class Campaign
{
    use Timestampable;

    public const TYPE_ANNIVERSAIRE = 'campaign_anniversaire';
    public const TYPE_BIENVENUE = 'campaign_bienvenue';
    public const TYPE_DORMANT = 'campaign_dormant';
    public const TYPE_EXPIRATION_CHEQUE = 'campaign_expiration_cheque';
    public const TYPE_FIDELITE = 'campaign_fidelite';
    public const TYPE_GOOGLE_REVIEW = 'campaign_google_review';
    public const TYPE_SMS = 'campaign_sms';

    public const TYPES = [
        self::TYPE_ANNIVERSAIRE,
        self::TYPE_BIENVENUE,
        self::TYPE_DORMANT,
        self::TYPE_EXPIRATION_CHEQUE,
        self::TYPE_FIDELITE,
        self::TYPE_GOOGLE_REVIEW,
        self::TYPE_SMS,
    ];

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    #[Groups(['read', 'campaign:read'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Identifiant unique de la campagne',
        ]
    )]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'campaigns')]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['campaign:write:sms'])]
    #[ApiProperty(
        openapiContext: [
            'example' => '/api/companies/12',
            'description' => 'Enseigne à laquelle la campagne est rattaché - format IRI',
        ]
    )]
    private ?Company $company = null;

    #[ORM\Column]
    #[Assert\NotNull]
    #[Assert\Type('boolean')]
    #[Groups(['read', 'campaign:read', 'campaign:write:enabled'])]
    #[ApiProperty(
        openapiContext: [
            'example' => true,
            'description' => '`true` si la campagne est activée',
        ]
    )]
    private bool $enabled = false;

    #[ORM\Column(type: Types::TEXT)]
    #[Assert\NotBlank]
    #[Groups(['read', 'campaign:read', 'campaign:write', 'history:read'])]
    #[ApiProperty(
        openapiContext: [
            'example' => 'Toute notre équipe vous remercie pour votre fidélité, voici un chèque cadeau valable 3 mois ! Profitez-en vite en magasin.',
            'description' => 'Contenu du SMS qui sera envoyé aux clients par la campagne',
        ]
    )]
    private string $smsContent = '';

    /**
     * @var Collection<int, Sms>
     */
    #[ORM\OneToMany(mappedBy: 'campaign', targetEntity: Sms::class, orphanRemoval: true)]
    private Collection $sms;

    public function __construct()
    {
        $this->sms = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCompany(): ?Company
    {
        return $this->company;
    }

    public function setCompany(?Company $company): self
    {
        $this->company = $company;

        return $this;
    }

    public function isEnabled(): ?bool
    {
        return $this->enabled;
    }

    public function setEnabled(bool $enabled): self
    {
        $this->enabled = $enabled;

        return $this;
    }

    public function getSmsContent(): ?string
    {
        return $this->smsContent;
    }

    public function setSmsContent(string $smsContent): self
    {
        $this->smsContent = $smsContent;

        return $this;
    }

    /**
     * @return Collection<int, Sms>
     */
    public function getSms(): Collection
    {
        return $this->sms;
    }

    public function addSms(Sms $sms): self
    {
        if (!$this->sms->contains($sms)) {
            $this->sms->add($sms);
            $sms->setCampaign($this);
        }

        return $this;
    }

    public function removeSms(Sms $sms): self
    {
        if ($this->sms->removeElement($sms)) {
            // set the owning side to null (unless already changed)
            if ($sms->getCampaign() === $this) {
                $sms->setCampaign(null);
            }
        }

        return $this;
    }

    public function getDefaultSmsContent(): string
    {
        return '';
    }

    #[Groups(['read'])]
    public function getType(): string
    {
        return '';
    }
}
