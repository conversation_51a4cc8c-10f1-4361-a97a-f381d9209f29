<?php

namespace App\Entity;

use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Delete;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\Patch;
use ApiPlatform\Metadata\Post;
use ApiPlatform\OpenApi\Model\Operation as OpenApiOperation;
use ApiPlatform\OpenApi\Model\Parameter as OpenApiParameter;
use ApiPlatform\Symfony\Action\NotFoundAction;
use App\Controller\Company\GenerateQrCode\CompanyGenerateQrCode;
use App\Controller\Company\GetSubscription\CompanyGetSubscription;
use App\Controller\Company\GetSubscription\CompanyGetSubscriptionOutput;
use App\Controller\CompanyCancelSubscription;
use App\Controller\CompanyCheckPin;
use App\Controller\CompanyForgotPin;
use App\Controller\CompanyStats;
use App\Controller\CompanyStatsExport;
use App\Controller\CompanyStripePortal;
use App\Controller\Payment\CheckSubscriptionPaymentSession;
use App\Controller\Payment\CreateSMSPaymentSession;
use App\Controller\Payment\CreateSubscriptionPaymentSession;
use App\Controller\SubscribeWithFiducial;
use App\Dto\CompanyCheckPinInputDto;
use App\Dto\CompanyCheckPinOutputDto;
use App\Dto\CompanyStatsDto;
use App\Dto\CompanyStripePortalDto;
use App\Dto\EmptyInputDto;
use App\Dto\EmptyOutputDto;
use App\Dto\PaymentSessionStatusDto;
use App\Dto\PaymentStartDto;
use App\Dto\SubscribeWithFiducialInputDto;
use App\Dto\SubscribeWithFiducialOutputDto;
use App\Entity\Trait\Timestampable;
use App\Enum\CompanyStatsType;
use App\Repository\CompanyRepository;
use App\State\CompanyUpdateProcessor;
use App\Validator\Constraints as CustomAssert;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Gedmo\SoftDeleteable\Traits\SoftDeleteableEntity;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: CompanyRepository::class)]
#[UniqueEntity(fields: ['email'], message: 'Un compte utilisant cet email existe déjà.', errorPath: 'email')]
#[ORM\HasLifecycleCallbacks]
#[Gedmo\SoftDeleteable()]
#[ApiResource(
    operations: [
        // En place pour garder l'uri de la ressource, mais on ne fait rien lors de l'appel pour désactiver la route
        new Get(
            uriTemplate: '/companies/{id}',
            controller: NotFoundAction::class,
            output: false,
            read: false
            // @todo checker si le remove de openApiContext est nécessaire
        ),
        new Patch(
            uriTemplate: '/companies/{id}/reminder',
            denormalizationContext: ['groups' => ['company:write:reminder']],
            securityPostDenormalize: self::OWNER_SECURITY,
            name: self::OPERATION_SET_REMINDER,
            openapi: new OpenApiOperation(
                summary: 'Mettre à jour le rappel de l\'enseigne',
                description: "Permet de mettre à jour le rappel de l'enseigne"
            )
        ),
        new Patch(
            uriTemplate: '/companies/{id}/config',
            denormalizationContext: ['groups' => ['company:write:config']],
            securityPostDenormalize: self::OWNER_SECURITY,
            name: self::OPERATION_UPDATE_CONFIG,
            processor: CompanyUpdateProcessor::class,
            openapi: new OpenApiOperation(
                summary: 'Mettre à jour la configuration de l\'enseigne',
                description: "Permet de mettre à jour la configuration de l'enseigne"
            )
        ),
        new Post(
            uriTemplate: '/companies/{id}/subscribe-with-fiducial',
            controller: SubscribeWithFiducial::class,
            security: 'true', // La sécurité est géré par le controller car ce n'est pas possible de le faire ici avec un input dto
            input: SubscribeWithFiducialInputDto::class,
            output: SubscribeWithFiducialOutputDto::class,
            read: false,
            name: self::OPERATION_SUBSCRIBE_WITH_FIDUCIAL,
            openapi: new OpenApiOperation(
                summary: 'S\'abonner à MaCarteFid via Fiducial',
                description: 'Permet de s\'abonner à MaCarteFid via un code de société Fiducial'
            )
        ),
        new Get(
            uriTemplate: '/companies/{id}/start-payment-subscription',
            controller: CreateSubscriptionPaymentSession::class,
            output: PaymentStartDto::class,
            name: self::OPERATION_START_PAYMENT_SUBSCRIPTION,
            openapi: new OpenApiOperation(
                summary: 'S\'abonner à MaCarteFid via Stripe',
                description: "Permet de s'abonner à MaCarteFid en étant rediriger vers une page de paiement Stripe"
            )
        ),
        new Get(
            uriTemplate: '/companies/{id}/start-payment-sms',
            controller: CreateSMSPaymentSession::class,
            openapi: new OpenApiOperation(
                summary: 'Acheter du crédit SMS',
                description: "Permet d'acheter du crédit SMS en étant rediriger vers une page de paiement Stripe",
                parameters: [
                    new OpenApiParameter(
                        name: 'id',
                        in: 'path',
                        description: 'Company identifier',
                        required: true,
                        allowEmptyValue: false,
                        schema: ['type' => 'string'],
                        style: 'simple',
                    ),
                    new OpenApiParameter(
                        name: 'referer',
                        in: 'query',
                        description: "Url sur laquelle rediriger l'utilisateur après la session de paiement",
                        required: false,
                        allowEmptyValue: false,
                        schema: ['type' => 'string'],
                    ),
                ]),
            output: PaymentStartDto::class,
            name: self::OPERATION_START_PAYMENT_SMS
        ),
        new Get(
            uriTemplate: '/companies/{id}/check-payment-subscription',
            controller: CheckSubscriptionPaymentSession::class,
            output: PaymentSessionStatusDto::class,
            name: self::OPERATION_CHECK_PAYMENT_SUBSCRIPTION,
            openapi: new OpenApiOperation(
                summary: "Vérifier l'état de l'abonnement",
                description: "Permet de vérifier l'état de l'abonnement de l'enseigne"
            )
        ),
        new Get(
            uriTemplate: '/companies/{id}/stats',
            controller: CompanyStats::class,
            output: CompanyStatsDto::class,
            name: self::OPERATION_GET_STATS,
            openapi: new OpenApiOperation(
                summary: "Récupérer les stats de l'enseigne",
                description: "Permet de récupérer diverses statistiques à propos de l'enseigne et de ses clients"
            )
        ),
        new Get(
            uriTemplate: '/companies/{id}/stats/export',
            controller: CompanyStatsExport::class,
            name: self::OPERATION_GET_STATS_EXPORT,
            openapi: new OpenApiOperation(
                summary: "Récupère les statistiques de l'enseigne au format Excel",
                description: "Permet de récupérer diverses statistiques à propos de l'enseigne et de ses clients au format Excel, une ligne par mois"
            )
        ),
        new Get(
            uriTemplate: '/companies/{id}/subscription',
            controller: CompanyGetSubscription::class,
            output: CompanyGetSubscriptionOutput::class,
            name: self::OPERATION_GET_SUBSCRIPTION,
            openapi: new OpenApiOperation(
                summary: "Récupérer l'abonnement de l'enseigne",
                description: "Permet de récupérer l'état de l'abonnement de l'enseigne"
            )
        ),
        new Patch(
            uriTemplate: '/companies/{id}/stats',
            denormalizationContext: ['groups' => ['company:write:stats']],
            validationContext: ['groups' => ['edit_stats']],
            name: self::OPERATION_PATCH_STATS,
            openapi: new OpenApiOperation(
                summary: "Mettre à jour l'ordre des stats de l'enseigne",
                description: "Permet de mettre à jour l'ordre d'affichage des stats de l'enseigne"
            )
        ),
        new Get(
            uriTemplate: '/companies/{id}/get-stripe-portal-url',
            controller: CompanyStripePortal::class,
            output: CompanyStripePortalDto::class,
            name: self::OPERATION_GET_STRIPE_PORTAL_URL,
            openapi: new OpenApiOperation(
                summary: "Obtenir l'URL du portail de facturation Stripe",
                description: "Permet d'obtenir l'URL du portail de facturation Stripe de l'enseigne"
            )
        ),
        new Post(
            uriTemplate: '/companies/{id}/check-pin',
            status: 200,
            controller: CompanyCheckPin::class,
            input: CompanyCheckPinInputDto::class,
            output: CompanyCheckPinOutputDto::class,
            name: self::OPERATION_CHECK_PIN,
            openapi: new OpenApiOperation(
                summary: "Vérifier le code PIN de l'enseigne",
                description: "Permet de faire une vérification du code PIN de l'enseigne afin d'obtenir un token qu'il faudra passer en paramètre des appels qui ont besoin d'avoir cette confirmation"
            )
        ),
        new Post(
            uriTemplate: '/companies/{id}/forgot-pin',
            status: 200,
            controller: CompanyForgotPin::class,
            input: EmptyInputDto::class,
            output: EmptyOutputDto::class,
            name: self::OPERATION_FORGOT_PIN,
            openapi: new OpenApiOperation(
                summary: "Obtenir le code PIN de l'enseigne par SMS",
                description: "Permet d'envoyer un SMS au numéro de téléphone configuré au niveau de l'enseigne pour lui rappeler son code PIN"
            )
        ),
        new Post(
            uriTemplate: '/companies/{id}/cancel-subscription',
            status: 200,
            controller: CompanyCancelSubscription::class,
            input: EmptyInputDto::class,
            output: EmptyOutputDto::class,
            name: self::OPERATION_CANCEL_SUBSCRIPTION,
            openapi: new OpenApiOperation(
                summary: "Annuler l'abonnement de l'enseigne",
                description: "Permet d'annuler l'abonnement de l'enseigne. L'abonnement restera actif jusqu'à la fin de la période en cours"
            )
        ),
        new Get(
            uriTemplate: '/companies/{id}/generate-qr-code',
            controller: CompanyGenerateQrCode::class,
            openapi: new OpenApiOperation(
                responses: [
                    Response::HTTP_OK => [
                        'description' => 'QR code image',
                        'content' => [
                            'image/png' => [
                                'schema' => [
                                    'type' => 'string',
                                    'format' => 'binary',
                                ],
                            ],
                        ],
                    ],
                ],
                summary: "Générer le QR code de l'enseigne",
                description: "Permet de générer un QR code pointant vers la page de création de client de l'enseigne"
            ),
            name: self::OPERATION_GENERATE_QR_CODE
        ),
        new Delete(
            name: self::OPERATION_DELETE,
            openapi: new OpenApiOperation(
                summary: "Supprimer l'enseigne",
                description: "Permet d'archiver l'enseigne, son abonnement sera automatiquement annulé"
            )
        ),
    ],
    normalizationContext: ['groups' => ['read']],
    denormalizationContext: ['groups' => ['write']],
    security: self::OWNER_SECURITY
)]
class Company implements UserInterface, PasswordAuthenticatedUserInterface, \Stringable
{
    use Timestampable;
    use SoftDeleteableEntity;

    private const string OWNER_SECURITY = 'is_granted(\'ROLE_USER\') and object == user';

    public const OPERATION_UPDATE_CONFIG = 'companyUpdateConfig';
    public const OPERATION_START_PAYMENT_SMS = 'companyStartPaymentSMS';
    public const OPERATION_SET_REMINDER = 'companySetReminder';
    public const string OPERATION_DELETE = 'companyDelete';
    public const string OPERATION_CANCEL_SUBSCRIPTION = 'companyCancelSubscription';
    public const string OPERATION_FORGOT_PIN = 'companyForgotPin';
    public const string OPERATION_CHECK_PIN = 'companyCheckPin';
    public const string OPERATION_GET_STRIPE_PORTAL_URL = 'companyGetStripePortalUrl';
    public const string OPERATION_PATCH_STATS = 'companyPatchStats';
    public const string OPERATION_GET_SUBSCRIPTION = 'companyGetSubscription';
    public const string OPERATION_GET_STATS = 'companyGetStats';
    public const string OPERATION_GET_STATS_EXPORT = 'companyGetStatsExport';
    public const string OPERATION_CHECK_PAYMENT_SUBSCRIPTION = 'companyCheckPaymentSubscription';
    public const string OPERATION_START_PAYMENT_SUBSCRIPTION = 'companyStartPaymentSubscription';
    public const string OPERATION_SUBSCRIBE_WITH_FIDUCIAL = 'companySubscribeWithFiducial';
    public const string OPERATION_GENERATE_QR_CODE = 'companyGenerateQrCode';

    public const COMPANY_TYPE_DEFAULT = 'default';
    public const COMPANY_TYPE_FIDUCIAL = 'fiducial';

    public const ACTIVITY_AREAS = [
        'Bijouterie',
        'Camping',
        'Chaussures',
        'Coiffure',
        'Commerce alimentaire',
        'Commerce de détail',
        'Fleuriste',
        'Garagiste',
        'Hôtel',
        'Institut',
        'Jardinerie',
        'Lingerie',
        'Parfumerie',
        'Prêt à porter',
        'Restauration',
        'Autre',
    ];

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    #[Groups(['read'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Identifiant unique de l\'enseigne',
        ]
    )]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    #[CustomAssert\PinCode]
    #[Groups(['write', 'company:write:config'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Code PIN de l\'enseigne. Peut être demandé lorsque des opérations d\'administration sont effectuées',
            'example' => '1234',
        ]
    )]
    private ?string $pin = null;

    #[ORM\Column(length: 255)]
    #[Assert\Length(max: 255)]
    #[Assert\Email]
    #[Assert\NotBlank]
    #[Groups(['read'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Adresse email de l\'enseigne',
            'example' => '<EMAIL>',
        ]
    )]
    private ?string $email = null;

    #[ORM\Column(length: 255)]
    private ?string $password = null;

    #[CustomAssert\PasswordRequirements]
    #[Groups(['write', 'company:write:config'])]
    #[ApiProperty(
        openapiContext: [
            'description' => "Mot de passe de l'enseigne. Sera chiffré avant d'être enregistré. ".CustomAssert\PasswordRequirements::PASSWORD_RULE,
            'example' => 'MyS3cr€tP@$$w0rd',
        ]
    )]
    private ?string $plainPassword = null;

    #[ORM\Column(length: 255)]
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    #[CustomAssert\OnlyLetters(['payload' => ['fieldLabel' => 'prénom']])]
    #[Groups(['read', 'write', 'company:write:config'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Prénom du responsable de l\'enseigne',
            'example' => 'François',
        ]
    )]
    private ?string $firstname = null;

    #[ORM\Column(length: 255)]
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    #[CustomAssert\OnlyLetters(['payload' => ['fieldLabel' => 'nom']])]
    #[Groups(['read', 'write', 'company:write:config'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Nom du responsable de l\'enseigne',
            'example' => 'Dupont',
        ]
    )]
    private ?string $lastname = null;

    #[ORM\Column(length: 255)]
    #[CustomAssert\MobilePhone]
    #[Groups(['read', 'write', 'company:write:config'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Numéro de mobile du responsable de l\'enseigne',
            'example' => '0612345678',
        ]
    )]
    private ?string $phoneNumber = null;

    #[ORM\Column(length: 255)]
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    #[Groups(['read', 'write', 'company:write:config'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Nom de l\'enseigne',
            'example' => 'Mon Enseigne',
        ]
    )]
    private ?string $name = null;

    #[ORM\Column(length: 255)]
    #[CustomAssert\SenderId]
    #[Groups(['read', 'write', 'company:write:config'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Nom d\'expéditeur des SMS',
            'example' => 'MON ENSEIGNE',
        ]
    )]
    private ?string $senderId = null;

    #[ORM\Column(nullable: true)]
    #[Groups(['read'])]
    #[ApiProperty(
        openapiContext: [
            'description' => '`true` si le nom d\'expéditeur des SMS a été vérifié par l\'API Link Mobility',
        ]
    )]
    private ?bool $senderIdVerified = null;

    #[ORM\Column(length: 255)]
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    #[Assert\Choice(choices: self::ACTIVITY_AREAS)]
    #[Groups(['read', 'write', 'company:write:config'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Secteur d\'activité de l\'enseigne parmis ceux qui sont prédéfinis',
            'example' => 'Bijouterie',
        ]
    )]
    private ?string $activityArea = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Assert\Length(max: 75)]
    #[Groups(['read', 'write', 'company:write:config'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Secteur d\'activité personnalisé de l\'enseigne',
            'example' => 'Bijoux fantaisie',
        ]
    )]
    private ?string $customActivityArea = null;

    #[ORM\Column(length: 255)]
    #[CustomAssert\PostalCode]
    #[Assert\NotBlank]
    #[Groups(['read', 'write', 'company:write:config'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Code postal de l\'enseigne',
            'example' => '33110',
        ]
    )]
    private ?string $postalCode = null;

    #[ORM\Column(length: 255)]
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    #[Groups(['read', 'write', 'company:write:config'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Ville de l\'enseigne',
            'example' => 'Le Bouscat',
        ]
    )]
    private ?string $city = null;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\IsTrue]
    #[Groups('write')]
    #[ApiProperty(
        openapiContext: [
            'description' => '`true` si les CGU ont été acceptées par l\'utilisateur lors de la création de l\'enseigne',
        ]
    )]
    private ?bool $cguAccepted = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Groups('read')]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Type de l\'enseigne',
            'enum' => [
                self::COMPANY_TYPE_DEFAULT,
                self::COMPANY_TYPE_FIDUCIAL,
            ],
        ]
    )]
    private ?string $companyType = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    #[Assert\Length(max: 180)]
    #[Groups(['read', 'write', 'company:write:reminder'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Rappel personnalisé pouvant être affiché sur la page d\'accueil de l\'enseigne',
            'example' => 'Penser à rappeler aux clients les jours de fermeture de la boutique la semaine prochaine',
        ]
    )]
    private ?string $reminder = null;

    #[ORM\Column(type: Types::SIMPLE_ARRAY, nullable: true)]
    #[Assert\Choice(callback: [CompanyStatsType::class, 'values'], multiple: true, groups: ['edit_stats'])]
    #[Groups(['read', 'company:write:stats'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Configuration de l\'ordre d\'affichage des statistiques de l\'enseigne',
            'example' => [
                'new_clients',
                'nb_transactions',
                'total_ca',
                'avg_ca',
                'nb_voucher',
                'total_voucher_amount',
                'sms_auto_sent',
                'sms_campaign_sent',
            ],
        ]
    )]
    private array $stats = [];

    #[ORM\Column(type: 'json_document', options: ['jsonb' => true])]
    #[Groups(['read', 'write', 'company:write:config'])]
    private SecuredArea $securedAreas;

    #[ORM\Column]
    #[Groups('read')]
    #[ApiProperty(
        openapiContext: [
            'description' => '`true` si l\'enseigne a entièrement terminé son inscription ainsi que la configuration initiale',
        ]
    )]
    private bool $registrationCompleted = false;

    #[ORM\Column(nullable: true)]
    #[Groups(['read', 'write', 'company:write:config'])]
    #[ApiProperty(
        openapiContext: [
            'example' => 'https://g.page/r/CXJCRz6ghNleEB5/review',
            'description' => <<<EOF
URL qui sera envoyée aux clients pour leur demander de laisser un avis sur la page Google de l'enseigne
Formats acceptés :
- https://g.page/r/CXJCRz6ghNleEB5/review
- https://g.co/kgs/KhbwdGC
- https://search.google.com/local/writereview?placeid=ChIJF5UMMCcoVQ0RAcpLN9pnMvI
EOF
        ]
    )]
    #[Assert\Url(protocols: ['https'], requireTld: true)]
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank(allowNull: true)]
    #[Assert\Regex('/^(https:\/\/g\.page\/r\/[\w-]+\/review|https:\/\/g\.co\/kgs\/[\w-]+|https:\/\/search\.google\.com\/local\/writereview\?placeid=[\w-]+)$/')]
    private ?string $googleReviewUrl = null;

    /** Flag servant à identifier les company de "test" en prod pour les exclures des stats. */
    #[ORM\Column]
    private bool $fakeForTesting = false;

    #[ORM\OneToOne(mappedBy: 'company', cascade: ['persist', 'remove'])]
    #[Groups(['read'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Informations sur l\'abonnement de l\'enseigne',
        ]
    )]
    private ?Subscription $subscription = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTimeInterface $archivedAt = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $archiveComment = null;

    /**
     * @var Collection<int, CompanyHistory>
     */
    #[ORM\OneToMany(mappedBy: 'company', targetEntity: CompanyHistory::class, cascade: ['persist'], orphanRemoval: true)]
    private Collection $companyHistories;

    /**
     * @var Collection<int, Customer>
     */
    #[ORM\OneToMany(mappedBy: 'company', targetEntity: Customer::class, orphanRemoval: true)]
    private Collection $customers;

    /**
     * @var Collection<int, Campaign>
     */
    #[ORM\OneToMany(mappedBy: 'company', targetEntity: Campaign::class, cascade: ['persist'], orphanRemoval: true)]
    private Collection $campaigns;

    public function __construct()
    {
        $this->companyHistories = new ArrayCollection();
        $this->customers = new ArrayCollection();
        $this->campaigns = new ArrayCollection();
        $this->securedAreas = new SecuredArea();
        $this->stats = CompanyStatsType::values();
    }

    public function __toString(): string
    {
        return (string) $this->name;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPin(): ?string
    {
        return $this->pin;
    }

    public function setPin(string $pin): self
    {
        $this->pin = $pin;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getPassword(): ?string
    {
        return $this->password;
    }

    public function setPassword(string $password): self
    {
        $this->password = $password;

        return $this;
    }

    public function getFirstname(): ?string
    {
        return $this->firstname;
    }

    public function setFirstname(string $firstname): self
    {
        $this->firstname = $firstname;

        return $this;
    }

    public function getLastname(): ?string
    {
        return $this->lastname;
    }

    public function setLastname(string $lastname): self
    {
        $this->lastname = $lastname;

        return $this;
    }

    public function getPhoneNumber(): ?string
    {
        return $this->phoneNumber;
    }

    public function setPhoneNumber(string $phoneNumber): self
    {
        $this->phoneNumber = $phoneNumber;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getSenderId(): ?string
    {
        return $this->senderId;
    }

    public function setSenderId(?string $senderId): self
    {
        $this->senderId = $senderId;

        return $this;
    }

    public function isSenderIdVerified(): ?bool
    {
        return $this->senderIdVerified;
    }

    public function setSenderIdVerified(?bool $senderIdVerified): Company
    {
        $this->senderIdVerified = $senderIdVerified;

        return $this;
    }

    public function getActivityArea(): ?string
    {
        return $this->activityArea;
    }

    public function setActivityArea(string $activityArea): self
    {
        $this->activityArea = $activityArea;

        return $this;
    }

    public function getCustomActivityArea(): ?string
    {
        return $this->customActivityArea;
    }

    public function setCustomActivityArea(?string $customActivityArea): self
    {
        $this->customActivityArea = $customActivityArea;

        return $this;
    }

    public function getPostalCode(): ?string
    {
        return $this->postalCode;
    }

    public function setPostalCode(string $postalCode): self
    {
        $this->postalCode = $postalCode;

        return $this;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(string $city): self
    {
        $this->city = $city;

        return $this;
    }

    public function isCguAccepted(): ?bool
    {
        return $this->cguAccepted;
    }

    public function setCguAccepted(bool $cguAccepted): self
    {
        $this->cguAccepted = $cguAccepted;

        return $this;
    }

    public function getCompanyType(): ?string
    {
        return $this->companyType;
    }

    public function setCompanyType(string $companyType): self
    {
        $this->companyType = $companyType;

        return $this;
    }

    public function isFiducial(): ?bool
    {
        return self::COMPANY_TYPE_FIDUCIAL === $this->companyType;
    }

    public function isDefaultType(): ?bool
    {
        return self::COMPANY_TYPE_DEFAULT === $this->companyType;
    }

    public function getReminder(): ?string
    {
        return $this->reminder;
    }

    public function setReminder(?string $reminder): self
    {
        $this->reminder = $reminder;

        return $this;
    }

    /**
     * @return CompanyStatsType[]
     */
    public function getStats(): array
    {
        return $this->stats;
    }

    /**
     * @param CompanyStatsType[] $stats
     */
    public function setStats(array $stats): self
    {
        $this->stats = $stats;

        return $this;
    }

    public function getSecuredAreas(): SecuredArea
    {
        return $this->securedAreas;
    }

    public function setSecuredAreas(SecuredArea $securedAreas): Company
    {
        $this->securedAreas = $securedAreas;

        return $this;
    }

    public function isRegistrationCompleted(): bool
    {
        return $this->registrationCompleted;
    }

    public function setRegistrationCompleted(bool $registrationCompleted): Company
    {
        $this->registrationCompleted = $registrationCompleted;

        return $this;
    }

    public function getSubscription(): ?Subscription
    {
        return $this->subscription;
    }

    public function setSubscription(?Subscription $subscription): self
    {
        // set the owning side of the relation if necessary
        if ($subscription && $subscription->getCompany() !== $this) {
            $subscription->setCompany($this);
        }

        $this->subscription = $subscription;

        return $this;
    }

    /**
     * @return Collection<int, CompanyHistory>
     */
    public function getCompanyHistories(): Collection
    {
        return $this->companyHistories;
    }

    public function addCompanyHistory(CompanyHistory $companyHistory): self
    {
        if (!$this->companyHistories->contains($companyHistory)) {
            $this->companyHistories->add($companyHistory);
            $companyHistory->setCompany($this);
        }

        return $this;
    }

    public function removeCompanyHistory(CompanyHistory $companyHistory): self
    {
        if ($this->companyHistories->removeElement($companyHistory)) {
            // set the owning side to null (unless already changed)
            if ($companyHistory->getCompany() === $this) {
                $companyHistory->setCompany(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Customer>
     */
    public function getCustomers(): Collection
    {
        return $this->customers;
    }

    public function addCustomer(Customer $customer): self
    {
        if (!$this->customers->contains($customer)) {
            $this->customers->add($customer);
            $customer->setCompany($this);
        }

        return $this;
    }

    public function removeCustomer(Customer $customer): self
    {
        if ($this->customers->removeElement($customer)) {
            // set the owning side to null (unless already changed)
            if ($customer->getCompany() === $this) {
                $customer->setCompany(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Campaign>
     */
    public function getCampaigns(): Collection
    {
        return $this->campaigns;
    }

    #[Groups(['read'])]
    public function getCampaignFidelite(): CampaignFidelite|false
    {
        return $this->campaigns->filter(function (Campaign $campaign) {
            return Campaign::TYPE_FIDELITE === $campaign->getType();
        })->first();
    }

    #[Groups(['read'])]
    public function getCampaignAnniversaire(): CampaignAnniversaire|false
    {
        return $this->campaigns->filter(function (Campaign $campaign) {
            return Campaign::TYPE_ANNIVERSAIRE === $campaign->getType();
        })->first();
    }

    #[Groups(['read'])]
    public function getCampaignBienvenue(): CampaignBienvenue|false
    {
        return $this->campaigns->filter(function (Campaign $campaign) {
            return Campaign::TYPE_BIENVENUE === $campaign->getType();
        })->first();
    }

    #[Groups(['read'])]
    public function getCampaignExpirationCheque(): CampaignExpirationCheque|false
    {
        return $this->campaigns->filter(function (Campaign $campaign) {
            return Campaign::TYPE_EXPIRATION_CHEQUE === $campaign->getType();
        })->first();
    }

    #[Groups(['read'])]
    public function getCampaignDormant(): CampaignDormant|false
    {
        return $this->campaigns->filter(function (Campaign $campaign) {
            return Campaign::TYPE_DORMANT === $campaign->getType();
        })->first();
    }

    #[Groups(['read'])]
    public function getCampaignGoogleReview(): CampaignGoogleReview|false
    {
        return $this->campaigns->filter(function (Campaign $campaign) {
            return Campaign::TYPE_GOOGLE_REVIEW === $campaign->getType();
        })->first();
    }

    /** @return Collection<int, CampaignSms> */
    public function getCampaignsSms(): Collection
    {
        return $this->campaigns->filter(function (Campaign $campaign) {
            return Campaign::TYPE_SMS === $campaign->getType();
        });
    }

    public function getCampaignsAuto(): Collection
    {
        return $this->campaigns->filter(function (Campaign $campaign) {
            return Campaign::TYPE_SMS !== $campaign->getType();
        });
    }

    public function addCampaign(Campaign $campaign): self
    {
        if (!$this->campaigns->contains($campaign)) {
            $this->campaigns->add($campaign);
            $campaign->setCompany($this);
        }

        return $this;
    }

    public function setCampaignFidelite(CampaignFidelite $campaignFidelite): self
    {
        if (!$this->campaigns->contains($campaignFidelite)) {
            if ($existingCampaign = $this->getCampaignFidelite()) {
                $this->campaigns->removeElement($existingCampaign);
            }

            $this->campaigns->add($campaignFidelite);
            $campaignFidelite->setCompany($this);
        }

        return $this;
    }

    public function setCampaignBienvenue(CampaignBienvenue $campaignBienvenue): self
    {
        if (!$this->campaigns->contains($campaignBienvenue)) {
            if ($existingCampaign = $this->getCampaignBienvenue()) {
                $this->campaigns->removeElement($existingCampaign);
            }

            $this->campaigns->add($campaignBienvenue);
            $campaignBienvenue->setCompany($this);
        }

        return $this;
    }

    public function setCampaignAnniversaire(CampaignAnniversaire $campaignAnniversaire): self
    {
        if (!$this->campaigns->contains($campaignAnniversaire)) {
            if ($existingCampaign = $this->getCampaignAnniversaire()) {
                $this->campaigns->removeElement($existingCampaign);
            }

            $this->campaigns->add($campaignAnniversaire);
            $campaignAnniversaire->setCompany($this);
        }

        return $this;
    }

    public function setCampaignDormant(CampaignDormant $campaignDormant): self
    {
        if (!$this->campaigns->contains($campaignDormant)) {
            if ($existingCampaign = $this->getCampaignDormant()) {
                $this->campaigns->removeElement($existingCampaign);
            }

            $this->campaigns->add($campaignDormant);
            $campaignDormant->setCompany($this);
        }

        return $this;
    }

    public function setCampaignExpirationCheque(CampaignExpirationCheque $campaignExpirationCheque): self
    {
        if (!$this->campaigns->contains($campaignExpirationCheque)) {
            if ($existingCampaign = $this->getCampaignExpirationCheque()) {
                $this->campaigns->removeElement($existingCampaign);
            }

            $this->campaigns->add($campaignExpirationCheque);
            $campaignExpirationCheque->setCompany($this);
        }

        return $this;
    }

    public function setCampaignGoogleReview(CampaignGoogleReview $campaignGoogleReview): self
    {
        if (!$this->campaigns->contains($campaignGoogleReview)) {
            if ($existingCampaign = $this->getCampaignGoogleReview()) {
                $this->campaigns->removeElement($existingCampaign);
            }

            $this->campaigns->add($campaignGoogleReview);
            $campaignGoogleReview->setCompany($this);
        }

        return $this;
    }

    public function removeCampaign(Campaign $campaign): self
    {
        if ($this->campaigns->removeElement($campaign)) {
            // set the owning side to null (unless already changed)
            if ($campaign->getCompany() === $this) {
                $campaign->setCompany(null);
            }
        }

        return $this;
    }

    public function getGoogleReviewUrl(): ?string
    {
        return $this->googleReviewUrl;
    }

    public function setGoogleReviewUrl(?string $googleReviewUrl): self
    {
        $this->googleReviewUrl = $googleReviewUrl;

        return $this;
    }

    #[Groups(['read'])]
    #[ApiProperty(
        openapiContext: [
            'description' => '`true` si l\'enseigne a été archivée depuis le Backoffice',
        ]
    )]
    public function isArchived(): bool
    {
        return null !== $this->archivedAt;
    }

    public function getArchivedAt(): ?\DateTimeInterface
    {
        return $this->archivedAt;
    }

    public function setArchivedAt(?\DateTimeInterface $archivedAt): self
    {
        $this->archivedAt = $archivedAt;

        return $this;
    }

    public function getArchiveComment(): ?string
    {
        return $this->archiveComment;
    }

    public function setArchiveComment(?string $archiveComment): self
    {
        $this->archiveComment = $archiveComment;

        return $this;
    }

    public function isFakeForTesting(): bool
    {
        return $this->fakeForTesting;
    }

    public function setFakeForTesting(bool $fakeForTesting): self
    {
        $this->fakeForTesting = $fakeForTesting;

        return $this;
    }

    public function getPlainPassword(): ?string
    {
        return $this->plainPassword;
    }

    public function setPlainPassword(?string $plainPassword): self
    {
        $this->plainPassword = $plainPassword;

        return $this;
    }

    /**
     * Génère un SenderId à partir de $this→name
     * en respectant les règles de l'API Link mobility :
     * - 11 caractères alphanumériques maximum
     * - ne doit pas contenir que des chiffres
     * - les caractères spéciaux sont à bannir (caractères UTF8, accents en tout genre)
     * - idéalement, les lettres [a-z][A-Z][0-9]. Le caractère “_” et l’espace sont autorisés.
     */
    public static function generateSenderId(string $name): string
    {
        // conservation des caractères autorisés uniquement
        preg_match_all('/[a-zA-Z0-9_ ]/m', $name, $matches);
        $senderId = implode('', $matches[0]);

        // si la chaine est trop longue on retire un underscore ou un espace à la fois
        // jusqu'à atteindre 11 caractères. Si ce n'est pas suffisant on retire le nombre de
        // caractères en trop en partant de la fin
        if (strlen($senderId) > 11) {
            $counts = array_count_values($matches[0]);
            $nbUnderscores = $counts['_'] ?? 0;
            $nbSpaces = $counts[' '] ?? 0;
            $nbCharToRemovePerIteration = 1;
            do {
                if ($nbUnderscores > 0) {
                    $senderId = str_replace('_', '', $senderId, $nbCharToRemovePerIteration);
                    --$nbUnderscores;
                } elseif ($nbSpaces > 0) {
                    $senderId = str_replace(' ', '', $senderId, $nbCharToRemovePerIteration);
                    --$nbSpaces;
                } else {
                    $senderId = substr($senderId, 0, 11);
                }
            } while (strlen($senderId) > 11);
        }

        return $senderId;
    }

    public function getRoles(): array
    {
        return ['ROLE_USER'];
    }

    public function eraseCredentials(): void
    {
        $this->plainPassword = null;
    }

    public function getUserIdentifier(): string
    {
        return $this->email;
    }

    public function initCampaigns(): void
    {
        $campaigns = [
            new CampaignAnniversaire(),
            new CampaignBienvenue(),
            new CampaignDormant(),
            new CampaignExpirationCheque(),
            new CampaignFidelite(),
            new CampaignGoogleReview(),
        ];

        foreach ($campaigns as $campaign) {
            $this->addCampaign($campaign);
            $campaign->setSmsContent($campaign->getDefaultSmsContent());
        }
    }

    public static function fromDraft(CompanyDraft $companyDraft): self
    {
        $company = (new self())
            ->setPin($companyDraft->getPin())
            ->setEmail($companyDraft->getEmail())
            ->setPassword($companyDraft->getPassword())
            ->setFirstname($companyDraft->getFirstname())
            ->setLastname($companyDraft->getLastname())
            ->setPhoneNumber($companyDraft->getPhoneNumber())
            ->setName($companyDraft->getName())
            ->setSenderId($companyDraft->getSenderId())
            ->setActivityArea($companyDraft->getActivityArea())
            ->setPostalCode($companyDraft->getPostalCode())
            ->setCity($companyDraft->getCity())
            ->setCguAccepted($companyDraft->isCguAccepted());

        $company->initCampaigns();

        return $company;
    }
}
