<?php

namespace App\Entity;

use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Delete;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Link;
use ApiPlatform\Metadata\Patch;
use ApiPlatform\Metadata\Post;
use ApiPlatform\OpenApi\Model\Operation as OpenApiOperation;
use App\Entity\Trait\Timestampable;
use App\Filter\CustomerSearchFilter;
use App\Repository\CustomerRepository;
use App\Security\Voter\CompanySubresourceVoter;
use App\Security\Voter\CustomerCreateAccountAnonymousVoter;
use App\Validator\Constraints as CustomAssert;
use App\Validator\PhoneNumberAvailable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use libphonenumber\NumberParseException;
use libphonenumber\PhoneNumber;
use libphonenumber\PhoneNumberFormat;
use libphonenumber\PhoneNumberUtil;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Serializer\Annotation\Context;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Serializer\Annotation\SerializedName;
use Symfony\Component\Serializer\Normalizer\DateTimeNormalizer;
use Symfony\Component\Validator\Constraints as Assert;

use function Symfony\Component\Clock\now;

#[ORM\Entity(repositoryClass: CustomerRepository::class)]
#[UniqueEntity(fields: ['company', 'phoneNumber'], message: PhoneNumberAvailable::MESSAGE, errorPath: 'phoneNumber')]
#[PhoneNumberAvailable]
#[ORM\HasLifecycleCallbacks]
#[ApiResource(
    uriTemplate: '/companies/{companyId}/customers',
    operations: [
        new GetCollection(
            security: CompanySubresourceVoter::RULE,
            name: self::OPERATION_GET_COLLECTION,
            openapi: new OpenApiOperation(
                summary: "Lister ou Rechercher les clients de l'enseigne",
                description: "Permet de lister ou de rechercher les clients de l'enseigne"
            )
        ),
        new Post(
            security: CompanySubresourceVoter::RULE,
            securityPostDenormalize: self::OWNER_SECURITY,
            read: false,
            name: self::OPERATION_POST,
            openapi: new OpenApiOperation(
                summary: "Création d'un nouveau client",
                description: 'Permet de créer un nouveau client'
            )
        ),
        new Post(
            uriTemplate: '/companies/{companyId}/create-customer-account',
            security: "true",
            securityPostDenormalize: CustomerCreateAccountAnonymousVoter::RULE_POST_DENORMALIZE,
            read: false,
            name: self::OPERATION_POST_ANONYMOUS,
            openapi: new OpenApiOperation(
                summary: "Création d'un nouveau client sans JWT",
                description: 'Permet de créer un nouveau client sans JWT'
            )
        ),
    ],
    uriVariables: [
        'companyId' => new Link(toProperty: 'company', fromClass: Company::class),
    ],

    normalizationContext: ['groups' => ['customer:read']],
    denormalizationContext: ['groups' => ['customer:write']],
    security: "is_granted('ROLE_USER')"
)]
#[ApiResource(
    uriTemplate: '/companies/{companyId}/customers/{id}',
    operations: [
        new Get(
            security: self::OWNER_SECURITY,
            name: self::OPERATION_GET,
            openapi: new OpenApiOperation(
                summary: "Récupération des information d'un client",
                description: "Permet de récupérer les informations d'un client ainsi que ses chèques"
            )
        ),
        new Patch(
            securityPostDenormalize: self::OWNER_SECURITY,
            name: self::OPERATION_PATCH,
            openapi: new OpenApiOperation(
                summary: "Mise à jour des informations d'un client",
                description: "Permet de mettre à jour les informations d'un client"
            )
        ),
        new Patch(
            uriTemplate: '/companies/{companyId}/customers/{id}/loyalty-points',
            denormalizationContext: ['groups' => ['customer:write:loyalty-points']],
            securityPostDenormalize: self::OWNER_SECURITY,
            name: self::OPERATION_SET_LOYALTY_POINTS,
            openapi: new OpenApiOperation(
                summary: "Mise à jour du nombre de points de fidélité d'un client",
                description: "Permet de mettre à jour le nombre de points de fidélité d'un client"
            )
        ),
        new Delete(
            security: self::OWNER_SECURITY,
            name: self::OPERATION_DELETE,
            openapi: new OpenApiOperation(
                summary: "Suppression d'un client",
                description: 'Permet de supprimer un client'
            )
        ),
    ],
    uriVariables: [
        'companyId' => new Link(toProperty: 'company', fromClass: Company::class),
        'id' => new Link(fromClass: Customer::class),
    ],

    normalizationContext: ['groups' => ['customer:read']],
    denormalizationContext: ['groups' => ['customer:write']],
    security: "is_granted('ROLE_USER')"
)]
#[ApiFilter(CustomerSearchFilter::class)]
class Customer implements \Stringable
{
    use Timestampable;

    public const OPERATION_GET = 'customerGet';
    public const OPERATION_GET_COLLECTION = 'customerGetCollection';
    public const OPERATION_POST = 'customerPost';
    public const OPERATION_POST_ANONYMOUS = 'customerPostAnonymous';
    public const OPERATION_PATCH = 'customerPatch';
    public const OPERATION_SET_LOYALTY_POINTS = 'customerSetLoyaltyPoints';
    public const OPERATION_DELETE = 'customerDelete';

    private const string OWNER_SECURITY = 'object.getCompany() == user';
    public const CIVILITIES = ['Monsieur', 'Madame', 'Non genré'];
    public const FRENCH_PHONE_COUNTRY_CODE = 'fr';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    #[Groups('customer:read')]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Identifiant unique du client',
        ]
    )]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'customers')]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['customer:write'])]
    #[ApiProperty(
        openapiContext: [
            'example' => '/api/companies/12',
            'description' => 'Enseigne à laquelle le client est rattaché - format IRI',
        ]
    )]
    private ?Company $company = null;

    #[ORM\Column(length: 255)]
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    #[CustomAssert\OnlyLetters(['payload' => ['fieldLabel' => 'prénom']])]
    #[Groups(['customer:read', 'customer:write', 'sms:read'])]
    #[ApiProperty(
        openapiContext: [
            'example' => 'François',
            'description' => 'Prénom du client',
        ]
    )]
    private string $firstname = '';

    #[ORM\Column(length: 255)]
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    #[CustomAssert\OnlyLetters(['payload' => ['fieldLabel' => 'nom']])]
    #[Groups(['customer:read', 'customer:write', 'sms:read'])]
    #[ApiProperty(
        openapiContext: [
            'example' => 'Dupont',
            'description' => 'Nom du client',
        ]
    )]
    private string $lastname = '';

    #[ORM\Column(length: 255)]
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    #[Assert\Choice(choices: self::CIVILITIES)]
    #[Groups(['customer:read', 'customer:write'])]
    #[ApiProperty(
        openapiContext: [
            'example' => 'Monsieur',
            'description' => 'Civilité du client',
        ]
    )]
    private string $civility = '';

    #[ORM\Column(length: 255, nullable: true)]
    #[CustomAssert\MobilePhone(['payload' => ['international' => true]])]
    #[Groups(['customer:read', 'customer:write'])]
    #[ApiProperty(
        openapiContext: [
            'example' => '0612345678',
            'description' => 'Numéro de téléphone du client',
        ]
    )]
    private ?string $phoneNumber = '';

    /**
     * Champ utilisé pour les recherches dans la base de données.
     */
    #[ORM\Column(length: 255, nullable: true)]
    private ?string $phoneNumberFormatted = '';

    #[ORM\Column(length: 10, nullable: true)]
    #[Assert\NotBlank]
    #[Assert\Choice(choices: CustomAssert\MobilePhone::ALLOWED_COUNTRY_CODES)]
    #[Groups(['customer:read', 'customer:write'])]
    #[ApiProperty(
        openapiContext: [
            'nullable' => false,
            'example' => 'fr',
            'description' => 'Code pays du client',
        ]
    )]
    private ?string $phoneNumberCountryCode = 'fr';

    #[ORM\Column(nullable: true)]
    #[Groups(['customer:read'])]
    #[ApiProperty(
        openapiContext: [
            'example' => true,
            'description' => 'Indique si le numéro de téléphone du client a bien été validé suite à l\'envoi d\'un premier SMS',
        ]
    )]
    private ?bool $phoneNumberVerified = null;

    #[ORM\Column()]
    #[Groups(['customer:read'])]
    #[ApiProperty(
        openapiContext: [
            'example' => false,
            'description' => '`true` si le client a demandé à ne plus recevoir de SMS via le STOP_SMS ou nofid.net',
        ]
    )]
    private bool $stopSmsReceived = false;

    #[ORM\Column(length: 255, nullable: true)]
    #[Assert\Length(max: 255)]
    #[Assert\Email]
    #[Groups(['customer:read', 'customer:write'])]
    #[ApiProperty(
        openapiContext: [
            'example' => '<EMAIL>',
            'description' => 'Adresse e-mail du client',
        ]
    )]
    private ?string $email = null;

    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    #[Assert\Type(\DateTimeInterface::class)]
    #[Assert\Range(
        min: '1900-01-01',
        max: 'today',
    )]
    #[Context([DateTimeNormalizer::FORMAT_KEY => 'Y-m-d'])]
    #[Groups(['customer:read', 'customer:write'])]
    #[ApiProperty(
        openapiContext: [
            'format' => 'date',
            'description' => 'Date de naissance du client au format YYYY-MM-DD',
            'example' => '1980-11-25',
        ],
        jsonSchemaContext: [
            'format' => 'date',
        ])]
    private ?\DateTimeInterface $birthDate = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Assert\Length(exactly: 5)]
    #[Groups(['customer:read', 'customer:write'])]
    #[ApiProperty(
        openapiContext: [
            'example' => '33110',
            'description' => 'Code postal du client',
        ]
    )]
    private ?string $postalCode = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Assert\Length(max: 255)]
    #[Groups(['customer:read', 'customer:write'])]
    #[ApiProperty(
        openapiContext: [
            'example' => 'Le Bouscat',
            'description' => 'Ville du client',
        ]
    )]
    private ?string $city = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Assert\Length(max: 255)]
    #[Groups(['customer:read', 'customer:write'])]
    #[ApiProperty(
        openapiContext: [
            'example' => 'Note client',
            'description' => 'Note libre à propos du client',
        ]
    )]
    private ?string $note = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    #[Assert\Length(max: 500)]
    #[Groups(['customer:read', 'customer:write'])]
    #[ApiProperty(
        openapiContext: [
            'example' => 'Commentaire client',
            'description' => 'Commentaire libre à propos du client. Retours à la ligne autorisés',
        ]
    )]
    private ?string $comment = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    #[Groups(['customer:read'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Date à laquelle le commentaire lié au client a été ajouté ou modifié - format ISO 8601',
        ]
    )]
    private ?\DateTimeInterface $commentDate = null;

    #[ORM\Column]
    #[Groups(['customer:read', 'customer:write:loyalty-points', 'sms:read'])]
    #[ApiProperty(
        openapiContext: [
            'example' => 72,
            'description' => 'Nombre de points de fidélité actuels du client',
        ]
    )]
    private int $loyaltyPoints = 0;

    /**
     * @var Collection<int, CustomerHistory>
     */
    #[ORM\OneToMany(mappedBy: 'customer', targetEntity: CustomerHistory::class, cascade: ['persist'])]
    private Collection $customerHistories;

    /**
     * @var Collection<int, Voucher>
     */
    #[ORM\OneToMany(mappedBy: 'customer', targetEntity: Voucher::class, fetch: 'EXTRA_LAZY', cascade: ['persist'])]
    #[ORM\OrderBy(['createdAt' => 'ASC'])]
    #[Groups(['customer:read'])]
    private Collection $vouchers;

    /**
     * @var Collection<int, Sms>
     */
    #[ORM\OneToMany(mappedBy: 'customer', targetEntity: Sms::class)]
    private Collection $sms;

    /**
     * @var Collection<int, CustomerTransaction>
     */
    #[ORM\OneToMany(mappedBy: 'customer', targetEntity: CustomerTransaction::class, cascade: ['persist'])]
    //    #[Groups(['customer:read'])]
    private Collection $transactions;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    #[Groups(['customer:read'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Date de création du client - format ISO 8601',
        ]
    )]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $lastTransactionDate = null;

    // Champ rempli temporairement lors de la création d'un chèque pour le réutiliser plus tard dans la même requête.
    private ?Voucher $lastCreatedVoucher = null;

    // Champ rempli temporairement lors de la création d'une transaction pour le réutiliser plus tard dans la même requête.
    private ?CustomerTransaction $lastCreatedTransaction = null;

    public function __construct()
    {
        $this->customerHistories = new ArrayCollection();
        $this->vouchers = new ArrayCollection();
        $this->sms = new ArrayCollection();
        $this->transactions = new ArrayCollection();
    }

    public function __toString(): string
    {
        return sprintf('#%d %s %s', $this->id, $this->firstname, $this->lastname);
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getCompany(): ?Company
    {
        return $this->company;
    }

    public function setCompany(?Company $company): self
    {
        $this->company = $company;

        return $this;
    }

    public function getFirstname(): string
    {
        return $this->firstname;
    }

    public function setFirstname(string $firstname): self
    {
        $this->firstname = $firstname;

        return $this;
    }

    public function getLastname(): string
    {
        return $this->lastname;
    }

    public function setLastname(string $lastname): self
    {
        $this->lastname = $lastname;

        return $this;
    }

    public function getCivility(): string
    {
        return $this->civility;
    }

    public function setCivility(string $civility): self
    {
        $this->civility = $civility;

        return $this;
    }

    public function getPhoneNumber(): ?string
    {
        return $this->phoneNumber;
    }

    public function setPhoneNumber(?string $phoneNumber): self
    {
        $this->phoneNumber = $phoneNumber;

        return $this;
    }

    public function getPhoneNumberFormatted(): ?string
    {
        return $this->phoneNumberFormatted;
    }

    public function setPhoneNumberFormatted(?string $phoneNumberFormatted): self
    {
        $this->phoneNumberFormatted = $phoneNumberFormatted;

        return $this;
    }

    public function getPhoneNumberCountryCode(): ?string
    {
        return $this->phoneNumberCountryCode;
    }

    public function setPhoneNumberCountryCode(?string $phoneNumberCountryCode): self
    {
        $this->phoneNumberCountryCode = $phoneNumberCountryCode;

        return $this;
    }

    public function isPhoneNumberVerified(): ?bool
    {
        return $this->phoneNumberVerified;
    }

    public function setPhoneNumberVerified(?bool $phoneNumberVerified): self
    {
        $this->phoneNumberVerified = $phoneNumberVerified;

        return $this;
    }

    public function isStopSmsReceived(): bool
    {
        return $this->stopSmsReceived;
    }

    public function setStopSmsReceived(bool $stopSmsReceived): self
    {
        $this->stopSmsReceived = $stopSmsReceived;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getBirthDate(): ?\DateTimeInterface
    {
        return $this->birthDate;
    }

    public function setBirthDate(?\DateTimeInterface $birthDate): self
    {
        $this->birthDate = $birthDate;

        return $this;
    }

    public function getPostalCode(): ?string
    {
        return $this->postalCode;
    }

    public function setPostalCode(?string $postalCode): self
    {
        $this->postalCode = $postalCode;

        return $this;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(?string $city): self
    {
        $this->city = $city;

        return $this;
    }

    public function getNote(): ?string
    {
        return $this->note;
    }

    public function setNote(?string $note): self
    {
        $this->note = $note;

        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(?string $comment): self
    {
        if ($this->comment !== $comment) {
            $this->setCommentDate(now());
        }
        $this->comment = $comment;

        return $this;
    }

    public function getCommentDate(): ?\DateTimeInterface
    {
        return $this->commentDate;
    }

    public function setCommentDate(?\DateTimeInterface $commentDate): self
    {
        $this->commentDate = $commentDate;

        return $this;
    }

    public function getLoyaltyPoints(): int
    {
        return $this->loyaltyPoints;
    }

    public function setLoyaltyPoints(int $loyaltyPoints): self
    {
        $this->loyaltyPoints = $loyaltyPoints;

        return $this;
    }

    public function addLoyaltyPoints(int $loyaltyPoints): self
    {
        $this->loyaltyPoints += $loyaltyPoints;

        return $this;
    }

    public function removeLoyaltyPoints(int $loyaltyPoints): self
    {
        $this->loyaltyPoints -= $loyaltyPoints;

        return $this;
    }

    public function getLastTransactionDate(): ?\DateTimeInterface
    {
        return $this->lastTransactionDate;
    }

    public function setLastTransactionDate(?\DateTimeInterface $lastTransactionDate): Customer
    {
        $this->lastTransactionDate = $lastTransactionDate;

        return $this;
    }

    /**
     * @return Collection<int, CustomerHistory>
     */
    public function getCustomerHistories(): Collection
    {
        return $this->customerHistories;
    }

    public function addCustomerHistory(CustomerHistory $customerHistory): self
    {
        if (!$this->customerHistories->contains($customerHistory)) {
            $this->customerHistories->add($customerHistory);
            $customerHistory->setCustomer($this);
        }

        return $this;
    }

    public function removeCustomerHistory(CustomerHistory $customerHistory): self
    {
        if ($this->customerHistories->removeElement($customerHistory)) {
            // set the owning side to null (unless already changed)
            if ($customerHistory->getCustomer() === $this) {
                $customerHistory->setCustomer(null);
            }
        }

        return $this;
    }

    /**
     * @return Voucher[]
     */
    public function getVouchers(): array
    {
        return $this->vouchers->getValues();
    }

    /**
     * @return Voucher[]
     */
    #[Groups(['customer:read'])]
    #[SerializedName('vouchers')]
    #[ApiProperty(
        openapiContext: [
            'type' => 'array',
            'description' => 'Chèques fidélités du client',
            'items' => [
                '$ref' => '#/components/schemas/Voucher.jsonld-customer.read',
            ],
        ],
        jsonSchemaContext: [
            'type' => 'array',
            'description' => 'Chèques fidélités du client',
            'items' => [
                '$ref' => '#/definitions/Voucher.jsonld-customer.read',
            ],
        ]
    )]
    public function getAvailableVouchers(?string $type = null): array
    {
        $criteria = Criteria::create()
            ->andWhere(Criteria::expr()->eq('burnt', false))
            ->andWhere(
                Criteria::expr()->orX(
                    Criteria::expr()->gte('expirationDate', now()),
                    Criteria::expr()->isNull('expirationDate')
                )
            );

        if ($type && in_array($type, Voucher::TYPES)) {
            $criteria->andWhere(Criteria::expr()->eq('type', $type));
        }

        return $this->vouchers->matching($criteria)->getValues();
    }

    public function addVoucher(Voucher $voucher): self
    {
        if (!$this->vouchers->contains($voucher)) {
            $this->vouchers->add($voucher);
            $voucher->setCustomer($this);
        }

        return $this;
    }

    public function removeVoucher(Voucher $voucher): self
    {
        if ($this->vouchers->removeElement($voucher)) {
            // set the owning side to null (unless already changed)
            if ($voucher->getCustomer() === $this) {
                $voucher->setCustomer(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Sms>
     */
    public function getSms(): Collection
    {
        return $this->sms;
    }

    public function addSms(Sms $sms): self
    {
        if (!$this->sms->contains($sms)) {
            $this->sms->add($sms);
            $sms->setCustomer($this);
        }

        return $this;
    }

    public function removeSms(Sms $sms): self
    {
        if ($this->sms->removeElement($sms)) {
            // set the owning side to null (unless already changed)
            if ($sms->getCustomer() === $this) {
                $sms->setCustomer(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, CustomerTransaction>
     */
    public function getTransactions(): Collection
    {
        return $this->transactions;
    }

    public function addTransaction(CustomerTransaction $transaction): self
    {
        if (!$this->transactions->contains($transaction)) {
            $this->transactions->add($transaction);
            $transaction->setCustomer($this);
        }

        return $this;
    }

    public function removeTransaction(CustomerTransaction $transaction): self
    {
        if ($this->transactions->removeElement($transaction)) {
            // set the owning side to null (unless already changed)
            if ($transaction->getCustomer() === $this) {
                $transaction->setCustomer(null);
            }
        }

        return $this;
    }

    public function getLastCreatedVoucher(): ?Voucher
    {
        return $this->lastCreatedVoucher;
    }

    public function setLastCreatedVoucher(?Voucher $lastCreatedVoucher): Customer
    {
        $this->lastCreatedVoucher = $lastCreatedVoucher;

        return $this;
    }

    public function getLastCreatedTransaction(): ?CustomerTransaction
    {
        return $this->lastCreatedTransaction;
    }

    public function setLastCreatedTransaction(?CustomerTransaction $lastCreatedTransaction): Customer
    {
        $this->lastCreatedTransaction = $lastCreatedTransaction;

        return $this;
    }

    public function isFrenchPhoneNumber(): bool
    {
        return self::FRENCH_PHONE_COUNTRY_CODE === $this->phoneNumberCountryCode;
    }

    public function getPhoneNumberInstance(): ?PhoneNumber
    {
        try {
            return PhoneNumberUtil::getInstance()->parse(
                $this->getPhoneNumber(),
                $this->getPhoneNumberCountryCode()
            );
        } catch (NumberParseException) {
            return null;
        }
    }

    public function getInternationalPhoneNumber(): ?string
    {
        $phoneNumber = $this->getPhoneNumberInstance();

        return $phoneNumber ?
            PhoneNumberUtil::getInstance()->format($phoneNumber, PhoneNumberFormat::E164)
            : null
        ;
    }

    /**
     * Utilisé pour checker si les prérequis pour envoyé un SMS au client sont remplis.
     */
    public function canSendSms(): bool
    {
        return null !== $this->getPhoneNumber() && !$this->isStopSmsReceived();
    }
}
