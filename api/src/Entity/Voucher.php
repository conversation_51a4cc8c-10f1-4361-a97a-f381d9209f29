<?php

namespace App\Entity;

use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use App\Entity\Trait\Timestampable;
use App\Repository\VoucherRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

use function Symfony\Component\Clock\now;

#[ORM\Entity(repositoryClass: VoucherRepository::class)]
#[ORM\HasLifecycleCallbacks]
#[ApiResource(
    operations: [],
    normalizationContext: ['groups' => ['voucher:read']],
    denormalizationContext: ['groups' => ['write']],
)]
class Voucher
{
    use Timestampable;

    public const TYPES = [
        self::TYPE_FIDELITE, self::TYPE_ANNIVERSAIRE, self::TYPE_BIENVENUE, self::TYPE_DORMANT,
    ];

    public const TYPE_FIDELITE = 'fidelite';
    public const TYPE_ANNIVERSAIRE = 'anniversaire';
    public const TYPE_BIENVENUE = 'bienvenue';
    public const TYPE_DORMANT = 'dormant';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    #[Groups(['voucher:read', 'customer:read', 'sms:read'])]
    #[ApiProperty(
        openapiContext: [
            'example' => 45,
            'description' => 'Identifiant unique du chèque',
        ]
    )]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'vouchers')]
    #[ORM\JoinColumn(nullable: false, onDelete: 'CASCADE')]
    #[Groups(['voucher:read'])]
    #[ApiProperty(
        openapiContext: [
            'example' => '/api/companies/2/customers/31',
            'description' => 'Client auquel le chèque est rattaché - format IRI',
        ]
    )]
    private ?Customer $customer = null;

    #[ORM\Column(length: 255)]
    #[Groups(['voucher:read', 'customer:read', 'sms:read'])]
    #[ApiProperty(
        openapiContext: [
            'example' => 45,
            'description' => 'Type de chèque',
            'enum' => self::TYPES,
        ]
    )]
    private ?string $type = null;

    #[ORM\Column]
    #[Groups(['voucher:read', 'customer:read', 'sms:read'])]
    #[ApiProperty(
        openapiContext: [
            'example' => 12,
            'description' => 'Montant du chèque, en euros (€)',
        ]
    )]
    private ?int $amount = null;

    #[ORM\Column]
    #[Groups(['voucher:read', 'customer:read', 'sms:read'])]
    #[ApiProperty(
        openapiContext: [
            'example' => true,
            'description' => "`true` si le chèque a été utilisé lors d'un encaissement",
        ]
    )]
    private bool $burnt = false;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    #[Groups(['voucher:read', 'customer:read'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Date à laquelle le chèque a été utilisé - format ISO-8601',
        ]
    )]
    private ?\DateTimeInterface $burntDate = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    #[Groups(['voucher:read', 'customer:read', 'sms:read'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Date à laquelle le chèque va expirer - format ISO-8601',
        ]
    )]
    private ?\DateTimeInterface $expirationDate = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    #[Groups(['voucher:read', 'customer:read', 'sms:read'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Date à partir de laquelle le chèque est utilisable - format ISO-8601',
        ]
    )]
    private ?\DateTimeInterface $availabilityDate = null;

    /**
     * CustomerTransactions qui ont utilisés le chèque (je ne sais pas pourquoi c'est un ManyToMany).
     *
     * @var Collection<int, CustomerTransaction>
     */
    #[ORM\ManyToMany(targetEntity: CustomerTransaction::class, mappedBy: 'vouchers')]
    private Collection $customerTransactions;

    /**
     * CustomerTransaction qui a généré le chèque.
     */
    #[ORM\ManyToOne(cascade: ['persist'], inversedBy: 'generatedVouchers')]
    private ?CustomerTransaction $generatedBy = null;

    /** @var Collection<int, CustomerHistory> */
    #[ORM\OneToMany(mappedBy: 'relatedVoucher', targetEntity: CustomerHistory::class)]
    private Collection $customerHistories;

    /** @var Collection<int, Sms> */
    #[ORM\OneToMany(mappedBy: 'voucher', targetEntity: Sms::class)]
    private Collection $sentSMSs;

    /** Nombre de points retirés de la cagnotte du client lors de la génération du chèque */
    #[ORM\Column(nullable: true)]
    private ?int $usedPoints = null;

    public function __construct()
    {
        $this->customerTransactions = new ArrayCollection();
        $this->customerHistories = new ArrayCollection();
        $this->sentSMSs = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCustomer(): ?Customer
    {
        return $this->customer;
    }

    public function setCustomer(?Customer $customer): self
    {
        $this->customer = $customer;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getAmount(): ?int
    {
        return $this->amount;
    }

    public function setAmount(int $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function isBurnt(): bool
    {
        return $this->burnt;
    }

    public function setBurnt(bool $burnt): self
    {
        $this->burnt = $burnt;

        return $this;
    }

    public function getBurntDate(): ?\DateTimeInterface
    {
        return $this->burntDate;
    }

    public function setBurntDate(?\DateTimeInterface $burntDate): self
    {
        $this->burntDate = $burntDate;

        return $this;
    }

    public function getExpirationDate(): ?\DateTimeInterface
    {
        return $this->expirationDate;
    }

    public function setExpirationDate(?\DateTimeInterface $expirationDate = null): self
    {
        $this->expirationDate = $expirationDate;

        return $this;
    }

    public function getAvailabilityDate(): ?\DateTimeInterface
    {
        return $this->availabilityDate ?? $this->getCreatedAt();
    }

    public function setAvailabilityDate(?\DateTimeInterface $availabilityDate = null): self
    {
        $this->availabilityDate = $availabilityDate;

        return $this;
    }

    /**
     * Méthode magique pour récupérer le premier CustomerTransaction comme il n'y a pas de raison d'en avoir plusieurs.
     */
    public function getCustomerTransaction(): ?CustomerTransaction
    {
        return $this->customerTransactions->first() ?? null;
    }

    /**
     * @return Collection<int, CustomerTransaction>
     */
    public function getCustomerTransactions(): Collection
    {
        return $this->customerTransactions;
    }

    public function addCustomerTransaction(CustomerTransaction $customerTransaction): self
    {
        if (!$this->customerTransactions->contains($customerTransaction)) {
            $this->customerTransactions->add($customerTransaction);
            $customerTransaction->addVoucher($this);
        }

        return $this;
    }

    public function removeCustomerTransaction(CustomerTransaction $customerTransaction): self
    {
        if ($this->customerTransactions->removeElement($customerTransaction)) {
            $customerTransaction->removeVoucher($this);
        }

        return $this;
    }

    public function getGeneratedBy(): ?CustomerTransaction
    {
        return $this->generatedBy;
    }

    public function setGeneratedBy(?CustomerTransaction $generatedBy): static
    {
        $this->generatedBy = $generatedBy;

        return $this;
    }

    public function getUsedPoints(): ?int
    {
        return $this->usedPoints;
    }

    public function setUsedPoints(?int $usedPoints): Voucher
    {
        $this->usedPoints = $usedPoints;

        return $this;
    }

    /**
     * @return Collection<int, CustomerHistory>
     */
    public function getCustomerHistories(): Collection
    {
        return $this->customerHistories;
    }

    /**
     * @return Collection<int, Sms>
     */
    public function getSentSMSs(): Collection
    {
        return $this->sentSMSs;
    }

    public function getSentSMS(): ?Sms
    {
        return $this->sentSMSs->first() ?: null;
    }

    public function burn(): void
    {
        $this->burnt = true;
        $this->burntDate = now();
    }

    public function isValid(): bool
    {
        return !$this->isBurnt() && !$this->isExpired() && $this->isAvailable();
    }

    public function isExpired(): bool
    {
        return null !== $this->getExpirationDate() && $this->getExpirationDate() <= now();
    }

    #[Groups(['voucher:read', 'customer:read'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Indique si le chèque est utilisable',
            'example' => true,
        ]
    )]
    public function isAvailable(): bool
    {
        return null === $this->getAvailabilityDate() || $this->getAvailabilityDate() <= now();
    }
}
