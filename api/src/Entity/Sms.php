<?php

namespace App\Entity;

use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use App\Entity\Trait\Timestampable;
use App\Enum\LinkMobilityStatus;
use App\Repository\SmsRepository;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity(repositoryClass: SmsRepository::class)]
#[ORM\HasLifecycleCallbacks]
#[ApiResource(
    operations: [],
)]
class Sms
{
    use Timestampable;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'sms')]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['history:read'])]
    #[ApiProperty(
        openapiContext: [
            'example' => '/api/campaigns/67',
            'description' => 'Campagne à laquelle le SMS est rattaché - format IRI',
        ]
    )]
    private ?Campaign $campaign = null;

    #[ORM\ManyToOne(inversedBy: 'sms')]
    #[ORM\JoinColumn(nullable: false, onDelete: 'CASCADE')]
    #[Groups(['sms:read'])]
    #[ApiProperty(
        openapiContext: [
            'example' => '/api/companies/2/customers/31',
            'description' => 'Client à laquelle la ligne d\'historique est rattaché - format IRI',
        ]
    )]
    private ?Customer $customer = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    #[Groups(['history:read'])]
    #[ApiProperty(
        openapiContext: [
            'description' => 'Date à laquelle le SMS a été envoyé - format ISO-8601',
        ]
    )]
    private ?\DateTimeInterface $sentDate = null;

    #[ORM\Column(nullable: true)]
    private array $apiResponse = [];

    #[ORM\Column(length: 20, nullable: true)]
    #[Groups(['history:read'])]
    #[ApiProperty(
        openapiContext: [
            'example' => LinkMobilityStatus::DELIVRE_CLIC->name,
            'description' => 'Statut de l\'envoi du SMS',
        ]
    )]
    private ?string $status = null;

    /**
     * - `NULL` si pas encore envoyé
     * - `false` si envoyé mais échec d'envoi ou de réception
     * - `true` si envoyé et bien reçu.
     */
    #[ORM\Column(nullable: true)]
    #[Groups(['history:read'])]
    #[ApiProperty(
        openapiContext: [
            'example' => LinkMobilityStatus::DELIVRE_CLIC->name,
            'description' => <<<EOD
            Etat de l\'envoi du SMS.
            `NULL` si pas encore envoyé\n
            `false` si envoyé mais échec d'envoi ou de réception\n
            `true` si envoyé et bien reçu.
            EOD
        ]
    )]
    private ?bool $sent = null;

    #[ORM\ManyToOne(inversedBy: 'sentSMSs')]
    #[ORM\JoinColumn(nullable: true, onDelete: 'SET NULL')]
    #[Groups(['sms:read'])]
    private ?Voucher $voucher = null;

    #[ORM\ManyToOne(inversedBy: 'sentSMSs')]
    #[ORM\JoinColumn(nullable: true, onDelete: 'SET NULL')]
    private ?CustomerTransaction $customerTransaction = null;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $linkMobilityOpeKey = null;

    #[ORM\Column]
    private int $consumedCredit = 1;

    /**
     * @var Collection<int, CustomerHistory>
     */
    #[ORM\OneToMany(mappedBy: 'relatedSms', targetEntity: CustomerHistory::class)]
    private Collection $customerHistories;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getCampaign(): ?Campaign
    {
        return $this->campaign;
    }

    public function setCampaign(?Campaign $campaign): self
    {
        $this->campaign = $campaign;

        return $this;
    }

    public function getCustomer(): ?Customer
    {
        return $this->customer;
    }

    public function setCustomer(?Customer $customer): self
    {
        $this->customer = $customer;

        return $this;
    }

    public function getSentDate(): ?\DateTimeInterface
    {
        return $this->sentDate;
    }

    public function setSentDate(\DateTimeInterface $sentDate): self
    {
        $this->sentDate = $sentDate;

        return $this;
    }

    public function getApiResponse(): array
    {
        return $this->apiResponse;
    }

    public function setApiResponse(?array $apiResponse): self
    {
        $this->apiResponse = $apiResponse;

        return $this;
    }

    public function getStatus(): ?string
    {
        if ($this->status) {
            return LinkMobilityStatus::from($this->status)->name;
        }

        return null;
    }

    public function setStatus(?string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getVoucher(): ?Voucher
    {
        return $this->voucher;
    }

    public function setVoucher(?Voucher $voucher): self
    {
        $this->voucher = $voucher;

        return $this;
    }

    public function getCustomerTransaction(): ?CustomerTransaction
    {
        return $this->customerTransaction;
    }

    public function setCustomerTransaction(?CustomerTransaction $customerTransaction): self
    {
        $this->customerTransaction = $customerTransaction;

        return $this;
    }

    public function isSent(): ?bool
    {
        return $this->sent;
    }

    public function setSent(?bool $sent): self
    {
        $this->sent = $sent;

        return $this;
    }

    public function getLinkMobilityOpeKey(): ?string
    {
        return $this->linkMobilityOpeKey;
    }

    public function setLinkMobilityOpeKey(?string $linkMobilityOpeKey): self
    {
        $this->linkMobilityOpeKey = $linkMobilityOpeKey;

        return $this;
    }

    public function getConsumedCredit(): int
    {
        return $this->consumedCredit;
    }

    public function setConsumedCredit(int $consumedCredit): self
    {
        $this->consumedCredit = $consumedCredit;

        return $this;
    }

    public function getCustomerHistories(): Collection
    {
        return $this->customerHistories;
    }

    public function setCustomerHistories(Collection $customerHistories): self
    {
        $this->customerHistories = $customerHistories;

        return $this;
    }

    public function __construct()
    {
        $this->customerHistories = new \Doctrine\Common\Collections\ArrayCollection();
    }
}
