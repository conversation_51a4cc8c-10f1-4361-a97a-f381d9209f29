<?php

namespace App\Entity;

use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Link;
use ApiPlatform\Metadata\Patch;
use ApiPlatform\OpenApi\Model\Operation as OpenApiOperation;
use App\Entity\Trait\CampaignWithRichSms;
use App\Entity\Trait\CampaignWithRichSmsInterface;
use App\Entity\Trait\CampaignWithVoucher;
use App\Entity\Trait\CampaignWithVoucherInterface;
use App\Repository\CampaignAnniversaireRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: CampaignAnniversaireRepository::class)]
#[ApiResource(
    uriTemplate: '/companies/{companyId}/campaign_anniversaires/{id}',
    operations: [
        new Patch(
            name: self::OPERATION_ANNIVERSAIRE_PATCH,
            openapi: new OpenApiOperation(
                summary: 'Mise à jour de la configuration de la campagne Anniversaire'
            )
        ),
    ],
    uriVariables: [
        'companyId' => new Link(toProperty: 'company', fromClass: Company::class),
        'id' => new Link(fromClass: self::class),
    ],
    normalizationContext: ['groups' => ['campaign:read']],
    denormalizationContext: ['groups' => ['campaign:write', 'campaign:write:enabled', 'campaign:write:voucherEnabled']],
    security: 'object.getCompany() == user',
)]
class CampaignAnniversaire extends Campaign implements CampaignWithRichSmsInterface, CampaignWithVoucherInterface
{
    use CampaignWithRichSms;
    use CampaignWithVoucher;

    public const OPERATION_ANNIVERSAIRE_PATCH = 'campaignAnniversairePatch';

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Range(min: 1, max: 999)]
    #[Groups(['read', 'campaign:read', 'campaign:write'])]
    #[ApiProperty(
        openapiContext: [
            'example' => 5,
            'description' => 'Montant du chèque qui sera généré, en euros (€)',
        ]
    )]
    private int $voucherAmount = 5;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Range(min: 1, max: 99)]
    #[Groups(['read', 'campaign:read', 'campaign:write'])]
    #[ApiProperty(
        openapiContext: [
            'example' => 2,
            'description' => 'Nombre de mois pendant lequel le chèque sera valide',
        ]
    )]
    private int $voucherValidityMonth = 2;

    public function getVoucherAmount(): int
    {
        return $this->voucherAmount;
    }

    public function setVoucherAmount(int $voucherAmount): self
    {
        $this->voucherAmount = $voucherAmount;

        return $this;
    }

    public function getVoucherValidityMonth(): int
    {
        return $this->voucherValidityMonth;
    }

    public function setVoucherValidityMonth(int $voucherValidityMonth): self
    {
        $this->voucherValidityMonth = $voucherValidityMonth;

        return $this;
    }

    public function getDefaultSmsContent(): string
    {
        return sprintf('Joyeux anniversaire ! %s vous offre %s€ pour votre prochaine visite. A très vite !', $this->getCompany()->getName(), $this->voucherAmount);
    }

    public function getType(): string
    {
        return self::TYPE_ANNIVERSAIRE;
    }
}
