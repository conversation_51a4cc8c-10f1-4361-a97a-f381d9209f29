<?php

namespace App\EventListener;

use App\Entity\Company;
use App\Entity\Customer;
use App\Entity\CustomerTransaction;
use App\Entity\Sms;
use App\Entity\Voucher;
use App\Service\Stats\StatsHandler;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\Event\PostPersistEventArgs;
use Doctrine\ORM\Event\PostRemoveEventArgs;
use Doctrine\ORM\Event\PostUpdateEventArgs;
use Doctrine\ORM\Events;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Contracts\Cache\TagAwareCacheInterface;

/**
 * Cette classe gère la suppression du cache des statistiques
 * lors de l'ajout/modification/suppression d'un élément en base de données.
 */
#[AsDoctrineListener(event: Events::postPersist, priority: 500, connection: 'default')]
#[AsDoctrineListener(event: Events::postRemove, priority: 500, connection: 'default')]
#[AsDoctrineListener(event: Events::postUpdate, priority: 500, connection: 'default')]
readonly class StatsTagsInvalidator
{
    public function __construct(
        private Security $security,
        private TagAwareCacheInterface $statsCache,
    ) {
    }

    public function postPersist(PostPersistEventArgs $args): void
    {
        $entity = $args->getObject();
        if (
            $entity instanceof Customer
            || $entity instanceof CustomerTransaction
            || $entity instanceof Sms
            || $entity instanceof Voucher
        ) {
            $this->invalidateStats();
        }
    }

    public function postRemove(PostRemoveEventArgs $args): void
    {
        $entity = $args->getObject();
        if (
            $entity instanceof Customer
            || $entity instanceof CustomerTransaction
            || $entity instanceof Sms
            || $entity instanceof Voucher
        ) {
            $this->invalidateStats();
        }
    }

    public function postUpdate(PostUpdateEventArgs $args): void
    {
        $entity = $args->getObject();
        if ($entity instanceof CustomerTransaction
            || $entity instanceof Sms
            || $entity instanceof Voucher
        ) {
            $this->invalidateStats();
        }
    }

    public function invalidateStats(): void
    {
        $user = $this->security->getUser();
        if ($user instanceof Company) {
            $this->statsCache->invalidateTags([StatsHandler::getGlobalCacheKey($user)]);
        }
    }
}
