<?php

namespace App\Dto;

use ApiPlatform\Metadata\ApiProperty;
use App\Enum\CompanyStatsType;
use Symfony\Component\Serializer\Annotation\Groups;

final class CompanyStatsDto
{
    /** @param CompanyStatDto[] $companyStats */
    public function __construct(
        #[Groups('read')]
        #[ApiProperty(
            openapiContext: [
                'type' => 'array',
                'description' => "Stats de l'enseigne",
                'items' => [
                    'type' => 'object',
                    'properties' => [
                        'id' => [
                            'type' => 'string',
                            'description' => 'Nom de la statistique',
                            'enum' => CompanyStatsType::TYPES,
                        ],
                        'value' => [
                            'type' => 'number',
                            'description' => 'Valeur de cette statistique',
                            'example' => 2340,
                        ],
                    ],
                ],
            ]
        )]
        public array $companyStats
    ) {
    }

    public function getStat(CompanyStatsType $key): ?CompanyStatDto
    {
        foreach ($this->companyStats as $stat) {
            if ($stat->id === $key) {
                return $stat;
            }
        }

        return null;
    }
}
