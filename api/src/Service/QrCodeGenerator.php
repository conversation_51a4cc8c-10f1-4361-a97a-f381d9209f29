<?php

namespace App\Service;

use App\Entity\Company;
use Endroid\QrCode\Builder\BuilderInterface;
use Endroid\QrCode\Writer\Result\ResultInterface;

readonly class QrCodeGenerator
{
    public function __construct(
        private FrontEndRouter $frontEndRouter,
        private BuilderInterface $defaultQrCodeBuilder,
    ) {
    }

    public function generateCompanyQrCode(Company $company): ResultInterface
    {
        $targetUrl = $this->getQrCodeTargetUrl($company);

        return $this->defaultQrCodeBuilder->build(
            data: $targetUrl,
        );
    }

    public function getQrCodeTargetUrl(Company $company): string
    {
        return $this->frontEndRouter->getCreateCustomerAccountUrl($company);
    }

    public static function createToken(Company $company): string
    {
        return substr(
            md5(sprintf('%sMaCarteFidV3QrCode%s', $company->getId(), $company->getCreatedAt()->format('YmdHis'))),
            0, 9
        );
    }

    public static function validateToken(string $token, Company $company): bool
    {
        return $token === self::createToken($company);
    }
}
