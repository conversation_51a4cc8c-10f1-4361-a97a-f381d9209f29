<?php

namespace App\Service\Stats;

use App\Dto\CompanyStatsDto;
use App\Entity\Company;
use App\Enum\CompanyStatsType;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Symfony\Component\Clock\ClockAwareTrait;
use Symfony\Component\String\Slugger\SluggerInterface;

class StatsExporter
{
    use ClockAwareTrait;

    public function __construct(
        private readonly StatsHandler $statsHandler,
        private readonly SluggerInterface $slugger
    ) {
    }

    public function generateExcelFile(Company $company): array
    {
        $stats = $this->getCompanyStats($company);
        $arrayData = $this->statsDtoToArray($stats);

        $writer = $this->createExcelFile($arrayData);

        return [$writer, sprintf('%s_STATISTIQUES_%s.xlsx', strtoupper($this->slugger->slug($company->getName())), $this->now()->format('Ymd'))];
    }

    private function getCompanyStats(Company $company): mixed
    {
        // Boucle sur tous les mois depuis la création de l'enseigne
        $stats = [];
        $referenceDate = \DateTimeImmutable::createFromInterface($company->getCreatedAt());

        $dates = [];

        do {
            $dates[] = clone $referenceDate;
            $referenceDate = $referenceDate->modify('first day of next month');
        } while ($referenceDate < $this->now());

        foreach ($dates as $date) {
            $this->statsHandler->setReferenceDate($date);
            $stats[$date->format('m/Y')] = $this->statsHandler->getCompanyStats($company);
        }

        return $stats;
    }

    /**
     * @return array<string>
     */
    private function getHeaders(): array
    {
        return [
            'Date',
            'Nouveaux clients fidélisés',
            'Montant total transactions',
            'Nombre encaissements',
            'Montant panier moyen',
            'SMS auto envoyés',
            'SMS campagnes envoyés',
        ];
    }

    /**
     * @param array<string, CompanyStatsDto> $stats
     *
     * @return string[][]
     */
    private function statsDtoToArray(array $stats): array
    {
        $result = [$this->getHeaders()];
        foreach ($stats as $date => $monthStats) {
            $result[] = [
                $date,
                $monthStats->getStat(CompanyStatsType::NEW_CLIENTS)->value ?? 0,
                round(($monthStats->getStat(CompanyStatsType::TOTAL_CA)->value ?? 0) / 100, 2),
                $monthStats->getStat(CompanyStatsType::NB_TRANSACTIONS)->value ?? 0,
                round(($monthStats->getStat(CompanyStatsType::AVG_CA)->value ?? 0) / 100, 2),
                $monthStats->getStat(CompanyStatsType::SMS_AUTO_SENT)->value ?? 0,
                $monthStats->getStat(CompanyStatsType::SMS_CAMPAIGN_SENT)->value ?? 0,
            ];
        }

        return $result;
    }

    private function createExcelFile(array $data): Xlsx
    {
        $rowCount = count($data);
        $spreadsheet = new Spreadsheet();

        $worksheet = $spreadsheet->getActiveSheet();

        $worksheet->fromArray($data, null, 'A1', true);

        $spreadsheet->setActiveSheetIndex(0);

        $this->setBold($worksheet, [
            'A1:G1',
        ]);

        $this->setFormat($worksheet, 'A', 'mm/yyyy');
        $this->setFormat($worksheet, 'B', NumberFormat::FORMAT_NUMBER);
        $this->setFormat($worksheet, 'C', NumberFormat::FORMAT_CURRENCY_EUR_INTEGER);
        $this->setFormat($worksheet, 'D', NumberFormat::FORMAT_NUMBER);
        $this->setFormat($worksheet, 'E', NumberFormat::FORMAT_CURRENCY_EUR_INTEGER);
        $this->setFormat($worksheet, 'F', NumberFormat::FORMAT_NUMBER);
        $this->setFormat($worksheet, 'G', NumberFormat::FORMAT_NUMBER);

        $this->autoSize($worksheet, ['A', 'B', 'C', 'D', 'E', 'F', 'G'], sprintf('A1:G%s', $rowCount));

        return new Xlsx($spreadsheet);
    }

    private function createTable(Worksheet $worksheet, string ...$ranges): void
    {
        foreach ($ranges as $range) {
            $worksheet->getStyle($range)
                ->getBorders()
                ->getAllBorders()
                ->setBorderStyle(Border::BORDER_THIN);
        }
    }

    /**
     * @param string[] $columns
     */
    private function autoSize(Worksheet $worksheet, array $columns = [], ?string $range = null): void
    {
        foreach ($columns as $column) {
            $worksheet->getColumnDimension($column)->setAutoSize(true);
        }

        if (null !== $range) {
            $worksheet->getStyle($range)
                ->getAlignment()
                ->setHorizontal('center');
        }
    }

    /**
     * @param string[] $ranges
     */
    private function setBold(Worksheet $worksheet, array $ranges): void
    {
        foreach ($ranges as $range) {
            $worksheet->getStyle($range)
                ->getFont()
                ->setBold(true);
        }
    }

    private function setFormat(Worksheet $worksheet, string $column, string $format): void
    {
        $worksheet->getStyle(sprintf('%s', $column))->getNumberFormat()->setFormatCode($format);
    }
}
