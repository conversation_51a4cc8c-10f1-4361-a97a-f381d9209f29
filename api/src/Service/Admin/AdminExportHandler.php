<?php

namespace App\Service\Admin;

use App\Dto\AdminExportClientDto;
use App\Dto\AdminExportCompanyDto;
use App\Dto\AdminImportClientDto;
use App\Entity\Company;
use App\Entity\Customer;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Clock\ClockAwareTrait;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Serializer\Encoder\CsvEncoder;
use Symfony\Component\Serializer\Exception\MissingConstructorArgumentsException;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class AdminExportHandler
{
    use ClockAwareTrait;

    public function __construct(
        public SerializerInterface $serializer,
        public ValidatorInterface $validator,
        public EntityManagerInterface $entityManager,
        private readonly AdminStatsHandler $adminStatsHandler,
    ) {
    }

    public function exportClients(Company $company): string
    {
        $data = array_map(function (Customer $customer) {
            return AdminExportClientDto::fromCustomer($customer);
        }, $company->getCustomers()->toArray());

        $csvData = $this->serializer->serialize($data, 'csv', [
            CsvEncoder::DELIMITER_KEY => ';',
        ]);

        return iconv('UTF-8', 'Windows-1252//IGNORE', $csvData);
    }

    public function importClients(Company $company, UploadedFile $file): array
    {
        $errors = [];

        $csvData = $file->getContent();

        $csvData = iconv('Windows-1252', 'UTF-8//IGNORE', $csvData);

        $columns = AdminImportClientDto::getColumns();

        try {
            $data = $this->serializer->deserialize($csvData, AdminImportClientDto::class.'[]', 'csv', [
                CsvEncoder::DELIMITER_KEY => ';',
            ]);

            $violations = $this->validator->validate($data);
            if (0 === $violations->count()) {
                $customers = array_map(fn (AdminImportClientDto $dto) => $dto->toCustomer($company), $data);
                foreach ($customers as $i => $customer) {
                    $this->entityManager->persist($customer);
                    if (0 === $i % 50) {
                        $this->entityManager->flush();
                    }
                }
                $this->entityManager->flush();

                return [];
            }

            foreach ($violations as $violation) {
                $property = $violation->getPropertyPath();
                foreach ($columns as $column => $label) {
                    $property = str_replace($column, $label, $property);
                }
                preg_match('/\[(\d+)]\./', $property, $matches);
                $rowNumber = $matches[1] + 2;
                $property = preg_replace('/\[(\d+)]\./', sprintf('Ligne %d : ', $rowNumber), $property);
                $errors[] = sprintf('%s : %s', $property, $violation->getMessage());
            }
        } catch (\Exception $exception) {
            if ($exception instanceof MissingConstructorArgumentsException) {
                $errors[] =
                    sprintf(
                        'Format de fichier invalide. Veuillez vérifier que les colonnes suivantes sont présentes : "%s"', implode('", "', array_values($columns))
                    );
            } else {
                $errors[] = 'Une erreur est survenue lors de la lecture du fichier.';
                $errors[] = $exception->getMessage();
            }
        }

        return $errors;
    }

    public function exportCompanies(): string
    {
        $qb = $this->entityManager->getRepository(Company::class)
            ->createQueryBuilder('c')
            ->select(sprintf(
                'NEW %s(c.name, c.email, c.phoneNumber, c.createdAt)',
                AdminExportCompanyDto::class
            ))
            ->orderBy('c.createdAt', 'ASC')
        ;

        $this->adminStatsHandler->restrictCompanyTypes($qb);

        $data = $qb->getQuery()->getResult();

        $csvData = $this->serializer->serialize($data, 'csv', [
            CsvEncoder::DELIMITER_KEY => ';',
        ]);

        return iconv('UTF-8', 'Windows-1252//IGNORE', $csvData);
    }
}
