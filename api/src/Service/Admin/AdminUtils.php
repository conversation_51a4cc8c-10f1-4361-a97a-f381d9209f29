<?php

namespace App\Service\Admin;

use App\Entity\Company;
use App\Entity\UserBackOffice;
use Symfony\Bundle\SecurityBundle\Security;

readonly class AdminUtils
{
    public function __construct(
        private Security $security,
    ) {
    }

    /**
     * @return string[]
     */
    public function getAllowedCompanyTypes(): array
    {
        // Les utillisateurs Aquitem et Fiducial voient leurs enseignes respectives
        $allowedTypes = [];
        if ($this->security->isGranted('ROLE_ADMIN_AQUITEM')) {
            $allowedTypes[] = Company::COMPANY_TYPE_DEFAULT;
        }

        if ($this->security->isGranted('ROLE_ADMIN_FIDUCIAL')) {
            $allowedTypes[] = Company::COMPANY_TYPE_FIDUCIAL;
        }

        return $allowedTypes;
    }

    /**
     * @return string[]
     */
    public static function getAllowedCompanyTypesForUser(UserBackOffice $user): array
    {
        return match (true) {
            $user->isFiducialAdmin() => [Company::COMPANY_TYPE_FIDUCIAL],
            $user->isAquitemAdmin() => [Company::COMPANY_TYPE_DEFAULT],
            $user->isSuperAdmin() => [Company::COMPANY_TYPE_DEFAULT, Company::COMPANY_TYPE_FIDUCIAL],
            default => [],
        };
    }
}
