<?php

namespace App\Controller\Company\GenerateQrCode;

use App\Entity\Company;
use App\Service\QrCodeGenerator;
use Endroid\QrCodeBundle\Response\QrCodeResponse;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
class CompanyGenerateQrCode extends AbstractController
{
    public function __construct(
        private readonly QrCodeGenerator $qrCodeGenerator
    ) {
    }

    public function __invoke(Company $company): QrCodeResponse
    {
        $result = $this->qrCodeGenerator->generateCompanyQrCode($company);

        $response = new QrCodeResponse($result);
        $response->headers->set('Content-Disposition', 'attachment;filename=macartefid-qrcode.png');

        return $response;
    }
}
