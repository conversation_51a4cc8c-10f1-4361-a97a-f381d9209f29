<?php

declare(strict_types=1);

namespace App\Controller;

use ApiPlatform\OpenApi\Model;
use ApiPlatform\OpenApi\Model\Operation as OpenApiOperation;
use ApiPlatform\OpenApi\Model\PathItem;
use ApiPlatform\OpenApi\Model\Response as OpenApiResponse;
use App\Entity\Company;
use App\Entity\Sms;
use App\Entity\Voucher;
use App\Service\QrCodeGenerator;
use App\Service\RichSmsManager;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryParameter;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\SerializerInterface;

final class CreateCustomerAccountController extends AbstractController implements CustomControllerInterface
{
    public const string PATH = '/api/create-customer-account/{id}';

    #[Route(path: self::PATH, name: 'create_customer_account_page', methods: ['GET'])]
    public function __invoke(
        #[MapQueryParameter('token')] string $token,
        Company $company,
        SerializerInterface $serializer,
        RichSmsManager $richSmsManager
    ): JsonResponse
    {
        if (!self::validateToken($token, $company)) {
            throw new AccessDeniedHttpException('Invalid token');
        }

        $data = [
            'company' => $company->getName(),
        ];

        $json = $serializer->serialize($data, 'jsonld', array_merge([
            'json_encode_options' => JsonResponse::DEFAULT_ENCODING_OPTIONS,
        ]));

        return new JsonResponse($json, Response::HTTP_OK, [], true);
    }

    public static function getOpenApiOperation(): PathItem
    {
        return (new PathItem())
            ->withGet(
                (new OpenApiOperation())
                    ->withSummary('Récupération des informations pour la création de compte client autonome')
                    ->withDescription('Permet de récupérer les informations nécessaire pour afficher le formulaire de création de compte client autonome')
                    ->withOperationId('getCreateCustomerAccountPage')
                    ->withTags(['Company'])
                    ->withParameter(new Model\Parameter(
                        'id',
                        'path',
                        'ID de l\'enseigne',
                        true,
                        schema: ['type' => 'string']
                    ))
                    ->withParameter(new Model\Parameter(
                        'token',
                        'query',
                        'Token de sécurité',
                        true,
                        schema: ['type' => 'string']
                    ))
                    ->withResponse(
                        Response::HTTP_OK,
                        (new OpenApiResponse())
                            ->withContent(new \ArrayObject([
                                'application/ld+json' => [
                                    'schema' => [
                                        'type' => 'object',
                                        'properties' => [
                                            'company' => [
                                                'type' => 'string',
                                                'description' => 'Nom de l\'enseigne',
                                                'example' => 'Enseigne',
                                            ],
                                        ],
                                    ],
                                ],
                            ]))
                    )
            );
    }

    public static function getOpenApiPath(): string
    {
        return self::PATH;
    }

    public static function createToken(Company $company): string
    {
        return substr(
            md5(sprintf('%sMaCarteFidV3QrCode%s', $company->getId(), $company->getCreatedAt()->format('YmdHis'))),
            0, 9
        );
    }

    public static function validateToken(string $token, Company $company): bool
    {
        return $token === self::createToken($company);
    }
}
