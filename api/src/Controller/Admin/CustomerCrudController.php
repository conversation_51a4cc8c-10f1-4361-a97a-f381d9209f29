<?php

namespace App\Controller\Admin;

use App\Entity\Customer;
use App\Security\Voter\AdminCompanyVoter;
use Doctrine\ORM\QueryBuilder;
use EasyCorp\Bundle\EasyAdminBundle\Collection\FieldCollection;
use EasyCorp\Bundle\EasyAdminBundle\Collection\FilterCollection;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\Filters;
use EasyCorp\Bundle\EasyAdminBundle\Dto\EntityDto;
use EasyCorp\Bundle\EasyAdminBundle\Dto\SearchDto;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\NumberField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use libphonenumber\PhoneNumberUtil;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_ADMIN_AQUITEM')]
class CustomerCrudController extends AbstractCustomCrudController
{
    public static function getEntityFqcn(): string
    {
        return Customer::class;
    }

    public function configureFields(string $pageName): iterable
    {
        return [
            IdField::new('id'),
            AssociationField::new('company', 'Enseigne'),
            TextField::new('firstname', 'Prénom'),
            TextField::new('lastname', 'Nom'),
            TextField::new('civility', 'Civilité')->hideOnIndex(),
            TextField::new('phoneNumber', 'N° de téléphone')
                ->setTemplatePath('admin/field/phone_number.html.twig')
                ->setCustomOption('phoneNumberUtils', PhoneNumberUtil::getInstance()),
            TextField::new('email', 'Email')->hideOnIndex(),
            TextField::new('postalCode', 'Code postal')->hideOnIndex(),
            TextField::new('city', 'Ville')->hideOnIndex(),
            DateField::new('birthDate', 'Date de naissance')->hideOnIndex(),
            NumberField::new('loyaltyPoints', 'Points de fidelité'),
            BooleanField::new('stopSmsReceived', 'Stop SMS')->hideOnIndex(),
            AssociationField::new('transactions', 'Encaissements')
                ->setTemplatePath('admin/field/collection_link.html.twig')
                ->setCustomOption('field', 'customer'),
            AssociationField::new('vouchers', 'Chèques')
                ->setTemplatePath('admin/field/collection_link.html.twig')
                ->setCustomOption('field', 'customer'),
            AssociationField::new('sms', 'SMS')
                ->setTemplatePath('admin/field/collection_link.html.twig')
                ->setCustomOption('field', 'customer'),
            DateField::new('lastTransactionDate', 'Dernier encaissement'),
        ];
    }

    public function configureActions(Actions $actions): Actions
    {
        $actions->add(Crud::PAGE_INDEX, Action::DETAIL);
        $actions->disable(Action::NEW);
        $actions->disable(Action::EDIT);
        $actions->disable(Action::DELETE);

        return $actions;
    }

    public function configureFilters(Filters $filters): Filters
    {
        return $filters
            ->add('company')
            ->add('lastTransactionDate')
            ->add('stopSmsReceived')
        ;
    }

    public function configureCrud(Crud $crud): Crud
    {
        $crud->setEntityLabelInSingular('Client');
        $crud->setEntityLabelInPlural('Clients');

        $crud->setSearchFields([
            'company.name',
            'firstname',
            'lastname',
            'phoneNumber',
            'email',
            'postalCode',
        ]);

        $crud->setDefaultSort(['createdAt' => 'DESC']);

        $crud->setEntityPermission(AdminCompanyVoter::VIEW);

        return $crud;
    }

    public function createIndexQueryBuilder(SearchDto $searchDto, EntityDto $entityDto, FieldCollection $fields, FilterCollection $filters): QueryBuilder
    {
        $qb = parent::createIndexQueryBuilder($searchDto, $entityDto, $fields, $filters);

        if (!in_array('company', $qb->getAllAliases())) {
            $qb = $qb->leftJoin('entity.company', 'company');
        }

        return $qb->andWhere('company.archivedAt IS NULL');
    }
}
