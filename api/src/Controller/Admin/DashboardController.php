<?php

namespace App\Controller\Admin;

use App\Entity\Company;
use App\Entity\Config;
use App\Entity\Customer;
use App\Entity\CustomerTransaction;
use App\Entity\Faq;
use App\Entity\LinkMobilityWebhook;
use App\Entity\Sms;
use App\Entity\Tutorial;
use App\Entity\UserBackOffice;
use App\Entity\Voucher;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Dashboard;
use EasyCorp\Bundle\EasyAdminBundle\Config\MenuItem;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractDashboardController;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use Lexik\Bundle\MaintenanceBundle\Drivers\DriverFactory;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class DashboardController extends AbstractDashboardController
{
    public function __construct(private readonly DriverFactory $driverFactory)
    {
    }

    #[Route('/admin/dashboard', name: 'admin_dashboard')]
    public function index(): Response
    {
        /** @var AdminUrlGenerator $adminUrlGenerator */
        $adminUrlGenerator = $this->container->get(AdminUrlGenerator::class);

        $route = $adminUrlGenerator
            ->setController(CompanyCrudController::class)
            ->setAction(Action::INDEX)
            ->generateUrl();

        return $this->redirect($route);
    }

    public function configureDashboard(): Dashboard
    {
        return Dashboard::new()
            ->setTitle('<svg fill="#11224d" class="mb-3" viewBox="0 0 352.36 51.64" width="336" height="50"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="/logo.svg#logo"></use></svg>')
            ->setFaviconPath('favicon.png');
    }

    public function configureMenuItems(): iterable
    {
        if ($this->driverFactory->getDriver()->isExists()) {
            yield MenuItem::section('<b class="text-danger">Maintenance activée</b>', 'fa fa-warning text-danger');
        }

        yield MenuItem::section('Administration');
        yield MenuItem::linkToRoute('Statistiques', 'fa fa-chart-simple', 'admin_stats')
            ->setPermission('ROLE_ADMIN_AQUITEM');
        yield MenuItem::linkToCrud('Configuration', 'fa fa-cogs', Config::class)
            ->setPermission('ROLE_SUPER_ADMIN');
        yield MenuItem::linkToCrud('Utilisateurs BO', 'fa fa-users-gear', UserBackOffice::class)
            ->setPermission('ROLE_SUPER_ADMIN');
        yield MenuItem::linkToRoute('Comptabilité', 'fa fa-receipt', 'admin_accounting')
            ->setPermission('ROLE_SUPER_ADMIN');
        yield MenuItem::linkToCrud('FAQ', 'fa fa-circle-question', Faq::class)
            ->setPermission('ROLE_ADMIN_AQUITEM');
        yield MenuItem::linkToCrud('Tutoriels', 'fa-brands fa-youtube', Tutorial::class)
            ->setPermission('ROLE_ADMIN_AQUITEM');

        yield MenuItem::section('Entités');
        yield MenuItem::linkToCrud('Enseignes', 'fa fa-home', Company::class)
            ->setController(CompanyCrudController::class)
            ->setPermission('ROLE_ADMIN');
        yield MenuItem::linkToCrud('Clients', 'fa fa-users', Customer::class)
            ->setPermission('ROLE_ADMIN_AQUITEM');
        yield MenuItem::linkToCrud('Encaissements', 'fa fa-receipt', CustomerTransaction::class)
            ->setPermission('ROLE_ADMIN_AQUITEM');
        yield MenuItem::linkToCrud('SMS', 'fa fa-paper-plane', Sms::class)
            ->setPermission('ROLE_ADMIN_AQUITEM');
        yield MenuItem::linkToCrud('Chèques', 'fa fa-wallet', Voucher::class)
            ->setPermission('ROLE_ADMIN_AQUITEM');

        yield MenuItem::section('Monitoring')->setPermission('ROLE_SUPER_ADMIN');
        yield MenuItem::linkToCrud('Webhooks LinkMobility', 'fa fa-mobile-retro', LinkMobilityWebhook::class)
            ->setPermission('ROLE_SUPER_ADMIN');

        yield MenuItem::section('Archives')->setPermission('ROLE_ADMIN');
        yield MenuItem::linkToCrud('Enseignes archivées', 'fa fa-house-circle-xmark', Company::class)
            ->setController(ArchivedCompanyCrudController::class)
            ->setPermission('ROLE_ADMIN');
    }
}
