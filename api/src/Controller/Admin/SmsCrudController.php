<?php

namespace App\Controller\Admin;

use App\Controller\Admin\Filter\CampaignTypeFilter;
use App\Controller\Admin\Filter\CustomerCompanyFilter;
use App\Entity\Campaign;
use App\Entity\LinkMobilityWebhook;
use App\Entity\Sms;
use App\Enum\LinkMobilityStatus;
use App\Repository\LinkMobilityWebhookRepository;
use App\Security\Voter\AdminCompanyVoter;
use Doctrine\ORM\QueryBuilder;
use EasyCorp\Bundle\EasyAdminBundle\Collection\FieldCollection;
use EasyCorp\Bundle\EasyAdminBundle\Collection\FilterCollection;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\Filters;
use EasyCorp\Bundle\EasyAdminBundle\Dto\EntityDto;
use EasyCorp\Bundle\EasyAdminBundle\Dto\SearchDto;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextEditorField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Form\Extension\Core\DataTransformer\DateTimeToLocalizedStringTransformer;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Service\Attribute\Required;

#[IsGranted('ROLE_ADMIN_AQUITEM')]
class SmsCrudController extends AbstractCustomCrudController
{
    private LinkMobilityWebhookRepository $linkMobilityWebhookRepository;

    #[Required]
    public function setLinkMobilityWebhookRepository(LinkMobilityWebhookRepository $linkMobilityWebhookRepository): void
    {
        $this->linkMobilityWebhookRepository = $linkMobilityWebhookRepository;
    }

    public static function getEntityFqcn(): string
    {
        return Sms::class;
    }

    public function configureFields(string $pageName): iterable
    {
        return [
            IdField::new('id'),
            AssociationField::new('customer', 'Client'),
            AssociationField::new('customer', 'Enseigne')
                ->setSortable(false)
                ->setTemplatePath('admin/field/nested_relation_link.html.twig')
                ->setCustomOption('field', 'company')
                ->setCustomOption(AssociationField::OPTION_EMBEDDED_CRUD_FORM_CONTROLLER, CompanyCrudController::class)
                ->setCustomOption(AssociationField::OPTION_AUTOCOMPLETE, true),
            ChoiceField::new('campaign.type', 'Campagne')
              ->setChoices([
                  'Fidélité' => Campaign::TYPE_FIDELITE,
                  'Anniversaire' => Campaign::TYPE_ANNIVERSAIRE,
                  'Dormant' => Campaign::TYPE_DORMANT,
                  'Bienvenue' => Campaign::TYPE_BIENVENUE,
                  'Expiration chèque' => Campaign::TYPE_EXPIRATION_CHEQUE,
                  'Avis Google' => Campaign::TYPE_GOOGLE_REVIEW,
                  'Campagne SMS' => Campaign::TYPE_SMS,
              ])->renderAsBadges([
                  Campaign::TYPE_FIDELITE => 'success',
                  Campaign::TYPE_ANNIVERSAIRE => 'info',
                  Campaign::TYPE_DORMANT => 'warning',
                  Campaign::TYPE_BIENVENUE => 'danger',
                  Campaign::TYPE_EXPIRATION_CHEQUE => 'primary',
                  Campaign::TYPE_GOOGLE_REVIEW => 'light',
                  Campaign::TYPE_SMS => 'secondary',
              ]),
            TextField::new('linkMobilityOpeKey', 'Clé LinkMobility')
                ->setPermission('ROLE_SUPER_ADMIN')
                ->setTemplatePath('admin/field/link_mobility_filter.html.twig')
                ->setCustomOption('crudControllerFqcn', LinkMobilityWebhookCrudController::class),
            BooleanField::new('sent', 'Reçu')->renderAsSwitch(false),
            ChoiceField::new('status', 'Statut Linkmobility')
                ->renderAsBadges()
                ->setChoices(array_combine(LinkMobilityStatus::names(), LinkMobilityStatus::names())),
            TextEditorField::new('campaign.smsContent', 'Texte du SMS'),
            DateTimeField::new('sentDate', 'Date d\'envoi'),
            DateTimeField::new('Date de réception')
                ->setValue(new \DateTimeImmutable())
                ->formatValue(function ($value, Sms $entity) {
                    if ($entity->getLinkMobilityOpeKey()) {
                        /** @var LinkMobilityWebhook $webhook */
                        $webhook = $this->linkMobilityWebhookRepository->findOneByOpeKey($entity->getLinkMobilityOpeKey());
                        if ($webhook) {
                            $smsWebhook = $webhook->getResultForSms($entity->getId());
                            if ($smsWebhook) {
                                $formatter = new DateTimeToLocalizedStringTransformer();

                                return $formatter->transform(new \DateTimeImmutable($smsWebhook['sending_datetime']));
                            }
                        }
                    }

                    return null;
                })
                ->onlyOnIndex(),
        ];
    }

    public function configureActions(Actions $actions): Actions
    {
        $actions->add(Crud::PAGE_INDEX, Action::DETAIL);

        $actions->disable(Action::NEW);
        $actions->disable(Action::EDIT);
        $actions->disable(Action::DELETE);

        $linkMobilityDoc = Action::new('linkMobilityDoc')
            ->setLabel('Documentation LinkMobility')
            ->linkToCrudAction('getLinkMobilityDoc')
            ->setIcon('fa fa-fw fa-file')
            ->createAsGlobalAction()
            ->setHtmlAttributes(['target' => '_blank'])
            ->setCssClass('btn btn-primary')
        ;

        $actions->add(Crud::PAGE_INDEX, $linkMobilityDoc);

        return $actions;
    }

    public function configureFilters(Filters $filters): Filters
    {
        $filters = $filters
            ->add(CustomerCompanyFilter::new('Enseigne'))
            ->add(CampaignTypeFilter::new('Type de campagne'))
            ->add('sentDate')
            ->add('sent')
            ->add('status')
        ;

        if ($this->isGranted('ROLE_SUPER_ADMIN')) {
            $filters = $filters->add('linkMobilityOpeKey');
        }

        return $filters;
    }

    public function configureCrud(Crud $crud): Crud
    {
        $crud->setEntityLabelInSingular('Sms');
        $crud->setEntityLabelInPlural('Sms');

        $crud->setDefaultSort(['sentDate' => 'DESC']);

        $crud->setSearchFields(['customer.company.name', 'customer.firstname', 'customer.lastname']);

        $crud->setEntityPermission(AdminCompanyVoter::VIEW);

        return $crud;
    }

    public function createIndexQueryBuilder(SearchDto $searchDto, EntityDto $entityDto, FieldCollection $fields, FilterCollection $filters): QueryBuilder
    {
        $qb = parent::createIndexQueryBuilder($searchDto, $entityDto, $fields, $filters);

        if (!in_array('company', $qb->getAllAliases())) {
            $qb = $qb
                ->leftJoin('entity.customer', 'customer')
                ->leftJoin('customer.company', 'company');
        }

        return $qb->andWhere('company.archivedAt IS NULL');
    }

    public function getLinkMobilityDoc(
        #[Autowire('%kernel.project_dir%')] string $projectDir
    ) {
        return new BinaryFileResponse(
            sprintf('%s/docs/linkmobility_campaign_api.pdf', $projectDir),
            contentDisposition: 'inline'
        );
    }
}
