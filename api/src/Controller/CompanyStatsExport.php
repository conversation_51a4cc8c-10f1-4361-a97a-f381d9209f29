<?php

namespace App\Controller;

use App\Entity\Company;
use App\Service\Stats\StatsExporter;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
class CompanyStatsExport extends AbstractController
{
    public function __construct(
        private readonly StatsExporter $statsExporter
    ) {
    }

    public function __invoke(Company $company): Response
    {
        [$writer, $filename] = $this->statsExporter->generateExcelFile($company);

        $response = new StreamedResponse(
            function () use ($writer): void {
                $writer->save('php://output');
            }
        );
        $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $response->headers->set('Content-Disposition', 'attachment;filename='.mb_convert_encoding($filename, 'ISO-8859-1', 'UTF-8'));
        $response->headers->set('Cache-Control', 'max-age=0');

        return $response;
    }
}
