<?php

declare(strict_types=1);

namespace App\Controller;

use ApiPlatform\OpenApi\Model;
use ApiPlatform\OpenApi\Model\Operation as OpenApiOperation;
use ApiPlatform\OpenApi\Model\PathItem;
use ApiPlatform\OpenApi\Model\Response as OpenApiResponse;
use App\Entity\Sms;
use App\Entity\Voucher;
use App\Service\RichSmsManager;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryParameter;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\SerializerInterface;

final class RichSmsController extends AbstractController implements CustomControllerInterface
{
    public const string PATH = '/api/rich_sms/{id}';

    #[Route(path: self::PATH, name: 'richsms_page', methods: ['GET'])]
    public function __invoke(
        #[MapQueryParameter('token')] string $token,
        Sms $sms,
        SerializerInterface $serializer,
        RichSmsManager $richSmsManager
    ): JsonResponse {
        if (!$richSmsManager->validateToken($token, $sms)) {
            throw new AccessDeniedHttpException('Invalid token');
        }

        $data = [
            'sms' => $sms,
            'company' => $sms->getCampaign()->getCompany()->getName(),
            'content' => $sms->getCampaign()->getRichSmsContent(),
        ];

        $json = $serializer->serialize($data, 'jsonld', array_merge([
            'json_encode_options' => JsonResponse::DEFAULT_ENCODING_OPTIONS,
        ], ['groups' => ['sms:read']]));

        return new JsonResponse($json, Response::HTTP_OK, [], true);
    }

    public static function getOpenApiOperation(): PathItem
    {
        return (new PathItem())
            ->withGet(
                (new OpenApiOperation())
                    ->withSummary('Récupération des informations d\'un SMS Rich')
                    ->withDescription('Permet de récupérérer les informations d\'un SMS Rich pour les afficher sur une page web')
                    ->withOperationId('getRichSms')
                    ->withTags(['RichSms'])
                    ->withParameter(new Model\Parameter(
                        'id',
                        'path',
                        'ID du Rich SMS',
                        true,
                        schema: ['type' => 'string']
                    ))
                    ->withParameter(new Model\Parameter(
                        'token',
                        'query',
                        'Token de sécurité',
                        true,
                        schema: ['type' => 'string']
                    ))
                    ->withResponse(
                        Response::HTTP_OK,
                        (new OpenApiResponse())
                            ->withContent(new \ArrayObject([
                                'application/ld+json' => [
                                    'schema' => [
                                        'type' => 'object',
                                        'properties' => [
                                            'content' => [
                                                'type' => 'string',
                                                'example' => '<h1>Enseigne</h1><b>Bonjour</b>, vous avez reçu un chèque cadeau valable 3 mois ! <a href="###">Profitez-en vite en magasin</a>.',
                                                'description' => 'Contenu HTML de la page',
                                            ],
                                            'company' => [
                                                'type' => 'string',
                                                'description' => 'Enseigne auquel appartient le client',
                                                'example' => '/api/companies/12',
                                            ],
                                            'sms' => [
                                                'type' => 'object',
                                                'description' => 'Informations sur le SMS liée à la page',
                                                'properties' => [
                                                    'customer' => [
                                                        'type' => 'object',
                                                        'description' => 'Informations sur le client qui a reçu le SMS',
                                                        'properties' => [
                                                            'firstname' => [
                                                                'type' => 'string',
                                                                'example' => 'François',
                                                                'description' => 'Prénom du client',
                                                            ],
                                                            'lastname' => [
                                                                'type' => 'string',
                                                                'example' => 'Dupont',
                                                                'description' => 'Nom du client',
                                                            ],
                                                            'loyaltyPoints' => [
                                                                'type' => 'integer',
                                                                'example' => 37,
                                                                'description' => 'Nombre de points de fidélité actuels du client',
                                                            ],
                                                        ],
                                                    ],
                                                    'voucher' => [
                                                        'type' => 'object',
                                                        'nullable' => true,
                                                        'description' => 'Informations sur le chèque lié à la page',
                                                        'properties' => [
                                                            'id' => [
                                                                'type' => 'integer',
                                                                'description' => 'Identifiant du chèque',
                                                                'example' => 12,
                                                            ],
                                                            'type' => [
                                                                'type' => 'string', 'enum' => Voucher::TYPES,
                                                                'description' => 'Type de chèque',
                                                            ],
                                                            'amount' => [
                                                                'type' => 'integer',
                                                                'description' => 'Montant du chèque en euros (€)',
                                                                'example' => 10,
                                                            ],
                                                            'burnt' => [
                                                                'type' => 'boolean',
                                                                'description' => '`true` si le chèque a été utilisé',
                                                                'example' => false,
                                                            ],
                                                            'expirationDate' => [
                                                                'type' => 'string', 'format' => 'date-time', 'nullable' => true,
                                                                'description' => "Date d'expiration du chèque - format ISO 8601",
                                                            ],
                                                            'availabilityDate' => [
                                                                'type' => 'string', 'format' => 'date-time',
                                                                'description' => 'Date à partir de laquelle le chèque est utilisable - format ISO 8601',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                ],
                            ]))
                    )
            );
    }

    public static function getOpenApiPath(): string
    {
        return self::PATH;
    }
}
