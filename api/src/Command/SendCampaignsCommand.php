<?php

namespace App\Command;

use App\Repository\CampaignAnniversaireRepository;
use App\Repository\CampaignDormantRepository;
use App\Repository\CampaignExpirationChequeRepository;
use App\Service\CampaignManager;
use Symfony\Component\Clock\ClockAwareTrait;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:send-campaigns',
    description: 'Add a short description for your command',
)]
class SendCampaignsCommand extends Command
{
    use ClockAwareTrait;

    public const TYPES = [self::TYPE_DORMANT, self::TYPE_ANNIVERSAIRE, self::TYPE_EXPIRATION_CHEQUE];
    public const TYPE_DORMANT = 'dormant';
    public const TYPE_ANNIVERSAIRE = 'anniversaire';
    public const TYPE_EXPIRATION_CHEQUE = 'expiration_cheque';

    public function __construct(
        private readonly CampaignAnniversaireRepository $campaignAnniversaireRepository,
        private readonly CampaignDormantRepository $campaignDormantRepository,
        private readonly CampaignExpirationChequeRepository $campaignExpirationChequeRepository,
        private readonly CampaignManager $campaignManager,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('type', InputArgument::REQUIRED, 'Argument description')
            ->addOption('force', 'f', InputOption::VALUE_NONE, 'Force')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $type = $input->getArgument('type');

        if (!in_array($type, self::TYPES)) {
            $io->error('Invalid type');

            return Command::FAILURE;
        }

        $this->sendCampaign($io, $type);

        $io->success('Traitement terminé.');

        return Command::SUCCESS;
    }

    private function sendCampaign(SymfonyStyle $io, mixed $type): void
    {
        switch ($type) {
            case self::TYPE_ANNIVERSAIRE:
                $this->sendAnniversaire($io);
                break;
            case self::TYPE_DORMANT:
                $this->sendDormant($io);
                break;
            case self::TYPE_EXPIRATION_CHEQUE:
                $this->sendExpirationCheque($io);
                break;
        }
    }

    private function sendAnniversaire(SymfonyStyle $io): void
    {
        $campaigns = $this->campaignAnniversaireRepository->findCampaignsToSend();
        $io->info('Envoi des campagnes anniversaire');
        foreach ($campaigns as $campaign) {
            $io->text(sprintf('#%s - %s', $campaign->getCompany()->getId(), $campaign->getCompany()->getName()));
            $this->campaignManager->scheduleAnniversaireCampaign($campaign);
        }
    }

    private function sendDormant(SymfonyStyle $io): void
    {
        $campaigns = $this->campaignDormantRepository->findCampaignsToSend();
        $io->info('Envoi des campagnes dormant');
        foreach ($campaigns as $campaign) {
            $io->text(sprintf('#%s - %s', $campaign->getCompany()->getId(), $campaign->getCompany()->getName()));
            $this->campaignManager->scheduleDormantCampaign($campaign);
        }
    }

    private function sendExpirationCheque(SymfonyStyle $io): void
    {
        $campaigns = $this->campaignExpirationChequeRepository->findCampaignsToSend();
        $io->info('Envoi des campagnes expiration chèque');
        foreach ($campaigns as $campaign) {
            $io->text(sprintf('#%s - %s', $campaign->getCompany()->getId(), $campaign->getCompany()->getName()));
            $this->campaignManager->scheduleExpirationChequeCampaign($campaign);
        }
    }
}
