{"api-platform/schema-generator": {"version": "v4.0.0"}, "api-platform/symfony": {"version": "4.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.0", "ref": "e9952e9f393c2d048f10a78f272cd35e807d972b"}, "files": ["config/packages/api_platform.yaml", "config/routes/api_platform.yaml", "src/ApiResource/.gitignore"]}, "composer/pcre": {"version": "1.0.0"}, "composer/semver": {"version": "3.2.7"}, "composer/xdebug-handler": {"version": "2.0.4"}, "dama/doctrine-test-bundle": {"version": "8.2", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "7.2", "ref": "896306d79d4ee143af9eadf9b09fd34a8c391b70"}, "files": ["config/packages/dama_doctrine_test_bundle.yaml"]}, "doctrine/cache": {"version": "2.1.1"}, "doctrine/collections": {"version": "1.6.8"}, "doctrine/common": {"version": "3.2.1"}, "doctrine/dbal": {"version": "3.2.1"}, "doctrine/deprecations": {"version": "v0.5.3"}, "doctrine/doctrine-bundle": {"version": "2.13", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.13", "ref": "8d96c0b51591ffc26794d865ba3ee7d193438a83"}, "files": ["config/packages/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-cache-bundle": {"version": "1.3.5"}, "doctrine/doctrine-fixtures-bundle": {"version": "3.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "1f5514cfa15b947298df4d771e694e578d4c204d"}, "files": ["src/DataFixtures/AppFixtures.php"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "doctrine/event-manager": {"version": "1.1.1"}, "doctrine/inflector": {"version": "2.0.4"}, "doctrine/instantiator": {"version": "1.4.0"}, "doctrine/lexer": {"version": "1.2.1"}, "doctrine/migrations": {"version": "3.3.2"}, "doctrine/orm": {"version": "2.10.4"}, "doctrine/persistence": {"version": "2.3.0"}, "doctrine/reflection": {"version": "1.2.1"}, "doctrine/sql-formatter": {"version": "1.1.2"}, "dunglas/doctrine-json-odm": {"version": "1.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "0.1", "ref": "c2ab78f625df0c89af5908d50a28602ff8c4919f"}}, "easycorp/easyadmin-bundle": {"version": "4.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "b131e6cbfe1b898a508987851963fff485986285"}}, "endroid/qr-code-bundle": {"version": "6.0.0"}, "friendsofphp/php-cs-fixer": {"version": "3.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.0", "ref": "be2103eb4a20942e28a6dd87736669b757132435"}, "files": [".php-cs-fixer.dist.php"]}, "friendsofphp/proxy-manager-lts": {"version": "v1.0.5"}, "gesdinet/jwt-refresh-token-bundle": {"version": "1.4", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "2390b4ed5c195e0b3f6dea45221f3b7c0af523a0"}, "files": ["config/packages/gesdinet_jwt_refresh_token.yaml", "config/routes/gesdinet_jwt_refresh_token.yaml", "src/Entity/RefreshToken.php"]}, "laminas/laminas-code": {"version": "4.5.1"}, "laminas/laminas-eventmanager": {"version": "3.3.1"}, "laminas/laminas-zendframework-bridge": {"version": "1.2.0"}, "lcobucci/clock": {"version": "2.1.0"}, "lcobucci/jwt": {"version": "4.1.5"}, "league/html-to-markdown": {"version": "5.0.2"}, "lexik/jwt-authentication-bundle": {"version": "2.19", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.5", "ref": "e9481b233a11ef7e15fe055a2b21fd3ac1aa2bb7"}, "files": ["config/packages/lexik_jwt_authentication.yaml"]}, "monolog/monolog": {"version": "2.3.5"}, "nelmio/cors-bundle": {"version": "1.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.5", "ref": "6bea22e6c564fba3a1391615cada1437d0bde39c"}, "files": ["config/packages/nelmio_cors.yaml"]}, "nette/php-generator": {"version": "v3.6.5"}, "nette/utils": {"version": "v3.2.6"}, "nikic/php-parser": {"version": "v4.13.2"}, "ocramius/package-versions": {"version": "1.9.0"}, "odolbeau/phone-number-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.0", "ref": "0d4442802a90dd2d1d6c18618998b43f69af0d95"}, "files": ["config/packages/misd_phone_number.yaml"]}, "php": {"version": "7.4"}, "php-cs-fixer/diff": {"version": "v2.0.2"}, "php-http/discovery": {"version": "1.19", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.18", "ref": "f45b5dd173a27873ab19f5e3180b2f661c21de02"}, "files": ["config/packages/http_discovery.yaml"]}, "phpstan/phpstan": {"version": "2.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "5e490cc197fb6bb1ae22e5abbc531ddc633b6767"}, "files": ["phpstan.dist.neon"]}, "phpunit/phpunit": {"version": "9.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "9.6", "ref": "7364a21d87e658eb363c5020c072ecfdc12e2326"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "prolix/maintenance-bundle": {"version": "2.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.1", "ref": "662179a8c0bdc0f96a366ac359bbb9e766010844"}, "files": ["config/packages/lexik_maintenance.yaml"]}, "psr/cache": {"version": "3.0.0"}, "psr/container": {"version": "2.0.2"}, "psr/event-dispatcher": {"version": "1.0.0"}, "psr/link": {"version": "2.0.1"}, "psr/log": {"version": "3.0.0"}, "stof/doctrine-extensions-bundle": {"version": "1.8", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.2", "ref": "e805aba9eff5372e2d149a9ff56566769e22819d"}, "files": ["config/packages/stof_doctrine_extensions.yaml"]}, "symfony/asset": {"version": "v6.0.1"}, "symfony/browser-kit": {"version": "v6.0.1"}, "symfony/cache": {"version": "v6.0.2"}, "symfony/cache-contracts": {"version": "v3.0.0"}, "symfony/config": {"version": "v6.0.2"}, "symfony/console": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "1781ff40d8a17d87cf53f8d4cf0c8346ed2bb461"}, "files": ["bin/console"]}, "symfony/css-selector": {"version": "v6.0.2"}, "symfony/debug-bundle": {"version": "6.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "5aa8aa48234c8eb6dbdd7b3cd5d791485d2cec4b"}, "files": ["config/packages/debug.yaml"]}, "symfony/dependency-injection": {"version": "v6.0.2"}, "symfony/deprecation-contracts": {"version": "v3.0.0"}, "symfony/doctrine-bridge": {"version": "v6.0.2"}, "symfony/dom-crawler": {"version": "v6.0.2"}, "symfony/dotenv": {"version": "v6.0.2"}, "symfony/error-handler": {"version": "v6.0.2"}, "symfony/event-dispatcher": {"version": "v6.0.2"}, "symfony/event-dispatcher-contracts": {"version": "v3.0.0"}, "symfony/expression-language": {"version": "v6.0.1"}, "symfony/fake-sms-notifier": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "b5809b9a44a5deb1b2df8654697756e8e73b7606"}}, "symfony/filesystem": {"version": "v6.0.0"}, "symfony/finder": {"version": "v6.0.2"}, "symfony/flex": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.4", "ref": "52e9754527a15e2b79d9a610f98185a1fe46622a"}, "files": [".env", ".env.dev"]}, "symfony/form": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.2", "ref": "7d86a6723f4a623f59e2bf966b6aad2fc461d36b"}, "files": ["config/packages/csrf.yaml"]}, "symfony/framework-bundle": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.2", "ref": "87bcf6f7c55201f345d8895deda46d2adbdbaa89"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/http-client": {"version": "v6.0.2"}, "symfony/http-client-contracts": {"version": "v3.0.0"}, "symfony/http-foundation": {"version": "v6.0.2"}, "symfony/http-kernel": {"version": "v6.0.2"}, "symfony/lock": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.2", "ref": "8e937ff2b4735d110af1770f242c1107fdab4c8e"}, "files": ["config/packages/lock.yaml"]}, "symfony/mailer": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "09051cfde49476e3c12cd3a0e44289ace1c75a4f"}, "files": ["config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/mercure": {"version": "v0.6.1"}, "symfony/mercure-bundle": {"version": "0.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "0.3", "ref": "528285147494380298f8f991ee8c47abebaf79db"}, "files": ["config/packages/mercure.yaml"]}, "symfony/messenger": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.0", "ref": "ba1ac4e919baba5644d31b57a3284d6ba12d52ee"}, "files": ["config/packages/messenger.yaml"]}, "symfony/monolog-bridge": {"version": "v6.0.1"}, "symfony/monolog-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "aff23899c4440dd995907613c1dd709b6f59503f"}, "files": ["config/packages/monolog.yaml"]}, "symfony/notifier": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.0", "ref": "178877daf79d2dbd62129dd03612cb1a2cb407cc"}, "files": ["config/packages/notifier.yaml"]}, "symfony/options-resolver": {"version": "v6.0.0"}, "symfony/password-hasher": {"version": "v6.0.2"}, "symfony/polyfill-php70": {"version": "v1.20.0"}, "symfony/process": {"version": "v6.0.2"}, "symfony/property-access": {"version": "v6.0.2"}, "symfony/property-info": {"version": "v6.0.2"}, "symfony/routing": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "21b72649d5622d8f7da329ffb5afb232a023619d"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/runtime": {"version": "v6.0.0"}, "symfony/scheduler": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.2", "ref": "caea3c928ee9e1b21288fd76aef36f16ea355515"}, "files": ["src/Schedule.php"]}, "symfony/security-bundle": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "2ae08430db28c8eb4476605894296c82a642028f"}, "files": ["config/packages/security.yaml", "config/routes/security.yaml"]}, "symfony/security-core": {"version": "v6.0.2"}, "symfony/security-csrf": {"version": "v6.0.1"}, "symfony/security-guard": {"version": "v5.3.7"}, "symfony/security-http": {"version": "v6.0.2"}, "symfony/serializer": {"version": "v6.0.2"}, "symfony/service-contracts": {"version": "v3.0.0"}, "symfony/stopwatch": {"version": "v6.0.0"}, "symfony/string": {"version": "v6.0.2"}, "symfony/translation": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "e28e27f53663cc34f0be2837aba18e3a1bef8e7b"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/translation-contracts": {"version": "v3.0.0"}, "symfony/twig-bridge": {"version": "v6.0.2"}, "symfony/twig-bundle": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "cab5fd2a13a45c266d45a7d9337e28dee6272877"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/uid": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "0df5844274d871b37fc3816c57a768ffc60a43a5"}, "files": []}, "symfony/ux-twig-component": {"version": "2.23", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.13", "ref": "67814b5f9794798b885cec9d3f48631424449a01"}, "files": ["config/packages/twig_component.yaml"]}, "symfony/validator": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "8c1c4e28d26a124b0bb273f537ca8ce443472bfd"}, "files": ["config/packages/validator.yaml"]}, "symfony/var-dumper": {"version": "v6.0.2"}, "symfony/var-exporter": {"version": "v6.0.0"}, "symfony/web-link": {"version": "v6.0.1"}, "symfony/web-profiler-bundle": {"version": "6.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.1", "ref": "e42b3f0177df239add25373083a564e5ead4e13a"}, "files": ["config/packages/web_profiler.yaml", "config/routes/web_profiler.yaml"]}, "symfony/yaml": {"version": "v6.0.2"}, "tilleuls/forgot-password-bundle": {"version": "1.4", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "48ab9b5581997a68245b79ac21e355152e29fcde"}, "files": ["config/packages/coop_tilleu<PERSON>_forgot_password.yaml", "config/routes/coop_tilleu<PERSON>_forgot_password.yaml"]}, "twig/extra-bundle": {"version": "v3.20.0"}, "twig/twig": {"version": "v3.3.7"}, "zendframework/zend-code": {"version": "3.4.1"}, "zendframework/zend-eventmanager": {"version": "3.2.1"}, "zenstruck/browser": {"version": "1.9", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.9", "ref": "66c5290398734ff02cedddf44dce6eb2002d958c"}}, "zenstruck/foundry": {"version": "1.33", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.10", "ref": "37c2f894cc098ab4c08874b80cccc8e2f8de7976"}, "files": ["config/packages/zenstruck_foundry.yaml"]}, "zenstruck/messenger-test": {"version": "1.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.7", "ref": "e0d2a904cd584d15bcbb4c4a011549840bc01daf"}}}