# In all environments, the following files are loaded if they exist,
# the latter taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.
# https://symfony.com/doc/current/configuration/secrets.html
#
# Run "composer dump-env prod" to compile .env files for production use (requires symfony/flex >=1.2).
# https://symfony.com/doc/current/best_practices.html#use-environment-variables-for-infrastructure-configuration

# API Platform distribution
TRUSTED_PROXIES=*********/8,10.0.0.0/8,**********/12,***********/16
TRUSTED_HOSTS=^(localhost|caddy)$

###> symfony/framework-bundle ###
APP_ENV=dev
APP_SECRET=
###< symfony/framework-bundle ###

###> doctrine/doctrine-bundle ###
# Format described at https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# IMPORTANT: You MUST configure your server version, either here or in config/packages/doctrine.yaml
#
# DATABASE_URL="sqlite:///%kernel.project_dir%/var/data.db"
# DATABASE_URL="mysql://app:!ChangeMe!@127.0.0.1:3306/app?serverVersion=8&charset=utf8mb4"
DATABASE_URL="*****************************************/app?serverVersion=15&charset=utf8"
###< doctrine/doctrine-bundle ###

###> nelmio/cors-bundle ###
CORS_ALLOW_ORIGIN='^https?://(localhost|127\.0\.0\.1)(:[0-9]+)?$'
###< nelmio/cors-bundle ###

###> symfony/mercure-bundle ###
# See https://symfony.com/doc/current/mercure.html#configuration
# The URL of the Mercure hub, used by the app to publish updates (can be a local URL)
MERCURE_URL=http://mercure/.well-known/mercure
# The public URL of the Mercure hub, used by the browser to connect
MERCURE_PUBLIC_URL=https://localhost/.well-known/mercure
# The secret used to sign the JWTs
MERCURE_JWT_SECRET="!ChangeThisMercureHubJWTSecretKey!"
###< symfony/mercure-bundle ###

STRIPE_KEY='pk_test_XXXXXX'
STRIPE_SECRET='sk_test_XXXXXX'
STRIPE_ENDPOINT_SECRET='whsec_XXXXXX'
MAILER_DEV_RECIPIENT='<EMAIL>'
MAILER_ADMIN_RECIPIENT='<EMAIL>'
MAILER_SENDER='<EMAIL>'
SMS_DEV_RECIPIENT=
LINKMOBILITY_API_KEY='xxx'
LINKMOBILITY_ACCP_TRANSACTIONAL='xxx'
LINKMOBILITY_ACCP_MARKETING='xxx'
LINKMOBILITY_DSN=linkmobility-sms://xxxx@default?from=MACARTEFID
FRONT_URL='http://localhost:3000'
API_HOST=localhost
API_SCHEME=https
USE_MAIL_NOTIFIER=false
ENABLE_SECURED_AREAS=true
PLAYWRIGHT_ENV=false
ACCOUNTING_RECIPIENTS='[]'
OPENAI_API_KEY=
FIDUCIAL_LICENCE_API_URL=https://services-licences-preprod.fiducial.fr/
FIDUCIAL_LICENCE_API_TOKEN='XXXXXXXX'
FIDUCIAL_LICENCE_API_SOURCE='XXXXXXXX-XXXX-XXXX-XXXXXXX'
FIDUCIAL_CANCEL_RECIPIENT='<EMAIL>'
NOFID_HOST='nofid.localhost'
ENABLE_SCHEDULER=false
SERVER_API_KEY='mcf-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'
REDIS_HOST=redis:6379

###> lexik/jwt-authentication-bundle ###
JWT_SECRET_KEY=%kernel.project_dir%/config/jwt/private.pem
JWT_PUBLIC_KEY=%kernel.project_dir%/config/jwt/public.pem
JWT_PASSPHRASE=****************************************************************
PINCODE_JWT_PASSPHRASE=
###< lexik/jwt-authentication-bundle ###

###> symfony/mailer ###
MAILER_DSN=sendmail://localhost?command=/usr/sbin/exim4%20-t
###< symfony/mailer ###

###> symfony/messenger ###
# Choose one of the transports below
# MESSENGER_TRANSPORT_DSN=amqp://guest:guest@localhost:5672/%2f/messages
# MESSENGER_TRANSPORT_DSN=redis://localhost:6379/messages
MESSENGER_TRANSPORT_DSN=doctrine://default?auto_setup=0
###< symfony/messenger ###

###> symfony/fake-sms-notifier ###
FAKE_SMS_DSN=fakesms+email://default?to=<EMAIL>&from=<EMAIL>
###< symfony/fake-sms-notifier ###

###> symfony/lock ###
# Choose one of the stores below
# postgresql+advisory://db_user:db_password@localhost/db_name
LOCK_DSN=flock
###< symfony/lock ###
