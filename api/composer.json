{"type": "project", "license": "MIT", "require": {"php": ">=8.3", "ext-ctype": "*", "ext-iconv": "*", "api-platform/doctrine-orm": "*", "api-platform/symfony": "^3 || ^4", "beberlei/doctrineextensions": "^1.3", "doctrine/doctrine-bundle": "^2.13.2", "doctrine/doctrine-fixtures-bundle": "^3.4", "doctrine/doctrine-migrations-bundle": "^3.4.1", "doctrine/orm": "^3.3.1", "dragonmantank/cron-expression": "^3.3", "dunglas/doctrine-json-odm": "^1.3", "easycorp/easyadmin-bundle": "^4.24", "endroid/qr-code-bundle": "*", "gesdinet/jwt-refresh-token-bundle": "^1.4", "lexik/jwt-authentication-bundle": "^3.1", "nelmio/cors-bundle": "^2.2", "nixilla/php-jwt": "^0.1.1", "nyholm/psr7": "^1.8", "odolbeau/phone-number-bundle": "^4.1", "openai-php/client": "^0.9.2", "phpoffice/phpspreadsheet": "^3.8", "phpstan/phpdoc-parser": "^1.16", "prolix/maintenance-bundle": "dev-sf7", "runtime/frankenphp-symfony": "^0.2.0", "stof/doctrine-extensions-bundle": "^1.8", "stripe/stripe-php": "^10.16", "symfony/asset": "7.2.*", "symfony/cache": "7.2.*", "symfony/console": "7.2.*", "symfony/doctrine-messenger": "7.2.*", "symfony/dotenv": "7.2.*", "symfony/expression-language": "7.2.*", "symfony/fake-sms-notifier": "7.2.*", "symfony/flex": "^2.2", "symfony/framework-bundle": "7.2.*", "symfony/html-sanitizer": "7.2.*", "symfony/http-client": "7.2.*", "symfony/lock": "7.2.*", "symfony/mailer": "7.2.*", "symfony/mercure-bundle": "^0.3.5", "symfony/messenger": "7.2.*", "symfony/monolog-bundle": "^3.8", "symfony/notifier": "7.2.*", "symfony/property-access": "7.2.*", "symfony/property-info": "7.2.*", "symfony/rate-limiter": "7.2.*", "symfony/runtime": "7.2.*", "symfony/scheduler": "7.2.*", "symfony/security-bundle": "7.2.*", "symfony/security-csrf": "7.2.*", "symfony/serializer": "7.2.*", "symfony/translation": "7.2.*", "symfony/twig-bundle": "7.2.*", "symfony/uid": "7.2.*", "symfony/validator": "7.2.*", "symfony/yaml": "7.2.*", "tilleuls/forgot-password-bundle": "^1.4", "zenstruck/foundry": "2.2.x"}, "require-dev": {"api-platform/schema-generator": "^5.0", "brianium/paratest": "^7.8", "captainhook/captainhook": "^5.16", "dama/doctrine-test-bundle": "^8.2", "friendsofphp/php-cs-fixer": "^3.21", "mtdowling/jmespath.php": "^2.6", "phpstan/phpstan": "*", "phpstan/phpstan-doctrine": "^2.0", "phpstan/phpstan-symfony": "^2.0", "phpunit/phpunit": "^11.0", "rector/rector": "^2.0", "robiningelbrecht/phpunit-pretty-print": "^1.3", "slam/phpstan-extensions": "^6.6", "symfony/browser-kit": "7.2.*", "symfony/css-selector": "7.2.*", "symfony/debug-bundle": "7.2.*", "symfony/maker-bundle": "^1.44", "symfony/stopwatch": "7.2.*", "symfony/var-dumper": "7.2.*", "symfony/web-profiler-bundle": "7.2.*", "zenstruck/browser": "^1.4", "zenstruck/messenger-test": "1.9.3"}, "repositories": [{"type": "git", "url": "https://github.com/mdevlamynck/LexikMaintenanceBundle"}], "config": {"optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true, "endroid/installer": true, "php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/", "Utils\\Rector\\": "utils/rector/src"}}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-intl-grapheme": "*", "symfony/polyfill-intl-normalizer": "*", "symfony/polyfill-mbstring": "*", "symfony/polyfill-php82": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php72": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.2.*", "docker": false}}}