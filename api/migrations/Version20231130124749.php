<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231130124749 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE customer ADD phone_number_country_code VARCHAR(10) DEFAULT NULL');
        $this->addSql("UPDATE customer SET phone_number_country_code = 'fr' WHERE phone_number IS NOT NULL");
        $this->addSql('ALTER TABLE campaign_sms ADD consumed_credit INT DEFAULT NULL');
        $this->addSql('UPDATE campaign_sms SET consumed_credit = customer_count WHERE consumed_credit IS NULL');
        $this->addSql('ALTER TABLE campaign_sms ALTER COLUMN consumed_credit SET NOT NULL');
        $this->addSql('ALTER TABLE sms ADD consumed_credit INT DEFAULT NULL');
        $this->addSql('UPDATE sms SET consumed_credit = 1 WHERE consumed_credit IS NULL');
        $this->addSql('ALTER TABLE sms ALTER COLUMN consumed_credit SET NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE customer DROP phone_number_country_code');
        $this->addSql('ALTER TABLE campaign_sms DROP consumed_credit');
        $this->addSql('ALTER TABLE sms DROP consumed_credit');
    }
}
