security:
    # https://symfony.com/doc/current/security.html#registering-the-user-hashing-passwords
    password_hashers:
        App\Entity\Company: 'auto'
        App\Entity\CompanyDraft: 'auto'
        App\Entity\UserBackOffice: 'auto'
    # https://symfony.com/doc/current/security.html#loading-the-user-the-user-provider
    providers:
        users:
            entity:
                class: App\Entity\Company
                property: email
        admin:
            entity:
                class: App\Entity\UserBackOffice
                property: email
    role_hierarchy:
        ROLE_ADMIN: ROLE_USER
        ROLE_ADMIN_AQUITEM: ROLE_ADMIN
        ROLE_ADMIN_FIDUCIAL: ROLE_ADMIN
        ROLE_SUPER_ADMIN: [ROLE_ADMIN_AQUITEM, ROLE_ADMIN_FIDUCIAL, ROLE_ALLOWED_TO_SWITCH]
    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        api:
            pattern: ^/api/
            stateless: true
            provider: users
            entry_point: jwt
            jwt: ~
            refresh_jwt:
                check_path: /api/token/refresh
                provider: users
        admin:
            pattern: ^/admin/
            provider: admin
            lazy: true
            switch_user: true
            entry_point: 'form_login'
            form_login:
                # The route name that the login form submits to
                check_path: admin_login
                # The name of the route where the login form lives
                # When the user tries to access a protected page, they are redirected here
                login_path: admin_login
                # Secure the login form against CSRF
                # Reference: https://symfony.com/doc/current/security/csrf.html
                enable_csrf: true
                # The page users are redirect to when there is no previous page stored in the
                # session (for example when the users access directly to the login page).
                default_target_path: admin_dashboard
            logout:
                # The route name the user can go to in order to logout
                path: admin_logout
                # The name of the route to redirect to after logging out
                target: admin_login
        main:
            lazy: true
            provider: users
            json_login:
                check_path: auth # The name in routes.yaml is enough for mapping
                username_path: email
                password_path: password
                success_handler: lexik_jwt_authentication.handler.authentication_success
                failure_handler: lexik_jwt_authentication.handler.authentication_failure

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
        - { path: ^/$, roles: PUBLIC_ACCESS } # Allows accessing the Swagger UI
        - { path: ^/docs, roles: PUBLIC_ACCESS } # Allows accessing the Swagger UI docs
        # Routes publiques
        - { path: ^/auth, roles: PUBLIC_ACCESS }
        - { path: ^/api/token/refresh, roles: PUBLIC_ACCESS }
        - { path: ^/api/company_drafts, roles: PUBLIC_ACCESS }
        - { path: ^/api/rich_sms, roles: PUBLIC_ACCESS }
        - { path: ^/api/create-customer-account, roles: PUBLIC_ACCESS }
        - { route: 'customerPostAnonymous', roles: PUBLIC_ACCESS }
        - { path: ^/payment-success, roles: PUBLIC_ACCESS }
        - { path: ^/payment-sms-success, roles: PUBLIC_ACCESS }
        - { path: ^/payment-webhooks, roles: PUBLIC_ACCESS }
        - { path: ^/webhook, roles: PUBLIC_ACCESS }
        - { path: ^/forgot-password, role: PUBLIC_ACCESS }
        # Tests playwright
        - { path: ^/playwright, role: PUBLIC_ACCESS }
        # Backoffice
        - { route: 'admin_entrypoint', role: PUBLIC_ACCESS }
        - { path: ^/admin/login, role: PUBLIC_ACCESS }
        - { path: ^/admin/, role: ROLE_ADMIN }
        # Exclusion de COMPANY_VALID
        - { path: ^/api/profile, roles: IS_AUTHENTICATED_FULLY }
        - { path: ^/api/check-payment-session, roles: IS_AUTHENTICATED_FULLY }
        - { route: 'get_subscription_price', roles: IS_AUTHENTICATED_FULLY }
        - { route: 'companySubscribeWithFiducial', roles: IS_AUTHENTICATED_FULLY }
        - { route: 'companyStartPaymentSMS', roles: IS_AUTHENTICATED_FULLY }
        - { route: 'companyCheckPaymentSubscription', roles: IS_AUTHENTICATED_FULLY }
        - { route: 'companyStartPaymentSubscription', roles: IS_AUTHENTICATED_FULLY }
        - { route: 'companyGetStripePortalUrl', roles: IS_AUTHENTICATED_FULLY }
        - { route: 'companyDelete', roles: IS_AUTHENTICATED_FULLY }
        - { route: 'companyGetSubscription', roles: IS_AUTHENTICATED_FULLY }
        # Vérification que l'abonnement est valide
        - { path: ^/api, allow_if: "is_granted('COMPANY_VALID', null)" }
        # Dev
        - { path: ^/_error, roles: PUBLIC_ACCESS }
        # Par défaut
        - { path: ^/, roles: IS_AUTHENTICATED_FULLY }

when@test:
    security:
        password_hashers:
            # By default, password hashers are resource intensive and take time. This is
            # important to generate secure password hashes. In tests however, secure hashes
            # are not important, waste resources and increase test times. The following
            # reduces the work factor to the lowest possible values.
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: md5
                encode_as_base64: false
                iterations: 0
