# https://taskfile.dev

version: '3'

vars:
    SUDO: 'sudo -u www-data'
    CONSOLE: 'php bin/console'
    DOCKER_COMP: 'docker-compose'
    DOCKER_EXEC: '{{.DOCKER_COMP}} exec -u www-data'
    DOCKER_PHP: '{{.DOCKER_EXEC}} php'
    DOCKER_COMPOSER: '{{.DOCKER_EXEC}} composer'
    DOCKER_SYMFONY: 'sudo -u www-data php bin/console'

tasks:
    install:
        desc: Installation.
        cmds:
            -   task: composer
            -   task: db-init
            #          - task: setup-playwright
            -   task: setup-test
            -   task: cache-clear
            #          - task: init-env
            -   task: build
            -   task: greet

    build:
        cmds:
            -   task: yarn-install

    composer:
        desc: Installation des dépendances php avec composer.
        cmds:
            - '{{.DOCKER_PHP}} composer install'

    db-init:
        cmds:
            - '{{.DOCKER_SYMFONY}} doctrine:database:create --env=dev --if-not-exists'
            - '{{.DOCKER_SYMFONY}} doctrine:schema:drop --force --env=dev'
            - '{{.DOCKER_SYMFONY}} doctrine:migrations:migrate --env=dev --no-interaction'
            - '{{.DOCKER_SYMFONY}} doctrine:fixtures:load --no-interaction --env=dev --group=default'
    setup-test:
        desc: Met à jour les bases de données pour les tests avec phpunit.
        cmds:
            - '{{.DOCKER_SYMFONY}} doctrine:database:create --env=test --if-not-exists'
            - '{{.DOCKER_SYMFONY}} doctrine:schema:update --force --env=test'
            - '{{.DOCKER_SYMFONY}} doctrine:fixtures:load --no-interaction --env=test --group=default'

    setup-paratest:
        desc: Met à jour les bases de données pour les tests avec paratest.
        cmds:
            -   for: [ 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 ]
                cmd: '{{.DOCKER_EXEC}} -e TEST_TOKEN={{.ITEM}} php {{.CONSOLE}} doctrine:database:create --env=test --if-not-exists'
            -   for: [ 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 ]
                cmd: '{{.DOCKER_EXEC}} -e TEST_TOKEN={{.ITEM}} php {{.CONSOLE}} doctrine:schema:update --force --env=test'
            -   for: [ 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 ]
                cmd: '{{.DOCKER_EXEC}} -e TEST_TOKEN={{.ITEM}} php {{.CONSOLE}} doctrine:fixtures:load --no-interaction --env=test --group=default'

    test:
        desc: Lance les tests du projet avec PHPUnit.
        cmds:
            - '{{.DOCKER_PHP}} php vendor/bin/phpunit'

    paratest:
        desc: Lance les tests du projet avec paratest.
        cmds:
            - '{{.DOCKER_PHP}} php vendor/bin/paratest'

    lint:
        desc: Lint les fichiers avec PHP CS Fixer.
        env:
            PHP_CS_FIXER_IGNORE_ENV: 1
        cmds:
            - '{{.DOCKER_COMP}} exec -u www-data -e PHP_CS_FIXER_IGNORE_ENV=1 php php vendor/bin/php-cs-fixer fix'

    phpstan:
        desc: Vérifie les fichiers avec phpstan.
        cmds:
            - '{{.DOCKER_PHP}} php vendor/bin/phpstan'


    cache-clear:
        desc: Vide le cache.
        cmds:
            - '{{.DOCKER_SYMFONY}} cache:clear'
    db-reset:
        desc: db-reset.
        cmds:
            - '{{.DOCKER_SYMFONY}} doctrine:database:drop --force --if-exists'
            - task: db-init
    stripe-listen:
        desc: Lance l'écouteur de webhook Stripe pour les recevoir en local.
        cmds:
            - '{{.DOCKER_PHP}} stripe listen --forward-to http://localhost/payment-webhooks -e invoice.paid,invoice.payment_failed,subscription_schedule.canceled,checkout.session.completed'
    #    greet:
    #        cmds:
    #            - 'echo "Bravo l\'installation est terminée !"'
    #            - echo 'Le site est disponibles sur l'URL suivante'
