{% extends 'base.html.twig' %}

{% block body %}
    <div class="container">
        {{ form_start(form, {attr: {'data-page-form': ''}}) }}
        {{ form_errors(form) }}

        {% if flowType == 'hot' and flow.currentStepNumber == 1 %}
            {{ include('survey/_index.html.twig') }}
        {% else %}
            {{ include('survey/_answer.html.twig') }}
        {% endif %}

        {{ form_rest(form) }}
        {{ form_end(form) }}
    </div>
    <script>
        const direction = '{{ pageDetails.direction }}';

        window.addEventListener("pageswap", async (e) => {
            if (e.viewTransition) {
                e.viewTransition.types.add(direction);
            }
        });

        window.addEventListener("pagereveal", async (e) => {
            if (e.viewTransition) {
                e.viewTransition.types.add(direction);
            }
        });

        document.addEventListener('DOMContentLoaded', () => {
            const progressBar = document.getElementById('progress-bar-outline');
            const totalPages = {{ flow.displayedMaxSteps }};
            const currentPage = {{ pageDetails.currentPage }};

            // Animation de la barre de progression de la progression précédente vers la progression actuelle
            requestAnimationFrame(() => {
                const progressionFinale = (currentPage / totalPages) * 100;
                progressBar.style.width = `${progressionFinale}%`;
            });
        });
    </script>
{% endblock %}
