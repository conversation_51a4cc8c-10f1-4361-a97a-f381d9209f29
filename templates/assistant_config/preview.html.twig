{% extends 'base.html.twig' %}

{% block title %}Aper<PERSON><PERSON> de l'assistant{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .preview-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            background-color: #fff;
        }
        .preview-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .preview-description {
            text-align: center;
            margin-bottom: 30px;
            color: #666;
        }
        .component {
            margin-bottom: 20px;
        }
        .actions {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .json-view {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 30px;
            overflow: auto;
            max-height: 500px;
        }
        pre {
            margin: 0;
        }
        .nav-tabs {
            margin-bottom: 20px;
        }
    </style>
{% endblock %}

{% block body %}
    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Aper<PERSON>u de l'assistant</h1>
            <div>
                <a href="{{ path('app_assistant_config_edit', {'filename': filename}) }}" class="btn btn-primary">
                    <i class="fas fa-edit"></i> Modifier
                </a>
                <a href="{{ path('app_assistant_config_index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour à la liste
                </a>
            </div>
        </div>

        <ul class="nav nav-tabs" id="previewTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="visual-tab" data-bs-toggle="tab" data-bs-target="#visual" type="button" role="tab" aria-controls="visual" aria-selected="true">Aperçu visuel</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="json-tab" data-bs-toggle="tab" data-bs-target="#json" type="button" role="tab" aria-controls="json" aria-selected="false">JSON</button>
            </li>
        </ul>

        <div class="tab-content" id="previewTabsContent">
            <div class="tab-pane fade show active" id="visual" role="tabpanel" aria-labelledby="visual-tab">
                <div class="preview-container">
                    <div class="preview-header">
                        <h2>{{ assistant.title }}</h2>
                    </div>
                    <div class="preview-description">
                        <p>{{ assistant.description }}</p>
                    </div>

                    {% if assistant.pages is defined and assistant.pages|length > 0 %}
                        <div id="pagesContainer">
                            <ul class="nav nav-pills mb-4" id="pagesTabs" role="tablist">
                                {% for page in assistant.pages %}
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link {% if loop.first %}active{% endif %}" 
                                                id="page-{{ page.id }}-tab" 
                                                data-bs-toggle="pill" 
                                                data-bs-target="#page-{{ page.id }}" 
                                                type="button" 
                                                role="tab" 
                                                aria-controls="page-{{ page.id }}" 
                                                aria-selected="{% if loop.first %}true{% else %}false{% endif %}">
                                            {{ page.title }}
                                        </button>
                                    </li>
                                {% endfor %}
                            </ul>

                            <div class="tab-content" id="pagesTabsContent">
                                {% for page in assistant.pages %}
                                    <div class="tab-pane fade {% if loop.first %}show active{% endif %}" 
                                         id="page-{{ page.id }}" 
                                         role="tabpanel" 
                                         aria-labelledby="page-{{ page.id }}-tab">
                                        
                                        <h3>{{ page.title }}</h3>
                                        
                                        {% if page.components is defined %}
                                            <div class="components">
                                                {% for component in page.components %}
                                                    <div class="component">
                                                        <div class="card">
                                                            <div class="card-header">
                                                                {{ component.component }} ({{ component.id }})
                                                            </div>
                                                            <div class="card-body">
                                                                {% if component.component == 'Text' %}
                                                                    <p>{{ component.props.content }}</p>
                                                                {% elseif component.component == 'Button' %}
                                                                    <button class="btn btn-primary">{{ component.props.label }}</button>
                                                                {% elseif component.component == 'ButtonGroup' %}
                                                                    <div class="btn-group" role="group">
                                                                        {% if component.children is defined %}
                                                                            {% for child in component.children %}
                                                                                <button class="btn btn-primary">{{ child.props.label }}</button>
                                                                            {% endfor %}
                                                                        {% endif %}
                                                                    </div>
                                                                {% elseif component.component == 'Textarea' or component.component == 'RichTextarea' %}
                                                                    <div class="form-group">
                                                                        <label>{{ component.props.label }}</label>
                                                                        <textarea class="form-control" rows="{{ component.props.rows|default(5) }}" placeholder="{{ component.props.placeholder|default('') }}"></textarea>
                                                                    </div>
                                                                {% elseif component.component == 'Select' %}
                                                                    <div class="form-group">
                                                                        <label>{{ component.props.label }}</label>
                                                                        <select class="form-control">
                                                                            {% if component.props.options is defined %}
                                                                                {% for option in component.props.options %}
                                                                                    <option value="{{ option.value }}">{{ option.label }}</option>
                                                                                {% endfor %}
                                                                            {% endif %}
                                                                        </select>
                                                                    </div>
                                                                {% elseif component.component == 'Checkbox' %}
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="checkbox" id="{{ component.id }}">
                                                                        <label class="form-check-label" for="{{ component.id }}">
                                                                            {{ component.props.label }}
                                                                        </label>
                                                                    </div>
                                                                {% elseif component.component == 'CheckboxGroup' %}
                                                                    <div class="form-group">
                                                                        <label>{{ component.props.label }}</label>
                                                                        {% if component.props.options is defined %}
                                                                            {% for option in component.props.options %}
                                                                                <div class="form-check">
                                                                                    <input class="form-check-input" type="checkbox" id="{{ component.id }}-{{ option.value }}">
                                                                                    <label class="form-check-label" for="{{ component.id }}-{{ option.value }}">
                                                                                        {{ option.label }}
                                                                                    </label>
                                                                                </div>
                                                                            {% endfor %}
                                                                        {% endif %}
                                                                    </div>
                                                                {% elseif component.component == 'Radio' %}
                                                                    <div class="form-group">
                                                                        <label>{{ component.props.label }}</label>
                                                                        {% if component.props.options is defined %}
                                                                            {% for option in component.props.options %}
                                                                                <div class="form-check">
                                                                                    <input class="form-check-input" type="radio" name="{{ component.id }}" id="{{ component.id }}-{{ option.value }}">
                                                                                    <label class="form-check-label" for="{{ component.id }}-{{ option.value }}">
                                                                                        {{ option.label }}
                                                                                    </label>
                                                                                </div>
                                                                            {% endfor %}
                                                                        {% endif %}
                                                                    </div>
                                                                {% elseif component.component == 'FileUpload' %}
                                                                    <div class="form-group">
                                                                        <label>{{ component.props.label }}</label>
                                                                        <div class="input-group">
                                                                            <input type="file" class="form-control">
                                                                        </div>
                                                                        <small class="form-text text-muted">{{ component.props.dropzoneText|default('') }}</small>
                                                                    </div>
                                                                {% elseif component.component == 'Panel' %}
                                                                    <div class="card">
                                                                        <div class="card-header">
                                                                            {{ component.props.title }}
                                                                        </div>
                                                                        <div class="card-body">
                                                                            {% if component.children is defined %}
                                                                                {% for child in component.children %}
                                                                                    <div class="mb-3">
                                                                                        <strong>{{ child.component }}</strong> ({{ child.id }})
                                                                                    </div>
                                                                                {% endfor %}
                                                                            {% endif %}
                                                                        </div>
                                                                    </div>
                                                                {% elseif component.component == 'List' %}
                                                                    <ul class="{% if component.props.type == 'bullet' %}list-disc{% elseif component.props.type == 'numbered' %}list-decimal{% endif %}">
                                                                        {% if component.props.items is defined %}
                                                                            {% for item in component.props.items %}
                                                                                <li>{{ item }}</li>
                                                                            {% endfor %}
                                                                        {% endif %}
                                                                    </ul>
                                                                {% else %}
                                                                    <div class="alert alert-info">
                                                                        Composant {{ component.component }} (prévisualisation non disponible)
                                                                    </div>
                                                                {% endif %}
                                                            </div>
                                                        </div>
                                                    </div>
                                                {% endfor %}
                                            </div>
                                        {% endif %}

                                        {% if page.actions is defined %}
                                            <div class="actions">
                                                {% for action in page.actions %}
                                                    <button class="btn {% if action.props.variant == 'primary' %}btn-primary{% elseif action.props.variant == 'secondary' %}btn-secondary{% else %}btn-outline-primary{% endif %}">
                                                        {{ action.props.label }}
                                                    </button>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            Cet assistant ne contient pas de pages définies.
                        </div>
                    {% endif %}
                </div>
            </div>
            <div class="tab-pane fade" id="json" role="tabpanel" aria-labelledby="json-tab">
                <div class="json-view">
                    <pre><code>{{ assistant|json_encode(constant('JSON_PRETTY_PRINT') b-or constant('JSON_UNESCAPED_UNICODE') b-or constant('JSON_UNESCAPED_SLASHES'))|raw }}</code></pre>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialisation des onglets Bootstrap
            const tabElements = document.querySelectorAll('button[data-bs-toggle="tab"], button[data-bs-toggle="pill"]');
            tabElements.forEach(tabElement => {
                tabElement.addEventListener('click', function(event) {
                    event.preventDefault();
                    const target = document.querySelector(this.dataset.bsTarget);
                    
                    // Désactiver tous les onglets et contenus
                    const parent = this.closest('.nav');
                    parent.querySelectorAll('.nav-link').forEach(link => {
                        link.classList.remove('active');
                        link.setAttribute('aria-selected', 'false');
                    });
                    
                    const tabContentParent = document.querySelector(this.dataset.bsTarget).closest('.tab-content');
                    tabContentParent.querySelectorAll('.tab-pane').forEach(pane => {
                        pane.classList.remove('show', 'active');
                    });
                    
                    // Activer l'onglet et le contenu sélectionnés
                    this.classList.add('active');
                    this.setAttribute('aria-selected', 'true');
                    target.classList.add('show', 'active');
                });
            });
        });
    </script>
{% endblock %}
