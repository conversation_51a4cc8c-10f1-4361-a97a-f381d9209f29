{% extends 'base.html.twig' %}

{% block title %}{{ isNew ? 'Nouvel assistant' : 'Modifier l\'assistant' }}{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jsoneditor/9.10.0/jsoneditor.min.css" referrerpolicy="no-referrer" />
    <style>
        #editor-container {
            height: 600px;
        }
        .component-template {
            cursor: pointer;
            margin-bottom: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        .component-template:hover {
            background-color: #f8f9fa;
        }
        .sidebar {
            height: 600px;
            overflow-y: auto;
        }
        .nav-tabs {
            margin-bottom: 15px;
        }
        .preview-frame {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .action-buttons {
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
        }
    </style>
{% endblock %}

{% block body %}
    <div class="container-fluid mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>{{ isNew ? 'Nouvel assistant' : 'Modifier l\'assistant: ' ~ assistant.title }}</h1>
            <div>
                <a href="{{ path('app_assistant_config_index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour à la liste
                </a>
                {% if not isNew %}
                    <a href="{{ path('app_assistant_config_preview', {filename: filename}) }}" class="btn btn-info" target="_blank">
                        <i class="fas fa-eye"></i> Aperçu
                    </a>
                {% endif %}
            </div>
        </div>

        <div class="row">
            <div class="col-md-3">
                <div class="sidebar">
                    <ul class="nav nav-tabs" id="templateTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="components-tab" data-bs-toggle="tab" data-bs-target="#components" type="button" role="tab" aria-controls="components" aria-selected="true">Composants</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="actions-tab" data-bs-toggle="tab" data-bs-target="#actions" type="button" role="tab" aria-controls="actions" aria-selected="false">Actions</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="apis-tab" data-bs-toggle="tab" data-bs-target="#apis" type="button" role="tab" aria-controls="apis" aria-selected="false">APIs</button>
                        </li>
                    </ul>
                    <div class="tab-content" id="templateTabsContent">
                        <div class="tab-pane fade show active" id="components" role="tabpanel" aria-labelledby="components-tab">
                            <div class="mb-3">
                                <input type="text" class="form-control" id="componentSearch" placeholder="Rechercher un composant...">
                            </div>
                            <div id="componentTemplates">
                                {% for type, template in componentTemplates %}
                                    <div class="component-template" data-type="{{ type }}" data-template="{{ template|json_encode }}">
                                        <strong>{{ type }}</strong>
                                        <p class="mb-0 small text-muted">{{ template.component }}</p>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="tab-pane fade" id="actions" role="tabpanel" aria-labelledby="actions-tab">
                            <div id="actionTemplates">
                                {% for type, template in actionTemplates %}
                                    <div class="component-template" data-type="{{ type }}" data-template="{{ template|json_encode }}">
                                        <strong>{{ type|capitalize }}</strong>
                                        <p class="mb-0 small text-muted">{{ template.type }}</p>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="tab-pane fade" id="apis" role="tabpanel" aria-labelledby="apis-tab">
                            <div id="apiTemplates">
                                {% for type, template in apiTemplates %}
                                    <div class="component-template" data-type="{{ type }}" data-template="{{ template|json_encode }}">
                                        <strong>{{ type|capitalize }}</strong>
                                        <p class="mb-0 small text-muted">{{ template.endpoint }}</p>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-9">
                <div id="editor-container"
                     data-save-url="{{ path('app_assistant_config_save') }}"
                     data-preview-url="{{ path('app_assistant_config_preview', {filename: 'PLACEHOLDER'}) }}"></div>

                <script type="text/javascript">
                    // Stocker les données JSON dans des variables globales pour éviter les problèmes de parsing
                    window.assistantData = {{ assistant|json_encode|raw }};
                    window.componentTemplatesData = {{ componentTemplates|json_encode|raw }};
                    window.actionTemplatesData = {{ actionTemplates|json_encode|raw }};
                    window.apiTemplatesData = {{ apiTemplates|json_encode|raw }};
                    window.assistantFilename = "{{ filename }}";
                </script>
                <div class="action-buttons">
                    <button id="generateIdButton" class="btn btn-secondary">
                        <i class="fas fa-magic"></i> Générer IDs
                    </button>
                    <div>
                        <button id="saveButton" class="btn btn-success">
                            <i class="fas fa-save"></i> Enregistrer
                        </button>
                        <button id="saveAndPreviewButton" class="btn btn-primary">
                            <i class="fas fa-eye"></i> Enregistrer et prévisualiser
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de sauvegarde -->
    <div class="modal fade" id="saveModal" tabindex="-1" aria-labelledby="saveModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="saveModalLabel">Sauvegarde en cours</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                    </div>
                    <p class="text-center mt-3">Sauvegarde de l'assistant en cours...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal d'erreur -->
    <div class="modal fade" id="errorModal" tabindex="-1" aria-labelledby="errorModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="errorModalLabel">Erreur</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p id="errorMessage"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jsoneditor/9.10.0/jsoneditor.min.js" referrerpolicy="no-referrer"></script>
    <script>
        // Vérifier que JSONEditor est chargé avant de charger notre script
        window.addEventListener('load', function() {
            if (typeof JSONEditor !== 'undefined') {
                // Charger notre script d'édition
                const script = document.createElement('script');
                script.src = "{{ asset('js/assistant-config-editor.js') }}";
                document.body.appendChild(script);
            } else {
                console.error('JSONEditor n\'est pas chargé. Impossible d\'initialiser l\'interface d\'édition.');
                alert('Erreur: La bibliothèque JSONEditor n\'est pas chargée correctement. Veuillez rafraîchir la page ou contacter l\'administrateur.');
            }
        });
    </script>
{% endblock %}
