{% extends 'form/bootstrap_5_layout.html.twig' %}

{% block switch_row %}
    <div class="mb-3">
        <div class="form-check form-switch">
            {{ block('switch_widget') }}
            <label class="form-check-label" for="{{ full_name }}">{{ label|trans }}</label>
        </div>
    </div>
{% endblock %}

{% block switch_widget %}
{% apply spaceless %}
    <input type="checkbox" id="{{ id }}" name="{{ full_name }}" {% if required %}required="required"{% endif %} class="make-switch form-check-input" data-on-text="{{label_on|trans}}" data-off-text="{{label_off|trans}}" {{ block('widget_attributes') }}{% if value is defined %} value="{{ value }}"{% endif %}{% if checked %} checked="checked"{% endif %} />
{% endapply %}
{% endblock %}

{% block dropzone_widget -%}
    {%- set dataController = (attr['data-controller']|default('') ~ ' dropzone')|trim -%}
    {%- set attr = attr|merge({ 'data-controller': '', class: (attr.class|default('') ~ ' dropzone-input')|trim}) -%}

    <div class="dropzone-container" data-controller="{{ dataController }}">
        <input type="file" {{ block('widget_attributes') }} data-dropzone-target="input" />

        <div class="dropzone-placeholder" data-dropzone-target="placeholder">
            {%- if attr.placeholder is defined and attr.placeholder is not none -%}
                {{- translation_domain is same as(false) ? attr.placeholder : attr.placeholder|trans({}, translation_domain) -}}
            {%- endif -%}
        </div>

        <div class="dropzone-preview" data-dropzone-target="preview" style="display: none">
            <button class="dropzone-preview-button" type="button" data-dropzone-target="previewClearButton"></button>
            <div class="dropzone-preview-image" style="display: none" data-dropzone-target="previewImage"></div>
            <div data-dropzone-target="previewFilename" class="dropzone-preview-filename"></div>
        </div>
    </div>
{%- endblock %}

{% block wysiwig_widget -%}
    {%- set dataController = (attr['data-controller']|default('') ~ ' wysiwig')|trim -%}
    {%- set attr = attr|merge({ 'data-controller': '', class: (attr.class|default('') ~ ' form-control')|trim}) -%}
    <div data-controller="{{ dataController }}" data-wysiwig-max-length-value="{{ max_length }}" data-wysiwig-max-new-line-value="{{ max_new_line }}" data-wysiwig-height-value="{{ height }}" data-wysiwig-toolbar-items-value="{{ toolbar_items|json_encode }}">
        <div data-wysiwig-target="editor" class="editor {{ editor_custom_class }}"></div>
        <textarea data-wysiwig-target="input" {{ block('widget_attributes') }} style="height: 0; padding: 0; font-size: 0; min-height: 10px; border: 0; opacity: 0;">
            {{ value }}
        </textarea>
    </div>
{%- endblock %}
