{% extends "base.html.twig" %}

{% block page_header %}
    <header class="page-header">
        <div class="{{ containerClass|default("container") }}">
            <div class="d-flex">
                <h1 class="page-title flex-grow-1">
                    {{ block("title")|spaceless|trans }}
                </h1>
                {% block header_btn %}
                    {% if entityName is defined %}
                        {% if pageType is defined and pageType == "form" %}
                            {% if is_granted("#{entityName}_list") or not app.user.permissionExists("#{entityName}_list") %}
                                <a href="{{ url("#{entityName}_liste") }}" class="btn btn-outline-dark">{{ "#{entityName}.lists"|trans }}</a>
                            {% endif %}
                        {% elseif (pageType is defined and pageType != "noLink") or pageType is not defined %}
                            {% if is_granted("#{entityName}_create") or not app.user.permissionExists("#{entityName}_create") %}
                                <a href="{{ url("#{entityName}_ajouter") }}" class="btn btn-outline-dark">{{ "#{entityName}.new"|trans }}</a>
                            {% endif %}
                        {% endif %}
                    {% endif %}
                {% endblock %}
            </div>
        </div>
    </header>
{% endblock %}