{# @var ea \EasyCorp\Bundle\EasyAdminBundle\Context\AdminContext #}
{# @var field \EasyCorp\Bundle\EasyAdminBundle\Dto\FieldDto #}
{# @var entity \EasyCorp\Bundle\EasyAdminBundle\Dto\EntityDto #}
{% set hot = entity.instance.participantSurveyHotCompletedCount %}
{% set cold = entity.instance.participantSurveyColdCompletedCount %}
{% set registered = entity.instance.registeredParticipantCount %}
{% set isStarted = entity.instance.sessionStartDate <= date() %}

<span class="badge badge-hot fs-6" title="Nombre de répondants à l'enquête à chaud">
    {{ isStarted ? hot : '-' }}
</span>
<span class="badge badge-cold fs-6" title="Nombre de répondants à l'enquête à froid">
    {{ isStarted ? cold : '-' }}
</span>
