{% types {
    url: 'string',
} %}
{% extends 'base.html.twig' %}

{% block importmap %}{{ importmap(['app', 'admin']) }}{% endblock %}

{% block title %}QR Code d'accès à l'enquête de satisfaction{% endblock %}

{% block body %}
    <div class="container d-flex flex-column justify-content-center align-items-center mb-5 gap-5">
        <img class="img-fluid" src="{{ asset('images/fiducial-enquete.png') }}" alt="Fiducial Enquête" width="200px"/>
        <div class="survey-title flex-grow-1 fs-3">{{ survey.label }}</div>
        <div>
            <img id="qr-code"
                 style="--bs-border-color: #4C4B4B; --bs-border-width: 3px; --bs-border-radius: 0.625rem;"
                 class="img-fluid border rounded p-2 mb-3" src="{{ qr_code_path(url) }}"
                 alt="QR Code pour accéder à l'enquête de satisfaction"/>
            <div class="fw-bold text-center fs-6">Télécharger le QR Code
                <a class="btn btn-sync ms-2" download="qr-code-{{ survey.identifier }}.png" href="{{ qr_code_path(url, 'download') }}">
                    <i class="fa fa-download"></i>
                </a>
            </div>
        </div>
        <div>
            <div class="fw-bold mb-3 text-center fs-4">Participants
                <a class="btn btn-sync ms-2" href="javascript: location.reload();">
                    <i class="fa fa-sync"></i>
                </a>
            </div>

            {% if survey.participants|length == 0 %}
                <div class="fst-italic">Aucun participant pour le moment</div>
            {% else %}
                <div class="d-flex flex-wrap gap-2 qrcode-participants align-items-center justify-content-center fw-medium">
                    {% for participant in survey.participants|sort((a, b) => a.lastName <=> b.lastName) %}
                        <div class="d-flex align-items-center justify-content-center gap-1 rounded-pill px-2 py-1 text-white" style="background: {{ participant.isCompletedHot ? '#04A943' : 'var(--orange)' }}">
                            {% if participant.isCompletedHot %}
                                <i class="fa fa-check"></i>
                            {% else %}
                                <i class="fa fa-spin fa-spinner"></i>
                            {% endif %}
                            {{ participant.lastName }} {{ participant.firstName }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}
