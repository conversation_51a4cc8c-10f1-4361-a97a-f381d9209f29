{% extends 'adhesion/base.html.twig' %}

{% block sub_body %}
<div class="card">
    <div class="card-body">
        {{ block('step') }}

        {{ form_start(form) }}

        {{ form_row(form.finess) }}
        {{ form_row(form.businessCode, { label_attr: { class: 'form-check-label'}}) }}
        {{ form_row(form.siret) }}
        {{ form_row(form.companyName) }}

        {{ form_row(form.joinDate) }}
        {{ form_row(form.status) }}

        <hr>
        <div class="mb-3">
            <div class="fw-bold">
                Coordonnées
            </div>
            <div class="form-text mb-3">Ces informations seront utilisés dans les communications</div>
        </div>

        {{ form_row(form.pharmacyName) }}
        {{ form_row(form.pharmacyEmail) }}
        {{ form_row(form.pharmacyWebsite) }}
        {{ form_row(form.address) }}
        {{ form_row(form.address2) }}
        <div class="row">
            <div class="col-sm-6">
                {{ form_row(form.zipCode) }}
            </div>
            <div class="col-sm-6">
                {{ form_row(form.city) }}
            </div>
        </div>

        {{ form_row(form.country) }}

        <div class="row">
            <div class="col-sm-6">
                {{ form_row(form.phone) }}
            </div>
            <div class="col-sm-6">
                {{ form_row(form.fax) }}
            </div>
        </div>

        <hr>
        <div class="fw-bold mb-3">
            Responsable de la pharmacie
        </div>

        {{ form_row(form.managerCivility) }}

        <div class="row">
            <div class="col-sm-6">
                {{ form_row(form.managerFirstname) }}
            </div>
            <div class="col-sm-6">
                {{ form_row(form.managerLastname) }}
            </div>
        </div>

        {{ form_row(form.managerPhone) }}
        {{ form_row(form.managerEmail) }}

        <hr>
        <div class="fw-bold mb-3">
            Responsable de la comptabilité
        </div>

        {{ form_row(form.accountingCivility) }}

        <div class="row">
            <div class="col-sm-6">
                {{ form_row(form.accountingFirstname) }}
            </div>
            <div class="col-sm-6">
                {{ form_row(form.accountingLastname) }}
            </div>
        </div>

        {{ form_row(form.accountingPhone) }}
        {{ form_row(form.accountingEmail) }}

        <hr>
        <div class="fw-bold mb-3">
            Votre prestataire informatique
        </div>

        {{ form_row(form.ItProvider) }}

        {{ form_rest(form) }}

        <div class="mt-4 mb-3">
            {{ enseigne.textUnderLGO|emailLink|nl2br }}
        </div>

        <div class="text-center mb-4">
            <a href="{{ url('app_adhesion_step_' ~ (step - 1), { identifier: enseigne.identifier }) }}" class="btn btn-primary">Retour</a>
            <input type="submit" class="btn btn-secondary" value="Poursuivre"/>
        </div>

        {{ form_end(form) }}

        <div class="form-text fst-italic">* Champs obligatoires</div>
    </div>
</div>
{% endblock %}

{% block scripts %}
    <div {{ stimulus_controller('adhesion-infos') }}/>
{% endblock %}