{% set sortParams = {'column': column, 'order': order} %}
{% set icon1 = 'fa-sort' %}
{% set icon2 = 'fa-sort' %}
{% set icon3 = 'fa-sort' %}
{% set icon4 = 'fa-sort' %}
{% if column == '' %}
    {% set sortParams = sortParams|merge({'column': 'title'}) %}
{% endif %}
{% if column == 'title' %}
    {% set icon1 = 'fa-sort' %}
    {% if order == 'asc' %}
        {% set icon1 = 'fa-sort-up' %}
    {% elseif order == 'desc' %}
        {% set icon1 = 'fa-sort-down' %}
    {% endif %}
{% endif %}
{% if column == 'language' %}
    {% set icon2 = 'fa-sort' %}
    {% if order == 'asc' %}
        {% set icon2 = 'fa-sort-up' %}
    {% elseif order == 'desc' %}
        {% set icon2 = 'fa-sort-down' %}
    {% endif %}
{% endif %}
{% if column == 'start_date' %}
    {% set icon3 = 'fa-sort' %}
    {% if order == 'asc' %}
        {% set icon3 = 'fa-sort-up' %}
    {% elseif order == 'desc' %}
        {% set icon3 = 'fa-sort-down' %}
    {% endif %}
{% endif %}
{% if column == 'end_date' %}
    {% set icon4 = 'fa-sort' %}
    {% if order == 'asc' %}
        {% set icon4 = 'fa-sort-up' %}
    {% elseif order == 'desc' %}
        {% set icon4 = 'fa-sort-down' %}
    {% endif %}
{% endif %}
{% if order == '' or order == 'asc' %}
    {% set sortParams = sortParams|merge({'order': 'desc'}) %}
{% elseif order == 'desc' %}
    {% set sortParams = sortParams|merge({'order': 'asc'}) %}
{% endif %}
{{ block('loaderPage', 'blocks.html.twig') }}
<table class="table rq-table">
    <thead>
        <tr>
            <th>
                {% set sortParams = sortParams|merge({'column': 'title'}) %}
                <a class="results-order" href="#!" data-column="title" data-order="">
                    <span>{{ "notification.list.title" | trans }}</span> <i class="fas {{ icon1 }} fa-white fa-small mlxs"></i>
                </a>
            </th>
            <th>
                {% set sortParams = sortParams|merge({'column': 'language'}) %}
                <a class="results-order" href="#!" data-column="language" data-order="">
                    <span>{{ "notification.list.language" | trans }}</span> <i class="fas {{ icon2 }} fa-white fa-small mlxs"></i>
                </a>
            </th>
            <th>
                {% set sortParams = sortParams|merge({'column': 'start_date'}) %}
                <a class="results-order" href="#!" data-column="start_date" data-order="">
                    <span>{{ "notification.list.startDate" | trans }}</span> <i class="fas {{ icon3 }} fa-white fa-small mlxs"></i>
                </a>
            </th>
            <th>
                {% set sortParams = sortParams|merge({'column': 'end_date'}) %}
                <a class="results-order" href="#!" data-column="end_date" data-order="">
                    <span>{{ "notification.list.endDate" | trans }}</span> <i class="fas {{ icon4 }} fa-white fa-small mlxs"></i>
                </a>
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
    {% for notification in notifications %}
        <tr>
            <td>{{ notification.title }}</td>
            <td>{{ notification.language | upper }}</td>
            <td>{{ notification.startDate ? notification.startDate|date('d/m/Y H:i') : '' }}</td>
            <td>{{ notification.endDate ? notification.endDate|date('d/m/Y H:i') : '' }}</td>
            <td>
                <form name="notification_delete" method="post">
                <a class="btn btn-edit phn" href="{{ path('aquitem_requeteur_notification_edit', {'id': notification.id}) }}">
                    <i class="fal fa-pen mhs mtxs"></i>
                </a>
                <button class="delete-notif btn btn-edit phn" name="{{ field_name(deleteForm.delete) }}" type="submit" data-id="{{ notification.id }}">
                    <i class="fal fa-trash mhs mtxs"></i>
                </button>
                <input type="hidden" name="_token_{{ notification.id }}" value="{{ csrf_token('delete' ~ notification.id) }}">
                </form>
            </td>
        </tr>
    {% else %}
        <tr>
            <td colspan="10">{{ "notification.list.noRecords" | trans }}</td>
        </tr>
    {% endfor %}
    </tbody>
</table>
<template id="delete-notification-tpl">
    <p></p>
</template>