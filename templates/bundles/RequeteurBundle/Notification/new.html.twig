{% extends 'base.html.twig' %}

{% block title %}{{ "notification.titles.liste" | trans({}, "messages", user_locale_langue) }}{% endblock %}
{% block mtitle %}{{ "notification.titles.liste" | trans({}, "messages", "fr") }}{% endblock %}

{% block body %}
<div class="page-header">
    <div class="page-header-title">
        <div class="pageWidth">
            <div class="row collapse">
                <div class="column medium-6">
                    <h1>{{ "notification.titles.new" | trans }}</h1>
                </div>
                <div class="column medium-6 text-right">
                    <div class="mrxl">
                        <a class="btn btn--red mbl" href="{{ path('aquitem_requeteur_notification_new') }}">{{ "notification.titles.new" | trans }}</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="pageWidth page-container">

    {{ include('@Requeteur/Notification/_form.html.twig') }}

    <a href="{{ path('aquitem_requeteur_notification_index') }}">back to list</a>

</div>
{% endblock %}
