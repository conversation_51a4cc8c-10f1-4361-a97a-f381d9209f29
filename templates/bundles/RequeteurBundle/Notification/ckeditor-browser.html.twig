<!DOCTYPE html>
<html lang="{{ app.request.locale|split('_')[0] }}">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=1024, initial-scale=1">

    <title>{% block title %}{{ "global.pageTitle" | trans }}{% endblock %}</title>
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}" />

    {% block stylesheet_files %}
        {{ encore_entry_link_tags('css') }}
    {% endblock %}

    {% block stylesheets %}
         <link href="https://icones.mydataviz.fr/css/all.min.css" type="text/css" rel="stylesheet" />
    {% endblock %}

</head>
<body>
    <ul class="tabs" data-tabs id="browser-tabs">
        <li class="tabs-title is-active"><a href="#browser-panel1" aria-selected="true">{{ "operations.titles.mesImages" | trans }}</a></li>
        <li class="tabs-title"><a href="#browser-panel2">{{ "operations.titles.downloadImage" | trans }}</a></li>
    </ul>
    <div class="tabs-content" data-tabs-content="browser-tabs">
        <div class="tabs-panel is-active" id="browser-panel1">
            <div class="row js-images-container-ckeditor" id="js-images-container">{{ "operations.titles.loadImages" | trans }}</div>
        </div>
        <div class="tabs-panel" id="browser-panel2">
            <form name="notification-image-upload">
                <label for="fileupload">{{ "operations.titles.selectImage" | trans }} ({{ (maxFileSize // 1024) }} Ko max) :</label>
                <input data-max-file-size="{{ maxFileSize }}" id="fileupload" type="file" name="files[]" multiple>
            </form>
            <br>
            <!-- The global progress bar -->
            <div class="success progress hide" role="progressbar" tabindex="0" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                <span class="progress-meter">
                    <p class="progress-meter-text"></p>
                  </span>
            </div>
            <!-- The container for the uploaded files -->
            <div id="uploadFile" class="files">
                <div id="js-images-container">
                    <div class="image-item"><a href="#" class="js-image-link" data-url=""><img src=""></a></div>
                </div>
            </div>
            <div class="modal-errors error mbl hide"></div>
        </div>
    </div>
    <template id="js-template-image">
        <div class="image-item">
            <a href="#" class="thumbnail js-image-link mbn" data-url="{thumbUrl}">
                <img src="{thumbUrl}" width="200">
            </a>
        </div>
    </template>
    {% block javascripts %}
        {{ encore_entry_script_tags('notification-images') }}
        <script src="{{ url('bazinga_jstranslation_js') }}" type="text/javascript"></script>
    {% endblock %}
</body>
</html>
