{% extends 'base.html.twig' %}
{% block title %}{{ "portail.titles.creerVente" | trans }}{% endblock %}
{% block body %}
<div id="portail-enseigne">
	<div class="page-header">
		<div class="page-header-title">
			<div class="pageWidth">
				<h1>{{ "portail.titles.creerVente" | trans }}</h1>
				{% if app.user.checkDroitsAjoutBonus %}
				<a href="{{ url('aquitem_requeteur_portail_enseigne_ajout_bonus') }}" class="show-for-medium btn btn--green right mrxl">
					<span>{{ "global.edit" | trans }} {% if app.session.get('type_bonus') | lower == "cagnotte" %}{% if app.session.get('current_user_profil')['langue'] is defined and app.session.get('current_user_profil')['langue'] == "en" %}{{ "portail.labels.amout" | trans }}{% else %}{{ app.session.get('type_bonus') | lower }}{% endif %}{% else %}{{ app.session.get('type_bonus') | lower }}{% endif %}</span>
				</a>
				{% endif %}
				{% if app.user.checkDroitsRetourProduit %}
				<a href="{{ url('aquitem_requeteur_portail_enseigne_retour_produit') }}" class="show-for-medium btn btn--green right mrxl">
					<span>{{ "menu.retourProduit" | trans }}</span>
				</a>
				{% endif %}
			</div>
		</div>
	</div>
	<div class="show-for-small hide-for-medium text-right mtm">
	{% if app.user.checkDroitsRetourProduit %}
		<a href="{{ url('aquitem_requeteur_portail_enseigne_retour_produit') }}" class="btn btn--green mrm">
			<span>{{ "menu.retourProduit" | trans }}</span>
		</a>
	{% endif %}
	{% if app.user.checkDroitsAjoutBonus %}
		<a href="{{ url('aquitem_requeteur_portail_enseigne_ajout_bonus') }}" class="btn btn--green mrm">
			<span>{{ "global.edit" | trans }} {% if app.session.get('type_bonus') | lower == "cagnotte" %}{% if app.session.get('current_user_profil')['langue'] is defined and app.session.get('current_user_profil')['langue'] == "en" %}{{ "portail.labels.amout" | trans }}{% else %}{{ app.session.get('type_bonus') | lower }}{% endif %}{% else %}{{ app.session.get('type_bonus') | lower }}{% endif %}</span>
		</a>
	{% endif %}
	</div>
	{% include '@Requeteur/PortailEnseigne/Includes/creation_vente_form.html.twig' %}
</div>
<template id="creation-vente-tpl"></template>
{% endblock %}
{% block javascripts %}
	{{ encore_entry_script_tags('portail-enseigne') }}
{% endblock %}