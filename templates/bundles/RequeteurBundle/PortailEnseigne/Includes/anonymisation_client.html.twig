{# {% if confirmAnonymiser %}
    <div class="desactivation-content">
        <div class="page-inner">
            <div class="mtl mll mrl">
    {% if erreurAnonymisation is defined %}
                <div class="bloc-exergue-pm ptl pbl">
                    <div class="sweet-alert not-in-modal">
                        <div class="sa-icon sa-error animateErrorIcon">
                            <span class="sa-x-mark animateXMark">
                                <span class="sa-line sa-left"></span>
                                <span class="sa-line sa-right"></span>
                            </span>
                        </div>
                    </div>
                </div>
                <p class="modal-errors error">{{ erreurAnonymisation }}</p>
    {% else %}
                <div class="bloc-exergue-pm ptl pbl mbl">
                    <div class="sweet-alert not-in-modal">
                        <div class="sa-icon sa-success animate" style="display: block;">
                            <span class="sa-line sa-tip animateSuccessTip"></span>
                            <span class="sa-line sa-long animateSuccessLong"></span>
                            <div class="sa-placeholder"></div>
                            <div class="sa-fix"></div>
                        </div>
                        <p class="text-center">{{ "portail.labels.successAnonymisation" | trans }}</p>
                    </div>
                </div>
    {% endif %}
            </div>
        </div>
    </div>
{% else %} #}
<div class="pageWidth page-container">
    <form id="desactivation-client-form" name="desactivation-client-form" method="post">
        <div class="desactivation-content">
            <div class="page-inner">
                <h2 class="mtn">{{ "portail.titles.anonymisation" | trans }}</h2>
                <div class="info-black mtl">{{ "portail.titles.textAnonymisation" | trans }}</div>
                <div class="mtxl mbxl">
                    <input type="checkbox" value="1" name="confirmAnonymiser" id="confirmAnonymiser">
                    <label for="confirmAnonymiser"><strong>{{ "portail.titles.confirmAnonymisation" | trans }}</strong></label>
                </div>
            </div>
            <input type="hidden" value="{{ idSession }}" name="idSession" id="idSession">
            <input type="hidden" value="{{ idClient }}" name="idClient" id="idClient">
            <div class="text-right mbl">
                <button id="btn-desactivation" type="submit" class="btn btn--disabled" disabled>
                    <span>{{ "global.anonymiser" | trans }}</span>
                </button>
            </div>
        </div>
    </form>
</div>
{# {% endif %} #}