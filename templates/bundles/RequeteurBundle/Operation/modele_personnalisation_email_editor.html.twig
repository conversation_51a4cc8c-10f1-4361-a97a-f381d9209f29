{% extends 'base.html.twig' %}
{% block stylesheets %}
	{{ parent()}}
	{{ encore_entry_link_tags('css-emojis') }}
{% endblock %}
{% block body %}
	{% block content %}
		{% if modelesValides[canal] is defined %}
			{% set idModele = modelesValides[canal]['idModele'] %}
		{% endif %}
		{% set idTagSenderID = modelesValides['EMAIL'].idTagSenderID %}
		{% set senderID = modelesValides['EMAIL'].senderID %}
		{% if idTagSenderID == "" and senderID != "" %}
			{% set idTagSenderID = "0" %}
		{% endif %}
		{% set usePack = false %}
		{% for currentCanal in modele.operation.valeur.valeurs[0].valeurs %}
			{% if currentCanal.libelleCanal == canal %}
				{% set usePack = currentCanal.packLibreService %}
			{% endif %}
		{% endfor %}

		{% set personalLibraryName = "" %}
		{% set commonLibraryName = "" %}
		{% if app.session.get('current_user_profil')['common_library_folder'] is defined %}
			{% if app.session.get('current_user_profil')['common_library_folder'] != "" %}
				{% set commonLibraryName = "operations.titles.maBibliotheque" | trans %}
				{% if app.session.get('current_user_profil')['common_library_name'] is defined %}
					{% if app.session.get('current_user_profil')['common_library_name'] != "" %}
						{% set commonLibraryName = app.session.get('current_user_profil')['common_library_name'] %}
					{% endif %}
				{% endif %}
			{% endif %}
		{% endif %}

		{% if app.session.get('current_user_profil')['personal_library'] is defined %}
			{% if app.session.get('current_user_profil')['personal_library'] == "true" %}
				{% set personalLibraryName = "operations.titles.mesImages" | trans %}
				{% if app.session.get('current_user_profil')['personal_library_name'] is defined %}
					{% if app.session.get('current_user_profil')['personal_library_name'] != "" %}
						{% set personalLibraryName = app.session.get('current_user_profil')['personal_library_name'] %}
					{% endif %}
				{% endif %}
			{% elseif app.session.get('current_user_profil')['personal_library'] == "false" %}
				{% if commonLibraryName != "" %}
					{% set personalLibraryName = commonLibraryName %}
				{% endif %}
			{% endif %}
		{% else %}
			{% set personalLibraryName = "operations.titles.mesImages" | trans %}
		{% endif %}

		<div id="operation-personnalisation-modele-sms" class="personnaliser-modele-email-editor"
			 data-idoperation="{{ idOperation }}"
			 data-idselection="{{ operation.idSelection }}"
			 data-selection-horsfid="{{ operation.selectionHorsFid }}"
			 data-prefixetag="{{ prefixeTag }}"
			 data-suffixetag="{{ suffixeTag }}"
			 data-prefixedesinscription="{{ prefixeDesinscription }}"
			 data-suffixedesinscription="{{ suffixeDesinscription }}"
			 data-prefixemiroir="{{ prefixeMiroir }}"
			 data-suffixemiroir="{{ suffixeMiroir }}"
			 data-email-emetteur="{{ modele.emailFrom.valeur }}"
			 data-domaines-emetteur = "{{ modele.domainesAutorises.valeurs | json_encode() }}"
			 data-nom-emetteur="{{ modele.emetteurFrom.valeur }}"
			 data-sujet="{{ modele.sujet.valeur }}"
			 data-header-html="{{ modele.headerHTML.valeur }}"
			 data-tags-sender-id="{{ idTagSenderID }}"
			 data-sender-id="{{ senderID }}"
			 data-alltags="{{ allTags | json_encode() }}"
			 data-selecttags="{{ defaultTags | json_encode() }}"
			 data-datetags="{{ dateTags | json_encode() }}"
			 data-linkthemes="{{ themesPourUrlsTrackables | json_encode() }}"
			 data-unsubscribelinks="{{ modele.lesPagesDeDesinscription.valeurs | json_encode() }}"
			 data-message-html="{{ modele.messageHTML.valeur }}"
			 data-donnees-json="{{ modele.donneesJson.valeur }}"
			 data-editeur="{{ modele.typeEditeur.valeur }}"
			 data-urlstracees="{{ urlsTracees | json_encode() }}"
			 data-operation-lib="{{ operation.libelle }}"
			 data-readonly="{{ hasEditZone }}"
			 data-imgorigin="{{ imgOrigin | json_encode() }}"
			 data-textorigin="{{ textOrigin | json_encode() }}"
			 data-pnr-library="{% if app.session.get('current_user_profil')['pnr_library_url'] is defined and app.session.get('current_user_profil')['pnr_library_key'] is defined %}true{% else %}false{% endif %}"
			 data-pnr-library-name="{% if app.session.get('current_user_profil')['pnr_library_name'] is defined %}{{ app.session.get('current_user_profil')['pnr_library_name'] }}{% else %}{{ "operations.titles.maBibliothequePnr" | trans }}{% endif %}"
			 data-mesoigner-library="{% if app.session.get('current_user_profil')['mesoigner_library_url'] is defined and app.session.get('current_user_profil')['mesoigner_library_key'] is defined %}true{% else %}false{% endif %}"
			 data-mesoigner-library-name="{% if app.session.get('current_user_profil')['mesoigner_library_name'] is defined %}{{ app.session.get('current_user_profil')['mesoigner_library_name'] }}{% else %}{{ "operations.titles.maBibliothequeMesoigner" | trans }}{% endif %}"
			 data-mesoigner-library-url="{% if app.session.get('current_user_profil')['mesoigner_library_url'] is defined %}{{ app.session.get('current_user_profil')['mesoigner_library_url'] }}{% endif %}"
			 data-personal-library="{% if app.session.get('current_user_profil')['personal_library'] is defined %}{{ app.session.get('current_user_profil')['personal_library'] }}{% else %}true{% endif %}"
			 data-personal-library-name="{{ personalLibraryName }}"
			 data-common-library="{% if app.session.get('current_user_profil')['common_library_folder'] is defined %}{{ app.session.get('current_user_profil')['common_library_folder'] }}{% endif %}"
			 data-common-library-name="{{ commonLibraryName }}"
			 data-mesoigner-save-images="{% if app.session.get('current_user_profil')['mesoigner_save_images'] is defined %}{{ app.session.get('current_user_profil')['mesoigner_save_images'] }}{% endif %}">
			<h2>{{ operation.libelle }}</h2>
			<div class="btn-loader mlm">
				<a href="#" class="btn-submit btn btn--blue btn--largePadding" type="button">{{ "global.save" | trans }}</a>
			{% if guideExist %}
				<div class="separate separate--grey"></div>
				<div class="guide-link-wrap">
					<a href="{{ guideName }}" target="_blank" class="guide-link" data-action="EMAIL">{{ "operations.actions.telechargerGuide" | trans }}</a>
				</div>
			{% endif %}
			</div>
			{% if hasEditZone %}
				<a href="{{ path('aquitem_requeteur_operation_mode_de_diffusion', {idOperation: idOperation, canal: 'email' }) }}" class="cross-cancel cross-cancel-top" data-close aria-label="Close modal"><svg viewBox="0 0 31.3 21" width="35" height="35" class="svg svg--red"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="/img/sprite-svg.svg#close-plain"></use></svg></a>
			{% else %}
				<a href="{{ path('aquitem_requeteur_operation_personnaliser_modele_email', {idOperation:idOperation, idModele: idModele}) }}" class="cross-cancel cross-cancel-top" data-close aria-label="Close modal"><svg viewBox="0 0 31.3 21" width="35" height="35" class="svg svg--red"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="/img/sprite-svg.svg#close-plain"></use></svg></a>
			{% endif %}
			<ul class="tabs modal-link-list-editor" data-tabs id="tabs-editor" data-deep-link="true" data-update-history="true" data-deep-link-smudge="true">
				{% if hasEditZone and modele.typeEditeur.valeur == 0 %}
					<li class="tabs-title is-active" role="presentation">
						<a href="#email-editor" role="tab" aria-controls="email-editor" aria-selected="true" id="email-editor-label">
							<i class="fas fa-edit" aria-hidden="true"></i>
							<span>{{ "operations.titles.edition" | trans }}</span>
						</a>
					</li>
				{% else %}
					<li class="tabs-title is-active" role="presentation">
						<a href="#email-editor" role="tab" aria-controls="email-editor" aria-selected="true" id="email-editor-label">
							<i class="fas fa-edit" aria-hidden="true"></i>
							<span>{{ "operations.modeles.perso.menu.tabs.edition" | trans }}</span>
						</a>
					</li>
                    {% if app.user.checkDroitsEmailPersoAvancee %}
						{% if modele.typeEditeur.valeur == 1 or (not isBlank and modele.typeEditeur.valeur == 0 and modele.donneesJson.valeur | length) %}
					<li class="tabs-title" role="presentation">
						<a href="#img-assign" role="tab" aria-controls="email-editor" aria-selected="false" id="img-assign-label">
							<i class="fas fa-image" aria-hidden="true"></i>
							<span>{{ "operations.modeles.perso.menu.tabs.img" | trans }}</span>
						</a>
					</li>
						{% endif %}
					{% endif %}
					<li class="tabs-title" role="presentation">
						<a href="#parameters-link" role="tab" aria-controls="parameters-link" aria-selected="false" id="parameters-link-label">
							<i class="fas fa-cogs" aria-hidden="true"></i>
							{% if modele.typeEditeur.valeur == '1' %}
								<span>{{ "operations.modeles.perso.menu.emetteurMos" | trans }}</span>
							{% else %}
								<span>{{ "operations.modeles.perso.menu.emetteur" | trans }}</span>
							{% endif %}
							<i class="fas fa-exclamation-circle hide" aria-hidden="true">
								<div class="hover-description">{{ "operations.modeles.perso.requiredObject" | trans }}</div>
							</i>
						</a>
					</li>
                    {% if app.user.checkDroitsEmailPersoAvancee %}
					<li class="tabs-title" role="presentation">
						<a href="#valeurs-defaut" role="tab" aria-controls="valeurs-defaut" aria-selected="false" id="valeurs-defaut-label">
							<i class="fas fa-tags" aria-hidden="true"></i>
							<span>{{ "operations.modeles.perso.menu.defaut" | trans }}</span>
						</a>
					</li>
					<li class="tabs-title" role="presentation">
						<a href="#utm-link" role="tab" aria-controls="utm-link" aria-selected="false" id="utm-link-label">
							<i class="fas fa-code" aria-hidden="true"></i>
							<span>{{ "operations.modeles.perso.menu.tabs.utm" | trans }}</span>
						</a>
					</li>
					<li class="tabs-title" role="presentation">
						<a href="#links-assign" role="tab" aria-controls="links-assign" aria-selected="false" id="links-assign-label">
							<i class="fas fa-link" aria-hidden="true"></i>
							<span>{{ "links.titles.assign" | trans }}</span>
						</a>
					</li>
					{% endif %}
				{% endif %}
				<li class="tabs-title" role="presentation">
					<a href="#email-previsu-test" role="tab" aria-controls="email-previsu-test" aria-selected="false" id="email-previsu-test-label">
						<i class="fas fa-eye" aria-hidden="true"></i>
						<span>{{ "operations.modeles.perso.menu.previsu" | trans }}</span>
					</a>
				</li>
			</ul>

			{# Contenu des onglets #}
			<div class="tabs-content" data-tabs-content="tabs-editor">

				{# Editeur #}
				<div class="tabs-panel {% if modele.typeEditeur.valeur == 0 %}modal-body-editor{% else %}modal-body-mosaico{% endif %} is-active" id="email-editor" role="tabpanel" aria-hidden="false" aria-labelledby="email-editor-label">
                    {% if hasEditZone %}
					<div class="row mbl">
						<div class="column small-12 medium-10">
							<div class="row collapse">
								<div class="column small-2 text-right pbm"><strong>{{ "operations.titles.emetteur" | trans }} :</strong></div>
								<div class="column small-10 pbm"><span class="mls">{{ modele.emetteurFrom.valeur }} ({{ modele.emailFrom.valeur }})</span></div>
								<div class="column small-2 text-right pbm pts"><strong>{{ "operations.modeles.perso.sujet" | trans }}</strong></div>
								<div class="column small-10 pbm">
									<span class="mls">
										<input type="text" name="sujetId" id="sujetId" value="{{ modele.sujet.valeur }}">
									</span>
									<button type="button" class="btn btn--red mls" id="trigger"><i class="far fa-smile" aria-hidden="true"></i></button>
									<div class="info-emojis mls">
										<span>{{ "operations.modeles.perso.infoEmojis" | trans }}</span>
									</div>
								</div>
							</div>
						</div>
					</div>
					{% endif %}
					{# CKEditor #}
					{% if modele.typeEditeur.valeur == 0 %}
						{% if hasEditZone %}
						<div class="row sticky-container">
							<div class="column small-12 medium-7 email-zones sticky-row">
                                {{ form_start(form) }}
                                {{ form_widget(form) }}
                                {{ form_end(form) }}
							</div>
							<div class="column small-6 edit-zones">
								{{ form_start(zonesForm) }}
								{{ form_widget(zonesForm) }}
								{{ form_end(zonesForm) }}
							</div>
						{% else %}
						<div class="row">
                            {% if app.user.checkDroitsEmailPersoAvancee %}
							<div class="column small-12 text-center mbl">
								<button type="button" id="ckeditor-toggle-editor" class="btn btn-edit modePrevisu"
										data-mode="0"><span>{{ "modeles.form.editorModeHTML"|trans }}</span>
									<span class="hover-description">{{ "operations.infos.modeHtml" | trans }}</span>
								</button>
								<button type="button" id="ckeditor-toggle-textarea" class="btn btn-edit modePrevisu"
										data-mode="1"><span>{{ "modeles.form.editorMode"|trans }}</span>
									<span class="hover-description">{{ "operations.infos.modeEditeur" | trans }}</span>
								</button>
							</div>
                        	{% endif %}
							<div class="column">
								{{ form_start(form) }}
								{{ form_widget(form) }}
								{{ form_end(form) }}

								<div class="accordion hide mbl">
									<button id="btn-import-file" class="btn btn--red">
										<span>{{ "operations.titles.importFile"|trans }}</span>
									</button>
								</div>
							</div>
                        {% endif %}
						</div>
					{% else %}
						{# Mosaico #}
						<iframe id="iframe-mosaico" width="100%" height="560"
								src="{{ url("aquitem_requeteur_operation_personnaliser_modele_email_mosaico", {idOperation: idOperation, idModele: idModele}) }}#uydbd"
								frameborder="0">
						</iframe>
					{% endif %}
				</div>

				{# Mosaiso:image #}
				<div class="tabs-panel modal-body-perso" id="img-assign" role="tabpanel" aria-hidden="true" aria-labelledby="img-assign-label">
					<div class="row noElements hide">
						<div class="modal-errors mbs">
							<div class="error">{{ "operations.empty.img"|trans }}</div>
						</div>
					</div>
					<div id="baseImgGroup" class="hide">
						<div class="img-group">
							<div class="column large-12">
								<input type="hidden" value="" data-key=""/>
								<div class="column large-1">
									<img class="img-assign-preview" src="" alt="">
								</div>
								<div class="column large-11">
									<div class="img-assign-group">
										<div class="column large-6 medium-12">
											<div class="linksName input-group">
												<label class="">{{ "img.form.source" | trans }} :</label>
												<input class="img-assign-url img-assign-url-source" type="text" value="" data-key=""/>
											</div>
										</div>
										<div class="column large-6 medium-12 add-tags">
											<label for="tagsEmail">{{ "operations.modeles.perso.listeTags" | trans }}</label>
											<select id="tagsEmail">
												{% for tag in allTags %}
													<option value="{{ tag.id }}">{{ tag.libelle }}</option>
												{% endfor %}
											</select>
											<button type="button" id="ajoutTag" class="btn btn--red">{{ "global.add" | trans }}</button>
										</div>
									</div>
									<div class="img-assign-group">
										<div class="column large-6 medium-12">
											<div class="linksName input-group">
												<label class="">{{ "img.form.link" | trans }} :</label>
												<input class="img-assign-url img-assign-url-link" type="text" value="" data-key=""/>
											</div>
										</div>
										<div class="column large-6 medium-12 add-tags">
											<label for="tagsEmail">{{ "operations.modeles.perso.listeTags" | trans }}</label>
											<select id="tagsEmail">
												{% for tag in allTags %}
													<option value="{{ tag.id }}">{{ tag.libelle }}</option>
												{% endfor %}
											</select>
											<button type="button" id="ajoutTag" class="btn btn--red">{{ "global.add" | trans }}</button>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div id="imgGroup"></div>
				</div>

				{# Paramètre des liens #}
				<div class="tabs-panel modal-body-perso" id="parameters-link" role="tabpanel" aria-hidden="true" aria-labelledby="parameters-link-label">
					<form id="parameters-form">
						<div id="perso-parameters">
							<div class="row">
								<p class="column required-legende">{{ "operations.requiredFields" | trans | raw }}</p>
							</div>
						{% if app.user.checkDroitsEmailExpediteur %}
							<div class="row">
								<div class="column medium-7">
									<div class="input-group">
										<label for="emailEmetteur">{{ "global.asterisque" | trans | raw }} {{ "operations.modeles.perso.emailEmetteur" | trans }}</label>
										<input type="text" id="emailEmetteur" required="required" value="{{ modele.emailFrom.valeur }}" />
									</div>
									<div class="input-group">
										<label for="nomEmetteur">{{ "global.asterisque" | trans | raw }} {{ "operations.modeles.perso.nomEmetteur" | trans }}</label>
										<input type="text" id="nomEmetteur" required="required" value="{{ modele.emetteurFrom.valeur }}" />
									</div>
								</div>
							</div>
							<hr/>
						{% else %}
							<input type="hidden" id="emailEmetteur" value="{{ modele.emailFrom.valeur }}" />
							<input type="hidden" id="nomEmetteur" value="{{ modele.emetteurFrom.valeur }}" />
						{% endif %}
							<div class="row add-tags">
								<div class="column large-7 medium-12">
									<div class="input-group">
										<label for="sujetId">{{ "global.asterisque" | trans | raw }} {{ "operations.modeles.perso.sujet" | trans }}</label>
										<input type="text" id="sujetId" required="required" value="{{ modele.sujet.valeur }}" />
										<button type="button" class="btn btn--red mls" id="trigger"><i class="far fa-smile" aria-hidden="true"></i></button>
										<div class="info-emojis">
											<span>{{ "operations.modeles.perso.infoEmojis" | trans }}</span>
										</div>
									</div>
								</div>
								<div class="column large-5 medium-12">
									<div class="row add-tags-group">
										<div class="column medium-7 text-right">
											<label for="tagsEmailSujet">{{ "operations.modeles.perso.listeTags" | trans }}</label>
											<select id="tagsEmailSujet">
												{% for tag in allTags %}
													<option value="{{ tag.id }}">{{ tag.libelle }}</option>
												{% endfor %}
											</select>
										</div>
										<div class="column medium-5">
											<button type="button" data-action="ajoutTag" data-type-tag="tagsEmailSujet" data-field="sujetId" data-num="0"
												class="btn btn--red man">{{ "global.add" | trans }}</button>
										</div>
									</div>
								</div>
							</div>
							{% if modele.typeEditeur.valeur == 0 %}
								<div class="row add-tags mtm">
									<div class="column large-7 medium-12">
										<div class="input-group">
											<label for="preHeaderId">
												{{ "operations.modeles.perso.preHeader" | trans }}
												<a class="svg-info-bulle" href="#">
													<svg class="svg svg--white" viewBox="0 0 31.3 21" width="20" height="20" alt="">
														<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#info') }}"></use>
													</svg>
													<span class="hover-description">{{ "operations.infos.preHeaderIB" | trans }}</span>
												</a> :
											</label>
											<textarea id="preHeaderId"></textarea>
										</div>
									</div>
									<div class="column large-5 medium-12">
										<div class="row add-tags-group">
											<div class="column medium-7 text-right">
												<label for="tagsEmailPreHeader">{{ "operations.modeles.perso.listeTags" | trans }}</label>
												<select id="tagsEmailPreHeader">
													{% for tag in allTags %}
														<option value="{{ tag.id }}">{{ tag.libelle }}</option>
													{% endfor %}
												</select>
											</div>
											<div class="column medium-5">
												<button type="button" data-action="ajoutTag" data-type-tag="tagsEmailPreHeader"
													data-field="preHeaderId" data-num="1" class="btn btn--red man">{{ "global.add" | trans }}</button>
											</div>
										</div>
									</div>
								</div>
							{% endif %}
						</div>
					</form>
				</div>

				{# Valeur par défaut des tags #}
				<div class="tabs-panel modal-body-perso" id="valeurs-defaut" role="tabpanel" aria-hidden="true" aria-labelledby="valeurs-defaut-label">
					<div class="row noElements hide">
						<div class="modal-errors mbs">
							<div class="error">{{ "operations.empty.tag"|trans }}</div>
						</div>
					</div>
					<div id="baseTagGroup" class="hide">
						<h3 class="hide"></h3>
						<div class="input-group mll">
							<span class="tagLibelle"></span>
							<br/>
							<div class="input-radio tagNonvide">
								<input type="radio" name="tag" class="nonvide" value=""/>
								<label for="nonvide"></label>
							</div>
							<div class="input-radio tagVide">
								<input type="radio" name="tag" class="vide" value=""/>
								<label for="vide">{{ "global.vide"|trans }}</label>
							</div>
							<div class="input-radio tagPerso">
								<input type="radio" name="tag" class="perso" value="perso"/>
								<label for="perso">{{ "global.personnaliser"|trans }}</label>
							</div>
							<div class="input-radio tagPersoValeur">
								<input type="text" name="persoValeur" class="persoValeur" value=""/>
							</div>
						</div>
					</div>
					<div class="row valeursDefautError hide">
						<div class="modal-errors mbs">
							<div class="error"></div>
						</div>
					</div>
					<form id="valeurs-defaut-form">
						<div class="row">
							<div class="tagGroup" data-group="tagsEmailSujet"></div>
							<div class="tagGroup" data-group="tagsEmailPreHeader"></div>
							<div class="tagGroup" data-group="tagsEmailCorps"></div>
							<div id="valeurs-defaut-form-error" class=""></div>
						</div>
					</form>
				</div>

				{# Définir les UTM #}
				<div class="tabs-panel modal-body-perso" id="utm-link" role="tabpanel" aria-hidden="true" aria-labelledby="utm-link-label">
					<div class="row noElements hide">
						<div class="modal-errors mbs">
							<div class="error">{{ "operations.empty.link"|trans }}</div>
						</div>
					</div>
					<form id="utm-form">
						<div class="row utmError hide">
							<div class="modal-errors mbs">
								<div class="error">{{ "utm.form.noLinkChecked" | trans }}</div>
							</div>
						</div>
						<div class="row">
							<div class="utm-bloc">
								<h3 id="tagLibelle">{{ "utm.form.applyUtmOnChecked" | trans }}</h3>
								<div id="baseUtmGroup">
									<div class="utmLink mtl hide">
										<input type="checkbox" name="utmLink[]" id="utmLink" value="" data-key=""/>
										<label for="utmLink"></label>
									</div>
									<div class="utmItems row collapse">
										<div class="column large-2">
											<div class="input-group">
												<label for="utmSource">{{ "utm.form.source.label" | trans }}</label>
												<input type="text" name="utmSource" id="utmSource" value=""/>
											</div>
										</div>
										<div class="column large-2">
											<div class="input-group">
												<label for="utmSupport">{{ "utm.form.support.label" | trans }}</label>
												<input type="text" name="utmSupport" id="utmSupport" value=""/>
											</div>
										</div>
										<div class="column large-2">
											<div class="input-group">
												<label for="utmCampagne">{{ "utm.form.campagne.label" | trans }}</label>
												<input type="text" name="utmCampagne" id="utmCampagne" value=""/>
											</div>
										</div>
										<div class="column large-2">
											<div class="input-group">
												<label for="utmTerme">{{ "utm.form.terme.label" | trans }}</label>
												<input type="text" name="utmTerme" id="utmTerme" value=""/>
											</div>
										</div>
										<div class="column large-2">
											<div class="input-group">
												<label for="utmContenu">{{ "utm.form.contenu.label" | trans }}</label>
												<input type="text" name="utmContenu" id="utmContenu" value=""/>
											</div>
										</div>
										<div class="column large-2 appliquerUtm">
											<div class="input-group">
												<button type="button" id="appliquerUtm" class="btn btn--red">{{ "utm.bouton.appliquerUtm" | trans }}</button>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="utm-bloc row">
							<div class="mbl">
								<button type="button" id="selectAllUtm" class="btn btn-edit">{{ "utm.bouton.selectAllUtm" | trans }}</button>
								<button type="button" id="unselectAllUtm" class="btn btn-edit">{{ "utm.bouton.unselectAllUtm" | trans }}</button>
							</div>
							<div id="utmGroup"></div>
						</div>
					</form>
				</div>

				{# Tracer les liens #}
				<div class="tabs-panel modal-body-perso" id="links-assign" role="tabpanel" aria-hidden="true" aria-labelledby="links-assign-label">
					<div class="row noElements hide">
						<div class="modal-errors mbs">
							<div class="error">{{ "operations.empty.link"|trans }}</div>
						</div>
					</div>
					<form id="links-form">
						<div class="row">
							<h3 id="tagLibelle">{{ "links.form.checkLinks" | trans }}</h3>
						</div>
						<div id="nbLinks" class="row"><span class="nbCheckedLinks"></span> sur <span class="nbTotalLinks"></span> liens tracés</div>
						<div id="baseLinksGroup">
							<div class="row">
								<div class="column large-6">
									<div class="linksLink hide">
										<input type="checkbox" name="linksLink[]" id="linksLink" value="" data-key=""/>
										<label for="linksLink"></label>
									</div>
								</div>
								<div class="column large-6">
									<div class="linksName input-group mbs">
										<div class="column medium-4"><label class="" for="linksName">{{ "links.form.name" | trans }} :</label></div>
										<div class="column medium-8"><input type="text" name="linksName[]" id="linksName" value="" data-key=""/></div>
									</div>
									<div class="linksCateg input-group linksCategGroup">
										<div class="column medium-4"><label class="" for="linksCateg">{{ "links.form.categ" | trans }} :</label></div>
										<div class="column medium-8"><select name="linksCateg[]" id="linksCateg" data-key=""></select></div>
									</div>
								</div>
							</div>
						</div>
						<div id="linksGroup"></div>
						<div class="row collapse">
							<div class="column medium-8">
								<button type="button" id="selectAllLinks" class="btn btn-edit">{{ "utm.bouton.selectAllUtm" | trans }}</button>
								<button type="button" id="unselectAllLinks" class="btn btn-edit">{{ "utm.bouton.unselectAllUtm" | trans }}</button>
							</div>
							<div class="column medium-4">
								<input type="checkbox" name="showCateg" id="showCateg" value=""/>
								<label for="showCateg">{{ "links.form.showCateg" | trans }}</label>
							</div>
						</div>
					</form>
				</div>

				{# Prévisualisation et test #}
				<div class="tabs-panel modal-body-perso ptn" id="email-previsu-test" role="tabpanel" aria-hidden="true" aria-labelledby="email-previsu-test-label">
					<div class="sizePrevisu text-center mtl">
                        {% if modele.modeleV1.valeur == "false" or isBlank %}
						<button class="btn btn-edit mtm active" type="button" data-size="sizeMedium">{{ "operations.boutons.desktop" | trans }}</button>
						<button class="btn btn-edit mtm" type="button" data-size="sizeSmall">{{ "operations.boutons.mobile" | trans }}</button>
                        {% endif %}
					</div>
					<div class="text-center mbm">
						<b>Objet : </b><span class="previsu-objet">{% if modele.sujet.valeur == "" %}{{ 'global.vide2' | trans }}{% else %}{{ modele.sujet.valeur }}{% endif %}</span>
					</div>
					<div class="emailPrevisu text-center">
						<iframe id="messageEmailPrevisu" class="sizeMedium" height="100%" width="100%"></iframe>
					</div>
					{% if app.user.checkDroitsEmailSmsPrevisuAvancee %}
						<div id="container-envoi-test" class="text-right mbs">
							<button class="btn btn--green btn-envoi-test" type="button" data-size="sizeSmall">{{ 'operations.boutons.envoiTest' | trans }}</button>
						</div>
					{% endif %}
					<div id="container-tags">
						{% if app.user.checkDroitsEmailSmsPrevisuAvancee %}
							<div class="previsu-title">
								<div class="column large-6">
									<h3>{{ "operations.modeles.perso.title" | trans }}</h3>
								</div>
                                {% if operation.selectionExterne == 'false' or (operation.selectionExterne == 'true' and operation.selectionExterneFichierValide == 'true') %}
									<div class="column large-6 text-right"><a href="#" id="gerer-colonnes">{{ "operations.titles.gestionColonnes" | trans }}</a>
                                {% endif %}
								</div>
								{# Todo : mettre le tableau comme en js #}
							</div>
							<div id="collapsible-previsu">
								<div class="fake-scroll out">
									<div class="fake-content"></div>
								</div>
								<div class="default-values-list">
									<table id="table-tags" cellpadding="0" cellspacing="0" border="0">
										<thead>
                                        {% if operation.selectionExterne == 'true' and operation.selectionExterneFichierValide == 'false' %}
											<tr>
												<th style="padding-bottom: 0.5rem;">
													{{ "operations.modeles.perso.noSelectionExterne"|trans|raw }}<br>
												</th>
												<th></th>
											</tr>
                                        {% endif %}
										</thead>
										<tbody></tbody>
									</table>
								</div>
								{#<div id="pagination-infos" class="mlm hide">#}
									{#<span class="pagination-min"></span> à <span class="pagination-max"></span> sur <span class="pagination-total">{{ nbContacts }}</span> contacts#}
								{#</div>#}
								{#<div id="pagination-nav" class="hide row" data-total="{{ nbContacts }}" data-page="1" data-nb="20" data-positions="">#}
									{#<div id="less-values" class="column large-6 text-right" data-pos="0"><a class="btn btn--smallBlue"#}
										{#href="#">{{ "operations.modeles.perso.pagination.less" | trans }}</a></div>#}
									{#<div id="more-values" class="column large-6" data-pos="0"><a class="btn btn--smallBlue"#}
										{#href="#">{{ "operations.modeles.perso.pagination.more" | trans }}</a>#}
									{#</div>#}
								{#</div>#}
							</div>
						{% endif %}
						<div id="go-to-selections" class="hide">
							<div class="row collapse">
								<div class="column small-7">
									<p>{{ "operations.modeles.perso.goToSelections" | trans | raw }}</p>
								</div>
								<div class="column small-5 text-right">
									<a href="{{ url('aquitem_requeteur_operation_selection_creation', {idOperation: idOperation}) }}"
									   class="btn btn--blue btn--largePadding">{{ "operations.boutons.mesSelections" | trans }}</a>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="ptl text-right">
				<div class="btn-loader mls">
					<a href="#" class="btn-submit btn btn--blue btn--largePadding" type="button">{{ 'global.save' | trans }}</a>
				</div>
			</div>
		</div>

		<template id="pre-header-tpl">
			<div>
				<div class="pre-header-text"
					 style="display:none !important;mso-hide: all;font-size:1px;color:#333333;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>
			</div>
		</template>

		<template id="gestion-colonnes-form-tpl">
			<a href="#" id="select-all" class="btn btn-edit">{{ "global.select_all" | trans }}</a>
			<a href="#" id="unselect-all" class="btn btn-edit">{{ "global.unselect_all" | trans }}</a>
			<div id="gestion-colonnes">
				<form id="gestion-colonnes-form"></form>
			</div>
		</template>

		<template id="perso-save-form-tpl">
			<form id="perso-save-form">
				<div class="input-group mll">
					<div></div>
				</div>
			</form>
		</template>

		<template id="ckeditor-import">
			<div id="collapsible-import-file" class="mbl">
				<label for="fileupload">{{ "operations.titles.importLocal"|trans }}</label>
				<input id="fileupload" type="file" name="files[]">
				<br>
				<label for="fileupload">{{ "operations.titles.importExterne"|trans }}</label>
				<input id="import-html-url" type="url" placeholder="URL" required>
			</div>
		</template>

		<template id="envoi-test-form-tpl">
			<form id="contact-test-form">
				<div id="envoi-test" class="row">
                    {% if app.user.checkDroitsEmailContactsTest %}
					<div class="column small-12 mtm">
						<button class="btn-ajouter-contact btn" data-canal-id="{{ idCanal }}">{{ "contacts.titles.ajouterContact" | trans }}</button>
					</div>
					<div class="options-pieges-liste options-pieges-liste-{{ idCanal }} column large-7 left">
						<span class="label-like"><i>{{ "operations.titles.listeContactsPiege" | trans }}</i></span>
						<div id="contacts-{{ idCanal }}" class="bloc-exergue pbs"></div>
						<div class="select-all-contacts mts">
							<a href="#" class="select-all btn btn-edit">{{ "global.select_all" | trans }}</a>
							<a href="#" class="unselect-all btn btn-edit">{{ "global.unselect_all" | trans }}</a>
						</div>
					</div>
                    {% endif %}
					<div class="options-pieges-liste column large-5">
						<label for="autres-contacts-test">{{ "operations.modeles.perso.saisieContacts" | trans }}</label>
						<textarea name="autres-contacts-test" id="autres-contacts-test" rows="10"></textarea>
					</div>
					<div class="column small-12 mtl">
						<label for="objet-test">{{ 'operations.modeles.perso.sujet' | trans }}</label>
						<input type="text" name="objet-test" id="objet-test" value="">
					</div>
				</div>
			</form>
		</template>

		{% if soldePackCanal %}
			<template id="use-pack-envoi-test-form-tpl">
				<form id="use-pack-envoi-test-form">
					<div class="input-group">
						<div>
							<span class="confirm-use-pack">{{ "operations.actions.confirmEnvoiTest" | trans | raw }}</span>
						</div>
					</div>
				</form>
			</template>
		{% endif %}

        {% include '@Requeteur/Operation/Partials/Contacts/base_contact.html.twig' %}

	{% endblock %}
{% endblock %}

{% block javascripts %}
	{{ encore_entry_script_tags('operation-email') }}
{% endblock %}
