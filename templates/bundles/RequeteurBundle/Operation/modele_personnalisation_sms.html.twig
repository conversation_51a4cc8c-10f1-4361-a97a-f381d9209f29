{% extends '@Requeteur/Operation/mode_de_diffusion_base.html.twig' %}

{% block content %}
    {% set idTagSenderID = modelesValides['SMS'].idTagSenderID %}
    {% set libelleTagSenderID = modelesValides['SMS'].libelleTagSenderID %}
    {% set senderID = modelesValides['SMS'].senderID %}
    {% if idTagSenderID == "" and senderID != "" %}
        {% set idTagSenderID = "0" %}
    {% endif %}
    {% set noSender = modelesValides['SMS'].noSender %}
    {% set offreDematerialiseeActivee = operation.offreDematerialiseeActivee %}

    {% if senderID == "" and listeTagsSenderId[1] is defined %}
        {% if listeTagsSenderId[1]['valeur'] != "" %}
            {% set senderID = listeTagsSenderId[1].valeur %}
        {% endif %}
    {% endif %}

    {% if operation['modifiable'] == "true" %}
        {% if isBlank %}
            <div class="message row">{{ "operations.diffusion.modeleCree1" | trans }} {{ canal }} <a data-blank="true" class="link-annuler-modele" href="#" data-operation-id="{{ idOperation }}" data-canal-id="{{ idCanal }}" data-canal="{{ canal|lower }}">{{ "global.delete" | trans }}</a></div>
        {% else %}
            <div class="message row">{{ "operations.diffusion.modeleChoisi1" | trans }} {{ canal }} {{ "operations.diffusion.modeleChoisi2" | trans }}<a data-blank="false" class="link-annuler-modele" href="#" data-operation-id="{{ idOperation }}" data-canal-id="{{ idCanal }}" data-canal="{{ canal|lower }}">{{ "operations.boutons.changer" | trans }}</a></div>
        {% endif %}
    {% elseif operation['modifiable'] == "false" and (operation['idTypeOperation'] == '1') %}
        <div class="message row">{{ "operations.diffusion.modeleChoisi1" | trans }} {{ canal }} {{ "operations.diffusion.modeleChoisi2" | trans }}
            <a class="link-remplacer-modele" href="{{ url('aquitem_requeteur_operation_mode_de_diffusion', {idOperation: idOperation, canal: canal|lower, idModele: idModele}) }}"
               data-blank="false"
               data-operation-id="{{ idOperation }}"
               data-canal-id="{{ idCanal }}"
               data-canal="{{ canal|lower }}">{{ "operations.boutons.remplacer" | trans }}</a></div>
    {% endif %}

<div id="operation-personnalisation-modele-sms" class="personnaliser-modele-sms-editor detail-op mtxl" data-idoperation="{{ idOperation }}" data-idselection="{{ idSelection }}" data-prefixetag="{{ prefixeTag }}" data-suffixetag="{{ suffixeTag }}" data-message="{{ message }}" data-tags-sender-id="{{ idTagSenderID }}" data-lib-tags-sender-id="{{ libelleTagSenderID }}" data-sender-id="{{ senderID }}" data-user-pack-access="{{ droitsTarifsLibreService }}"  data-offreactivee="{{ offreDematerialiseeActivee }}" data-taglienoffre="{{tagLienOffreDemat}}">
    <h3 class="h3-black">{{ "operations.titles.persoSms" | trans }}</h3>
    {% if guideExist %}
    <div class="guide-perso mbm">
        <div class="separate separate--grey show-for-large"></div>
        <div class="guide-link-wrap">
            <a href="{{ guideName }}" target="_blank" class="guide-link" data-action="SMS">{{ "operations.actions.telechargerGuide" | trans }}</a>
        </div>
    </div>
    {% endif %}
    <form id="form-enregistrer-modele-sms">
    <div class="row">
        {% set hasDefaultSender = false %}
        {% if listeTagsSenderId[1] is defined %}
            {% if listeTagsSenderId[1]['valeur'] != "" %}
                {% set hasDefaultSender = true %}
            {% endif %}
        {% endif %}
        {% set uniqueExpediteur = false %}
        {% if (not app.user.checkDroitsSmsModifierExpediteur and not app.user.checkDroitsSmsAucunExpediteur and not hasDefaultSender and listeTagsSenderId[0]['valeurs'] | length)
            or (not app.user.checkDroitsSmsModifierExpediteur and not app.user.checkDroitsSmsAucunExpediteur and hasDefaultSender and not listeTagsSenderId[0]['valeurs'] | length)
            or (not app.user.checkDroitsSmsModifierExpediteur and app.user.checkDroitsSmsAucunExpediteur and not hasDefaultSender and not listeTagsSenderId[0]['valeurs'] | length)
            or (app.user.checkDroitsSmsModifierExpediteur and not app.user.checkDroitsSmsAucunExpediteur and not hasDefaultSender and not listeTagsSenderId[0]['valeurs'] | length)
        %}
            {% set uniqueExpediteur = true %}
        {% endif %}
        {% if app.user.checkDroitsSmsModifierExpediteur or app.user.checkDroitsSmsAucunExpediteur or hasDefaultSender or listeTagsSenderId[0]['valeurs'] | length %}
        <h4 class="h3-like mbs">{{ "operations.modeles.perso.emetteur" | trans }}</h4>
        {% endif %}
        <div id="perso-emetteur">
            <div class="input-group bloc-exergue-grey ptxs pbm">
                {% if hasDefaultSender %}
                    <div class="input-radio">
                        <input {% if senderID == listeTagsSenderId[1].valeur or (senderID == "" and idTagSenderID == "" and not noSender) or uniqueExpediteur %}checked{% endif %} data-default name="tag-sender" id="tag-sender-default" type="radio" value="{{ listeTagsSenderId[1].valeur }}" data-is-tag="false">
                        <label for="tag-sender-default">{{ listeTagsSenderId[1].valeur }}</label>
                    </div>
                {% endif %}
                {% for tagSenderId in listeTagsSenderId[0]['valeurs'] %}
                    <div class="input-radio">
                        <input {% if idTagSenderID == tagSenderId.id or (senderID == "" and idTagSenderID == "" and not noSender) or uniqueExpediteur %}checked{% endif %} name="tag-sender" id="tag-sender-{{ loop.index }}" type="radio" value="{{ tagSenderId.id }}" data-is-tag="true">
                        <label for="tag-sender-{{ loop.index }}">{{ tagSenderId.libelle }}</label>
                    </div>
                {% endfor %}
                {% if app.user.checkDroitsSmsAucunExpediteur %}
                <div class="input-radio">
                    <input {% if noSender or (listeTagsSenderId[1]['valeur'] is empty and listeTagsSenderId[0]['valeurs'] is empty) or uniqueExpediteur %}checked{% endif %} name="tag-sender" id="tag-sender-empty" type="radio" value="" data-is-tag="false">
                    <label for="tag-sender-empty">{{ "operations.titles.noEmetteur" | trans }}</label>
                </div>
                {% endif %}
                {% if app.user.checkDroitsSmsModifierExpediteur %}
                <div class="input-radio">
                    <input name="tag-sender" id="tag-sender-perso" type="radio" value="perso" data-is-tag="false"{% if (senderID != "" and listeTagsSenderId[1]['valeur'] != senderID) or uniqueExpediteur %} checked{% endif %}>
                    <label for="tag-sender-perso" class="{% if senderID != "" and listeTagsSenderId[1]['valeur'] != senderID %}sender-perso{% endif %}">
                        <span class="sender-perso-title">{{ "operations.titles.personnaliser" | trans }}</span>
                        <span class="sender-perso-input{% if (senderID == "" or listeTagsSenderId[1]['valeur'] == senderID) and not uniqueExpediteur %} hide{% endif %}">
                            :&nbsp;<input type="text" name="senderId" id="senderId" value="{% if senderID != "" and listeTagsSenderId[1]['valeur'] != senderID %}{{ senderID }}{% endif %}">
                            <a class="svg-info-bulle mts mls" href="#!">
                                <svg class="svg svg--white" viewBox="0 0 31.3 21" width="20" height="20" alt="">
                                    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#info') }}"></use>
                                </svg>
                                <span class="hover-description">{{ "operations.infos.senderPerso" | trans | raw }}</span>
                            </a>
                        </span>
                    </label>
                </div>
                {% endif %}
            </div>
        </div>
        <h4 class="h3-like mbs">{{ "operations.modeles.perso.contenu" | trans }}</h4>
        <div class="row collapse perso-sms" data-equalizer>
            <div class="column medium-6 perso-edition bloc-exergue-grey" data-equalizer-watch>
                <div class="perso-message-wrap mrxs pvm phl">
					<div class="row mts mbm">
						<div class="column large-6">
							{{ "operations.modeles.perso.nbTags" | trans }} <strong><span class="nbTags">0</span></strong>
						</div>
						<div class="column large-6 text-right">
							{{ "operations.modeles.perso.nbLiens" | trans }} <strong><span class="nbLiens">0</span></strong>
						</div>
					</div>
                    <div class="perso-message">
                        <textarea id="messageSMS" data-alltags="{{ allTags | json_encode() }}"
                            data-selecttags="{{ defaultTags | json_encode() }}"
                            data-stop-key="{{ modele.stopSMSKey.valeur }}">{{ modele.messageSMS.valeur }}</textarea>
                        <br /><div class="validSaisie hide"></div>
                    </div>
                    <div id="perso-infos">
                        <div class="row collapse">
                            <div class="column large-8 mbs-show-for-small">
						{% set smsLongs = app.user.smsLongs %}
                        {% if smsLongs %}
                            {{ "operations.modeles.perso.cout" | trans }} <strong><span class="compteNbSmsEnCours">1 {{ "operations.modeles.perso.sms" | trans }}</span> (<span class="compteNbCaracteresEnCours"></span> {{ "operations.modeles.perso.carateres" | trans }})</strong>
                            <a class="svg-info-bulle" href="#!">
                                <svg class="svg svg--white" viewBox="0 0 31.3 21" width="20" height="20" alt="">
                                    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#info') }}"></use>
                                </svg>
                                <span class="smsLong hover-description">{{ "operations.modeles.perso.infoSmsLong" | trans | raw }}</span>
                            </a>
                        {% else %}
                            {% set transInfoTags = "operations.infoTags" | trans %}
                            {% if app.session.get('routeurSmsNetMessage') == "false" %}
                                {% set transInfoTags = "operations.infoTagsB2sms" | trans %}
                            {% endif %}
                            <span class="info">{{ "operations.modeles.perso.nbCaracteres" | trans }} <strong><span class="compteNbCaracteresRestants"></span></strong></span>
                            <a class="svg-info-bulle mls" href="#!">
                                <svg class="svg svg--white" viewBox="0 0 31.3 21" width="20" height="20" alt="">
                                    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#info') }}"></use>
                                </svg>
                                <span class="smsCourt hover-description">{{ transInfoTags | raw }}</span>
                            </a>
                        {% endif %}
                            </div>
                            <div class="column large-4 large-text-right">
                                <button id="verifSaisie" class="btn btn-edit">{{ "operations.boutons.verifSaisie" | trans }}</button>
                            </div>
                        </div>
                        {% if app.user.smsLongs %}
                            <div class="compte-error lib-error little-info mts">
                                <span class="hide"><i>{{ "operations.compteSmsError" | trans }}</i></span>
                            </div>
                        {% else %}
                            <div class="compte-error lib-error little-info mts">
                                <span class="hide"><i>{{ "operations.compteCaracError" | trans }}</i></span>
                            </div>
                        {% endif %}
						{% if app.session.get('current_user_profil')['activer_suggestions_sms'] is defined and app.session.get('current_user_profil')['activer_suggestions_sms'] %}
						<div class="text-center mbl mtl">
							<button type="button" id="suggestion-sms" class="btn btn--red">{{ "operations.titles.manqueInspi" | trans }}</button>
						</div>
						{% endif %}
                    </div>
                </div>
            </div>
            <div class="column medium-6 perso-tags" data-equalizer-watch>
                <div class="relative">
                    <div class="ptm plm prm pbs">
                        <label for="tagsSMS"><strong>{{ "operations.modeles.perso.listeTags" | trans }}</strong></label>
                        <div class="row collapse mtxs">
                            <div class="column large-8 mbs-show-for-small">
                                <select id="tagsSMS" class="mbs">
                                    <optgroup label="Tags">
                                    {% for tag in tags %}
                                        <option value="{{ tag.libelle }}" data-id="{{ tag.id }}">{{ tag.libelle }} ({{ tag.tailleMaximale }} {{ 'operations.modeles.perso.carateres' | trans }})</option>
                                    {% endfor %}
                                    </optgroup>
                                    <optgroup label="Dates">
                                    {% for tag in modele.lesTagsDatePourSMS.valeurs %}
                                        <option value="{{ tag.libelle }}" data-id="{{ tag.id }}">{{ tag.libelle }} ({{ tag.tailleMaximale }} {{ 'operations.modeles.perso.carateres' | trans }})</option>
                                    {% endfor %}
                                    </optgroup>
                                </select>
                            </div>
                            <div class="column large-4 large-text-right mbs-show-for-small">
                                <button id="ajoutTag" class="btn-ajout-tag btn btn-edit mls-large-only">{{ "operations.boutons.ajoutTag" | trans }}</button>
                            </div>
                        </div>
                    </div>
                    <div class="perso-bloc ptm plm prm pbs">
                        <label for="urlCourte">
                            <strong>{{ "operations.modeles.perso.ajoutUrlCourte" | trans }}</strong>
                            <a class="svg-info-bulle mls" href="#!">
                                <svg class="svg svg--white" viewBox="0 0 31.3 21" width="20" height="20" alt="">
                                    <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#info') }}"></use>
                                </svg>
                                <span class="hover-description mtxl">{{ "operations.modeles.perso.lienAraccourcirInfo" | trans }}</span>
                            </a>
                        </label>
                        <div class="row collapse mtxs">
                            <div class="column large-8 mbs-show-for-small">
                                <input type="text" name="urlCourte" id="urlCourte"
                                    placeholder="{{ "operations.modeles.perso.lienAraccourcir" | trans }}" class="mbs">
                            </div>
                            <div class="column large-4 large-text-right mbs-show-for-small">
                                <button id="ajoutUrlCourte" class="btn-ajout-url-courte btn btn-edit mls-large-only">{{ "operations.modeles.perso.ajoutLien" | trans }}</button>
                            </div>
                        </div>
                    </div>
                    {% if app.user.checkDroitsUrlCourtePdf %}
                    <div id="perso-add-file" class="perso-bloc pam">
                        <label for="tagsSMS"><strong>{{ "operations.modeles.perso.addFile" | trans({'%maxPDFsize%': app.session.get('tailleMaxDocument')}) }}</strong></label>
                        <div class="fileupload-zone mtxs">
                            <span class="fileinput-button fileupload_rq ptm mbs">
                                <i class="fad fa-file-plus mbxs"></i>
                                <br>
                                <small class="bar-action">{{ "operations.boutons.addFiles2" | trans | raw }}</small>
                                {% set tailleMaxDocument = app.session.get('tailleMaxDocument') * 1000000 %}
                                <input id="fileupload_rq" type="file" name="files[]" accept=".pdf" data-max-size="{{ tailleMaxDocument }}">
                            </span>
                        </div>
                        <div class="fileupload-selected-zone text-right mtxs mbs hide">
                            <a href="#!" class="delete-file relative" title="{{ "operations.modeles.perso.deleteFile" | trans }}"><i class="fas fa-times" aria-hidden="true"></i></a>
                            <span class="fileupload-selected"><span></span></span>
                            <button id="ajoutFichier" class="btn btn-edit mtm">{{ "operations.modeles.perso.ajoutLien" | trans }}</button>
                        </div>
                    </div>
                    {% endif %}
                {% if modeleSMSRich %}
                    <div id="sms-rich" class="perso-bloc phm ptm mts pbm">
                        <span class="label-like"><strong>{{ "operations.smsrich.mainTitle" | trans }}</strong></span>
                        <div class="row collapse mtxs">
                            <div class="column large-5 mbs">
                                <input type="checkbox" id="landingPage" name="landingPage" {% if landingPageIsActived %}checked="checked"{% if tarifsModele | length == 1 %} data-richonly="true" disabled{% endif %}{% endif %} />
                                <label for="landingPage">{{ "operations.smsrich.btnActivateMinisiteSMS" | trans }}</label>
                            </div>
                            <div class="column large-7 large-text-right">
                                <a href="{{ url('aquitem_requeteur_operation_personnaliser_landing_page', {idOperation: idOperation, idModele: idModele}) }}"
                                id="paramMinisiteToSMS" class="btn btn-edit btn--largePadding btn-landing-page"{% if not landingPageIsActived %} style="display: none"{% endif %}>{{ "operations.smsrich.btnUpdateMinisiteSMS" | trans }}</a>
                            </div>
                        </div>
                        <div class="landing-alert info mts" {% if idMiniSiteOK %}style="display:none;"{% endif %}>
                            <svg class="svg svg--white svg-info-bulle left" viewBox="0 0 31.3 21" width="22" height="22" alt="">
                                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#info') }}"></use>
                            </svg>
                            <span class="landing-alert-record right" style="display:none;">{{ "operations.smsrich.infoParametrage" | trans }}</span>
                            {% if not idMiniSiteOK %}
                            <span class="info landing-alert-parametrage">{{ "operations.smsrich.infoParametrage2" | trans }}</span>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
                </div>
            </div>
        </div>
        {% if app.user.checkDroitsSmsPersoAvancee %}
        <div class="tagGroup-container mts">
            <h4 class="h3-like mtl mbs">{{ "operations.modeles.perso.menu.defaut" | trans }}</h4>
            <div class="tagGroup-group bloc-exergue-grey phl ptm">
                <div class="tagGroup pbxs" data-group="tagsSmsMessage"></div>
            </div>
        </div>
        {% endif %}
        <div class="row mtxl">
                <div class="column medium-6">
                    {% if not isBlank %}
                    <a href="{{ url('aquitem_requeteur_operation_mode_de_diffusion', {idOperation: idOperation, canal: 'sms' }) }}" class="btn btn--grey mbs-show-for-small">
                        <span>{{ "operations.boutons.quitterPerso" | trans }}</span>
                    </a>
                    {% endif %}
                </div>
            <div class="column medium-6 medium-text-right">
                <button class="btn btn--green mbs-show-for-small" id="sms-previsu-test">
                    {{ "operations.modeles.perso.menu.previsu" | trans }}
                </button>
                <div class="action-enregistrer-sms mls right">
                    <button class="btn-enregistrer-modele btn">
                        <span>{{ "global.save" | trans }}</span>
                    </button>
                    <div class="infobulle-content">{{ "operations.modeles.perso.bulleSave" | trans }}</div>
                </div>
            </div>
        </div>
    </div>
    </form>
</div>
	{% if app.user.checkDroitsSmsPersoAvancee %}
    <div id="baseTagGroup" class="hide">
        <div class="input-group">
            <span class="tagLibelle" data-id-tag=""></span>
            <br />
            <div class="input-radio tagNonvide">
                <input type="radio" name="tag" class="nonvide" value="" />
                <label for="nonvide"></label>
            </div>
            <div class="input-radio tagVide">
                <input type="radio" name="tag" class="vide" value="" />
                <label for="vide">{{ "global.vide" | trans }}</label>
            </div>
            <div class="input-radio tagPerso">
                <input type="radio" name="tag" class="perso" value="perso" />
                <label for="perso">{{ "global.personnaliser" | trans }}</label>
            </div>
            <div class="input-radio tagPersoValeur">
                <input type="text" name="persoValeur" class="persoValeur" value="" />
            </div>
        </div>
    </div>
	{% endif %}
	{% include '@Requeteur/Operation/Partials/Contacts/base_contact.html.twig' %}

	{# TEMPLATES MODALS #}
	{% include '@Requeteur/Operation/ModalsTemplates/PersoSms/previsu_test.html.twig' %}
	{% include '@Requeteur/Operation/ModalsTemplates/PersoSms/gestion_colonnes.html.twig' %}
	{% include '@Requeteur/Operation/ModalsTemplates/PersoSms/verif_saisie.html.twig' %}
	{% include '@Requeteur/Operation/ModalsTemplates/PersoSms/save_personnalisation.html.twig' %}
	{% include '@Requeteur/Operation/ModalsTemplates/PersoSms/envoi_test.html.twig' %}
	{% include '@Requeteur/Operation/ModalsTemplates/PersoSms/use_pack_envoi_test.html.twig' %}
	{% include '@Requeteur/Operation/ModalsTemplates/PersoSms/suggestion_sms.html.twig' %}
{% endblock %}


{% block javascripts %}
    {{ encore_entry_script_tags('operation-sms') }}
{% endblock %}
