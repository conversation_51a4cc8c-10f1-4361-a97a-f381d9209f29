{% set selectionOK = false %}
{% set planificationOK = operation.planificationsValides %}
{% set offreOK = operation.offreDematerialiseeModifeeLe != "" %}
{# {% set planificationOK = operation.offresValides %} #}
{% set tarifsOK = operation.tarifsValides %}
{% set budgetOK = operation.budgetsValides %}
{% set optionsOK = operation.optionsAvanceesOK %}
{% set annulable = false %}
{#{% set budgetTotal = 0 %}#}
{% if operation.idTypeOperation == "1" and operation.valeurs[0].valeurs[0].budgetTotal is defined %}
    {% set budgetTotal = operation.valeurs[0].valeurs[0].budgetTotal %}
{% else %}
    {% set budgetTotal = operation.budgetOperation %}
{% endif %}
{% set diffusionParams = {idOperation: idOperation} %}
{% set selectionUrl = url('aquitem_requeteur_operation_selection_creation', {idOperation: idOperation}) %}
{% if app.user and app.user.requeteurAcceptantLesImportsHorsFid and app.user.requeteurContenantDesEnseignesHorsFid %}
    {% set selectionUrl = url('aquitem_requeteur_operation_selection_creation_hors_fid', {idOperation: idOperation}) %}
{% endif %}
{% if operation.idSelection != "" %}
    {% if operation.selectionACompleter == "true" %}
        {% set selectionOK = false %}
    {% else %}
        {% set selectionOK = true %}
    {% endif %}
    {% set selectionUrl = url('aquitem_requeteur_operation_selection_read_only', {idOperation: idOperation, id: operation.idSelection}) %}
{% endif %}
{% set canaux = operation.valeurs[0].valeurs %}
{% set diffusionOK = (canaux | length) > 0 %}
{% set canalSms = 0 %}
{% set canalLink = '' %}
{% if canaux | length %}
    {% for key,canal in canaux %}
        {% set diffusionOK = diffusionOK and canal.idModele != "" and canal.donneesModeleOK %}

        {% set method = 'checkDroitsCanal' ~ canal.libelleCanal | capitalize %}
        {% if attribute(app.user, method) %}
            {% set canalLink = canal.libelleCanal|lower %}
        {% endif %}
        {% if canal.libelleCanal|lower == 'sms' and attribute(app.user, method) %}
            {% set canalSms = 1 %}
        {% endif %}
        {% if canal.annulable == "true" %}
            {% set annulable = true %}
        {% endif %}
    {% endfor %}
    {% if canalSms  %}
        {% set canalLink = 'sms' %}
    {% endif %}
    {% set diffusionParams = {idOperation: idOperation, canal: canalLink} %}
{% else %}
    {% if app.user.checkDroitsCanalAudio %}
        {% set diffusionParams = {idOperation: idOperation, canal: 'audio'} %}
    {% endif %}
    {% if app.user.checkDroitsCanalFtp %}
        {% set diffusionParams = {idOperation: idOperation, canal: 'ftp'} %}
    {% endif %}
    {% if app.user.checkDroitsCanalCourrier %}
        {% set diffusionParams = {idOperation: idOperation, canal: 'courrier'} %}
    {% endif %}
    {% if app.user.checkDroitsCanalEmail %}
        {% set diffusionParams = {idOperation: idOperation, canal: 'email'} %}
    {% endif %}
    {% if app.user.checkDroitsCanalSms %}
        {% set diffusionParams = {idOperation: idOperation, canal: 'sms'} %}
    {% endif %}
    {% if libCanalHorFid != "" %}
        {% set diffusionParams = {idOperation: idOperation, canal: libCanalHorFid | lower} %}
    {% endif %}
{% endif %}

<div class="page-header">
    <div class="page-header-title">
        <div class="pageWidth">
            <div class="row">
                <div class="column small-12 medium-12 large-4">
            <h1>
                {% if (operation.retourEnCreationPossible == "false" and not annulable) and operation.statut != "operations.statuts.creation.lib" | trans %}
                    <svg viewBox="0 0 31.3 21" width="28" height="28" class="svg svg--darkgrey svg--cadenas" aria-label="cadenas"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#cadenas') }}"></use></svg>
                {% endif %}
                {% set words = operation.libelle | split(' ') %}
                {% if words | length > 1 %}
                    {% set libelleOP = '' %}
                    {% for word in words %}
                        {% if word | length > 18 %}
                            {% set libelleTruncated = true %}
                        {% endif %}
                        {% set libelleOP = libelleOP ~ ' ' ~ (word|length > 18 ? word|slice(0, 18) ~ '...' : word) %}
                    {% endfor %}
                {% else %}
                    {% if operation.libelle | length > 18 %}
                        {% set libelleTruncated = true %}
                    {% endif %}
                    {% set libelleOP = (operation.libelle|length > 18 ? operation.libelle|slice(0, 18) ~ '...' : operation.libelle) %}
                {% endif %}
                <span class="op-title">{{ libelleOP }}</span>
            </h1>
            </div>
        {% if budgetTotal == "0.0" and budgetOK == "false" %}
            {% set cout = "<span>" ~ "operations.budget.nonDispo" | trans ~ "</span>"%}
        {% else %}
            {% set cout = budgetTotal | number_format(2, ',', ' ') ~ ' €' %}
        {% endif %}
        {% if route != "aquitem_requeteur_operation_budget" or operation.idTypeOperation == 1 %}
            <div class="page-operation-subtitle column small-12 medium-6 large-4">
            {% if operation.idTypeOperation == '1' %}
                {{ "operations.budget.coutMax" | trans }} <span>{{ cout | raw }}</span>
            {% else %}
                {{ "operations.budget.cout" | trans }} <span>{{ cout | raw }}</span>
            {% endif %}
            {% if operation.idTypeOperation == "0" %}
                {% if canaux | length %}
                    {% for key,canal in canaux %}
                        {% set totalPack = 0 %}
                        {% if canal.packLibreService == "true" and budgetOK == "true" %}
                            {% set totalPack = canal.tailleSelectionnee + canal.nbTestsAFacturerSiPack + canal.contactsPiege | length %}
                            <br />PACK {{ canal.libelleCanal }} : <span>{{ totalPack | number_format(0, ',', ' ') }}</span> envois
                        {% endif %}
                    {% endfor %}
                {% endif %}
            {% endif %}
            </div>
        {% endif %}
            <div class="page-operation-buttons column small-12 medium-6 large-4">
        {% set permettreLancement = false %}
        {% if operation.idStatutOperation == "0" and budgetOK == "true" and tarifsOK == "true" and planificationOK == "true" and selectionOK and diffusionOK and optionsOK == "true" %}
            {% set permettreLancement = true %}
        {% endif %}
        {% if permettreLancement %}
            <button id="btn-lancer-operation" class="btn {% if operation.offreDematerialiseeObligatoire == "true" and operation.offreDematerialiseeActivee == "false" %} btn--grey {% else %} btn--red {% endif %} right mlm" data-user="{{ app.user.organisation }}" data-selection-visuelle="{{ operation.selectionExterne }}" data-has-auto="{{ hasTarifAutonome }}" data-offrerequired="{{operation.offreDematerialiseeObligatoire}}" data-offreactivee="{{operation.offreDematerialiseeActivee}}" data-operation-type="{{ operation.idTypeOperation }}" data-operation-budget="{{ operation.budgetOperation }}">
                <span>{{ "operations.boutons.lancerOP" | trans }}</span>
            </button>
        {% elseif operation.idStatutOperation == "0" %}
            <a id="btn-info-lancer-operation" class="btn btn--grey right mlm mrm" data-offrerequired="{{operation.offreDematerialiseeObligatoire}}" data-offreactivee="{{operation.offreDematerialiseeActivee}}">
                <span>{{ "operations.boutons.lancerOP" | trans }}</span>
            </a>
        {% endif %}
        {% if operation.idTypeOperation == '2' and infosSerie is defined %}
            {% if infosSerie.envoiDemandeAnnulationPossible %}
            <button id="btn-arreter-serie" class="btn btn--red right mlm">
                <span>{{ "operations.boutons.arreterSerie" | trans }}</span>
            </button>
            {% else %}
                {% if infosSerie.dateDemandeAnnulationSerie is defined %}
                    {% if infosSerie.dateDemandeAnnulationSerie != "" %}
            <button class="btn right mlm btn--disabled">
                <span>{{ "operations.boutons.arreterSerieDemandee" | trans }}</span>
            </button>
                    {% endif %}
                {% endif %}
            {% endif %}
        {% endif %}
        {% if operation.statut != "operations.statuts.creation.lib" | trans %}
            <button class="btn-gerer-operation btn btn--white right" data-operation-id="{{ operation.id }}" data-operation-libelle="{{ operation.libelle }}" data-description-operation="" data-modifiable-operation="{% if operation.retourEnCreationPossible == "true" %}true{% else %}false{% endif %}">
                <span>{{ "operations.boutons.gererCommande" | trans }}</span>
            </button>
        {% else %}
            <button class="btn-infos-operation btn btn--white right" data-operation-id="{{ operation.id }}" data-modifiable-operation="{{ operation.modifiable }}" data-operation-type="{{ operation.idTypeOperation }}">
                <span>{{ "operations.boutons.infos" | trans }}</span>
            </button>
        {% endif %}
            </div>
        </div>
        </div>
    </div>
    <div class="page-header-nav">
        <div class="operation-header pbs pts pageWidth text-center">
            <ul class="{% if operation.modifiable == "false" %}desactivate-onglet{% endif %}" id="process-tabs">
                <li class="selection" data-process-name="selection" data-id-selection="{{ operation.idSelection }}"{% if operation.idTypeOperation == '1' and infosOpNat | length %} data-id-selection-compl="{{ infosOpNat.idSelectionComplementaire }}"{% endif %}>
                    <a href="{{ selectionUrl }}">
                        <svg viewBox="0 0 31.3 21" width="35" height="35" class="svg{% if selectionOK %} svg--color{% else %} svg--cleargrey{% endif %}">
                            {% if selectionOK %}
                                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#check') }}"></use>
                            {% else %}
                                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#uncheck') }}"></use>
                            {% endif %}
                        </svg>
                        <br />{{ "operations.processTabs.selection" | trans | raw }}
                    </a>
                </li>
                {% if app.user.checkDroitsOffreDemat %}
                    <li class="offre" data-process-name="offre">
                        <a href="{{ url('aquitem_requeteur_operation_offre', {idOperation: idOperation}) }}">
                            <svg viewBox="0 0 31.3 21" width="35" height="35" class="svg{% if offreOK == "true" or operation.idStatutOperation != "0" %} svg--color{% else %} svg--cleargrey{% endif %}">
                            {% if offreOK == "true" or operation.idStatutOperation != "0" %}
                                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#check') }}"></use>
                            {% else %}
                                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#uncheck') }}"></use>
                            {% endif %}
                            </svg>
                            <br />{{ "operations.processTabs.offreDemat" | trans | raw }}
                        </a>
                    </li>
                {% endif %}
                {% if app.user.checkDroitsCanalCourrier or app.user.checkDroitsCanalEmail or app.user.checkDroitsCanalSms or app.user.checkDroitsCanalFtp or app.user.checkDroitsCanalAudio %}
                <li class="diffusion" data-process-name="diffusion">
                    <a href="{{ url('aquitem_requeteur_operation_mode_de_diffusion', diffusionParams) }}">
                        <svg viewBox="0 0 31.3 21" width="35" height="35" class="svg{% if diffusionOK %} svg--color{% else %} svg--cleargrey{% endif %}">
                        {% if diffusionOK %}
                            <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#check') }}"></use>
                        {% else %}
                            <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#uncheck') }}"></use>
                        {% endif %}
                        </svg>
                        <br />{{ "operations.processTabs.canaux" | trans | raw }}
                    </a>
                </li>
                {% endif %}
                <li class="budget" data-process-name="budget">
                    <a href="{{ url('aquitem_requeteur_operation_budget', {idOperation: idOperation}) }}">
                        <svg viewBox="0 0 31.3 21" width="35" height="35" class="svg{% if budgetOK == "true" or operation.idStatutOperation != "0" %} svg--color{% else %} svg--cleargrey{% endif %}">
                            {% if budgetOK == "true" or operation.idStatutOperation != "0" %}
                                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#check') }}"></use>
                            {% else %}
                                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#uncheck') }}"></use>
                            {% endif %}
                        </svg>
                        <br />{{ "operations.budget.title" | trans }}
                    </a>
                </li>
                <li class="planning" data-process-name="planning">
                    <a href="{{ url('aquitem_requeteur_operation_planning', {idOperation: idOperation}) }}">
                        <svg viewBox="0 0 31.3 21" width="35" height="35" class="svg{% if planificationOK == "true" or operation.idStatutOperation != "0" %} svg--color{% else %} svg--cleargrey{% endif %}">
                        {% if planificationOK == "true" or operation.idStatutOperation != "0" %}
                            <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#check') }}"></use>
                        {% else %}
                            <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#uncheck') }}"></use>
                        {% endif %}
                        </svg>
                        <br />{{ "operations.titles.planning" | trans }}
                    </a>
                </li>
                <li class="options" data-process-name="options">
                    <a href="{{ url('aquitem_requeteur_operation_options', {idOperation: idOperation}) }}">
                        <svg viewBox="0 0 31.3 21" width="35" height="35" class="svg{% if optionsOK == "true" or operation.idStatutOperation != "0" %} svg--color{% else %} svg--cleargrey{% endif %}">
                            {% if optionsOK == "true" or operation.idStatutOperation != "0" %}
                                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#check') }}"></use>
                            {% else %}
                                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#uncheck') }}"></use>
                            {% endif %}
                        </svg>
                        <br />{{ "operations.titles.options" | trans }}
                    </a>
                </li>
            </ul>

        </div>
    </div>
</div>

<template id="operation-arreter-form-tpl">
    <form id="operation-arreter-form">
        <div id="operation-arreter">
            {{ "operations.actions.confirmArretSerie" | trans }} "<span class="libOperation">{{ operation.libelle }}</span>" ?
            <br>{{ "operations.actions.confirmArretSerieSuite" | trans }}
        </div>
    </form>
</template>
{% set canalIds = [] %}
{% for key,canal in operation.valeurs[0].valeurs %}
    {% set canalIds = canalIds|merge({(loop.index0) : canal.idCanal}) %}
{% endfor %}
<template id="operation-annuler-form-tpl">
    <form id="operation-annuler-form">
        <div class="input-group">
            <div id="operation-annuler">
                {{ "operations.actions.confirmAnnulerOperation" | trans }} "<span class="libOperation">{{ operation.libelle }}</span>" ?
                <br />{{ "operations.actions.confirmAnnulerOperation2" | trans }}
                <br />{{ "operations.actions.confirmAnnulerOperation3" | trans }}
            </div>
        </div>
        <input type="hidden" name="annuler-canal-ids" id="annuler-canal-ids" value="{{ canalIds|json_encode() }}">
    </form>
</template>

<template id="info-lancer-form-tpl">
    <p></p>
</template>
<template id="lancer-form-tpl">
    <form id="lancer-form">
        <div class="input-group">
            <div>
                {% set startLibsCanaux = "" %}
                {% set libsCanaux = "" %}
                {% set liensCanaux = "" %}
                {% set canalAuto = false %}
                {% if canaux | length %}
                    {% set liensCanaux = "" %}
                    {% for key,canal in canaux %}
                        {% if canal.libelleCanal | upper != "FTP" and (canal.idTarif != "" or canal.idTarifRemplacement != "") %}
                            {% for tarif in canal.tarifsDisponibles %}
                                {% set currentTarif = canal.idTarif %}
                                {% if canal.idTarifRemplacement != ""  %}
                                    {% set currentTarif = canal.idTarifRemplacement %}
                                {% endif %}
                                {% if tarif.id == currentTarif %}
                                    {% if tarif.necessiteUneValidationOperateur == "false" %}
                                        {% set canalAuto = true %}
                                        {% set startLibsCanaux = "le canal " %}
                                        {% if libsCanaux != "" %}
                                            {% set startLibsCanaux = "les canaux " %}
                                            {% set libsCanaux = libsCanaux ~ '" et "' %}
                                        {% endif %}
                                        {% set libsCanaux = libsCanaux ~ canal.libelleCanal %}
                                        {% if canal.libelleCanal | lower == 'email' %}
                                            {% set liensCanaux = liensCanaux ~ '<a class="btn btn--blue mrl" href="' ~ url('aquitem_requeteur_operation_personnaliser_modele_email', {idOperation: idOperation, idModele: canal.idModele}) ~ '#modal-email-previsu-test">' ~ canal.libelleCanal ~ '</a>' %}
                                        {% elseif canal.libelleCanal | lower == 'sms' %}
                                            {% set liensCanaux = liensCanaux ~ '<a class="btn btn--blue mrl" href="' ~ url('aquitem_requeteur_operation_personnaliser_modele_sms', {idOperation: idOperation, idModele: canal.idModele}) ~ '">' ~ canal.libelleCanal ~ '</a>' %}
                                        {% endif %}
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                        {% endif %}
                    {% endfor %}
                {% endif %}

                {% if canalAuto %}
                    {{ "operations.actions.confirmLancerOperation1" | trans | raw }} {{ startLibsCanaux }} "{{ libsCanaux }}".
                    <br /><br />
                    {{ "operations.actions.confirmLancerOperation2" | trans | raw }}
                    {{ liensCanaux | raw }}
                {% else %}
                    {% if app.user.checkDroitsOffreDemat and operation.offreDematerialiseeActivee == "true" and operation.offreDematerialiseeObligatoire == "false" %}
                    </div>
                    <div class="info info-no-stop info-icon mbm">
                        <span class="svg-info-link">
                            <svg class="svg" viewBox="0 0 31.3 21" width="20" height="20" alt="">
                                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#info') }}"></use>
                            </svg>
                        </span>
                        <span class="info-no-stop-text info-icon-text mls">{{ 'operations.actions.alertNoTagOffre' | trans | raw }}</span>
                    </div>
                    <div class="input-group mtl">
                    {% endif %}
                    <p>{{ "operations.actions.confirmLancerOperationSansPack" | trans | raw }}</p>
                {% endif %}
            </div>
        </div>
        {% if canalAuto %}
        <div class="input-group">
            <div class="right">
                <input type="checkbox" name="lancer-operation-confirm" id="lancer-operation-confirm">
                <label for="lancer-operation-confirm">{{ "operations.actions.confirmLancerOperationCheck" | trans }}</label>
            </div>
        </div>
        {% endif %}
        <input type="hidden" name="lancer-operation-id" id="lancer-operation-id">
    </form>
</template>

{# renseignaments des coordonnées si utilisateur pas "...(Aquitem) et pas "... (Siège)" #}
<template id="coordonnees-form-tpl">
    <form id="coordonnees-form">
        <div class="mbl">{{ "operations.infos.lancerCommande" | trans | raw }}</div>
        <div class="input-group">
            <div class="row collapse">
                <div class="small-3 columns">
                    <label for="coordonnees-nom" class="inline text-right prs">{{ "operations.titles.nom" | trans }} :</label>
                </div>
                <div class="small-9 columns">
                    <input class="input-text" type="text" id="coordonnees-nom" name="coordonnees-nom" value="">
                </div>
            </div>
        </div>
        {% if app.session.get('current_user_profil')['AUTH_EMAIL'] is not defined or (app.session.get('current_user_profil')['AUTH_EMAIL'] is defined and app.session.get('current_user_profil')['AUTH_EMAIL'] != "1") %}
        <div class="input-group">
            <div class="row collapse">
                <div class="small-3 columns">
                    <label for="coordonnees-email" class="inline text-right prs"><span class="required">*</span> {{ "operations.titles.email" | trans }} :</label>
                </div>
                <div class="small-9 columns">
                    <input class="input-text" type="text" id="coordonnees-email" name="coordonnees-email" value="" required>
                </div>
            </div>
        </div>
        {% else %}
        <input type="hidden" name="coordonnees-email" value="{{ app.user.email }}">
        {% endif %}
        <div class="input-group">
            <div class="row collapse">
                <div class="small-3 columns">
                    <label for="coordonnees-tel" class="inline text-right prs">{{ "operations.titles.tel" | trans }} :</label>
                </div>
                <div class="small-9 columns">
                    <input class="input-text" type="text" id="coordonnees-tel" name="coordonnees-tel" value="">
                </div>
            </div>
        </div>
        <input type="hidden" name="lancer-operation-id" id="lancer-operation-id" value="{{ idOperation }}">
        <div class="row noEmail hide">
            <div class="modal-errors mbs">
                <div class="error">{{ "operations.infos.emailRequired"|trans }}</div>
            </div>
        </div>
    </form>
</template>