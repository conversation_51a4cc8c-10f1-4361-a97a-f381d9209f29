{% extends '@Requeteur/Operation/operation_process_base.html.twig' %}

{% block content_base %}

    <div id="page-operation-content" class="pageWidth page-container{% if opModifiable is defined %}{% if opModifiable == "false" %} desactivate{% endif %}{% endif %}" data-process="diffusion">
        <div class="page-inner page-inner-short">
            <div class="pam">
        {% if modelesValides[canal] is defined %}
            {% set idModele = modelesValides[canal]['idModele'] %}
        {% endif %}
        {% if idModele is not defined %}
        <h2 class="out">{{ "operations.titles.modeDeDiffusion" | trans }}</h2>
        {% endif %}
        <div id="btn-canaux" class="mtm mbxxl text-center">
            {% block canaux %}{{ render(controller('App\\Controller\\RequeteurBundle\\OperationController::listeCanauxOperationAction', { operation: operation, canal: canal })) }}{% endblock %}
        </div>
        {% block content %}{% endblock %}
        </div>
    </div>

    <template id="operation-assos-infos-tpl">
        <div id="infos-operation">
            <div class="input-group">
                <div class="row">
                    <div class="small-4 columns">
                        <div class="inline text-right">{{ "operations.titles.nomOp" | trans }}</div>
                    </div>
                    <div class="small-8 columns">
                        <div class="input-text" id="infos-operation-assos-name"></div>
                    </div>
                </div>
            </div>
            <div class="input-group">
                <div class="row">
                    <div class="small-4 columns">
                        <div class="inline text-right">{{ "operations.titles.description" | trans }}</div>
                    </div>
                    <div class="small-8 columns">
                        <div class="input-text" id="infos-operation-assos-desc"></div>
                    </div>
                </div>
            </div>
            <div class="input-group hide">
                <div class="row">
                    <div class="small-4 columns">
                        <div class="inline text-right">{{ "operations.titles.dateDebut" | trans }}</div>
                    </div>
                    <div class="small-8 columns">
                        <div class="input-text" id="infos-operation-assos-debut"></div>
                    </div>
                </div>
            </div>
            <div class="input-group hide">
                <div class="row">
                    <div class="small-4 columns">
                        <div class="inline text-right">{{ "operations.titles.dateFin" | trans }}</div>
                    </div>
                    <div class="small-8 columns">
                        <div class="input-text" id="infos-operation-assos-fin"></div>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <template id="operation-assos-status-tpl">
        <div class="modal-body hide">
            <div id="operation-infos-selection">
                <div class="input-group">
                    <div class="row">
                        <div class="small-4 columns">
                            <div class="inline text-right"><span id="infos-operation-assos-selection-prefix"></span><strong><span class="input-text" id="infos-operation-assos-selection"></span></strong> :</div>
                        </div>
                        <div class="small-8 columns">
                            <div class="input-text" id="infos-operation-assos-comptage"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-body hide">
            <div id="operation-infos-budget" class="input-group">
                <div class="row">
                    <div class="small-4 columns">
                        <div class="inline text-right">{{ "operations.titles.budget" | trans }}</div>
                    </div>
                    <div class="small-8 columns">
                        <div class="input-text" id="infos-operation-assos-budget"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-body">
            <div id="operation-status">
                <h3 class="text-center">{{ "operations.titles.diffusions" | trans }}</h3>
                <div id="bat-links" class="text-center mbl hide"></div>
                <ul id="operation-assos-canaux" class="nav"></ul>
            </div>
        </div>
    </template>
{% if canal != "wallet" %}
    <template id="modele-annuler-form-tpl">
        <form id="modele-annuler-form">
            <div class="input-group">
                <div id="modele-annuler" class="hide">
                    {{ "operations.modeles.actions.confirmChanger" | trans }}<br />{{ "operations.modeles.actions.confirmChanger2" | trans }}
                </div>
                <div id="modele-annuler-blank" class="hide">
                    {{ ("operations.modeles.actions.confirmAnnulation" ~ canal) | trans }}<br />{{ "operations.modeles.actions.confirmChanger2" | trans }}
                </div>
            </div>
            <input type="hidden" name="modele-annuler-id" id="modele-annuler-id">
        </form>
    </template>

    <template id="modele-remplacer-form-tpl">
        <div class="confirm hide">
            <p>{{ "operations.actions.confirmRemplacer" | trans | raw }}</p>
        </div>
        <div class="confirm-confirm hide">
            <p>{{ "operations.actions.confirmConfirmRemplacer" | trans | raw }}</p>
        </div>
    </template>
{% endif %}
{% endblock %}

{% block javascripts %}
    {{ encore_entry_script_tags('operation-common') }}
{% endblock %}
