{% block content %}

{% set opModifiable = modifiable %}
{% if typeOperation != '0' %}
	{% if opModifiable == "false" and idStatutOperation != "5" %}
		{% set opModifiable = "true" %}
	{% endif %}
{% endif %}
	{% if modifiable == "true" or (modifiable == "false" and typeOperation == '1') %}
		<div class="message row">{{ "operations.diffusion.modeleChoisi1" | trans }} {{ canal }} {{ "operations.diffusion.modeleChoisi2" | trans }}
		{% if modifiable == "false" and typeOperation == '1' %}
			<a class="link-remplacer-modele" href="{{ url('aquitem_requeteur_operation_mode_de_diffusion', {idOperation: idOperation, canal: canal|lower, idModele: idModele}) }}"
			   data-blank="false"
			   data-operation-id="{{ idOperation }}"
			   data-canal-id="{{ idCanal }}"
			   data-canal="{{ canal|lower }}">{{ "operations.boutons.remplacer" | trans }}</a></div>
		{% else %}
			<a data-blank="false" class="link-annuler-modele" href="#" data-operation-id="{{ idOperation }}"
			   data-canal-id="{{ idCanal }}"
			   data-canal="{{ canal|lower }}">{{ "operations.boutons.changer" | trans }}</a></div>
		{% endif %}
	{% endif %}
	{% if opModifiable == "true" %}
		<div id="detail-operation" class="detail-op row">
            {% if canal == 'EMAIL' or canal == 'COURRIER' %}
			<div class="column small-12 medium-6">
				<h3>{{ "operations.titles.modele" | trans }}</h3>
				<div class="detail-op-box">
					{% if modele.libelleModele is defined %}
						{{ modele.libelleModele.valeur }}
					{% else %}
						???
					{% endif %}
				</div>
				{% if modele.lesOperationsAssociees.valeurs is defined %}
					{% if modele.lesOperationsAssociees.valeurs|length %}
						<h3>{{ "operations.titles.opAsso" | trans }}</h3>
						<div class="detail-op-box">
							<ul>
								{% for key, operation in modele.lesOperationsAssociees.valeurs %}
									{% if operation.id != idOperation %}
										<li class="btn-infos-assos-operation{% if key > 4 %} hide{% endif %}">
											<a href="{{ url('aquitem_requeteur_operation_planning', {idOperation: operation.id}) }}" class="btn-infos-assos-operation" data-operation-id="{{ operation.id }}">{{ operation.libelle }}</a>
										</li>
									{% endif %}
								{% endfor %}
							</ul>
							{% if modele.lesOperationsAssociees.valeurs|length > 5 %}
								<a class="removehideclass" href=""><u>{{ "operations.actions.afficherallop" | trans}}</u></a>
							{% endif %}
						</div>
					{% endif %}
				{% endif %}
			</div>
            {% endif %}
			<div class="column small-12 {% if canal == 'EMAIL' or canal == 'COURRIER' %}medium-6{% else %}medium-12{% endif %} text-center">
				{% if canal == 'EMAIL' or canal == 'COURRIER' %}
                    {% if opModifiable == "true" and not hasEditZone is same as(true)  %}
						<div>
							<a href="{{ url('aquitem_requeteur_operation_personnaliser_modele_' ~ canal|lower, {idOperation: idOperation, idModele: idModele}) }}"
							   class="btn btn--blue btn--largePadding">{{ "operations.boutons.personnaliser" | trans }}</a>
						</div>
                    {% else %}
						<div>
							<a href="{{ url('aquitem_requeteur_operation_personnaliser_modele_email_editor', {idOperation: idOperation}) }}"
						   		class="btn btn--blue btn--largePadding">{{ "operations.boutons.personnaliser" | trans }}</a>
					   </div>
                    {% endif %}
					<div class="textPerso">
						<div class="textPersoBulle{% if canal == 'COURRIER' %} courrier{% endif %}">
							<div class="textPersoBulle-fixHeight">
								<img src="{{ url('aquitem_requeteur_operation_get_modele_image', {idModele: idModele, canal: canal}) }}"
									 width="{% if canal == 'COURRIER' %}100%{% else %}350{% endif %}" alt=""/>
							</div>
						</div>
					</div>
				{% else %}
					<div class="textPerso">
						<div class="text-left mlm mbs">
							<i>{{ "operations.titles.emetteur" | trans }} :</i>
                            {% if senderSMS | length %}
                                {{ senderSMS }}
                            {% else %}
                                {{ "operations.titles.noEmetteur" | trans }}
                            {% endif %}
						</div>
						<div class="textPersoBulle">
							{{ message | raw }}
						</div>
					</div>
                    {% if opModifiable == "true" %}
						<div>
							<a href="{{ url('aquitem_requeteur_operation_personnaliser_modele_' ~ canal|lower, {idOperation: idOperation, idModele: idModele}) }}"
							   class="btn btn--blue btn--largePadding">{{ "operations.boutons.personnaliser" | trans }}</a>
						</div>
                    {% endif %}
				{% endif %}
			</div>
		</div>
	{% else %}
		<div id="detail-operation" class="detail-op row" title="data-idoperation="{{ idOperation }}"
		{% if modele.messageHTML.valeur is defined %}
			data-email-emetteur="{{ modele.emailFrom.valeur }}" data-nom-emetteur="{{ modele.emetteurFrom.valeur }}"
			data-sujet="{{ modele.sujet.valeur }}" data-header-html="{{ modele.headerHTML.valeur }}"
			data-message-html="{{ modele.messageHTML.valeur }}"
			data-donnees-json="{{ modele.donneesJson.valeur }}"
			data-editeur="{{ modele.typeEditeur.valeur }}"{% endif %}>
		<div class="column small-12 medium-12">
			{% if canal == 'EMAIL' %}
				<div class="text-center">
					<div class="textPerso textPerso-email">
						<div class="">
							<div class="emailPrevisu iframe-container text-center">
								<iframe id="messageEmailPrevisu"
										class="{% if modele.typeEditeur.valeur == '1' %}iframe-mosaico{% endif %} sizeMedium"
										height="100%" width="100%"></iframe>
							</div>
						</div>
					</div>
				</div>
			{% elseif canal == 'COURRIER' %}
				{% if modele.imageBODisponible.valeur == "true" %}
				<h3>{{ "operations.titles.batValide" | trans }}</h3>
				<div class="text-center">
					<div class="textPerso">
						<div class="textPersoBulle courrier">
							<div class="iframe-container">
								<img src="{{ url('aquitem_requeteur_operation_get_courrier_bat_image', {idOperation: idOperation, idModele: idModele}) }}" width="100%" alt=""/>
							</div>
						</div>
					</div>
				</div>
				{% else %}
                	{{ include('@Requeteur/Operation/Partials/modele_courrier_choisi.html.twig') }}
				{% endif %}
			{% elseif canal == constant('App\\Services\\WebserviceBundle\\Response\\OperationsResponseManager::CANAL_AUDIO') %}
				<div class="text-center">
					<div class="mtm mbxl">
						<div class="label-like label-inline">{{ "operations.modeles.perso.audio.titles.emetteur" | trans }}</div>
						<div class="label-like label-inline">{{ modele.numeroFixeEmetteur.valeur }}</div>
					</div>
					<figure class="mhn">
						<figcaption class="label-like label-like-block hide">{{ "operations.modeles.perso.audio.titles.urlAudio" | trans }}</figcaption>
						<audio controls src="{{ modele.urlAudio }}"></audio>
					</figure>
				</div>
			{% else %}
				{% if isBlankModele %}
					<div class="text-center mlm mbs">
						<i>{{ "operations.titles.emetteur" | trans }} :</i>
                        {% if senderSMS | length %}
                            {{ senderSMS }}
                        {% else %}
                            {{ "operations.titles.noEmetteur" | trans }}
                        {% endif %}
					</div>
					<div id="messageSMSPrevisu" class="messageSMSPrevisu-verrou text-left">

						{{ message | raw }}
					</div>
				{% else %}
					<div class="column small-12 medium-6">
						<h3>{{ "operations.titles.modele" | trans }}</h3>
						<div class="detail-op-box">
							{% if modele.libelleModele is defined %}
								{{ modele.libelleModele.valeur }}
							{% else %}
								???
							{% endif %}
						</div>
						{% if modele.lesOperationsAssociees.valeurs is defined %}
							{% if modele.lesOperationsAssociees.valeurs|length %}
								<h3>{{ "operations.titles.opAsso" | trans }}</h3>
								<div class="detail-op-box">
									<ul>
										{% for operation in modele.lesOperationsAssociees.valeurs %}
											{% if operation.id != idOperation %}
												{#TODO refacto sur btn-infos-assos-operation#}
												<li><a href="#" class="btn-infos-assos-operation"
													   data-selection-comptage="{{ operationsAssociees[operation.id].tailleCalcul | number_format(0, ',', ' ') }}"
													   data-selection-date-comptage="{{ operationsAssociees[operation.id].dateCalcul | date('d/m/Y H:i') }}"
													   data-operation-id="{{ operation.id }}"
													   data-selection-date-debut="{% if operationsAssociees[operation.id].dateDebut | length %}{{ operationsAssociees[operation.id].dateDebut | date('d/m/Y') }}{% endif %}"
													   data-selection-date-fin="{% if operationsAssociees[operation.id].dateFin | length %}{{ operationsAssociees[operation.id].dateFin | date('d/m/Y') }}{% endif %}">{{ operation.libelle }}</a>
												</li>
											{% endif %}
										{% endfor %}
									</ul>
								</div>
							{% endif %}
						{% endif %}
					</div>
					<div class="column small-12 medium-6 text-center">
						<div class="textPerso">
							<div class="text-left mlm mbs">
								<i>{{ "operations.titles.emetteur" | trans }} :</i>
                                {% if senderSMS | length %}
                                    {{ senderSMS }}
                                {% else %}
                                    {{ "operations.titles.noEmetteur" | trans }}
                                {% endif %}
							</div>
							<div class="textPersoBulle">
								{{ message | raw }}
							</div>
						</div>
					</div>
				{% endif %}
			{% endif %}
		</div>
	{% endif %}
{% endblock %}
