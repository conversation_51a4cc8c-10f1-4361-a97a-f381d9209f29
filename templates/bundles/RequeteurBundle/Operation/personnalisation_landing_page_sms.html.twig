{% extends 'base.html.twig' %}

{% block body %}
    {% block content %}
        <div class="personnaliser-modele-email-editor"
             data-idoperation="{{ idOperation }}"
             data-operation-lib="{{ libelle }}">
            {{ block('loaderPage', 'blocks.html.twig') }}
            <h2>{{ libelle }}</h2>
            <div class="landing">
                <div class="column small-12">
                <button onclick="window.location.href='{{ path('aquitem_requeteur_operation_personnaliser_modele_sms', {idOperation:idOperation, idModele: idModele}) }}'" class="btn" style="float:right">
                        {{ "global.fermerediteur" | trans }}
                </button>
                    <h3>{{ "operations.smsrich.title" | trans }}</h3>
                </div>
                    <div class="mbm persominisite">
                        <span class="landing-info">{{ "operations.modeles.perso.landingInfo" | trans }}</span>
                    </div>
            </div>
            <iframe id="landingPage" src="{{ urlIframe }}" frameborder="0" height="825" allowfullscreen style="width:100%;border: 1px solid #dedede;"></iframe>
            <div class="text-right mts">
                <button id="reinitMinisiteSMS" class="btn btn--grey">{{ "global.reinit" | trans }}</button>
            </div>
        </div>

    {% endblock %}
{% endblock %}

{% block javascripts %}
    {{ encore_entry_script_tags('perso-sms-rich') }}
{% endblock %}
