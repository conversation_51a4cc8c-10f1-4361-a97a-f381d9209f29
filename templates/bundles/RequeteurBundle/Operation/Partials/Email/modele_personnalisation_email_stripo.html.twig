{% extends 'base_stripo.html.twig' %}
{% set transLibCanal = "operations.titles.modeDeDiffusion" ~ canal | upper %}
{% block title %}{{ "operations.titles.listeOperations" | trans({}, "messages", user_locale_langue) }} : {{ operation.libelle }} - {{ transLibCanal | trans({}, "messages", user_locale_langue) }} - {{ "operations.modeles.perso.menu.contenu" | trans({}, "messages", user_locale_langue) }}{% endblock %}
{% block mtitle %}{{ "operations.titles.listeOperations" | trans({}, "messages", "fr") }} : {{ operation.libelle }} - {{ transLibCanal | trans({}, "messages", "fr") }} - {{ "operations.modeles.perso.menu.contenu" | trans({}, "messages", "fr") }}{% endblock %}
{% block stylesheets %}
	<link href="https://icones.mydataviz.fr/css/all.min.css" type="text/css" rel="stylesheet" />
	<style>
		#externalSystemContainer {
			padding: 10px 0 5px 19%;
			background-color: #f5f5f5;
		}
		#undoButton, #redoButton {
			background-color: #ffffff;
			border: 1px solid #dddddd;
			font-size: 0.75em;
			border-radius: 10px;
		}
		#undoButton[disabled], #redoButton[disabled] {
			opacity: 0.5;
		}
		#undoButton:hover, #redoButton:hover {
			border-color: var(--primary-color)
		}
		#undoButton[disabled]:hover, #redoButton[disabled]:hover {
			border-color: #dddddd;
		}
		#stripoSettingsContainer {
			width: 400px;
			float: left;
		}
		#stripoPreviewContainer {
			width: calc(100% - 400px);
			float: left;
		}
		.notification-zone {
			position: fixed;
			width: 400px;
			z-index: 99999;
			right: 20px;
			bottom: 80px;
		}
		.control-button {
			border-radius: 17px;
			padding: 5px 10px;
			border-color: grey;
		}
		#changeHistoryLink {
			cursor: pointer;
		}
		/* The Modal (background) */
		.modal {
		display: none; /* Hidden by default */
		position: fixed; /* Stay in place */
		z-index: 3; /* Sit on top */
		left: 0;
		top: 0;
		width: 100%; /* Full width */
		height: 100%; /* Full height */
		overflow: auto; /* Enable scroll if needed */
		background-color: rgb(0,0,0); /* Fallback color */
		background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
		}

		/* Modal Content/Box */
		.modal-content {
		background-color: #fefefe;
		margin: 15% auto; /* 15% from the top and centered */
		padding: 20px;
		border: 1px solid #888;
		width: 50%; /* Could be more or less, depending on screen size */
		}

		/* The Close Button */
		.close {
		color: #aaa;
		float: right;
		font-size: 28px;
		font-weight: bold;
		}

		.close:hover,
		.close:focus {
		color: black;
		text-decoration: none;
		cursor: pointer;
		}
	</style>
{% endblock %}
{% block body %}
	{% block content %}
		{% if modelesValides[canal] is defined %}
			{% set idModele = modelesValides[canal]['idModele'] %}
		{% endif %}
		{% set idTagSenderID = modelesValides['EMAIL'].idTagSenderID %}
		{% set senderID = modelesValides['EMAIL'].senderID %}
		{% if idTagSenderID == "" and senderID != "" %}
			{% set idTagSenderID = "0" %}
		{% endif %}
		{% set usePack = false %}
		{% for currentCanal in modele.operation.valeur.valeurs[0].valeurs %}
			{% if currentCanal.libelleCanal == canal %}
				{% set usePack = currentCanal.packLibreService %}
			{% endif %}
		{% endfor %}

		{% set personalLibraryName = "" %}
		{% set commonLibraryName = "" %}
		{% if app.session.get('current_user_profil')['common_library_folder'] is defined %}
			{% if app.session.get('current_user_profil')['common_library_folder'] != "" %}
				{% set commonLibraryName = "operations.titles.maBibliotheque" | trans %}
				{% if app.session.get('current_user_profil')['common_library_name'] is defined %}
					{% if app.session.get('current_user_profil')['common_library_name'] != "" %}
						{% set commonLibraryName = app.session.get('current_user_profil')['common_library_name'] %}
					{% endif %}
				{% endif %}
			{% endif %}
		{% endif %}

		{% if app.session.get('current_user_profil')['personal_library'] is defined %}
			{% if app.session.get('current_user_profil')['personal_library'] == "true" %}
				{% set personalLibraryName = "operations.titles.mesImages" | trans %}
				{% if app.session.get('current_user_profil')['personal_library_name'] is defined %}
					{% if app.session.get('current_user_profil')['personal_library_name'] != "" %}
						{% set personalLibraryName = app.session.get('current_user_profil')['personal_library_name'] %}
					{% endif %}
				{% endif %}
			{% elseif app.session.get('current_user_profil')['personal_library'] == "false" %}
				{% if commonLibraryName != "" %}
					{% set personalLibraryName = commonLibraryName %}
				{% endif %}
			{% endif %}
		{% else %}
			{% set personalLibraryName = "operations.titles.mesImages" | trans %}
		{% endif %}


		{% if app.session.get('current_user_profil')['langue'] is defined %}
			{% set user_locale_langue = app.session.get('current_user_profil')['langue'] %}
		{% else %}
			{% set user_locale_langue = "fr" %}
		{% endif %}


		<div id="operation-personnalisation-modele-email" class="personnaliser-gestion-modele-email-editor"
			 data-offreactivee="{{ offreDematerialiseeActivee }}"
			 data-idoperation="{{ idOperation }}"
			 data-idselection="{{ operation.idSelection }}"
			 data-selection-horsfid="{{ operation.selectionHorsFid }}"
			 data-prefixetag="{{ prefixeTag }}"
			 data-suffixetag="{{ suffixeTag }}"
			 data-prefixedesinscription="{{ prefixeDesinscription }}"
			 data-suffixedesinscription="{{ suffixeDesinscription }}"
			 data-prefixemiroir="{{ prefixeMiroir }}"
			 data-suffixemiroir="{{ suffixeMiroir }}"
			 data-email-emetteur="{{ modele.emailFrom.valeur }}"
			 data-domaines-emetteur = "{{ modele.domainesAutorises.valeurs | json_encode() }}"
			 data-nom-emetteur="{{ modele.emetteurFrom.valeur }}"
			 data-sujet="{{ modele.sujet.valeur }}"
			 data-header-html="{{ modele.headerHTML.valeur }}"
			 {# data-tags-sender-id="{{ idTagSenderID }}" #}
			 {# data-sender-id="{{ senderID }}" #}
			 data-alltags="{{ allTags | json_encode() }}"
			 data-selecttags="{{ defaultTags | json_encode() }}"
			 data-datetags="{{ dateTags | json_encode() }}"
			 data-linkthemes="{{ themesPourUrlsTrackables | json_encode() }}"
			 data-unsubscribelinks="{{ modele.lesPagesDeDesinscription.valeurs | json_encode() }}"
			 data-message-html="{{ modele.messageHTML.valeur }}"
			 data-donnees-json="{{ modele.donneesJson.valeur }}"
			 data-editeur="{{ modele.typeEditeur.valeur }}"
			 data-urlstracees="{{ urlsTracees | json_encode() }}"
			 data-operation-lib="{{ operation.libelle }}"
			 data-pnr-library="{% if app.session.get('current_user_profil')['pnr_library_url'] is defined and app.session.get('current_user_profil')['pnr_library_key'] is defined %}true{% else %}false{% endif %}"
			 data-pnr-library-name="{% if app.session.get('current_user_profil')['pnr_library_name'] is defined %}{{ app.session.get('current_user_profil')['pnr_library_name'] }}{% else %}{{ "operations.titles.maBibliothequePnr" | trans }}{% endif %}"
			 data-mesoigner-library="{% if app.session.get('current_user_profil')['mesoigner_library_url'] is defined and app.session.get('current_user_profil')['mesoigner_library_key'] is defined %}true{% else %}false{% endif %}"
			 data-mesoigner-library-name="{% if app.session.get('current_user_profil')['mesoigner_library_name'] is defined %}{{ app.session.get('current_user_profil')['mesoigner_library_name'] }}{% else %}{{ "operations.titles.maBibliothequeMesoigner" | trans }}{% endif %}"
			 data-mesoigner-library-url="{% if app.session.get('current_user_profil')['mesoigner_library_url'] is defined %}{{ app.session.get('current_user_profil')['mesoigner_library_url'] }}{% endif %}"
			 data-personal-library="{% if app.session.get('current_user_profil')['personal_library'] is defined %}{{ app.session.get('current_user_profil')['personal_library'] }}{% else %}true{% endif %}"
			 data-personal-library-name="{{ personalLibraryName }}"
			 data-common-library="{% if app.session.get('current_user_profil')['common_library_folder'] is defined %}{{ app.session.get('current_user_profil')['common_library_folder'] }}{% endif %}"
			 data-common-library-name="{{ commonLibraryName }}"
			 data-mesoigner-save-images="{% if app.session.get('current_user_profil')['mesoigner_save_images'] is defined %}{{ app.session.get('current_user_profil')['mesoigner_save_images'] }}{% endif %}"
			 data-stripo-fonts="{{ stripoFonts }}">
			 <div id="stripo-datas" data-urlemptymodele="{{ url("empty_modele") }}" data-apikey="{{ secretKey }}" data-id="{{ pluginId }}" data-tags='{{ allTags|json_encode() }}' data-datetags="{{ dateTags | json_encode() }}" data-headerhtml="{{ modele.headerHTML.valeur }}" data-modele='{{ modele.donneesJson.valeur }}' data-role="{{ stripoRole }}" data-prefixedesinscription="{{ prefixeDesinscription }}" data-suffixedesinscription="{{ suffixeDesinscription }}" data-unsubscribelinks="{{ modele.lesPagesDeDesinscription.valeurs | json_encode() }}" data-prefixemiroir="{{ prefixeMiroir }}" data-suffixemiroir="{{ suffixeMiroir }}" data-unsubscribelinks="{{ modele.lesPagesDeDesinscription.valeurs | json_encode() }}" data-langue="{{user_locale_langue}}" data-requeteur="{{requeteur}}" data-defaultValueUser="{{valeurParDefautTagsUser | json_encode()}}" data-userid="{{userId}}">
			 {# <div id="stripo-datas" data-urlemptymodele="{{ url("empty_modele") }}" data-apikey="{{ secretKey }}" data-id="{{ pluginId }}" data-tags='{{ allTags|json_encode() }}' data-modele='{{ modele.donneesJson.valeur }}' data-role="{{ stripoRole }}"> #}

			<div class="loaderPage">
				<div class="loaderWheel"></div>
			</div>
			<h2>{{ operation.libelle }}</h2>
			<div class="btn-loader mlm">
				<a href="#" class="btn-submit btn btn--blue btn--largePadding" type="button">{{ "global.save" | trans }}</a>
			{% if guideExist %}
				<div class="separate separate--grey"></div>
				<div class="guide-link-wrap">
					<a href="{{ guideName }}" target="_blank" class="guide-link" data-action="EMAIL">{{ "operations.actions.telechargerGuide" | trans }}</a>
				</div>
			{% endif %}
			</div>
			<a href="{{ path('aquitem_requeteur_operation_personnaliser_modele_email', {idOperation:idOperation, idModele: idModele}) }}" class="cross-cancel cross-cancel-top" data-close aria-label="Close modal">
				<svg viewBox="0 0 31.3 21" width="35" height="35" class="svg svg--red"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="/img/sprite-svg.svg#close-plain"></use></svg></a>

			{# Onglets #}
			{% include '@Requeteur/Operation/Partials/Email/onglets.html.twig' %}



			{# <div id="myModalTags" class="modal">
				<!-- Modal content -->
				<div class="modal-content">
					<span class="close">&times;</span>
					<select id="stripoTags">
						<option value="">Sélectionnez un tag</option>
						{% for tag in allTags %}
							<option value={{tag.id}}>{{tag.libelle}}</option>
						{% endfor %}
					</select>
					<br><br>
					<button id="addTagImage" class="btn-creer-modele btn">test</button>
				</div>
			</div> #}

			<div id="myModal" class="modal">

				<!-- Modal content -->
				<div class="modal-content">
					<h3 style="text-align:center">{{ "stripo.insertImage" | trans }}</h3>
					<div style="display:flex">
						<div style="width:20%" class="stripo-modals-labels">
							<span> {{ "stripo.source" | trans }}</span>
						</div>
						<div style="width:50%">
							<input type="text" class="max-size" id="source-value"></input>
						</div>
					</div>
					<div class="mts" style="display:flex">
						<div style="width:20%" class="stripo-modals-labels">
						</div>
						<div>
							<button id="choose-mes-images" class="btn-edit btn">{{ "stripo.mesImages" | trans }}</button>
							{% if app.session.get('current_user_profil')['common_library_folder'] is defined %}
							<button id="choose-common" class="btn-edit btn">
								{% if app.session.get('current_user_profil')['common_library_name'] is defined %}
									{{ app.session.get('current_user_profil')['common_library_name'] }}
								{% else %}
									{{ "operations.titles.maBibliotheque" | trans }}
								{% endif %}
							</button>
							{% endif %}
							{% if app.session.get('current_user_profil')['pnr_library_url'] is defined and app.session.get('current_user_profil')['pnr_library_key'] is defined %}
								<button id="choose-pnr" class="btn-edit btn">
									{% if app.session.get('current_user_profil')['pnr_library_name'] is defined %}{{ app.session.get('current_user_profil')['pnr_library_name'] }}{% else %}{{ "operations.titles.maBibliothequePnr" | trans }}{% endif %}
								</button>
							{% endif %}
							{% if app.session.get('current_user_profil')['mesoigner_library_url'] is defined and app.session.get('current_user_profil')['mesoigner_library_key'] is defined %}
							<button id="choose-me-soigner" class="btn-edit btn">
								{% if app.session.get('current_user_profil')['mesoigner_library_name'] is defined %}{{ app.session.get('current_user_profil')['mesoigner_library_name'] }}{% else %}{{ "operations.titles.maBibliothequeMesoigner" | trans }}{% endif %}
							</button>
							{% endif %}
						</div>
					</div>
					<div class="url-block mtm" style="display:flex;">
						<div style="width:20%" class="stripo-modals-labels">
							<span> {{ "stripo.tags" | trans }} </span>
						</div>
						<div style="width:50%">
							<select id="stripoTagsSource" class="max-size">
								<option value="">{{ "stripo.selectTags" | trans }}</option>
								{% for tag in allTags %}
									{% set tagValue = '{{' ~ tag.libelle ~ '}}' %}
									<option value={{tagValue}}>{{tag.libelle}}</option>
								{% endfor %}
								{# {% for tag in dateTags %}
									{% set tagValue = '{{' ~ tag.libelle ~ '}}' %}
									<option value={{tagValue}}>{{tag.libelle}}</option>
								{% endfor %} #}
							</select>
						</div>
						<button id="addTagSource" class="btn-add-tag-image btn btn--green mls">{{ "stripo.addTag" | trans }}</button>
					</div>
					<div class="mts alt-mantadory" style="display: flex; margin-bottom: -18px;display:none">
						<div style="width:50%;margin-left: 20%;color:#e02d20">
							<p>{{ "stripo.altMandatory" | trans }}</p>
						</div>
					</div>
					<div class="mts alt-block" style="display: flex;">
						<div style="width:20%" class="stripo-modals-labels">
							<span class="required">*</span><span> {{ "stripo.alt" | trans }}</span>
						</div>
						<div style="width:50%">
							<input type="text" class="max-size" id="alt-value"></input>
						</div>
					</div>
					<br><br>
					<div class="text-right">
						<button id="closeSource" class="btn--grey btn">{{ "global.cancel" | trans }}</button>
						<button id="editSource" class="btn-valid-image btn">{{ "global.validate" | trans }}</button>
					</div>
				</div>
        	</div>
			<div id="myModalTags" class="modal">
				<!-- Modal content -->
				<div class="modal-content">
					{# <span id="closeLinkCross" class="close">&times;</span> #}
					<h3 style="text-align:center">{{ "stripo.linkManagement" | trans }}</h3>
					<div class="link-type-block" style="display:flex">
						<div style="width:20%" class="stripo-modals-labels">
							<span> {{ "stripo.linkType" | trans }}</span>
						</div>
						<div style="width:50%">
							<select id="stripoLinkType" class="max-size">
								<option value="url">Url</option>
								<option value="desinscription">{{ "stripo.desinscription" | trans }}</option>
								<option value="miroir">{{ "stripo.miroir" | trans }}</option>
								<option value="anchor">{{ "stripo.anchor" | trans }}</option>
								<option value="tel">{{ "stripo.tel" | trans }}</option>
								<option value="mail">{{ "stripo.email" | trans }}</option>
								<option value="fichier">{{ "stripo.fichier" | trans }}</option>
							</select>
						</div>
					</div>
					<div class="url-block mts">
						<div style="display:flex">
							<div style="width:20%" class="stripo-modals-labels">
								<span> {{ "stripo.lien" | trans }} </span>
							</div>
							<div style="width:50%">
								<input type="text" class="max-size" id="link-value"></input>
							</div>
						</div>
					</div>
					<div class="url-block mts">
						<div style="display:flex;">
							<div style="width:20%" class="stripo-modals-labels">
								<span> {{ "stripo.tags" | trans }} </span>
							</div>
							<div style="width:50%">
								<select id="stripoTags" class="max-size">
									<option value="">{{ "stripo.selectTags" | trans }}</option>
									{% for tag in allTags %}
										{% set tagValue = '{{' ~ tag.libelle ~ '}}' %}
										<option value={{tagValue}}>{{tag.libelle}}</option>
									{% endfor %}
									{# {% for tag in dateTags %}
										{% set tagValue = '{{' ~ tag.libelle ~ '}}' %}
										<option value={{tagValue}}>{{tag.libelle}}</option>
									{% endfor %} #}
								</select>
							</div>
							<button id="addTag" class="btn-add-tag-stripo btn btn--green mls">{{ "stripo.addTag" | trans }}</button>
						</div>
					</div>


					<div class="desinsciption-block mts" style="display:flex">
						<div style="width:20%" class="stripo-modals-labels">
							<span> {{ "stripo.desinscriptionLabel" | trans }} </span>
						</div>
						<div style="width:50%">
							<select id="desinscription-value" class="max-size">
								{% for PageDeDesinscription in lesPagesDeDesinscription %}
									<option value="{{PageDeDesinscription.id}}"> {{PageDeDesinscription.libelle}}</option>
								{% endfor %}
							</select>
						</div>
					</div>

					<div class="miroir-block mts" style="display:flex">
						<div style="width:20%" class="stripo-modals-labels">
							<span> {{ "stripo.miroirLabel" | trans }} </span>
						</div>
						<div style="width:50%">
							<input type="text" class="max-size" id="miroir-value"></input>
						</div>
					</div>

					<div class="anchor-block mts" style="display:flex">
						<div style="width:20%" class="stripo-modals-labels">
							<span> {{ "stripo.anchorLabel" | trans }} </span>
						</div>
						<div style="width:50%">
							<input type="text" class="max-size" id="anchor-value"></input>
						</div>
					</div>

					<div class="tel-block mts" style="display:flex">
						<div style="width:20%" class="stripo-modals-labels">
							<span> {{ "stripo.telLabel" | trans }} </span>
						</div>
						<div style="width:50%">
							<input type="text" class="max-size" id="tel-value"></input>
						</div>
					</div>

					<div class="mail-block mts" style="display:none">
						<div style="display:flex">
							<div style="width:20%" class="stripo-modals-labels">
								<span> {{ "stripo.emailLabel" | trans }} </span>
							</div>
							<div style="width:50%">
								<input type="text" class="max-size" id="mail-value"></input>
							</div>
						</div>
						<div class="mts" style="display:flex">
							<div style="width:20%" class="stripo-modals-labels">
								<span> {{ "stripo.emailObject" | trans }} </span>
							</div>
							<div style="width:50%">
								<input type="text" class="max-size" id="email-subject-value"></input>
							</div>
						</div>
						<div class="mts" style="display:flex">
							<div style="width:20%" class="stripo-modals-labels">
								<span> {{ "stripo.emailBody" | trans }} </span>
							</div>
							<div style="width:50%">
								<textarea style="min-height: 100px;" class="max-size" id="email-body-value"></textarea>
							</div>
						</div>
					</div>

					<div class="fichier-block mts" style="display:flex">
						<div style="width:20%" class="stripo-modals-labels">
							<span> {{ "stripo.fileLabel" | trans }} </span>
						</div>
						<div style="width:50%">
							<input type="text" class="max-size" id="fichier-value"></input>
						</div>
						<button id="selectPdfFile" class="btn-edit btn mls">{{ "stripo.mesFichiers" | trans }}</button>
					</div>

					<br><br>
					<div class="text-right">
						<button id="closeLink" class="btn--grey btn">{{ "global.cancel" | trans }}</button>
						<button id="editLink" class="btn-valid-file btn">{{ "global.validate" | trans }}</button>
					</div>
				</div>
			</div>

			{# Contenu #}
			<div id="externalSystemContainer" class="text-center hide">
				<button id="undoButton" class="control-button" title="{{ "global.cancel" | trans }}" disabled>
					<svg class="svg" width="25" height="25" viewBox="0 0 512 512" alt="{{ "global.cancel" | trans }}">
						<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg?t=' ~ 'now'|date('U') ~ '#undo') }}"></use>
					</svg>
				</button>
				<button id="redoButton" class="control-button" title="{{ "global.redo" | trans }}" disabled>
					<svg class="svg" width="25" height="25" viewBox="0 0 512 512" alt="{{ "global.redo" | trans }}">
						<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg?t=' ~ 'now'|date('U') ~ '#redo') }}"></use>
					</svg>
				</button>
			</div>
			<div class="notification-zone"></div>
			<div style="display:flex;">
				<div style="width:20%" id="stripoSettingsContainer">Loading...</div>
				<div style="width:80%" id="stripoPreviewContainer"></div>
			</div>

			<input type="hidden" id="sujet" value="{{ modele.emailFrom.valeur }}" />
			<input type="hidden" id="emailEmetteur" value="{{ modele.emailFrom.valeur }}" />
			<input type="hidden" id="nomEmetteur" value="{{ modele.emetteurFrom.valeur }}" />

			{# POUR CHARGER STRIPO COMME AVANT #}
			{# aquitem_requeteur_personnaliser_modele_email_stripo #}

			{# <div class="tabs-content" data-tabs-content="tabs-editor">
				<div class="modal-body-mosaico is-active" id="email-editor" role="tabpanel" aria-hidden="false" aria-labelledby="email-editor-label">
					<iframe id="" width="100%" height="560"
							src="{{ url("aquitem_requeteur_personnaliser_modele_email_stripo", {idModele: idModele}) }}#{{ date().timestamp }}"
							frameborder="0">
					</iframe>
					<div>
						<input id="blank" name="blank" type="checkbox" {% if isBlank is same as("true") %} checked="checked" {% endif %} /> <label for="blank">{{ "modeles.form.blank" | trans }}</label>
					</div>
				</div>
			</div> #}


			<div class="ptl text-right">
				<div class="btn-loader mls">
					<a href="#" class="btn-submit btn btn--blue btn--largePadding" type="button">{{ 'global.save' | trans }}</a>
				</div>
			</div>
		</div>


		<template id="pre-header-tpl">
			<div>
				<div class="pre-header-text"
					 style="display:none !important;mso-hide: all;font-size:1px;color:#333333;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>
			</div>
		</template>

		<template id="perso-save-form-tpl">
			<form id="perso-save-form">
				<div class="input-group mll">
					<div class="alert hide">{{ "operations.actions.alertSavePerso" | trans | raw }}</div>
				</div>
				<div class="info info-no-lienOffre info-icon mbm hide">
					<span class="svg-info-link">
					<svg class="svg" viewBox="0 0 31.3 21" width="20" height="20" alt="">
						<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#info') }}"></use>
					</svg>
					</span>
					<span class="info-no-lienOffre-text info-icon-text mls">
						{{ "operations.modeles.perso.noLienOffre" | trans | raw }}
					</span>
				</div>
			</form>
		</template>

	{% endblock %}
{% endblock %}

{% block javascripts %}
	{{ encore_entry_script_tags('operation-perso-email-stripo') }}
{% endblock %}
