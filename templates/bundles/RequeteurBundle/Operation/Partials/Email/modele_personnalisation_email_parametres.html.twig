{% extends 'base.html.twig' %}
{% set transLibCanal = "operations.titles.modeDeDiffusion" ~ canal | upper %}
{% block title %}{{ "operations.titles.listeOperations" | trans({}, "messages", user_locale_langue) }} : {{ operation.libelle }} - {{ transLibCanal | trans({}, "messages", user_locale_langue) }} - {{ "operations.modeles.perso.menu.emetteur" | trans({}, "messages", user_locale_langue) }}{% endblock %}
{% block mtitle %}{{ "operations.titles.listeOperations" | trans({}, "messages", "fr") }} : {{ operation.libelle }} - {{ transLibCanal | trans({}, "messages", "fr") }} - {{ "operations.modeles.perso.menu.emetteur" | trans({}, "messages", "fr") }}{% endblock %}
{% block stylesheets %}
	{{ parent()}}
	{{ encore_entry_link_tags('css-emojis') }}
{% endblock %}
{% block body %}
	{% block content %}
		{% if modelesValides[canal] is defined %}
			{% set idModele = modelesValides[canal]['idModele'] %}
		{% endif %}
		{% set idTagSenderID = modelesValides['EMAIL'].idTagSenderID %}
		{% set senderID = modelesValides['EMAIL'].senderID %}
		{% if idTagSenderID == "" and senderID != "" %}
			{% set idTagSenderID = "0" %}
		{% endif %}
		{% set usePack = false %}
		{% for currentCanal in modele.operation.valeur.valeurs[0].valeurs %}
			{% if currentCanal.libelleCanal == canal %}
				{% set usePack = currentCanal.packLibreService %}
			{% endif %}
		{% endfor %}

		<div id="operation-personnalisation-modele-email" class="personnaliser-modele-email-editor"
			 data-idoperation="{{ idOperation }}"
			 data-idselection="{{ operation.idSelection }}"
			 data-selection-horsfid="{{ operation.selectionHorsFid }}"
			 data-prefixetag="{{ prefixeTag }}"
			 data-suffixetag="{{ suffixeTag }}"
			 data-prefixedesinscription="{{ prefixeDesinscription }}"
			 data-suffixedesinscription="{{ suffixeDesinscription }}"
			 data-prefixemiroir="{{ prefixeMiroir }}"
			 data-suffixemiroir="{{ suffixeMiroir }}"
			 data-email-emetteur="{{ modele.emailFrom.valeur }}"
			 data-domaines-emetteur = "{{ modele.domainesAutorises.valeurs | json_encode() }}"
			 data-nom-emetteur="{{ modele.emetteurFrom.valeur }}"
			 data-email-reponse="{{ modele.emailReponse.valeur }}"
			 data-sujet="{{ modele.sujet.valeur }}"
			 data-header-html="{{ modele.headerHTML.valeur }}"
			 data-tags-sender-id="{{ idTagSenderID }}"
			 data-sender-id="{{ senderID }}"
			 data-alltags="{{ allTags | json_encode() }}"
			 data-selecttags="{{ defaultTags | json_encode() }}"
			 data-datetags="{{ dateTags | json_encode() }}"
			 data-linkthemes="{{ themesPourUrlsTrackables | json_encode() }}"
			 data-unsubscribelinks="{{ modele.lesPagesDeDesinscription.valeurs | json_encode() }}"
			 data-message-html="{{ modele.messageHTML.valeur }}"
			 data-donnees-json="{{ modele.donneesJson.valeur }}"
			 data-editeur="{{ modele.typeEditeur.valeur }}"
			 data-urlstracees="{{ urlsTracees | json_encode() }}"
			 data-operation-lib="{{ operation.libelle }}">
			<h2>{{ operation.libelle }}</h2>
			<div class="btn-loader mlm">
				<a href="#" class="btn-submit btn btn--blue btn--largePadding" type="button">{{ "global.save" | trans }}</a>
			{% if guideExist %}
				<div class="separate separate--grey"></div>
				<div class="guide-link-wrap">
					<a href="{{ guideName }}" target="_blank" class="guide-link" data-action="EMAIL">{{ "operations.actions.telechargerGuide" | trans }}</a>
				</div>
			{% endif %}
			</div>
			
			{% if hasEditZone %}
				<a href="{{ path('aquitem_requeteur_operation_mode_de_diffusion', {idOperation: idOperation, canal: 'email' }) }}" class="cross-cancel cross-cancel-top" data-close aria-label="Close modal"><svg viewBox="0 0 31.3 21" width="35" height="35" class="svg svg--red"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="/img/sprite-svg.svg#close-plain"></use></svg></a>
			{% else %}
				<a href="{{ path('aquitem_requeteur_operation_personnaliser_modele_email', {idOperation:idOperation, idModele: idModele}) }}" class="cross-cancel cross-cancel-top" data-close aria-label="Close modal"><svg viewBox="0 0 31.3 21" width="35" height="35" class="svg svg--red"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="/img/sprite-svg.svg#close-plain"></use></svg></a>
			{% endif %}
			
			{# Onglets #}
			{% include '@Requeteur/Operation/Partials/Email/onglets.html.twig' %}

			{# Contenu #}
			<div class="tabs-content">

				{# Emetteur et objet #}
				<div class="modal-body-perso" id="parameters-link">
					<form id="parameters-form">
						<div id="perso-parameters">
							<div class="row">
								<p class="column required-legende">{{ "operations.requiredFields" | trans | raw }}</p>
							</div>
						{% if app.user.checkDroitsEmailExpediteur %}
							<div class="row">
								<div class="column medium-7">
									<div class="input-group">
										<label for="emailEmetteur">{{ "global.asterisque" | trans | raw }} {{ "operations.modeles.perso.emailEmetteur" | trans }}</label>
										<input type="text" id="emailEmetteur" required="required" value="{{ modele.emailFrom.valeur }}" />
									</div>
									<div class="input-group">
										<label for="nomEmetteur">{{ "global.asterisque" | trans | raw }} {{ "operations.modeles.perso.nomEmetteur" | trans }}</label>
										<input type="text" id="nomEmetteur" required="required" value="{{ modele.emetteurFrom.valeur }}" />
									</div>
								</div>
							</div>
							{% if not app.user.checkDroitsAdminAquitem %}
							<hr/>
							{% endif %}
						{% else %}
							<input type="hidden" id="emailEmetteur" value="{{ modele.emailFrom.valeur }}" />
							<input type="hidden" id="nomEmetteur" value="{{ modele.emetteurFrom.valeur }}" />
						{% endif %}
						{# email reponse #}
						{% if app.user.checkDroitsAdminAquitem %}
							<div class="row">
								<div class="column medium-7">
									<div class="input-group">
										<label for="emailReponse">{{ "global.asterisque" | trans | raw }} {{ "operations.modeles.perso.emailReponse" | trans }}</label>
										<input type="text" id="emailReponse" required="required" value="{{ modele.emailReponse.valeur }}" />
									</div>
								</div>
							</div>
							<hr/>
						{% endif %}
							<div class="row add-tags">
								<div class="column large-7 medium-12">
									<div class="input-group">
										<label for="sujetId">{{ "global.asterisque" | trans | raw }} {{ "operations.modeles.perso.sujet" | trans }}</label>
										<input type="text" id="sujetId" required="required" value="{{ modele.sujet.valeur }}" />
										<button type="button" class="btn btn--red mls" id="trigger"><i class="far fa-smile" aria-hidden="true"></i></button>
										<div class="info-emojis">
											<span>{{ "operations.modeles.perso.infoEmojis" | trans }}</span>
										</div>
									</div>
								</div>
								<div class="column large-5 medium-12">
									<div class="row add-tags-group">
										<div class="column medium-7 text-right">
											<label for="tagsEmailSujet">{{ "operations.modeles.perso.listeTags" | trans }}</label>
											<select id="tagsEmailSujet">
												{% for tag in allTags %}
													<option value="{{ tag.id }}">{{ tag.libelle }}</option>
												{% endfor %}
											</select>
										</div>
										<div class="column medium-5">
											<button type="button" data-action="ajoutTag" data-type-tag="tagsEmailSujet" data-field="sujetId" data-num="0"
												class="btn btn--red man">{{ "global.add" | trans }}</button>
										</div>
									</div>
								</div>
							</div>
							{% if modele.typeEditeur.valeur == 0 or modele.typeEditeur.valeur == 2 %}
								<div class="row add-tags mtm">
									<div class="column large-7 medium-12">
										<div class="input-group">
											<label for="preHeaderId">
												{{ "operations.modeles.perso.preHeader" | trans }}
												<a class="svg-info-bulle" href="#">
													<svg class="svg svg--white" viewBox="0 0 31.3 21" width="20" height="20" alt="">
														<use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#info') }}"></use>
													</svg>
													<span class="hover-description">{{ "operations.infos.preHeaderIB" | trans }}</span>
												</a> :
											</label>
											<textarea id="preHeaderId"></textarea>
										</div>
									</div>
									<div class="column large-5 medium-12">
										<div class="row add-tags-group">
											<div class="column medium-7 text-right">
												<label for="tagsEmailPreHeader">{{ "operations.modeles.perso.listeTags" | trans }}</label>
												<select id="tagsEmailPreHeader">
													{% for tag in allTags %}
														<option value="{{ tag.id }}">{{ tag.libelle }}</option>
													{% endfor %}
												</select>
											</div>
											<div class="column medium-5">
												<button type="button" data-action="ajoutTag" data-type-tag="tagsEmailPreHeader"
													data-field="preHeaderId" data-num="1" class="btn btn--red man">{{ "global.add" | trans }}</button>
											</div>
										</div>
									</div>
								</div>
							{% endif %}
						</div>
					</form>
				</div>

			</div>

			<div class="ptl text-right">
				<div class="btn-loader mls">
					<a href="#" class="btn-submit btn btn--blue btn--largePadding" type="button">{{ 'global.save' | trans }}</a>
				</div>
			</div>
		</div>

		<template id="pre-header-tpl">
			<div>
				<div class="pre-header-text"
					style="display:none !important;mso-hide: all;font-size:1px;color:#333333;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>
			</div>
		</template>

		<template id="perso-save-form-tpl">
			<form id="perso-save-form">
				<div class="input-group mll">
					<div class="alert hide">{{ "operations.actions.alertSavePerso" | trans | raw }}</div>
				</div>
			</form>
		</template>

	{% endblock %}
{% endblock %}

{% block javascripts %}
	{{ encore_entry_script_tags('operation-perso-email-parametres') }}
{% endblock %}
