{% if canal == 'EMAIL' %}
    {% set modelesBlanks = modeleBlanksMerged %}
{% endif %}

{% for modeleBlank in modelesBlanks %}
    <li class="column column-block text-center modele-item mbxl">
        <div class="modele-item-content">
            {% set editeur = '0' %}
            {% if modeleBlank.typeEditeur is defined %}
                {% set editeur = modeleBlank.typeEditeur %}
            {% endif %}
            <a data-pw="editeur-{{ editeur }}" {% if replaceModel %} class="link-confirm-remplacer-modele"{% endif %}href="{{ url('aquitem_requeteur_operation_selectionner_modele', {type: canal, idOperation: idOperation, idFormatRoutage: modeleBlank.idFormatRoutageDefaut, idModele: modeleBlank.id, idTarifModele: modeleBlank.tarifsAutorises[0], typeEditeur: editeur, personnalisationDirect: true}) }}">
                <div class="{% if canal != 'SMS' %}model-item-cadre {% endif %}model-item-{{ canal | lower }} model-item-{{ canal | lower }}-blank">
                    {% if canal == 'EMAIL' %}
                        {% if editeur == "2" %}
                            <span><img src="{{ asset('img/email-new-responsive.png') }}" width="80" height="109" alt="" /></span>
                            <span class="info-blank">{{ "operations.diffusion.infoModelBlankResponsive" | trans }}</span>
                        {% elseif editeur == "1" %}
                            <span><img src="{{ asset('img/email-new-responsive.png') }}" width="80" height="109" alt="" /></span>
                            <span class="info-blank">{{ "operations.diffusion.infoModelBlankResponsive" | trans }}</span>
                        {% else %}
                            <span><img src="{{ asset('img/email-new.png') }}" width="80" height="109" alt="" /></span>
                            <span class="info-blank">{{ "operations.diffusion.infoModelBlankHtml" | trans }}</span>
                        {% endif %}
                    {% elseif canal == 'SMS' %}
                        <img src="{{ asset('img/sms-new.png') }}" width="75" height="145" alt="" />
                    {% endif %}
                </div>
                <div class="operations-list-desc">
                    {% if editeur == "2" %}
                        {{ "operations.diffusion.modelBlankResponsiveV2" | trans | raw}}
                    {% elseif editeur == "1" %}
                        {{ "operations.diffusion.modelBlankResponsive" | trans | raw}}
                    {% else %}
                        {{ ("operations.diffusion." ~ (canal == 'EMAIL' ? 'modelBlankHtml' : 'modelBlankSms')) | trans | raw}}
                    {% endif %}
                </div>
            </a>
        </div>
    </li>
{% endfor %}
