<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=1024, initial-scale=1">

    <title>{% block title %}{{ "global.pageTitle" | trans }}{% endblock %}</title>
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}" />

    {% block stylesheet_files %}
        {{ encore_entry_link_tags('operation-images-css') }}
    {% endblock %}

    {% block stylesheets %}{% endblock %}

    {% include 'common/personnalisation.html.twig' %}
</head>
<body>
    <ul class="tabs" data-tabs id="browser-tabs" data-wallet="1">
        <li class="tabs-title is-active"><a href="#browser-panel1" aria-selected="true">{% if personalLibraryName != "" %}{{ personalLibraryName }}{% else %}{{ "operations.titles.mesImages" | trans }}{% endif %}</a></li>
        <li class="tabs-title"><a href="#browser-panel2">{{ "operations.titles.downloadImage" | trans }}</a></li>
        <li class="tabs-title"><a href="#browser-panel3" class="ptn pbn pln prn"></a></li>
    </ul>
    <div class="tabs-content" data-tabs-content="browser-tabs"{% if app.session.get('current_user_profil')['langue'] is defined and app.session.get('current_user_profil')['langue'] == "en" %} data-langue="en"{% else %} data-langue="fr"{% endif %}>
        <div class="tabs-panel is-active" id="browser-panel1">
            <div class="row" id="js-images-container">{{ "operations.titles.loadImages" | trans }}</div>
        </div>
        <div class="tabs-panel" id="browser-panel2">
            <label for="fileupload">{{ "operations.titles.selectImage" | trans }} ({{ (maxFileSize // 1024) }} Ko max) :</label>
            <input data-max-file-size="{{ maxFileSize }}" id="fileupload" type="file" name="files[]" multiple>
            <br>
            <!-- The global progress bar -->
            <div class="success progress hide" role="progressbar" tabindex="0" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                <span class="progress-meter">
                    <p class="progress-meter-text"></p>
                  </span>
            </div>
            <!-- The container for the uploaded files -->
            <div id="uploadFile" class="files">
                <div id="js-images-container">
                    <div class="image-item"><a href="#" class="js-image-link" data-url="" data-url-retina=""><img src=""></a></div>
                </div>
            </div>
            <div class="error hide"></div>
        </div>
        <div class="tabs-panel" id="browser-panel3">
            <div id="edit-wallet-image-wrap">
                <div id="edit-wallet-image">
                    <div class="dimensions mbl">{{ "operations.titles.walletDimensionsSelection" | trans }}</div>
                    <div class=" mbs">
                        <div class="svg-info-bulle">
                            <svg class="svg svg--white" viewBox="0 0 31.3 21" width="20" height="20" alt="">
                                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#info') }}"></use>
                            </svg>
                        </div>
                        {{ "operations.titles.infoCrop" | trans }}
                        <div class="text-right">
                            <form id="crop-wallet-image-form" name="crop-wallet-image-form" method="POST">
                                <input type="hidden" id="imgWidth" name="imgWidth">
                                <input type="hidden" id="imgHeight" name="imgHeight">
                                <input type="hidden" id="startX" name="startX">
                                <input type="hidden" id="startY" name="startY">
                                <button id="crop-image-leave" type="button" class="btn btn-edit mll mbs">{{ "operations.titles.autreImage" | trans }}</button>
                                <button id="crop-wallet-image" class="btn btn-edit mll mbs">{{ "global.saveAndUse" | trans }}</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="edited-image-container">
                <img id="edited-wallet-image" src="" data-name="">
            </div>
        </div>
    </div>

    <template id="js-template-image">
        <div class="image-item">
            <a href="#" class="thumbnail js-image-link" data-url="{thumbUrl}"><img src="{thumbUrl}"></a>
            {# <a href="#" class="js-image-edit" data-url="{thumbUrl}">{{ "global.edit" | trans }}</a> #}
        </div>
    </template>

    {% block javascripts %}
        {{ encore_entry_script_tags('wallet-images') }}
        <script src="{{ url('bazinga_jstranslation_js') }}" type="text/javascript"></script>
    {% endblock %}
</body>
</html>
