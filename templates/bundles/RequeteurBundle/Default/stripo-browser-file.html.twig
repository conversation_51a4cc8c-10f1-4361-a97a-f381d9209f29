<!DOCTYPE html>
{% if app.session.get('current_user_profil')['langue'] is defined %}
    {% set user_locale_langue = app.session.get('current_user_profil')['langue'] %}
{% else %}
    {% set user_locale_langue = "fr" %}
{% endif %}
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=1024, initial-scale=1">

    <title>{% block title %}{{ "global.pageTitle" | trans({}, "messages", user_locale_langue) }}{% endblock %}</title>
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}" />

    {% block stylesheet_files %}
        {{ encore_entry_link_tags('css') }}
    {% endblock %}

    {% block stylesheets %}
        <link href="https://icones.mydataviz.fr/css/all.min.css" type="text/css" rel="stylesheet" />
    {% endblock %}


    {% include 'common/personnalisation.html.twig' %}
</head>
<body>
    <ul class="tabs" data-tabs id="browser-tabs">
        <li class="tabs-title is-active"><a href="#browser-panel1" aria-selected="true">{{ "operations.titles.mesFichiers" | trans({}, "messages", user_locale_langue) }}</a></li>
        <li class="tabs-title"><a href="#browser-panel2">{{ "operations.titles.downloadFile" | trans({}, "messages", user_locale_langue) }}</a></li>
    </ul>
    <div class="tabs-content" data-tabs-content="browser-tabs">
        <div class="tabs-panel is-active" id="browser-panel1">
            <div class="row js-images-container-ckeditor ptm pbl" id="js-images-container">{{ "operations.titles.loadFiles" | trans({}, "messages", user_locale_langue) }}</div>
        </div>
        <div class="tabs-panel" id="browser-panel2">
            <div class="email-add-file pvm">
                <label for="fileupload_rq">{{ "operations.titles.selectFile" | trans({}, "messages", user_locale_langue) }} ({{ app.session.get('tailleMaxDocument') }} Mo max) :</label>
                <span class="fileinput-button fileupload_rq ptm">
                    <i class="fad fa-file-plus mbxs"></i>
                    <br>
                    <small class="bar-action">{{ "operations.boutons.addFiles2" | trans | raw }}</small>
                    {% set tailleMaxDocument = app.session.get('tailleMaxDocument') * 1000000 %}
                    <input id="fileupload_rq" type="file" name="files[]" accept=".pdf" data-max-size="{{ tailleMaxDocument }}" data-max-size-lib="{{ app.session.get('tailleMaxDocument') }}">
                </span>
            </div>
            <br>
            <!-- The global progress bar -->
            <div class="success progress hide" role="progressbar" tabindex="0" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                <span class="progress-meter">
                    <p class="progress-meter-text"></p>
                  </span>
            </div>
            <div class="mtn mbl modal-errors error hide"></div>
            <div class="upload-success mbm hide">
                <span></span>
                <br><br>
                <a class="btn btn--green js-image-link" href="#" data-url=""></a>
            </div>
        </div>
    </div>

    <template id="js-template-file">
        <div class="file-item">
            <i class="far fa-file-pdf"></i><a href="#" target="_blank" class="js-image-link mbn" data-url="{fileUrl}">{fileName}</a>
        </div>
    </template>

    {% block javascript_files %}
        {{ encore_entry_script_tags('stripo-files') }}
        <script src="{{ url('bazinga_jstranslation_js') }}" type="text/javascript"></script>
    {% endblock %}
</body>
</html>
