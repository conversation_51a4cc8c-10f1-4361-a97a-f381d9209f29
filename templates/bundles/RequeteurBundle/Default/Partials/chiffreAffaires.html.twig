{% if refresh is defined and refresh %}
    {% set item = 'ca' %}
    {% set p = periode %}
    {% include '@Requeteur/Default/Partials/refresh.html.twig' %}
{% else %}
    {% set devise = "€" %}
    {% if app.session.get('current_user_profil')['devise'] is defined %}
        {% set devise = app.session.get('current_user_profil')['devise'] %}
    {% endif %}
    {% if periode == 1 %}
    <div class="p1">
        <div class="ventes-datas-content prm">
            {% set mois1p1 = periodes.CA_PERIODE_TIMEZONE | date('n') %}
            {% set mois1p1 = "global.moisAbrege." ~ mois1p1 %}
            {% set annee1p1 = periodes.CA_PERIODE_TIMEZONE | date('Y') %}
            {% set mois2p1 = periodes.LIB_PERIODE_TIMEZONE_MOIS_ANNEE_PREC | date('n') %}
            {% set mois2p1 = "global.moisAbrege." ~ mois2p1 %}
            {% set annee2p1 = periodes.LIB_PERIODE_TIMEZONE_MOIS_ANNEE_PREC | date('Y') %}
            {{ mois1p1 | trans }}  {{ annee1p1 }}{{ "global.dots" | trans }} <span>{% if ca_1 | is_numeric %}{{ ca_1 | number_format(2, ',', ' ') }} {{ devise }}{% else %}{{ ca_1 }}{% endif %}</span>
            <br>
            {{ mois2p1 | trans }} {{ annee2p1 }}{{ "global.dots" | trans }}  <span>{% if ca_2 | is_numeric %}{{ ca_2 | number_format(2, ',', ' ') }} {{ devise }}{% else %}{{ ca_2 }}{% endif %}</span>
        </div>
        {% if progressionCa | is_numeric %}
        <div class="ventes-datas-content {% if progressionCa | number_format < 0 %}decline{% else %}increase{% endif %} pts">
            {% if progressionCa | number_format > 0 %}
                <i class="fal fa-smile-beam"></i>
            {% else %}
                <i class="fal fa-frown"></i>
            {% endif %}
            <span>
                {% if progressionCa | number_format > 0 %}+{% endif %}<strong>{{ progressionCa | number_format }}</strong>%
            </span>
        </div>
        {% endif %}
    </div>
    {% endif %}
    {% if periode == 2 %}
    <div class="p2">
        <div class="ventes-datas-content prm">
            {% set mois1p2 = periodes.LIB_PERIODE_TIMEZONE_MOIS_PREC_ANNEE | date('n') %}
            {% set mois1p2 = "global.moisAbrege." ~ mois1p2 %}
            {% set annee1p2 = periodes.LIB_PERIODE_TIMEZONE_MOIS_PREC_ANNEE | date('Y') %}
            {% set mois2p2 = periodes.LIB_PERIODE_TIMEZONE_MOIS_PREC_ANNEE_PREC | date('n') %}
            {% set mois2p2 = "global.moisAbrege." ~ mois2p2 %}
            {% set annee2p2 = periodes.LIB_PERIODE_TIMEZONE_MOIS_PREC_ANNEE_PREC | date('Y') %}
            {{ mois1p2 | trans }}  {{ annee1p2 }}{{ "global.dots" | trans }} <span>{% if ca_1 | is_numeric %}{{ ca_1 | number_format(2, ',', ' ') }} {{ devise }}{% else %}{{ ca_1 }}{% endif %}</span>
            <br>
            {{ mois2p2 | trans }} {{ annee2p2 }}{{ "global.dots" | trans }}  <span>{% if ca_2 | is_numeric %}{{ ca_2 | number_format(2, ',', ' ') }} {{ devise }}{% else %}{{ ca_2 }}{% endif %}</span>
        </div>
        {% if progressionCa | is_numeric %}
        <div class="ventes-datas-content {% if progressionCa | number_format < 0 %}decline{% else %}increase{% endif %} pts">
            {% if progressionCa | number_format > 0 %}
                <i class="fal fa-smile-beam"></i>
            {% else %}
                <i class="fal fa-frown"></i>
            {% endif %}
            <span>
                {% if progressionCa | number_format > 0 %}+{% endif %}<strong>{{ progressionCa | number_format }}</strong>%
            </span>
        </div>
        {% endif %}
    </div>
    {% endif %}
    {% if periode == 3 %}
    <div class="p3">
        <div class="ventes-datas-content prm">
            {{ "homepage.ventes.m12" | trans }}{{ "global.dots" | trans }} <span>{% if ca_1 | is_numeric %}{{ ca_1 | number_format(2, ',', ' ') }} {{ devise }}{% else %}{{ ca_1 }}{% endif %}</span>
            <br>
            {{ "homepage.ventes.periodePrec" | trans }}{{ "global.dots" | trans }} <span>{% if ca_2 | is_numeric %}{{ ca_2 | number_format(2, ',', ' ') }} {{ devise }}{% else %}{{ ca_2 }}{% endif %}</span>
        </div>
        {% if progressionCa | is_numeric %}
        <div class="ventes-datas-content {% if progressionCa | number_format < 0 %}decline{% else %}increase{% endif %} pts">
            {% if progressionCa | number_format > 0 %}
                <i class="fal fa-smile-beam"></i>
            {% else %}
                <i class="fal fa-frown"></i>
            {% endif %}
            <span>
                {% if progressionCa | number_format > 0 %}+{% endif %}<strong>{{ progressionCa | number_format }}</strong>%
            </span>
        </div>
        {% endif %}
    </div>
    {% endif %}
{% endif %}