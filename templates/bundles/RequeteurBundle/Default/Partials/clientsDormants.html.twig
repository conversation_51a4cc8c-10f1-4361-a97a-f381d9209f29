{% set mois1 = "now" | date_modify("-1 month") | date('n') %}
{% set mois1 = "global.mois." ~ mois1 %}
{% set annee1 = "now" | date_modify("-1 month") | date('Y') %}
{% set mois2 = "now" | date_modify("-1 month") | date_modify("-1 year") | date('n') %}
{% set mois2 = "global.mois." ~ mois2 %}
{% set annee2 = "now" | date_modify("-1 month") | date_modify("-1 year") | date('Y') %}
<div class="mbm">
    <div class="clients-datas-titre mbs hide-for-small-only">{{ "homepage.reseau.clientsDormants" | trans | raw }}</div>
    <div class="clients-datas-titre mbm hide-for-large hide-for-medium">{{ "homepage.reseau.clientsDormantsM" | trans | raw }}</div>
    {{ mois1 | trans }} {{ annee1 }}{{ "global.dots" | trans }} <span>{% if nbClientsDormants12mois1 | is_numeric %}{{ nbClientsDormants12mois1 | number_format(0, ',', ' ') }}{% else %}{{nbClientsDormants12mois1 }}{% endif %}</span><br>
    {{ mois2 | trans }} {{ annee2 }}{{ "global.dots" | trans }} <span>{% if nbClientsDormants12mois2 | is_numeric %}{{ nbClientsDormants12mois2 | number_format(0, ',', ' ') }}{% else %}{{nbClientsDormants12mois2 }}{% endif %}</span><br>
</div>
{% if progressionDormants | is_numeric %}
<div class="{% if progressionDormants | number_format < 0 %}increase{% else %}decline{% endif %}">
    {% if progressionDormants | number_format > 0 %}
        <i class="fal fa-frown"></i>
    {% else %}
        <i class="fal fa-smile-beam"></i>
    {% endif %}
    <span>
        {% if progressionDormants | number_format > 0 %}+{% endif %}<strong>{{ progressionDormants | number_format }}</strong>%
    </span>
</div>
{% endif %}
