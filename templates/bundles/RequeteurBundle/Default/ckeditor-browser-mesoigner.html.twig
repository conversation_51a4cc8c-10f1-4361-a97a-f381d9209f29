<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=1024, initial-scale=1">

    <title>{% block title %}{{ "global.pageTitle" | trans }}{% endblock %}</title>
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}" />

    {% block stylesheet_files %}
        {{ encore_entry_link_tags('css') }}
    {% endblock %}

    {% block stylesheets %}
         <link href="https://icones.mydataviz.fr/css/all.min.css" type="text/css" rel="stylesheet" />
    {% endblock %}


{% if not app.session.get('current_user_profil') is null %}
    <style type="text/css">
        .btn, .btn:hover, .btn:focus {
            font-size: 1em;
        }
        .btn.btn--more:focus {
            outline: none;
            opacity: 1;
        }
        .btn.btn--more:hover {
            outline: none;
            opacity: 0.7;
        }
    {% if app.session.get('current_user_profil')['couleurFond1'] is defined %}
        {% set couleurFond1 = app.session.get('current_user_profil')['couleurFond1'] %}
        .btn, .btn:hover, .btn:focus {
            background-color: {{ couleurFond1 }};
        }
    {% endif %}
    {% if app.session.get('current_user_profil')['couleurTexte1'] is defined %}
        {% set couleurTexte1 = app.session.get('current_user_profil')['couleurTexte1'] %}
        .btn, .btn:hover, .btn:focus {
            color: {{ couleurTexte1 }};
        }
    {% endif %}
        [type=text] {
            font-size: 1em;
        }
    </style>
{% endif %}
</head>
<body>
<div id="library" class="mll mrl">
    <h2>{{ 'global.librarySearch' | trans }}</h2>
    <div class="bloc-exergue ptl">
        <div class="pll">
            <form name="form-library" id="form-library">
                <input type="text" name="search" class="mtl">
                <input type="submit" name="searchMeSoigner" value="Rechercher" class="btn">
            </form>
            <div class="" id="js-mesoigner-container">
                <div class="js-mesoigner-content mtl" data-page="1" data-nb-pages=""></div>
                <div class="js-mesoigner-empty modal-errors error mbl" style="display: none;">{{ 'global.noResult' | trans }}</div>
                <div class="text-center">
                    <div id="pagination" class="text-center mtxl mbxxl btn-loader" style="display:none;" data-page="1" data-nb-pages="">
                        <a href="" class="btn btn--more next">{{ "global.chargerPlus" | trans }}</a>
                        <div class="la-ball-fall">
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                    </div>
            </div>
        </div>
    </div>
</div>
<template id="js-template-mesoigner">
    <div class="image-item"><a href="#" class="thumbnail js-image-link-biblio" data-url="{imgUrl}" data-mesoigner-save-images="{% if app.session.get('current_user_profil')['mesoigner_save_images'] is defined %}{{ app.session.get('current_user_profil')['mesoigner_save_images'] }}{% endif %}"><img src="{thumbUrl}"></a></div>
</template>

{% block javascripts %}
    {{ encore_entry_script_tags('ckeditor-images-mesoigner') }}
    <script src="{{ url('bazinga_jstranslation_js') }}" type="text/javascript"></script>
{% endblock %}

</body>
</html>
