{% import _self as macro %}
<div class="pageWidth page-container color-background">
    <div class="page-inner">
        <div class="operations">
            <div class="statut-switch">
                <div class="statut-switch-wrap">
                    <div class="multiswitch">
                        <div class="slide-container">
                            <input id="statut-dtz-switch-on" name="statut-dtz-switch" value="false" type="radio" checked>
                            <label for="statut-dtz-switch-on">{{ "homepage.operations.locales.enCours" | trans }}</label>
                            <input id="statut-dtz-switch-off" name="statut-dtz-switch" value="true" type="radio">
                            <label for="statut-dtz-switch-off">{{ "homepage.operations.locales.terminees" | trans }}</label>
                            <a class="slide" aria-hidden="true"></a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row collapse">
                <div class="column small-12 text-center large-text-left medium-text-left">
                    <h1>{{ "homepage.operations.locales.title" | trans }}</h1>
                </div>
                <div class="column small-12">
                    <div class="operations-container operations-locales-container show-for-large">
                        {{ macro.listeOperations(canAccessElasticDataviz, opEnCours, 1, tauxRetourActif) }}
                    </div>
                    <div class="operations-container operations-locales-container show-for-large hide">
                        {{ macro.listeOperations(canAccessElasticDataviz, opTerminees, 2, tauxRetourActif) }}
                    </div>
                    <div class="operations-container operations-locales-container op-en-cours hide-for-large">
                        {{ macro.listeOperationsMobile(canAccessElasticDataviz, opEnCours, 1, tauxRetourActif) }}
                    </div>
                    <div class="operations-container operations-locales-container op-terminees hide-for-large hide">
                        {{ macro.listeOperationsMobile(canAccessElasticDataviz, opTerminees, 2, tauxRetourActif) }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% macro listeOperations(canAccessElasticDataviz, operations, etat, tauxRetourActif) %}
	<div class="operations-list-wrap">
		{% if operations | length %}
		<table>
			<thead>
				<tr>
					<th class="operation-item-canal">{{ "homepage.operations.canaux" | trans }}</th>
					<th>{{ "homepage.operations.operation" | trans }}</th>
					{% if tauxRetourActif and app.user.canAccessDataviz and canAccessElasticDataviz %}
					<th class="operation-item-taux operation-item-exergue">{{ "homepage.operations.locales.taux" | trans }}</th>
					{% endif %}
					<th{% if not tauxRetourActif or not app.user.canAccessDataviz or not canAccessElasticDataviz %} class="operation-item-exergue"{% endif %}>{{ "homepage.operations.locales.contacts" | trans }}</th>
					<th>{{ "homepage.operations.debut" | trans }}</th>
					<th class="operation-item-dates">{{ "homepage.operations.fin" | trans }}</th>
				</tr>
			</thead>
			<tbody>
			{% set currentOp = operations[0].id %}
			{% set icoCanaux = '' %}
			{% set ico = {'email': '<i class="fal fa-at"></i>', 'sms': '<i class="fal fa-mobile-android"></i>', 'courrier': '<i class="fal fa-envelope"></i>', 'ftp': '<i class="fal fa-file-export"></i>', 'n/d': '<i class="fal fa-question"></i>', 'n/a': '<i class="fal fa-question"></i>'} %}
			{% for key,op in operations %}
				{% set libCanal = op.canal | lower %}
				{% if op.id == currentOp %}
					{% set icoCanaux = icoCanaux ~ ico[libCanal] %}
				{% else %}
					{% set icoCanaux = ico[libCanal] %}
				{% endif %}
				{% if operations[key+1] is not defined or (operations[key+1] is defined and op.id != operations[key+1].id) %}
					{% if libCanal == "n/d" or libCanal == "n/a" %}
						{% set libCanal = "" %}
					{% endif %}
				<tr class="operation-item{% if operations | length > 3 and operations | length - 1 == key %} show-more{% endif %}">
					<td class="operation-item-canal">
						<a href="{{ url("aquitem_requeteur_operation_mode_de_diffusion", {idOperation: op.id, canal: libCanal }) }}" class="link-description">
							{{ icoCanaux | raw }}
						</a>
					</td>
					<td class="operation-item-libelle">
						<a href="{{ url("aquitem_requeteur_operation_mode_de_diffusion", {idOperation: op.id, canal: libCanal }) }}" class="link-description">
							<span class="op-title">{{ op.libelle }}</span>
						</a>
					</td>
					{% if tauxRetourActif and app.user.canAccessDataviz and canAccessElasticDataviz %}
					<td class="operation-item-taux operation-item-exergue">
						{{ op.tauxRetour }}%
					</td>
					{% endif %}
					<td class="operation-item-selection{% if not tauxRetourActif or not app.user.canAccessDataviz or not canAccessElasticDataviz %} operation-item-exergue{% endif %}">
						{{ op.tailleSelection | number_format(0, ',', ' ') }}
					</td>
					<td class="operation-item-dates">
						{{ op.dateDebutOperation | date('d/m/Y') }}
					</td>
					<td class="operation-item-dates">
						{{ op.dateFinOperation | date('d/m/Y') }}
					</td>
				</tr>
				{% endif %}
				{% set currentOp = op.id %}
			{% endfor %}
			</tbody>
		</table>
		<div class="text-center mtl mbl">
			<a href="{{ url('aquitem_requeteur_operation', {type: 0}) }}" class="btn btn--black">{{ "homepage.operations.locales.toutes" | trans }}</a>
		</div>
		{% else %}
		<p class="ptl warning text-center">
			{% if etat == 1 %}
			{{ "operations.noOpEnCours" | trans }}
			{% elseif etat == 2 %}
			{{ "operations.noOpTerminees" | trans }}
			{% endif %}
		</p>
		<p class="text-center">
			<button href="#" class="btn btn--red btn-creer-operation mrn">{{ "operations.boutons.creerOp" | trans }}</button>
		</p>
		{% endif %}
	</div>
{% endmacro %}
{% macro listeOperationsMobile(canAccessElasticDataviz, operations, etat, tauxRetourActif) %}
    {% set nameEtat = "en-cours" %}
    {% if etat == 2 %}
        {% set nameEtat = "terminees" %}
    {% endif %}
    {% if operations | length %}
<div class="operations-for-mobile-wrap">
    <div class="op-slider operations-for-mobile mbm">
		<ul class="op-slider-wrapper">
		{% set currentOp = operations[0].id %}
		{% set icoCanaux = '' %}
		{% set total = 0 %}
		{% set ico = {'email': '<i class="fal fa-at"></i>', 'sms': '<i class="fal fa-mobile-android"></i>', 'courrier': '<i class="fal fa-envelope"></i>', 'ftp': '<i class="fal fa-file-export"></i>', 'n/d': '<i class="fal fa-question"></i>', 'n/a': '<i class="fal fa-question"></i>'} %}
		{% for key,op in operations %}
			{% set libCanal = op.canal | lower %}
			{% if op.id == currentOp %}
				{% set icoCanaux = icoCanaux ~ ico[libCanal] %}
			{% else %}
				{% set icoCanaux = ico[libCanal] %}
			{% endif %}
			{% if operations[key+1] is not defined or (operations[key+1] is defined and op.id != operations[key+1].id) %}
				{% if libCanal == "n/d" or libCanal == "n/a" %}
					{% set libCanal = "" %}
				{% endif %}
				{% set total = total + 1 %}
			<li id="slide-{{ etat }}-{{ key }}" class="op-slide op-slide-{{ nameEtat }} operation-item{% if operations | length > 3 and operations | length - 1 == key %} show-more{% endif %}">
				<div class="operation-item-line">
					<div class="operation-item-head">
						{{ "homepage.operations.canaux" | trans }}
					</div>
					<div class="operation-item-value">
						{{ icoCanaux | raw }}
					</div>
				</div>
				<div class="operation-item-line">
					<div class="operation-item-head">
						{{ "homepage.operations.operation" | trans }}
					</div>
					<div class="operation-item-value">
						{% set libelleTruncated = false %}
						{% set words = op.libelle | split(' ') %}
						{% if op.libelle | length > 15 %}
							{% set libelleTruncated = true %}
						{% endif %}
						{% set libelleOP = (op.libelle|length > 15 ? op.libelle|slice(0, 15) ~ '...' : op.libelle) %}
						<a href="{{ url("aquitem_requeteur_operation_mode_de_diffusion", {idOperation: op.id, canal: libCanal }) }}" class="link-description">
							<span class="op-title">{{ op.libelle }}</span>
						</a>
					</div>
				</div>
				{% if tauxRetourActif and app.user.canAccessDataviz and canAccessElasticDataviz %}
				<div class="operation-item-line operation-item-line-exergue">
					<div class="operation-item-head">
						{{ "homepage.operations.locales.taux" | trans }}
					</div>
					<div class="operation-item-value operation-item-taux operation-item-exergue">
						{{ op.tauxRetour }}%
					</div>
				</div>
				{% endif %}
				<div class="operation-item-line">
					<div class="operation-item-head">
						{{ "homepage.operations.locales.contacts" | trans }}
					</div>
					<div class="operation-item-value">
						{{ op.tailleSelection | number_format(0, ',', ' ') }}
					</div>
				</div>
				<div class="operation-item-line">
					<div class="operation-item-head">
						{{ "homepage.operations.debut" | trans }}
					</div>
					<div class="operation-item-value">
						{{ op.dateDebutOperation | date('d/m/Y') }}
					</div>
				</div>
				<div class="operation-item-line">
					<div class="operation-item-head">
						{{ "homepage.operations.fin" | trans }}
					</div>
					<div class="operation-item-value">
						{{ op.dateFinOperation | date('d/m/Y') }}
					</div>
				</div>
			</li>
			{% endif %}
				{% set currentOp = op.id %}
			{% endfor %}
		</ul>
	</div>
        <div class="op-slider-nav op-slider-nav-{{ nameEtat }}">
            <div class="op-slider-arrows text-right label-inline"><button class="op-slider-previous-{{ nameEtat }}" style="display:none;"><i class="fas fa-angle-left"></i></button></div>
            <div class="op-slider-compte label-inline"><span class="op-slider-current">1</span>/<span class="op-slider-total">{{ total }}</span></div>
            <div class="op-slider-arrows text-left label-inline"><button class="op-slider-next-{{ nameEtat }}"><i class="fas fa-angle-right"></i></button></div>
        </div>
	</div>
		<div class="text-center mtxl mbl">
			<a href="{{ url('aquitem_requeteur_operation', {type: 0}) }}" class="btn btn--black">{{ "homepage.operations.locales.toutes" | trans }}</a>
		</div>
	{% else %}
    <div class="">
		<p class="ptl warning text-center">
			{% if etat == 1 %}
			{{ "operations.noOpEnCours" | trans }}
			{% elseif etat == 2 %}
			{{ "operations.noOpTerminees" | trans }}
			{% endif %}
		</p>
		<p class="text-center">
			<button href="#" class="btn btn--red btn-creer-operation mrn">{{ "operations.boutons.creerOp" | trans }}</button>
		</p>
    </div>
	{% endif %}
{% endmacro %}
