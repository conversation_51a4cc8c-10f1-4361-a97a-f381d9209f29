<template id="selection-explorer-horsfid-form-tpl">
    <div class="explore relative">
        <div class="loaderWindow">
            <div class="loaderWheel"></div>
        </div>
        {% if explorationHorsFid is defined and explorationHorsFid | length %}
            <div class="explore-container">
                <table id="explore-table" class="table">
                    <thead>
                    <tr>
                        {% for colonne in colonnesHorsFid %}
                            <th class="headCol"><i>{{ colonne.nom }} ({{ "horsFid.titles.tag" | trans }} {{ colonne.valeurs[0].libelle }})</i></th>
                        {% endfor %}
                    </tr>
                    </thead>
                    <tbody>
                    {% for key,datas in explorationHorsFid %}
                        {% if key > 0 %}
                            <tr>
                            {% if datas is iterable %}
                                {% for data in datas %}
                                    <td>{{ data }}</td>
                                {% endfor %}
                            {% else %}
                                <td>{{ datas }}</td>
                            {% endif %}
                            </tr>
                        {% endif %}
                    {% endfor %}
                    </tbody>
                </table>
            </div>
            {% if selection.tailleCalcul > (maxResultats+1) %}
                <div id="pagination" class="mtl" data-id-selection="{{ selection.id }}" data-nb="{{ maxResultats }}" data-total="{{ selection.tailleCalcul }}">
                    <div id="pagination-nav" class="row" data-page="1" data-new-pos="">
                        <div class="column large-6 medium-6 small-12 text-right">
                            <a class="btn btn--smallBlue prev hide" href="#" data-pos="0">{{ "operations.modeles.perso.pagination.less" | trans }}</a>
                        </div>
                        <div class="column large-6 medium-6 small-12">
                            <a class="btn btn--smallBlue next" href="#" data-pos="{{ maxResultats }}">{{ "operations.modeles.perso.pagination.more" | trans }}</a>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% else %}
            {% if errorExplore is defined and errorExplore | length %}
                <div class="modal-errors">
                    {{ errorExplore }}
                </div>
            {% endif %}
        {% endif %}
    </div>
</template>
