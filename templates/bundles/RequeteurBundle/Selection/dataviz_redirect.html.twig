{% extends 'base.html.twig' %}

{% block body %}
    <div class="row collapse table relative criterium-inactive">
        <div id="content" class="text-center mtxxl">
            <h1>{{ "dataviz.loading"|trans }}</h1>
            <div class="pageWidth page-container">
                <form id="external-login" method="POST" action="{{ url }}">
                    {% for name, value in parameters %}
                        <input type="hidden" name="{{ name }}" value="{{ value }}">
                    {% endfor %}
                </form>
            </div>
        </div>
    </div>
    <script type="text/javascript">
		document.getElementById('external-login').submit();
    </script>
{% endblock %}
