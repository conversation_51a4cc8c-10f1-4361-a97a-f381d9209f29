{% if app.session.get('current_user_profil')['langue'] is defined %}
    {% set user_locale_langue = app.session.get('current_user_profil')['langue'] %}
{% else %}
    {% set user_locale_langue = "fr" %}
{% endif %}
{% set canaux = operation.valeurs[0].valeurs %}
{% set budgetTotal = operation.budgetOperation %}
{% set monthTexteFr = {
    "1":"global.mois.janvier" | trans ({}, "messages", user_locale_langue),
    "2":"global.mois.fevrier" | trans ({}, "messages", user_locale_langue),
    "3":"global.mois.mars" | trans ({}, "messages", user_locale_langue),
    "4":"global.mois.avril" | trans ({}, "messages", user_locale_langue),
    "5":"global.mois.mai" | trans ({}, "messages", user_locale_langue),
    "6":"global.mois.juin" | trans ({}, "messages", user_locale_langue),
    "7":"global.mois.juillet" | trans ({}, "messages", user_locale_langue),
    "8":"global.mois.aout" | trans ({}, "messages", user_locale_langue),
    "9":"global.mois.septembre" | trans ({}, "messages", user_locale_langue),
    "10":"global.mois.octobre" | trans ({}, "messages", user_locale_langue),
    "11":"global.mois.novembre" | trans ({}, "messages", user_locale_langue),
    "12":"global.mois.decembre" | trans ({}, "messages", user_locale_langue)} %}
<div class="operation-tpl">
    <template id="operation-infos-tpl">
        <h3 class="recap-op"></h3>
        <div id="infos-operation">
            <div class="input-group">
                <div class="row">
                    <div class="small-4 columns">
                        <div class="inline text-right">{{ "operations.titles.nomOp" | trans ({}, "messages", user_locale_langue) }}</div>
                    </div>
                    <div class="small-8 columns">
                        <div class="input-text" id="infos-operation-name"><span class="operation-lib">{{ operation.libelle }}</span> (<span class="operation-id">{{ operation.id }}</span>)</div>
                    </div>
                </div>
            </div>
            <div class="input-group">
                <div class="row">
                    <div class="small-4 columns">
                        <div class="inline text-right">{{ "operations.titles.description" | trans ({}, "messages", user_locale_langue) }}</div>
                    </div>
                    <div class="small-8 columns">
                        <div class="input-text" id="infos-operation-desc">{% if operation.description == "" %}<em>{{ 'global.vide2' | trans ({}, "messages", user_locale_langue) }}</em>{% else %}{{ operation.description | nl2br }}{% endif %}</div>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <template id="operation-annuler-canal-form-tpl">
        <form id="operation-annuler-canal-form">
            <div class="input-group">
                <div id="operation-annuler-canal">
                    {{ "operations.actions.confirmAnnulerCanalOperation" | trans ({}, "messages", user_locale_langue) }} <span class="libCanal"></span> ?
                </div>
            </div>
            <input type="hidden" name="annuler-canal-id" id="annuler-canal-id">
        </form>
    </template>

    <template id="operation-delete-form-tpl">
        <form id="operation-delete-form">
            <div class="input-group">
                <div id="operation-suppression">
                    {{ "operations.actions.confirmSuppression" | trans ({}, "messages", user_locale_langue) }} "<span></span>" ?<br /><b>{{ "operations.titles.warningDelete" | trans ({}, "messages", user_locale_langue) }}</b>
                </div>
            </div>
            <input type="hidden" name="delete-operation-id" id="delete-operation-id">
        </form>
    </template>

    <template id="operation-modifier-statut-tpl">
        <form id="operation-modifier-statut-form-tpl">
            <div class="input-group">
                <div id="operation-modifier-statut">
                    {{ "operations.actions.confirmModifierStatutOperation" | trans ({}, "messages", user_locale_langue) | raw }}
                </div>
            </div>
        </form>
    </template>

    <template id="operation-modify-form-tpl">
        <form id="operation-modify-form">
            <div class="input-group">
                <p class="required-legende">{{ "operations.titles.required1" | trans ({}, "messages", user_locale_langue) }} <span class="required">*</span> {{ "operations.titles.required2" | trans ({}, "messages", user_locale_langue) }}</p>
                <div class="row collapse">
                    <div class="small-3 columns">
                        <label for="modify-operation-name" class="inline text-left"><span class="required">*</span> {{ "operations.titles.nomOp" | trans ({}, "messages", user_locale_langue) }}</label>
                    </div>
                    <div class="small-9 columns">
                        <input class="input-text" type="text" id="modify-operation-name" name="modify-operation-name" value="{{ operation.libelle }}" maxlength="{% if app.session.get('current_user_profil')['limite_nom_operation'] is defined %}{{ app.session.get('current_user_profil')['limite_nom_operation'] }}{% endif %}" data-maxlength="{% if app.session.get('current_user_profil')['limite_nom_operation'] is defined %}{{ app.session.get('current_user_profil')['limite_nom_operation'] }}{% endif %}">
                        {% if app.session.get('current_user_profil')['limite_nom_operation'] is defined %}
                        <i>{{ "operations.nomInfo" | trans }} {{ app.session.get('current_user_profil')['limite_nom_operation'] }} {{ "global.caracteres" | trans }}.</i>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="input-group">
                <div class="row collapse">
                    <div class="small-3 columns">
                        <label for="modify-operation-desc" class="inline text-left">{{ "operations.titles.description" | trans ({}, "messages", user_locale_langue) }}</label>
                    </div>
                    <div class="small-9 columns">
                        <textarea class="input-text" rows="5" id="modify-operation-desc" name="modify-operation-desc">{{ operation.description }}</textarea>
                    </div>
                </div>
            </div>
            <input type="hidden" name="modify-operation-id" id="modify-operation-id">
        </form>
    </template>

    {% if (operation.dateDebutOperation and operation.dateFinOperation) or operation.idSelection != "" or budgetTotal > 0 %}
    <template id="operation-recap-tpl">
        {% if operation.dateDebutOperation and operation.dateFinOperation %}
            <div class="modal-body">
                {% if operation.dateDebutOperation %}
                    <div class="input-group">
                        <div class="row">
                            <div class="small-4 columns">
                                <div class="inline text-right">{{ "operations.titles.dateDebut" | trans ({}, "messages", user_locale_langue) }}</div>
                            </div>
                            <div class="small-8 columns">
                                <div class="input-text" id="infos-operation-debut">
                                    {{ operation.dateDebutOperation | date('d/m/Y') }}
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}
                {% if operation.dateFinOperation %}
                    <div class="input-group">
                        <div class="row">
                            <div class="small-4 columns">
                                <div class="inline text-right">{{ "operations.titles.dateFin" | trans ({}, "messages", user_locale_langue) }}</div>
                            </div>
                            <div class="small-8 columns">
                                <div class="input-text" id="infos-operation-fin">
                                    {{ operation.dateFinOperation | date('d/m/Y') }}
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        {% endif %}
        {% if operation.idSelection != "" %}
            <div class="modal-body">
                <div class="input-group">
                    <div class="row">
                        <div class="small-4 columns">
                            <div class="inline text-right">
								{% if operation.libelleSelection != "operations.selection.selectionPerso" | trans
									and operation.libelleSelection != "operations.selection.selectionAvanceePerso" | trans %}
									{{ "operations.titles.selection" | trans({}, "messages", user_locale_langue) }}
								{% endif %}
								{% set libSelection = operation.libelleSelection %}
                                <strong><span class="input-text" id="infos-operation-selection">{{ libSelection }}</span></strong> :
                            </div>
                        </div>
                        <div class="small-8 columns">
                            <div class="input-text" id="infos-operation-comptage"><strong>{{ comptage | number_format(0, ',', ' ') }}</strong> ({{ dateComptage | date('d/m/Y H:i') }})</div>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        {% if budgetTotal > 0 %}
            <div class="modal-body">
                <div class="input-group">
                    <div class="row">
                        <div class="small-4 columns">
                            <div class="inline text-right">
                               {{ "operations.titles.budget" | trans({}, "messages", user_locale_langue) }}
                            </div>
                        </div>
                        <div class="small-8 columns">
                            <div class="input-text" id="infos-operation-budget">{{ budgetTotal | number_format(2, ',', ' ') }} €</div>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
    </template>
    {% endif %}

    <template id="operation-action-modifier-tpl">
        <div id="operation-modifier-statut">
            <h3>{{ "operations.titles.modifierStatut" | trans({}, "messages", user_locale_langue) }}</h3>
            <p>{{ "operations.titles.txtModifierStatut" | trans({}, "messages", user_locale_langue) | raw }}</p>
        </div>
    </template>

    <template id="operation-action-supprimer-tpl">
        <div id="operation-modifier-statut">
            <h3>{{ "operations.titles.supprimerOperation" | trans({}, "messages", user_locale_langue) }}</h3>
            <p>{{ "operations.titles.txtSupprimerOperation" | trans({}, "messages", user_locale_langue) | raw }}</p>
            <div class="text-center mbs">
                <button class="btn-gerer-supprimer-operation btn-supprimer-bouton btn btn--red" data-operation-id="{{ operation.id }}" data-operation-libelle="{{ operation.libelle }}" >
                    {{ "operations.boutons.supprimer" | trans({}, "messages", user_locale_langue) }}
                </button>
            </div>
        </div>
    </template>

    <template id="operation-action-annuler-tpl">
        {% if canaux|length %}
            <div id="operation-status">
                <h3 class="diffusions-title">{{ "operations.titles.diffusions" | trans({}, "messages", user_locale_langue) }}</h3>
                <input type="hidden" name="annuler-canal-id" id="annuler-canal-id">
                <ul id="operation-canaux" class="nav">
                    {% for key,canal in canaux %}
                        <li class="canal-statut row mbm" data-id-canal="{{ canal.idCanal }}">
                            <div class="large-4 medium-12 columns{% if canal.dateEnvoi == "" %} mts{% endif %}">
                                {{ canal.libelleCanal | upper }}
                                {% if canal.idDiffusion != "" %} ({{ canal.idDiffusion }}) :{% endif %}
                                {% if canal.dateEnvoi != "" %}
                                    {% if canal.codeMailing | length %}<br><small>{{ "operations.diffusion.codeMailing" | trans({}, "messages", user_locale_langue) }} {{ canal.codeMailing }}</small>{% endif %}
                                    <br><small>{{ "operations.diffusionLe" | trans({}, "messages", user_locale_langue) }}{{ canal.dateEnvoi | date('d/m/Y H:i')}}</small>
                                {% endif %}
                            </div>
                            <div class="large-8 medium-12 columns">
                                {% set libStatutTrans = "operations.statuts." ~ canal.idStatut %}
                                <span><span class="operations-list-statut operations-list-statut--{{ canal.idStatut }}">{{ libStatutTrans | trans({}, "messages", user_locale_langue) }}</span></span>
								<span> {{ canal.tailleSelectionnee | number_format(0, ',', ' ') }} contacts</span>
                                {% if canal.statistiqueDelivrabiliteDisponible %}
                                    <a class="btn-stats btn btn-edit btn-stats-series mbm mll" href="{{ url('aquitem_webservice_telechargerStatistiques', {canal: canal.libelleCanal | lower, idOperation: operation.id}) }}" title="{{ "operations.titles.downloadStats" | trans({}, "messages", user_locale_langue) }}" target="_blank">
                                        <svg class="svg svg--color" viewBox="0 0 30 30" width="22" height="24" alt="">
                                            <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#file-download') }}"></use>
                                        </svg>
                                        {{ "operations.titles.stats" | trans({}, "messages", user_locale_langue) }}
                                    </a>
                                {% endif %}
                            </div>
                        </li>
                    {% endfor %}
                </ul>
                <div class="text-center mtxl">
                    {% if operation.batDisponible == "true" and operation.idStatutOperation != "4" %}
                        <a class="download-bat btn btn--green mbs" href="{{ url('aquitem_webservice_telechargerBat', {idOperation: operation.id}) }}" target="_blank">{{ "operations.diffusion.bat" | trans({}, "messages", user_locale_langue) }}</a>
                    {% elseif operation.commandable == "true" %}
                        <a class="download-bat btn btn--green mbs" href="{{ url('aquitem_webservice_telechargerProjetDevisBat', {idOperation: operation.id}) }}" target="_blank">{{ "operations.diffusion.devis" | trans({}, "messages", user_locale_langue) }}</a>
                    {% endif %}
                </div>
            </div>
        {% endif %}
    </template>
</div>
