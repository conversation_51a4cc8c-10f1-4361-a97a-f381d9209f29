{% extends 'base.html.twig' %}
{% block title %}{{ "modeles.titles.modele" | trans({}, "messages", user_locale_langue) }} {{ libelle }} - {{ "operations.modeles.perso.menu.img" | trans({}, "messages", user_locale_langue) }}{% endblock %}
{% block mtitle %}{{ "modeles.titles.modele" | trans({}, "messages", "fr") }} {{ libelle }} - {{ "operations.modeles.perso.menu.img" | trans({}, "messages", "fr") }}{% endblock %}
{% block body %}
	{% block content %}
	
		{% set personalLibraryName = "" %}
		{% set commonLibraryName = "" %}
		{% if app.session.get('current_user_profil')['common_library_folder'] is defined %}
			{% if app.session.get('current_user_profil')['common_library_folder'] != "" %}
				{% set commonLibraryName = "operations.titles.maBibliotheque" | trans %}
				{% if app.session.get('current_user_profil')['common_library_name'] is defined %}
					{% if app.session.get('current_user_profil')['common_library_name'] != "" %}
						{% set commonLibraryName = app.session.get('current_user_profil')['common_library_name'] %}
					{% endif %}
				{% endif %}
			{% endif %}
		{% endif %}

		{% if app.session.get('current_user_profil')['personal_library'] is defined %}
			{% if app.session.get('current_user_profil')['personal_library'] == "true" %}
				{% set personalLibraryName = "operations.titles.mesImages" | trans %}
				{% if app.session.get('current_user_profil')['personal_library_name'] is defined %}
					{% if app.session.get('current_user_profil')['personal_library_name'] != "" %}
						{% set personalLibraryName = app.session.get('current_user_profil')['personal_library_name'] %}
					{% endif %}
				{% endif %}
			{% elseif app.session.get('current_user_profil')['personal_library'] == "false" %}
				{% if commonLibraryName != "" %}
					{% set personalLibraryName = commonLibraryName %}
				{% endif %}
			{% endif %}
		{% else %}
			{% set personalLibraryName = "operations.titles.mesImages" | trans %}
		{% endif %}

		<div id="operation-personnalisation-modele-email" class="personnaliser-gestion-modele-email-editor"
			 data-idmodele="{{ idModele }}"
			 data-isBlank="{{ isBlank }}"
			 data-prefixetag="{{ prefixeTag }}"
			 data-suffixetag="{{ suffixeTag }}"
			 data-prefixedesinscription="{{ prefixeDesinscription }}"
			 data-suffixedesinscription="{{ suffixeDesinscription }}"
			 data-prefixemiroir="{{ prefixeMiroir }}"
			 data-suffixemiroir="{{ suffixeMiroir }}"
			 data-email-emetteur="{{ modele.emailFrom.valeur }}"
			 data-nom-emetteur="{{ modele.emetteurFrom.valeur }}"
			 data-sujet="{{ modele.sujet.valeur }}"
			 data-header-html="{{ modele.headerHTML.valeur }}"
			 data-alltags="{{ allTags | json_encode() }}"
			 data-selecttags="{{ defaultTags | json_encode() }}"
			 data-datetags="{{ dateTags | json_encode() }}"
			 data-linkthemes="{{ themesPourUrlsTrackables | json_encode() }}"
			 data-unsubscribelinks="{{ modele.lesPagesDeDesinscription.valeurs | json_encode() }}"
			 data-message-html="{{ modele.messageHTML.valeur }}"
			 data-donnees-json="{{ modele.donneesJson.valeur }}"
			 data-editeur="{{ modele.typeEditeur.valeur }}"
			 data-urlstracees="{{ urlsTracees | json_encode() }}"
			 data-modele-lib="{{ libelle }}"
			 data-pnr-library="{% if app.session.get('current_user_profil')['pnr_library_url'] is defined and app.session.get('current_user_profil')['pnr_library_key'] is defined %}true{% else %}false{% endif %}"
			 data-pnr-library-name="{% if app.session.get('current_user_profil')['pnr_library_name'] is defined %}{{ app.session.get('current_user_profil')['pnr_library_name'] }}{% else %}{{ "operations.titles.maBibliothequePnr" | trans }}{% endif %}"
			 data-mesoigner-library="{% if app.session.get('current_user_profil')['mesoigner_library_url'] is defined and app.session.get('current_user_profil')['mesoigner_library_key'] is defined %}true{% else %}false{% endif %}"
			 data-mesoigner-library-name="{% if app.session.get('current_user_profil')['mesoigner_library_name'] is defined %}{{ app.session.get('current_user_profil')['mesoigner_library_name'] }}{% else %}{{ "operations.titles.maBibliothequeMesoigner" | trans }}{% endif %}"
			 data-mesoigner-library-url="{% if app.session.get('current_user_profil')['mesoigner_library_url'] is defined %}{{ app.session.get('current_user_profil')['mesoigner_library_url'] }}{% endif %}"
			 data-personal-library="{% if app.session.get('current_user_profil')['personal_library'] is defined %}{{ app.session.get('current_user_profil')['personal_library'] }}{% else %}true{% endif %}"
			 data-personal-library-name="{{ personalLibraryName }}"
			 data-common-library="{% if app.session.get('current_user_profil')['common_library_folder'] is defined %}{{ app.session.get('current_user_profil')['common_library_folder'] }}{% endif %}"
			 data-common-library-name="{{ commonLibraryName }}"
			 data-mesoigner-save-images="{% if app.session.get('current_user_profil')['mesoigner_save_images'] is defined %}{{ app.session.get('current_user_profil')['mesoigner_save_images'] }}{% endif %}">
			<h2>{{ "modeles.titles.modele" | trans }} {{ libelle }}</h2>
			<div class="btn-loader mlm">
				<a href="#" class="btn-submit btn btn--blue btn--largePadding" type="button">{{ "global.save" | trans }}</a>
			</div>
			<a href="{{ url('aquitem_requeteur_modele') }}" class="cross-cancel cross-cancel-top" data-close aria-label="Close modal"><svg viewBox="0 0 31.3 21" width="35" height="35" class="svg svg--red"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="/img/sprite-svg.svg#close-plain"></use></svg></a>
			
			{# Onglets #}
			{% include '@Requeteur/Modele/Partials/onglets.html.twig' %}

			{# Contenu #}
			<div class="tabs-content" data-tabs-content="tabs-editor">
				<div class="modal-body-perso is-active" id="img-assign" role="tabpanel" aria-hidden="true" aria-labelledby="img-assign-label">
					<div class="row noElements hide">
						<div class="modal-errors mbs">
							<div class="error">{{ "operations.empty.img"|trans }}</div>
						</div>
					</div>
					<div id="baseImgGroup" class="hide">
						<div class="img-group">
							<div class="column large-12">
								<input type="hidden" value="" data-key=""/>
								<div class="column large-1">
									<img class="img-assign-preview" src="" alt="">
								</div>
								<div class="column large-11">
									<div class="img-assign-group">
										<div class="column large-6 medium-12">
											<div class="linksName input-group">
												<label class="">{{ "img.form.source" | trans }} :</label>
												<input class="img-assign-url img-assign-url-source" type="text" value="" data-key=""/>
											</div>
										</div>
										<div class="column large-6 medium-12 add-tags">
											<label for="tagsEmail">{{ "operations.modeles.perso.listeTags" | trans }}</label>
											<select id="tagsEmail">
												{% for tag in allTags %}
													<option value="{{ tag.id }}">{{ tag.libelle }}</option>
												{% endfor %}
											</select>
											<button type="button" id="ajoutTag" class="btn btn--red">{{ "global.add" | trans }}</button>
										</div>
									</div>
									<div class="img-assign-group">
										<div class="column large-6 medium-12">
											<div class="linksName input-group">
												<label class="">{{ "img.form.link" | trans }} :</label>
												<input class="img-assign-url img-assign-url-link" type="text" value="" data-key=""/>
												<button type="button" class="btn btn-edit mll fileLink" data-key="">{{ "operations.titles.mesFichiers" | trans }}</button>
											</div>
										</div>
										<div class="column large-6 medium-12 add-tags">
											<label for="tagsEmail">{{ "operations.modeles.perso.listeTags" | trans }}</label>
											<select id="tagsEmail">
												{% for tag in allTags %}
													<option value="{{ tag.id }}">{{ tag.libelle }}</option>
												{% endfor %}
											</select>
											<button type="button" id="ajoutTag" class="btn btn--red">{{ "global.add" | trans }}</button>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div id="imgGroup"></div>
				</div>
			</div>

			<div class="ptl text-right">
				<div class="btn-loader mls">
					<a href="#" class="btn-submit btn btn--blue btn--largePadding" type="button">{{ 'global.save' | trans }}</a>
				</div>
			</div>
		</div>

		<template id="perso-save-form-tpl">
			<form id="perso-save-form">
				<div class="input-group mll">
					<div class="alert hide">{{ "operations.actions.alertSavePerso" | trans | raw }}</div>
				</div>
			</form>
		</template>
	{% endblock %}
{% endblock %}

{% block javascripts %}
	{{ encore_entry_script_tags('operation-modele-email-images') }}
{% endblock %}
