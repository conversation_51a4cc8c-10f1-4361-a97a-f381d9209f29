{% extends 'base.html.twig' %}
{# {% block title %}{{ "modeles.titles.modele" | trans({}, "messages", user_locale_langue) }} {{ libelle }} - {{ "operations.modeles.perso.menu.tabs.edition" | trans({}, "messages", user_locale_langue) }}{% endblock %}
{% block mtitle %}{{ "modeles.titles.modele" | trans({}, "messages", "fr") }} {{ libelle }} - {{ "operations.modeles.perso.menu.tabs.edition" | trans({}, "messages", "fr") }}{% endblock %} #}
{% block stylesheets %}
	{{ parent() }}
	{{ encore_entry_link_tags('css-emojis') }}
    <link href="https://icones.mydataviz.fr/css/all.min.css" type="text/css" rel="stylesheet" />
{% endblock %}
{% block body %}
	{% block content %}
	
		{% set personalLibraryName = "" %}
		{% set commonLibraryName = "" %}
		{% if app.session.get('current_user_profil')['common_library_folder'] is defined %}
			{% if app.session.get('current_user_profil')['common_library_folder'] != "" %}
				{% set commonLibraryName = "operations.titles.maBibliotheque" | trans %}
				{% if app.session.get('current_user_profil')['common_library_name'] is defined %}
					{% if app.session.get('current_user_profil')['common_library_name'] != "" %}
						{% set commonLibraryName = app.session.get('current_user_profil')['common_library_name'] %}
					{% endif %}
				{% endif %}
			{% endif %}
		{% endif %}

		{% if app.session.get('current_user_profil')['personal_library'] is defined %}
			{% if app.session.get('current_user_profil')['personal_library'] == "true" %}
				{% set personalLibraryName = "operations.titles.mesImages" | trans %}
				{% if app.session.get('current_user_profil')['personal_library_name'] is defined %}
					{% if app.session.get('current_user_profil')['personal_library_name'] != "" %}
						{% set personalLibraryName = app.session.get('current_user_profil')['personal_library_name'] %}
					{% endif %}
				{% endif %}
			{% elseif app.session.get('current_user_profil')['personal_library'] == "false" %}
				{% if commonLibraryName != "" %}
					{% set personalLibraryName = commonLibraryName %}
				{% endif %}
			{% endif %}
		{% else %}
			{% set personalLibraryName = "operations.titles.mesImages" | trans %}
		{% endif %}

		<div id="operation-personnalisation-modele-email" class="personnaliser-gestion-modele-email-editor"
			 data-idmodele="{{ idModele }}"
			 data-isBlank="{{ isBlank }}"
			 data-prefixetag="{{ prefixeTag }}"
			 data-suffixetag="{{ suffixeTag }}"
			 data-prefixedesinscription="{{ prefixeDesinscription }}"
			 data-suffixedesinscription="{{ suffixeDesinscription }}"
			 data-prefixemiroir="{{ prefixeMiroir }}"
			 data-suffixemiroir="{{ suffixeMiroir }}"
			 data-email-emetteur="{{ modele.emailFrom.valeur }}"
			 data-nom-emetteur="{{ modele.emetteurFrom.valeur }}"
			 data-sujet="{{ modele.sujet.valeur }}"
			 data-header-html="{{ modele.headerHTML.valeur }}"
			 data-alltags="{{ allTags | json_encode() }}"
			 data-selecttags="{{ defaultTags | json_encode() }}"
			 data-datetags="{{ dateTags | json_encode() }}"
			 data-linkthemes="{{ themesPourUrlsTrackables | json_encode() }}"
			 data-unsubscribelinks="{{ modele.lesPagesDeDesinscription.valeurs | json_encode() }}"
			 data-message-html="{{ modele.messageHTML.valeur }}"
			 data-donnees-json="{{ modele.donneesJson.valeur }}"
			 data-editeur="{{ modele.typeEditeur.valeur }}"
			 data-urlstracees="{{ urlsTracees | json_encode() }}"
			 data-modele-lib="{{ libelle }}"
			 data-pnr-library="{% if app.session.get('current_user_profil')['pnr_library_url'] is defined and app.session.get('current_user_profil')['pnr_library_key'] is defined %}true{% else %}false{% endif %}"
			 data-pnr-library-name="{% if app.session.get('current_user_profil')['pnr_library_name'] is defined %}{{ app.session.get('current_user_profil')['pnr_library_name'] }}{% else %}{{ "operations.titles.maBibliothequePnr" | trans }}{% endif %}"
			 data-mesoigner-library="{% if app.session.get('current_user_profil')['mesoigner_library_url'] is defined and app.session.get('current_user_profil')['mesoigner_library_key'] is defined %}true{% else %}false{% endif %}"
			 data-mesoigner-library-name="{% if app.session.get('current_user_profil')['mesoigner_library_name'] is defined %}{{ app.session.get('current_user_profil')['mesoigner_library_name'] }}{% else %}{{ "operations.titles.maBibliothequeMesoigner" | trans }}{% endif %}"
			 data-mesoigner-library-url="{% if app.session.get('current_user_profil')['mesoigner_library_url'] is defined %}{{ app.session.get('current_user_profil')['mesoigner_library_url'] }}{% endif %}"
			 data-personal-library="{% if app.session.get('current_user_profil')['personal_library'] is defined %}{{ app.session.get('current_user_profil')['personal_library'] }}{% else %}true{% endif %}"
			 data-personal-library-name="{{ personalLibraryName }}"
			 data-common-library="{% if app.session.get('current_user_profil')['common_library_folder'] is defined %}{{ app.session.get('current_user_profil')['common_library_folder'] }}{% endif %}"
			 data-common-library-name="{{ commonLibraryName }}"
			 data-mesoigner-save-images="{% if app.session.get('current_user_profil')['mesoigner_save_images'] is defined %}{{ app.session.get('current_user_profil')['mesoigner_save_images'] }}{% endif %}">
			<h2>{{ "modeles.titles.modele" | trans }} {{ libelle }}</h2>
			<div class="btn-loader mlm">
				<a href="#" class="btn-submit btn btn--blue btn--largePadding" type="button">{{ "global.save" | trans }}</a>
			</div>
			<a href="{{ url('aquitem_requeteur_modele') }}" class="cross-cancel cross-cancel-top" data-close aria-label="Close modal"><svg viewBox="0 0 31.3 21" width="35" height="35" class="svg svg--red"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="/img/sprite-svg.svg#close-plain"></use></svg></a>
			
			{# Onglets #}
			{% include '@Requeteur/Modele/Partials/onglets.html.twig' %}

			{# Contenu #}
			<div class="tabs-content" data-tabs-content="tabs-editor">
				<div class="modal-body-editor is-active" id="email-editor" role="tabpanel" aria-hidden="false" aria-labelledby="email-editor-label">
					<div class="row">
						<div class="column small-12 text-center mbl">
							<button type="button" id="ckeditor-toggle-editor" class="btn btn-edit modePrevisu" data-mode="0"><span>{{ "modeles.form.editorModeHTML"|trans }}</span>
								<span class="hover-description">{{ "operations.infos.modeHtml" | trans }}</span>
							</button>
							<button type="button" id="ckeditor-toggle-textarea" class="btn btn-edit modePrevisu" data-mode="1"><span>{{ "modeles.form.editorMode"|trans }}</span>
								<span class="hover-description">{{ "operations.infos.modeEditeur" | trans }}</span>
							</button>
						</div>
						<div class="column">
							{{ form_start(form) }}
							{{ form_widget(form) }}
							{{ form_end(form) }}
							<div class="accordion hide mbl">
								<button id="btn-import-file" class="btn btn--red">
									<span>{{ "operations.titles.importFile"|trans }}</span>
								</button>
							</div>
						</div>
					</div>
					<div>
						<input id="blank" name="blank" type="checkbox" {% if isBlank is same as("true") %} checked="checked" {% endif %} value="{% if isBlank is same as("true") %}true{% else %}false{% endif %}" /> <label for="blank">{{ "modeles.form.blank" | trans }}</label>
					</div>
				</div>
			</div>
			<div class="ptl text-right">
				<div class="btn-loader mls">
					<a href="#" class="btn-submit btn btn--blue btn--largePadding" type="button">{{ "global.save" | trans }}</a>
				</div>
			</div>
		</div>
		
		<template id="pre-header-tpl">
			<div>
				<div class="pre-header-text"
					style="display:none !important;mso-hide: all;font-size:1px;color:#333333;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>
			</div>
		</template>

		<template id="perso-save-form-tpl">
			<form id="perso-save-form">
				<div class="input-group mll">
					<div class="alert hide">{{ "operations.actions.alertSavePerso" | trans | raw }}</div>
				</div>
			</form>
		</template>

		<template id="ckeditor-import">
			<div id="collapsible-import-file" class="mbl">
				<label for="fileupload">{{ "operations.titles.importLocal"|trans }}</label>
				<input id="fileupload" type="file" name="files[]">
				<br>
				<label for="fileupload">{{ "operations.titles.importExterne"|trans }}</label>
				<input id="import-html-url" type="url" placeholder="URL" required>
			</div>
		</template>

	{% endblock %}
{% endblock %}

{% block javascripts %}
	{{ encore_entry_script_tags('operation-modele-email-ckeditor') }}
{% endblock %}

