<ul class="tabs modal-link-list-editor" id="tabs-editor">
	<li class="tabs-title{% if action == "editeur" %} is-active{% endif %}" role="presentation">
		<a href="{{ path('aquitem_requeteur_personnaliser_modele_email_editor', {'idModele':idModele}) }}" id="email-editor-label">
			<i class="fas fa-edit" aria-hidden="true"></i>
			<span>{{ "operations.modeles.perso.menu.tabs.edition" | trans }}</span>
		</a>
	</li>
	{% if typeEditeur == 1 or (not isBlank and typeEditeur == 0 and don<PERSON><PERSON>son | length) %}
		<li class="tabs-title{% if action == "images" %} is-active{% endif %}" role="presentation">
			<a href="{{ path('aquitem_requeteur_personnaliser_modele_email_editor', {'idModele':idModele, 'action': 'images'}) }}" id="img-assign-label">
				<i class="fas fa-image" aria-hidden="true"></i>
				<span>{{ "operations.modeles.perso.menu.tabs.img" | trans }}</span>
			</a>
		</li>
	{% endif %}
	<li class="tabs-title{% if action == "parametres" %} is-active{% endif %}" role="presentation">
		<a href="{{ path('aquitem_requeteur_personnaliser_modele_email_editor', {'idModele':idModele, 'action': 'parametres'}) }}" id="parameters-link-label">
			<i class="fas fa-cogs" aria-hidden="true"></i>
			{% if typeEditeur == '1' %}
				<span>{{ "operations.modeles.perso.menu.emetteurMos" | trans }}</span>
			{% else %}
				<span>{{ "operations.modeles.perso.menu.emetteur" | trans }}</span>
			{% endif %}
		</a>
	</li>
	<li class="tabs-title{% if action == "tags" %} is-active{% endif %}" role="presentation">
		<a href="{{ path('aquitem_requeteur_personnaliser_modele_email_editor', {'idModele':idModele, 'action': 'tags'}) }}" id="valeurs-defaut-label">
			<i class="fas fa-tags" aria-hidden="true"></i>
			<span>{{ "operations.modeles.perso.menu.defaut" | trans }}</span>
		</a>
	</li>
	<li class="tabs-title{% if action == "utm" %} is-active{% endif %}" role="presentation">
		<a href="{{ path('aquitem_requeteur_personnaliser_modele_email_editor', {'idModele':idModele, 'action': 'utm'}) }}" id="utm-link-label">
			<i class="fas fa-code" aria-hidden="true"></i>
			<span>{{ "operations.modeles.perso.menu.tabs.utm" | trans }}</span>
		</a>
	</li>
	<li class="tabs-title{% if action == "liens" %} is-active{% endif %}" role="presentation">
		<a href="{{ path('aquitem_requeteur_personnaliser_modele_email_editor', {'idModele':idModele, 'action': 'liens'}) }}" id="links-assign-label">
			<i class="fas fa-link" aria-hidden="true"></i>
			<span>{{ "links.titles.assign" | trans }}</span>
		</a>
	</li>
	<li class="tabs-title{% if action == "droits" %} is-active{% endif %}" role="presentation">
		<a href="{{ path('aquitem_requeteur_personnaliser_modele_email_editor', {'idModele':idModele, 'action': 'droits'}) }}" id="droits-label">
			<i class="fas fa-users" aria-hidden="true"></i>
			<span>{{ "modeles.links.titles.rights" | trans }}</span>
		</a>
	</li>
	<li class="tabs-title{% if action == "tarifs" %} is-active{% endif %}" role="presentation">
		<a href="{{ path('aquitem_requeteur_personnaliser_modele_email_editor', {'idModele':idModele, 'action': 'tarifs'}) }}" id="tarifs-label">
			<i class="fas fa-euro-sign" aria-hidden="true"></i>
			<span>{{ "modeles.links.titles.prices" | trans }}</span>
		</a>
	</li>
	<li class="tabs-title{% if action == "previsu" %} is-active{% endif %}" role="presentation">
		<a href="{{ path('aquitem_requeteur_personnaliser_modele_email_editor', {'idModele':idModele, 'action': 'previsu'}) }}" id="email-previsu-test-label">
			<i class="fas fa-eye" aria-hidden="true"></i>
			<span>{{ "operations.modeles.perso.menu.previsu" | trans }}</span>
		</a>
	</li>
</ul>
