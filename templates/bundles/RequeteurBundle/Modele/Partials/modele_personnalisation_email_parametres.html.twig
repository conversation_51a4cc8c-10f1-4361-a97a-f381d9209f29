{% extends 'base.html.twig' %}
{% block title %}{{ "modeles.titles.modele" | trans({}, "messages", user_locale_langue) }} {{ libelle }} - {% if typeEditeur == '1' %}{{ "operations.modeles.perso.menu.emetteurMos" | trans({}, "messages", user_locale_langue) }}{% else %}{{ "operations.modeles.perso.menu.emetteur" | trans({}, "messages", user_locale_langue) }}{% endif %}{% endblock %}
{% block mtitle %}{{ "modeles.titles.modele" | trans({}, "messages", "fr") }} {{ libelle }} - {% if typeEditeur == '1' %}{{ "operations.modeles.perso.menu.emetteurMos" | trans({}, "messages", "fr") }}{% else %}{{ "operations.modeles.perso.menu.emetteur" | trans({}, "messages", "fr") }}{% endif %}{% endblock %}
{% block stylesheets %}
	{{ parent()}}
	{{ encore_entry_link_tags('css-emojis') }}
{% endblock %}
{% block body %}
	{% block content %}
		<div id="operation-personnalisation-modele-email" class="personnaliser-gestion-modele-email-editor"
			 data-idmodele="{{ idModele }}"
			 data-isBlank="{{ isBlank }}"
			 data-prefixetag="{{ prefixeTag }}"
			 data-suffixetag="{{ suffixeTag }}"
			 data-prefixedesinscription="{{ prefixeDesinscription }}"
			 data-suffixedesinscription="{{ suffixeDesinscription }}"
			 data-prefixemiroir="{{ prefixeMiroir }}"
			 data-suffixemiroir="{{ suffixeMiroir }}"
			 data-email-emetteur="{{ modele.emailFrom.valeur }}"
			 data-nom-emetteur="{{ modele.emetteurFrom.valeur }}"
			 data-sujet="{{ modele.sujet.valeur }}"
			 data-header-html="{{ modele.headerHTML.valeur }}"
			 data-alltags="{{ allTags | json_encode() }}"
			 data-selecttags="{{ defaultTags | json_encode() }}"
			 data-datetags="{{ dateTags | json_encode() }}"
			 data-linkthemes="{{ themesPourUrlsTrackables | json_encode() }}"
			 data-unsubscribelinks="{{ modele.lesPagesDeDesinscription.valeurs | json_encode() }}"
			 data-message-html="{{ modele.messageHTML.valeur }}"
			 data-donnees-json="{{ modele.donneesJson.valeur }}"
			 data-editeur="{{ modele.typeEditeur.valeur }}"
			 data-urlstracees="{{ urlsTracees | json_encode() }}"
			 data-modele-lib="{{ libelle }}"
			 data-typemailing="{{ modele.typeMailing.valeur }}"
			 data-emailreponse="{{ modele.emailReponse.valeur }}">
			<h2>{{ "modeles.titles.modele" | trans }} {{ libelle }}</h2>
			<div class="btn-loader mlm">
				<a href="#" class="btn-submit btn btn--blue btn--largePadding" type="button">{{ "global.save" | trans }}</a>
			</div>
			<a href="{{ url('aquitem_requeteur_modele') }}" class="cross-cancel cross-cancel-top" data-close aria-label="Close modal"><svg viewBox="0 0 31.3 21" width="35" height="35" class="svg svg--red"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="/img/sprite-svg.svg#close-plain"></use></svg></a>
			
			{# Onglets #}
			{% include '@Requeteur/Modele/Partials/onglets.html.twig' %}

			{# Contenu #}
			<div class="tabs-content">

				{# Paramètre des liens #}
				<div class="modal-body-perso" id="parameters-link" role="tabpanel" aria-hidden="true" aria-labelledby="parameters-link-label">
					<form id="parameters-form">
						<div id="perso-parameters">
							<div class="row">
								<p class="column required-legende">{{ "modeles.requiredFields" | trans | raw }}</p>
							</div>
							<div class="row">
								<div class="column medium-7">
									<div class="input-group">
										<label for="typeMailing">{{ "global.asterisque" | trans | raw }} {{ "modeles.form.typeMailing" | trans }}</label>
										<select id="typeMailing" name="typeMailing" required>
											<option value=""></option>
											{% for type in lesTypesMailing %}
												<option value="{{ type['id'] }}"{% if type['id'] == modele.typeMailing.valeur %} selected{% endif %}>{{ type['libelle'] }}</option>
											{% endfor %}
										</select>
									</div>
									<div class="input-group">
										<label for="emailEmetteur">{{ "global.asterisque" | trans | raw }} {{ "operations.modeles.perso.emailEmetteur" | trans }}</label>
										<input type="text" id="emailEmetteur" required="required" value="{{ emailFrom }}" />
									</div>
									<div class="input-group">
										<label for="nomEmetteur">{{ "global.asterisque" | trans | raw }} {{ "operations.modeles.perso.nomEmetteur" | trans }}</label>
										<input type="text" id="nomEmetteur" required="required" value="{{ emetteurFrom }}" />
									</div>
									<div class="input-group">
										<label for="emailReponse">{{ "global.asterisque" | trans | raw }} {{ "modeles.form.emailResponse" | trans }}</label>
										<input type="text" id="emailReponse" required="required" value="{{ emailReponse }}" />
									</div>
								</div>
							</div>
							<hr/>
							<div class="row add-tags">
								<div class="column large-6 medium-12">
									<div class="input-group">
										<label for="sujetId">{{ "operations.modeles.perso.sujet" | trans }}</label>
										<input type="text" id="sujetId" required="required" value="{{ sujet }}" />
										<button type="button" class="btn btn--red mls" id="trigger"><i class="far fa-smile" aria-hidden="true"></i></button>
										<div class="">
											<span>{{ "operations.modeles.perso.infoEmojis" | trans }}</span>
										</div>
									</div>
								</div>
								<div class="column large-6 medium-12">
									<div class="row add-tags-group">
										<div class="column medium-7 text-right">
											<label for="tagsEmailSujet">{{ "operations.modeles.perso.listeTags" | trans }}</label>
											<select id="tagsEmailSujet">
												{% for tag in allTags %}
													<option value="{{ tag.id }}">{{ tag.libelle }}</option>
												{% endfor %}
											</select>
										</div>
										<div class="column medium-5">
											<button type="button" data-action="ajoutTag" data-type-tag="tagsEmailSujet" data-field="sujetId" data-num="0"
													class="btn btn--red man">{{ "global.add" | trans }}</button>
										</div>
									</div>
								</div>
							</div>
							{% if typeEditeur == 0 or typeEditeur == 2 %}
								<div class="row add-tags">
									<div class="column large-6 medium-12">
										<div class="input-group">
											<label for="preHeaderId">{{ "operations.modeles.perso.preHeader" | trans }}</label>
											<textarea id="preHeaderId"></textarea>
										</div>
									</div>
									<div class="column large-6 medium-12">
										<div class="row add-tags-group">
											<div class="column medium-7 text-right">
												<label for="tagsEmailPreHeader">{{ "operations.modeles.perso.listeTags" | trans }}</label>
												<select id="tagsEmailPreHeader">
													{% for tag in allTags %}
														<option value="{{ tag.id }}">{{ tag.libelle }}</option>
													{% endfor %}
												</select>
											</div>
											<div class="column medium-5">
												<button type="button" data-action="ajoutTag" data-type-tag="tagsEmailPreHeader"
													data-field="preHeaderId" data-num="1" class="btn btn--red man">{{ "global.add" | trans }}</button>
											</div>
										</div>
									</div>
								</div>
							{% endif %}
						</div>
					</form>
				</div>
			</div>

			<div class="ptl text-right">
				<div class="btn-loader mls">
					<a href="#" class="btn-submit btn btn--blue btn--largePadding" type="button">{{ 'global.save' | trans }}</a>
				</div>
			</div>
		</div>

		<template id="pre-header-tpl">
			<div>
				<div class="pre-header-text"
					style="display:none !important;mso-hide: all;font-size:1px;color:#333333;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>
			</div>
		</template>

		<template id="perso-save-form-tpl">
			<form id="perso-save-form">
				<div class="input-group mll">
					<div class="alert hide">{{ "operations.actions.alertSavePerso" | trans | raw }}</div>
				</div>
			</form>
		</template>

	{% endblock %}
{% endblock %}


{% block javascripts %}
	{{ encore_entry_script_tags('operation-modele-email-parametres') }}
{% endblock %}
