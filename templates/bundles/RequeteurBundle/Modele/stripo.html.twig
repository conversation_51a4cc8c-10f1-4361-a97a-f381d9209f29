<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=1024, initial-scale=1">

    <title>{% block title %}{{ "global.pageTitle" | trans }}{% endblock %}</title>
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}" />

    {# <link rel="stylesheet" type="text/css" href="{{ asset('js/vendor/mosaico/vendor/notoregular/stylesheet.css') }}" />
    <link rel="stylesheet" type="text/css" href="{{ asset('js/vendor/mosaico/mosaico-material.min.css') }}" />
    <link rel="stylesheet" type="text/css" href="{{ asset('css/mosaico-custom.css') }}" /> #}
    {% block stylesheets %}
        <link href="https://icones.mydataviz.fr/css/all.min.css" type="text/css" rel="stylesheet" />
        <style>
            #externalSystemContainer {
                background-color: darkgrey;
                padding: 5px 0 5px 20px;
            }
            #undoButton, #redoButton {
                display: none;
            }
            #stripoSettingsContainer {
                width: 400px;
                float: left;
            }
            #stripoPreviewContainer {
                width: calc(100% - 400px);
                float: left;
            }
            .notification-zone {
                position: fixed;
                width: 400px;
                z-index: 99999;
                right: 20px;
                bottom: 80px;
            }
            .control-button {
                border-radius: 17px;
                padding: 5px 10px;
                border-color: grey;
            }
            #changeHistoryLink {
                cursor: pointer;
            }
            /* The Modal (background) */
            .modal {
            display: none; /* Hidden by default */
            position: fixed; /* Stay in place */
            z-index: 3; /* Sit on top */
            left: 0;
            top: 0;
            width: 100%; /* Full width */
            height: 100%; /* Full height */
            overflow: auto; /* Enable scroll if needed */
            background-color: rgb(0,0,0); /* Fallback color */
            background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
            }

            /* Modal Content/Box */
            .modal-content {
            background-color: #fefefe;
            margin: 15% auto; /* 15% from the top and centered */
            padding: 20px;
            border: 1px solid #888;
            width: 50%; /* Could be more or less, depending on screen size */
            }

            /* The Close Button */
            .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            }

            .close:hover,
            .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
            }
        </style>
    {% endblock %}

</head>
<body>
    {# <div id="externalSystemContainer">
        <!--This is external system container where you can place plugin buttons -->
        <button id="undoButton" class="control-button">Undo</button>
        <button id="redoButton" class="control-button">Redo</button>
        <button id="codeEditor" class="control-button">Code editor</button>
        <span id="changeHistoryContainer" style="display: none;">Last change: <a id="changeHistoryLink"></a></span>
    </div>
    <div class="notification-zone"></div> #}
    <div>
        <!--Plugin containers -->
		
        <!-- The Modal -->
        <div id="myModal" class="modal">

            <!-- Modal content -->
            <div class="modal-content">
                <span class="closeTag">&times;</span>
                {# {{dump(personalLibraryName)}} #}
                {{ include('@Requeteur/Default/operation-browser.html.twig', {maxFileSize: maxFileSize, personalLibraryName : "", target : ""}) }}
                {# <a type="button" data-lien="lien1.fr" class="lelien btn-submit btn btn--blue btn--largePadding" href="#">Lien1</a><br><br>
                <a type="button" data-lien="lien2.fr" class="lelien btn-submit btn btn--blue btn--largePadding" href="#">Lien2</a><br><br>
                <a type="button" data-lien="lien3.fr" class="lelien btn-submit btn btn--blue btn--largePadding" href="#">Lien3</a><br><br>
                <a type="button" data-lien="https://aqui-req-dev-4.aquitem.fr/images" class="lelien btn-submit btn btn--blue btn--largePadding" href="#">
                    <img src="{{ asset('img/tuto1.jpg') }}" width="100px" height="100px"><img>
                </a><br><br> #}
                {# <a id="mesImages" class="btn btn--white">{{ 'operations.titles.mesImages' | trans }}</a> #}
            </div>
        </div>

        <div id="stripo-datas" data-apikey="{{ secretKey }}" data-id="{{ pluginId }}" data-tags='{{ allTags|json_encode() }}' data-modele='{{ modele.donneesJson.valeur }}' data-role="{{ stripoRole }}">


         <div id="myModalTags" class="modal">
            <!-- Modal content -->
            <div class="modal-content" style="margin: 15% 35%;">
                <span class="close">&times;</span>
                <select id="stripoTags">
                    <option value="">Sélectionnez un tag</option>
                    {% for tag in allTags %} 
                        <option value={{tag.id}}>{{tag.libelle}}</option>
                    {% endfor %}
                </select>
                <br><br>
                <button id="addTagImage" class="btn-add-tag-stripo btn">test</button>
            </div>
        </div>

        
        <div id="stripoSettingsContainer">Loading...</div>
        <div id="stripoPreviewContainer">
        </div>
    </div>

    <div id="undoButton" class="btn-loader mls">
        <a href="#" class="btn-submit btn btn--blue btn--largePadding" type="button">undo</a>
    </div>
        <div id="redoButton" class="btn-loader mls">
        <a href="#" class="btn-submit btn btn--blue btn--largePadding" type="button">undo</a>
    </div>
    <div id="btn-save" class="btn-loader mls">
        <a href="#" id="save-strip" class="btn-submit btn btn--blue btn--largePadding" type="button">Enregistrera</a>
    </div>
    <script>

        var notifications = {
        autoCloseTimeout: 4000,
        container: '.notification-zone',
        error: function (text, id, params) {
            this.showNotification(this.getErrorNotification.bind(this), text, id, params);
        },
        warn: function (text, id, params) {
            this.showNotification(this.getWarningNotification.bind(this), text, id, params);
        },
        info: function (text, id, params) {
            this.showNotification(this.getInfoNotification.bind(this), text, id, params);
        },
        success: function (text, id, params) {
            this.showNotification(this.getSuccessNotification.bind(this), text, id, params);
        },
        loader: function (text, id, params) {
            this.showNotification(this.getLoaderNotification.bind(this), text, id, params);
        },
        hide: function (id) {
            var toast = $('#' + id, this.container);
            toast.effect('fade', 600, function () {
                toast.remove()
            })
        },
        showNotification: function (notificationGetter, text, id, params) {
            params = Object.assign({autoClose: true, closeable: true}, params || {});
            if (!id || !$('#' + id).length) {
                var toast = notificationGetter(text, id, !params.closeable);
                $(this.container).append(toast);
                toast.effect('slide', {direction: 'down'}, 300);
                if (params.autoClose) {
                    setTimeout(function () {
                        toast.effect('fade', 600, function () {
                            toast.remove()
                        })
                    }, this.autoCloseTimeout);
                }
            }
        },
        getErrorNotification: function (text, id, nonclosable) {
            return this.getNotificationTemplate('alert-danger', text, id, nonclosable);
        },
        getWarningNotification: function (text, id, nonclosable) {
            return this.getNotificationTemplate('alert-warning', text, id, nonclosable);
        },
        getInfoNotification: function (text, id, nonclosable) {
            return this.getNotificationTemplate('alert-info', text, id, nonclosable);
        },
        getSuccessNotification: function (text, id, nonclosable) {
            return this.getNotificationTemplate('alert-success', text, id, nonclosable);
        },
        getLoaderNotification: function (text, id) {
            var notification = $('\
                <div class="alert alert-info" role="alert">\
                    <div style="width:auto; margin-right: 15px; float: left !important;">\
                        <div style="width:20px;height:20px;border-radius:50%;box-shadow:1px 1px 0px #31708f;\
                            animation:cssload-spin 690ms infinite linear"></div>\
                    </div>' + text + '\
                </div>');
            id && notification.attr('id', id);
            return notification;
        },
        getNotificationTemplate: function (classes, text, id, nonclosable) {
            var notification = $('\
                <div class="alert alert-dismissible ' + classes + (nonclosable ? ' nonclosable' : '') + '" role="alert">\
                        ' + (nonclosable ? '' :
                            '<button type="button" class="close" data-dismiss="alert" aria-label="Close">\
                                <span aria-hidden="true">&times;</span>\
                            </button>') +
                            text +
                '</div>');
                id && notification.attr('id', id);
                return notification;
            }
        };
        function request(method, url, data, callback) {
            var req = new XMLHttpRequest();
            req.onreadystatechange = function () {
                if (req.readyState === 4 && req.status === 200) {
                    callback(req.responseText);
                } else if (req.readyState === 4 && req.status !== 200) {
                    console.error('Can not complete request. Please check you entered a valid PLUGIN_ID and SECRET_KEY values');
                }
            };
            req.open(method, url, true);
            if (method !== 'GET') {
                req.setRequestHeader('content-type', 'application/json');
            }
            req.send(data);
        }
        
        function loadDemoTemplate(callback) {
            request('GET', '{{ url("empty_modele") }}', null, function(html) {
                request('GET', 'https://raw.githubusercontent.com/ardas/stripo-plugin/master/Public-Templates/Basic-Templates/Empty-Template/Empty-Template.css', null, function(css) {
                    callback({html: html, css: css});
                });
            });
        }

        function getTemplate(callback) {
            
        }

        var stripoDatas = document.getElementById('stripo-datas'); 
        var pluginId = stripoDatas.dataset.id
        var secretKey = stripoDatas.dataset.apikey
        var allTags = JSON.parse(stripoDatas.dataset.tags)
        var html = stripoDatas.dataset.modele
        var role = stripoDatas.dataset.role


        function initPlugin(template) {
            var tags = [];
            allTags.forEach(function(tag){
                tags.push({label: tag["libelle"] + " (" + tag["id"] + ")", value : "{" + tag["libelle"] + "|"+ tag["id"] +"}"});
            });
            const apiRequestData = {
                emailId: 123
            };

            html = html.length == 0 ? template.html : html 

            const script = document.createElement('script');
            script.id = 'stripoScript';
            script.type = 'text/javascript';
            script.src = 'https://plugins.stripo.email/static/latest/stripo.js';
            script.onload = function () {
                window.Stripo.init({
                    settingsId: 'stripoSettingsContainer',
                    previewId: 'stripoPreviewContainer',
                    codeEditorButtonId: 'codeEditor',
                    undoButtonId: 'undoButton',
                    redoButtonId: 'redoButton',
                    codeEditorButtonId: 'codeEditorButton',
                    locale: 'fr',
                    html: html,
                    css: template.css,
                    notifications: {
                        info: notifications.info.bind(notifications),
                        error: notifications.error.bind(notifications),
                        warn: notifications.warn.bind(notifications),
                        loader: notifications.loader.bind(notifications),
                        hide: notifications.hide.bind(notifications),
                        success: notifications.success.bind(notifications)
                    },
                    "mergeTags": 
                    [
                        {
                            "category": "Personnalisés",
                            "entries": tags
                        }
                    ],
                    //"extensions": [
                    //{
                      //  "globalName": "YourExtensionName",
                        //"url":"https://your.hosting/main.hash.extension.js"
                    //}
                    //
                    //],
                    apiRequestData: apiRequestData,
                    userFullName: 'Plugin Demo User',
                    versionHistory: {
                        changeHistoryLinkId: 'changeHistoryLink',
                        onInitialized: function(lastChangeIndoText) {
                            $('#changeHistoryContainer').show();
                        }
                    },
                    getAuthToken: function (callback) {
                        request('POST', 'https://plugins.stripo.email/api/v1/auth',
                            JSON.stringify({
                                pluginId: pluginId,
                                secretKey: secretKey,
                                role: role
                            }),
                            function(data) {
                                callback(JSON.parse(data).token);
                            });
                    }
                });
            };
            document.body.appendChild(script);
        }

        loadDemoTemplate(initPlugin); 

        setTimeout(function(){
                const targetNode = document.getElementById('collapseTwo');

                const config = { attributes: true, childList: true, subtree: true };

                const callback = function(mutationsList, observer) {

                    // Suppression du bloc téléchargement
                    
                        var blocDownload = $('.esdev-mb15')[0];
                        if (blocDownload != 'undefined') {
                            if($('.esdev-mb15')[0] !== undefined) {
                            $('.esdev-mb15')[0].remove();
                            }
                        }

                    // Bloc information galerie vide
                   // var messageInfo = $('.empty-message')[0];
                    //if (messageInfo != 'undefined') {
                        // $('.empty-message')[0].remove();
                    //}

                    // Champ de saisie lien externe
                    var linkInput = $('.upload-link')[0];
                    // link-url-href


                    
                    // $( '<li id="tagAjout" class="tagAdds"><a style="cursor: pointer;" class="abcdef"><span>Ajouter un tag</span></a></li>' ).appendTo($('.keep')[0]);
                    
                    // Ciblag de la div qui contiens le bouton
                    // var imgLib = $('#settings-panel-image-lib-library')[0];
                    
                    if (typeof linkInput !== 'undefined' && typeof $('.abcdef')[0] == 'undefined') {

                        var buttonSearchImage = $('#settings-panel-image-lib-library').find( "li" ).find("a");
                        buttonSearchImage.addClass("abcdef");
                        var buttonSearchImage = $('.abcdef')[0];
                        
                        // var imgSearchHtml = document.createElement("div");
                        //imgSearchHtml.type = "button";
                        //imgSearchHtml.style = "background-color: green; color: white;";
                        // imgSearchHtml.className = "abcdef btn-submit btn btn--blue btn--largePadding";
                        //imgSearchHtml.href = "#";
                        //var html = document.createTextNode('Images');
                        //imgSearchHtml.appendChild(html);

                        //var route = "{{ path('aquitem_requeteur_image')|escape('js') }}";  
                        //$('#settings-panel-image-lib-library').load(route);
                        
                        //$('.upload-link')[0]

                        linkInput.onclick = function(e) {
                            e.preventDefault();
                            var modalTags = document.getElementById("myModalTags");
                            var span = document.getElementsByClassName("closeTag")[0];
                            modalTags.style.display = "block";

                            modalTags.onclick = function(e) {
                                 if(e.target.id == "addTagImage") {
                                     linkInput.value = linkInput.value + "{"+$('#stripoTags').val()+"}";
                                     e.stopPropagation();
                                     // linkInput.keyup();
                                     modalTags.style.display = "none";
                                 } else {
                                    e.stopPropagation();
                                 }
                            }

                            span.onclick = function() {
                                modalTags.style.display = "none";
                            }

                            var addTagButton = $('#addTagImage');

                            addTagButton.onclick = function(e) {
                                console.log("test");
                                console($('#stripoTags').val());
                            }
                        };

                        buttonSearchImage.onclick = function(e) {
                            e.preventDefault();
                            var modal = document.getElementById("myModal");

                            modal.onclick = function(e) {
                                e.stopPropagation();
                                console.log("propastop");
                            }

                            var span = document.getElementsByClassName("close")[0];
                            var lien = document.getElementsByClassName("lelien")[0];
                            modal.style.display = "block";
                            span.onclick = function() {
                                modal.style.display = "none";
                            }
                            $('.thumbnail').click(function(e) {
                                e.preventDefault();
                                modal.style.display = "none";
                                $('.upload-link')[0].value = $(this).data("url");
                                $('.upload-link').keyup();
                                //"https://aqui-req-dev-4.aquitem.fr/images";
                            });
                            window.onclick = function(event) {
                                if (event.target == modal) {
                                    modal.style.display = "none";
                                }
                            }

                            // linkInput.value = "https://aqui-req-dev-4.aquitem.fr/images";
                        };                        
                    }

                    console.log("mutationObserver");
                    var blocsPerso = $('.esdev-item-name');
                    if(blocsPerso.length > 0) {
                        blocsPerso.each(function( element ) {
                            if(!blocsPerso[element].title.includes("coucou")) {
                                blocsPerso[element].parentNode.remove();
                            }
                        });
                    }


                    $('.esdev-accordion-title-library').click(function(e) {
                        console.log("accordion-click")
                        var blocsPerso = $('.esdev-item-name');
                        if(blocsPerso.length > 0) {
                            blocsPerso.each(function( element ) {
                                if(!blocsPerso[element].title.includes("coucou")) {
                                    blocsPerso[element].parentNode.remove();
                                }
                            });
                        }
                    });

                    $('.btn-default').click(function(e) { 
                        console.log("btn-default")
                        var blocsPerso = $('.esdev-item-name');
                        if(blocsPerso.length > 0) {
                            blocsPerso.each(function( element ) {
                                if(!blocsPerso[element].title.includes("coucou")) {
                                    blocsPerso[element].parentNode.remove();
                                }
                            });
                        }
                    })
                };

                // Create an observer instance linked to the callback function
                const observer = new MutationObserver(callback);

                // Start observing the target node for configured mutations
                observer.observe(targetNode, config);
        }, 3000);        


    </script>
</body>
</html>
