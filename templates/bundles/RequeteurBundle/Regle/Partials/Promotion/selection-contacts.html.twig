<div class="page-inner regle-etape" data-equalizer>
    <div class="text-center montserratregular mtm mbxl">{{ "regles.creation.titles.selectionContacts" | trans }}</div>
    <div class="row collapse mbl regle-blocs">
        <div class="column medium-4 small-12">
            <div class="regle-bloc{% if typeSelection == 1 %} active{% endif %}">
                <div class="regle-bloc-select" data-action="all" data-equalizer-watch>
                    <div class="regle-bloc-ico mbm">
                        <i class="fas fa-users" aria-hidden="true"></i>
                    </div>
                    <div class="mbs">
                        {{ "regles.creation.labels.allContacts" | trans }}
                        <span class="regle-bloc-nb{% if typeSelection != 1 %} hide{% endif %}"><span>{{ nbContacts | number_format(0, ',', ' ') }}</span> {{ "regles.creation.labels.contactsSelected" | trans }}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="column medium-4 small-12">
            <div class="regle-bloc mrm{% if typeSelection == 2 %} active{% endif %}">
                <div class="regle-bloc-select" data-action="creation" data-id-session="{{ idSession }}" data-id-regle="{{ idRegle }}" data-equalizer-watch>
                    <div class="regle-bloc-ico mbm">
                        <i class="fas fa-bullseye" aria-hidden="true"></i>
                    </div>
                    <div class="mbs">
                        {{ "regles.creation.labels.creerSelection" | trans }}
                        <span class="regle-bloc-nb {% if typeSelection != 2 %}hide{% endif %}">
                            <span>{% if nbContacts is defined %}{{ nbContacts | number_format(0, ',', ' ') }}{% endif %}</span> {% if nbContacts is defined and nbContacts > 1 %}{{ "regles.creation.labels.contactsSelected" | trans }}{% else %}{{ "regles.creation.labels.contactSelected" | trans }}{% endif %} - <a href="#!">{{ "global.edit" | trans}}</a>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="column medium-4 small-12">
            <div class="regle-bloc{% if typeSelection == 3 %} active{% endif %}">
                <div class="regle-bloc-select" data-action="select" data-equalizer-watch>
                    <div class="regle-bloc-ico mbm">
                        <i class="fas fa-list-ul" aria-hidden="true"></i>
                    </div>
                    <div class="mbs">
                        {{ "regles.creation.labels.utiliserSelection" | trans }}
                        <span class="regle-bloc-nb{% if typeSelection != 3 %} hide{% endif %}">
                            {{ "regles.creation.labels.selection" | trans }} <span class="nom-selection">{{ nomSelection }}</span> (<span class="nb-contacts">{{ nbContacts }}</span> <span class="lib-nb-contacts">{% if nbContacts <= 1 %}{{ "regles.creation.labels.contact" | trans }}{% else %}{{ "regles.creation.labels.contacts" | trans }}{% endif %}</span>) - <a href="#!">{{ "global.edit" | trans}}</a>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <form method="post" action="{{ url('aquitem_requeteur_regle_etapes', {type: "avantage-fid", idSession: idSession, numEtape: (numEtape + 1), etape: etapes[(numEtape + 1)], idRegle: idRegle}, true) }}" id="selection" name="selection">
        <input type="hidden" name="idSelection" id="idSelection" value="{% if regleSession[idSession]['idSelection'] is defined and regleSession[idSession]['idSelection'] > 0 %}{{ regleSession[idSession]['idSelection'] }}{% endif %}">
        <input type="hidden" name="nomSelection" id="nomSelection" value="{% if regleSession[idSession]['nomSelection'] is defined and regleSession[idSession]['nomSelection'] > 0 %}{{ regleSession[idSession]['nomSelection'] }}{% endif %}">
        <input type="hidden" name="tokenSelection" id="tokenSelection" value="{% if regleSession[idSession]['tokenSelection'] is defined %}{{ regleSession[idSession]['tokenSelection'] }}{% endif %}">
        <input type="hidden" name="sites" id="sites" value="{% if regleSession[idSession]['sites'] is defined %}{{ regleSession[idSession]['sites'] }}{% endif %}">
        <input type="hidden" name="magasins" id="magasins" value="{% if regleSession[idSession]['magasins'] is defined %}{{ regleSession[idSession]['magasins'] }}{% endif %}">
        <input type="hidden" name="typeSelection" id="typeSelection" value="{% if regleSession[idSession]['typeSelection'] is defined %}{{ regleSession[idSession]['typeSelection'] }}{% endif %}">
        <input type="hidden" name="nbContacts" id="nbContacts" value="{% if regleSession[idSession]['nbContacts'] is defined %}{{ regleSession[idSession]['nbContacts'] }}{% else %}0{% endif %}">
        <div class="text-right ptl">

        {% if pointsDeVente | length > 1 %}
            <a href="{{ url('aquitem_requeteur_regle_etapes', {type: "avantage-fid", idSession: idSession, numEtape: 2, etape: etapes[2], idRegle: idRegle}, true) }}" class="btn mrs">{{ "global.precedent" | trans }}</a>
        {% else %}
            <a href="{{ url('aquitem_requeteur_regle_etapes', {type: "avantage-fid", idSession: idSession, numEtape: 1, etape: etapes[1], idRegle: idRegle}, true) }}" class="btn mrs">{{ "global.precedent" | trans }}</a>
        {% endif %}
            <button class="btn submitEtape{% if regleSession[idSession]['tokenSelection'] is not defined %} btn--disabled{% endif %}"{% if regleSession[idSession]['tokenSelection'] is not defined %} disabled{% endif %}>{{ "global.suivant" | trans }}</button>
        </div>
    </form>
</div>

{% if regleSession[idSession] is defined %}
<template id="regle-view-selection-tpl" class="regle-view-selection">
    <div class="criterium-inactive">
        <div class="droppable checkbox-block-group" data-id="0"></div>
    </div>
</template>
<template id="regle-use-selection-tpl" class="regle-use-selection">
        <form id="regle-use-selection-form" class="regle-use-selection mbn">
        {% if mesSelections is not empty %}
                <div class="search-selection">
                    <div class="row">
                        <div class="column small-8">
                            <div class="relative">
                                <input id="searchKeywords" name="searchKeywords" class="input-search" value="" type="text" placeholder="{{ "comptage.search" | trans ({}, "messages", user_locale_langue) }}...">
                                <a id="reset-search" href="" class="hide">
                                    <svg class="svg svg--red" viewBox="0 0 31.3 21" width="25" height="25" alt="{{ "global.cancel" | trans({}, "messages", user_locale_langue) }}">
                                        <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#close') }}"></use>
                                    </svg>
                                </a>
                            </div>
                        </div>
                        <div class="column small-4">
                            <button type="submit" id="btn-search-selection" class="btn btn--green btn-externe">Recherche</button>
                        </div>
                    </div>
                </div>
                <div class="dossiers-list-wrap">
                    <ul class="dossiers-list">
                        {{ render(controller('App\\Controller\\RequeteurBundle\\RegleController::selectionsContactsAction', {})) }}
                    </ul>
                </div>
        {% else %}
                <p class="modal-errors error"><strong>{{ "selections.mesSelections.empty" | trans ({}, "messages", user_locale_langue) }}</strong></p>
        {% endif %}
        </form>
</template>

{% endif %}