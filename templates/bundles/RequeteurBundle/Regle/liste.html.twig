{% extends '@Requeteur/Regle/regle_base.html.twig' %}
{% block content %}
<div class="page-header">
    <div class="page-header-title">
        <div class="pageWidth">
            <div class="row collapse">
                <div class="column medium-6">
                    <h1>{{ "menu.mesRegles" | trans }}</h1>
                </div>
                <div class="column medium-6 text-right">
                    <div class="mrxl">
                    {% set multiRegles = false %}
                    {% if (app.user.checkDroitsPromotions and app.user.checkDroitsBri)
                        or (app.user.checkDroitsPromotions and (app.user.checkDroitsExclusions and app.session.get('canAccessExclusions')))
                        or (app.user.checkDroitsBri and (app.user.checkDroitsExclusions and app.session.get('canAccessExclusions'))) %}
                            {% set multiRegles = true %}
                    {% endif %}
                    {% if multiRegles %}
                            <a data-pw="btn-regle-creation" href="{{ url('aquitem_requeteur_regle_choix_regle', {}, true) }}" class="btn btn--red mbl" title="{{ "menu.creerRegle" | trans }}">
                                {{ "menu.creerRegle" | trans }}
                            </a>
                    {% else %}
                        {% if app.user.checkDroitsPromotions %}
                            <a data-pw="btn-regle-creation" href="#!" class="btn-creer-avantage-fid btn btn--red mbl" data-id-session="{{ idSession }}">
                                {{ "menu.nouvellePromotion" | trans }}
                            </a>
                        {% elseif (app.user.checkDroitsExclusions and app.session.get('canAccessExclusions')) %}
                            <a data-pw="btn-regle-creation" href="{{ url('aquitem_requeteur_regle_etapes', {type: "exclusion"}, true) }}" class="btn btn--red mbl" title="{{ "menu.nouvelleExclusion" | trans }}">
                                {{ "menu.nouvelleExclusion" | trans }}
                            </a>
                        {% elseif app.user.checkDroitsBri %}
                            <a data-pw="btn-regle-creation" href="{{ url('aquitem_requeteur_regle_etapes', {type: "bri"}, true) }}" class="btn btn--red mbl" title="{{ "menu.nouveauBri" | trans }}">
                                {{ "menu.nouveauBri" | trans }}
                            </a>
                        {% endif %}
                    {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="page-header-nav">
        <div class="page-header-regles">
            <div class="pageWidth pln">
                <ul id="process-tabs">
                    {% if app.user.checkDroitsPromotions %}
                    <li class="pts{% if type == "avantage-fid" %} is-active{% endif %}" data-process-name="avantage-fid">
                        <a href="{{ url('aquitem_requeteur_regle_liste', {type: 'avantage-fid'}, true) }}">
                            {{ "regles.titles.mesPromotions" | trans }}
                        </a>
                    </li>
                    {% endif %}
                    {% if app.user.checkDroitsBri %}
                    <li class="pts{% if type == "bri" %} is-active{% endif %}" data-process-name="bri">
                        <a href="{{ url('aquitem_requeteur_regle_liste', {type: 'bri'}, true) }}">
                            {{ "menu.mesBri" | trans }}
                        </a>
                    </li>
                    {% endif %}
                    {% if app.user.checkDroitsExclusions and app.session.get('canAccessExclusions') %}
                    <li class="pts{% if type == "exclusion" %} is-active{% endif %}" data-process-name="exclusion">
                        <a href="{{ url('aquitem_requeteur_regle_liste', {type: 'exclusion'}, true) }}">
                            {{ "regles.titles.mesExclusions" | trans }}
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </div>
</div>
<div class="pageWidth page-container">
    <div id="regles">
    {% if type == "avantage-fid" and not app.user.checkDroitsSelections %}
        <p class="modal-errors error">{{ "regles.liste.droit" | trans | raw }}</p>
    {% else %}
        {% include '@Requeteur/Regle/Includes/searchEngine.html.twig' %}
        <div class="regles-list-wrap">
        {% include '@Requeteur/Regle/Includes/searchResultDatas.html.twig' %}
        </div>
    {% endif %}
    </div>
</div>
<template id="regle-impact-tpl" class="regle-impact">
    {{ block('loaderModal', 'blocks.html.twig') }}
    <div class="regle-impact-text hide">
        <strong>
        {% set infoTrans = "regles.liste.priorites.infoImpact1-" ~ type %}
        {{ infoTrans | trans }}"<span class="regle-current-nom"></span>"{{ "regles.liste.priorites.infoImpact2" | trans }}
        <br>
        {{ "regles.liste.priorites.infoImpact3" | trans }}
        </strong>
        <div class="regle-conflit-wrap">
            <div class="regle-impact-base mtl hide">
                <strong><span class="operations-list-statut regle-statut"></span> <span class="regle-type"></span> "<span class="regle-nom"></span>"</strong><span class="regle-createur"></span>
                <br>
                <div class="row collapse">
                    <div class="column medium-12">
                        {{ "regles.creation.titles.conflitProduits1" | trans }} <span class="regle-nbproduits"></span> {{ "regles.creation.titles.conflitPourPeriode" | trans }} {{ "global.du" | trans | lower }} <span class="regle-datedebut"></span> {{ "global.au" | trans }} <span class="regle-datefin"></span>
                        <br>
                        <a class="telecharger-priorites" href="#!">
                            <span>{{ "regles.liste.priorites.lienListe" | trans }}</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<template id="regle-priorites-tpl" class="regle-priorites">
    {{ block('loaderModal', 'blocks.html.twig') }}
    <div class="regle-priorites-text hide">
        <strong>
        {{ "regles.liste.priorites.infoPrio1" | trans }}"<span class="regle-current-nom"></span>".
        <br>
        {% set infoPrio2Trans = "regles.liste.priorites.infoPrio2-" ~ type %}
        {{ infoPrio2Trans | trans }}
        </strong>
        <div class="regle-conflit-wrap mtl">
            <div class="regle-priorites-base mtl hide">
                <strong>
                <span class="operations-list-statut regle-statut"></span> <span class="regle-type"></span> "<span class="regle-nom"></span>"</strong><span class="regle-createur"></span>
                <br>
                <div class="row collapse">
                    <div class="column medium-12">
                        {{ "regles.creation.titles.conflitProduits1" | trans }} <span class="regle-nbproduits"></span> {{ "regles.creation.titles.conflitPourPeriode" | trans }} {{ "global.du" | trans | lower }} <span class="regle-datedebut"></span> {{ "global.au" | trans }} <span class="regle-datefin"></span>
                        <br>
                        <a class="telecharger-priorites" href="#!">
                            <span>{{ "regles.liste.priorites.lienListe" | trans }}</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<template id="regle-supprimer-tpl" class="regle-supprimer">
    {% set confirmSupprTrans = "regles.titles.confirmSuppression-" ~ type %}
    <p>{{ confirmSupprTrans | trans }} "<span></span>" ?</p>
</template>
<template id="regle-arreter-tpl" class="regle-arreter">
    {% set confirmArretTrans = "regles.titles.confirmArreter-" ~ type %}
    <p>{{ confirmArretTrans | trans }} "<span></span>" ?</p>
</template>
<template id="regle-dupliquer-tpl" class="regle-dupliquer">
    <div class="regle-dupliquer">
        <form name="regle-dupliquer-form" id="regle-dupliquer-form">
            <div class="row collapse">
                <div class="column medium-3 text-right">
                        <span class="required">*</span> <label for="titre" class="label-valign label-inline mrm">{{ "regles.creation.labels.nom" | trans }}</label>
                    </div>
                    <div class="column medium-7">
                        <input required type="text" id="titre" name="titre" value="{% if regleSession[idSession]['titre'] is defined %}{{ regleSession[idSession]['titre'] }}{% endif %}">
                    </div>
                    <div class="column large-2 hide-for-medium"></div>
                </div>
                <div class="row collapse pbm ptm">
                    <div class="column medium-3 text-right">
                        <label for="descriptif" class="label-valign label-inline mrm">{{ "regles.creation.labels.description" | trans }}</label>
                    </div>
                    <div class="column medium-7">
                        <textarea rows="5" id="descriptif" name="descriptif">{% if regleSession[idSession]['descriptif'] is defined %}{{ regleSession[idSession]['descriptif'] }}{% endif %}</textarea>
                    </div>
                    <div class="column large-2 hide-for-medium"></div>
                </div>
                <div class="row collapse">
                    <div class="column medium-3 text-right">
                        <span class="required">*</span> <label for="dateDebut" class="label-valign label-inline mrm">{{ "regles.creation.labels.dateDebut" | trans }}</label>
                    </div>
                    <div class="column medium-7">
                        <div class="row collapse">
                            <div class="column large-4 medium-4">
                                <input type="text" required id="dateDebut" name="dateDebut" value="{% if regleSession[idSession]['dateDebut'] is defined %}{{ regleSession[idSession]['dateDebut'] | replace({'/': "-"}) | date('d/m/Y') }}{% endif %}">
                            </div>
                            <div class="column large-4 medium-2 medium-text-right">
                                <span class="required">*</span> <label for="dateFin" class="label-valign label-inline mrs">{{ "regles.creation.labels.dateFin" | trans }}</label>
                            </div>
                            <div class="column large-4 medium-4">
                                <input type="text" required id="dateFin" name="dateFin" value="{% if regleSession[idSession]['dateFin'] is defined %}{{ regleSession[idSession]['dateFin'] | replace({'/': "-"}) | date('d/m/Y') }}{% endif %}">
                            </div>
                        </div>
                    </div>
                    <div class="column large-2 hide-for-medium"></div>
                </div>
        </form>
    </div>
</template>

{% if type == "avantage-fid" and erreurSessionEnCours %}
<template id="session-en-cours-tpl">
    <p class="modal-errors error mtn">{{ "regles.titles.sessionEnCours" | trans }}</p>
</template>
{% endif %}

{% if type == "avantage-fid" and errorSession == 2 %}
<template id="session-en-cours-liste-tpl">
    <p class="modal-errors error mtn">{{ "regles.titles.sessionEnCours" | trans }}</p>
</template>
{% endif %}

{% if type == "avantage-fid" and errorSession == 1 %}
<template id="session-expired-tpl"></template>
{% endif %}


{% endblock %}