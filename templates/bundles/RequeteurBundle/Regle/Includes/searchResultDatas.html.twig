<div class="regles-list mbxl" data-page="{{ page }}" data-nb="{{ nbParPage }}" data-colonne-tri="{{ colonneTri }}" data-type-tri="{{ typeTri }}" data-id-session="{{ idSession }}">
{% if nbTotal > 0 %}
    {% set promotions = resultsRegles.promotion %}
    <div class="total-regles mbs mtxxl">
    {% if submittedSearch %}
        {{ "global.resultats" | trans }} ({{ nbTotal }})
    {% endif %}
    </div>
    <table>
        <thead>
            <tr>
                <th class="nom-head">
                    <a href="#" class="results-order btn-infobulle underline" data-type-tri="ASC" data-colonne-tri="LIBELLE" data-id-session="{{ idSession }}">
                    {% if colonneTri == "LIBELLE" %}
                        <i class="fas {% if typeTri == "ASC" %}fa-sort-down{% else %}fa-sort-up{% endif %}" aria-hidden="true"></i>
                        <span class="plm2">{{ "regles.liste.labels.nom" | trans }}</span>
                    {% else %}
                        {{ "regles.liste.labels.nom" | trans }}
                    {% endif %}
                    </a>
                    <div class="infobulle-content">Trier par nom</div>
                </th>
                <th class="date-head">
                    <a href="#" class="results-order btn-infobulle underline" data-type-tri="ASC" data-colonne-tri="DATEDEBUT" data-id-session="{{ idSession }}">
                    {% if colonneTri == "DATEDEBUT" %}
                        <i class="fas {% if typeTri == "ASC" %}fa-sort-down{% else %}fa-sort-up{% endif %}" aria-hidden="true"></i>
                        <span class="plm2">{{ "regles.liste.labels.dateDebut" | trans }}</span>
                    {% else %}
                        {{ "regles.liste.labels.dateDebut" | trans }}
                    {% endif %}
                    </a>
                    <div class="infobulle-content">Trier par date de debut</div>
                </th>
                <th class="date-head">
                    <a href="#" class="results-order btn-infobulle underline" data-type-tri="ASC" data-colonne-tri="DATEFIN" data-id-session="{{ idSession }}">
                    {% if colonneTri == "DATEFIN" %}
                        <i class="fas {% if typeTri == "ASC" %}fa-sort-down{% else %}fa-sort-up{% endif %}" aria-hidden="true"></i>
                        <span class="plm2">{{ "regles.liste.labels.dateFin" | trans }}</span>
                    {% else %}
                        {{ "regles.liste.labels.dateFin" | trans }}
                    {% endif %}
                    </a>
                    <div class="infobulle-content">Trier par date de fin</div>
                </th>
                <th class="statut-head">
                    <a href="#" class="results-order btn-infobulle underline" data-type-tri="ASC" data-colonne-tri="STATUT" data-id-session="{{ idSession }}">
                    {% if colonneTri == "STATUT" %}
                        <i class="fas {% if typeTri == "ASC" %}fa-sort-down{% else %}fa-sort-up{% endif %}" aria-hidden="true"></i>
                        <span class="plm2">{{ "regles.liste.labels.statut" | trans }}</span>
                    {% else %}
                        {{ "regles.liste.labels.statut" | trans }}
                    {% endif %}
                    </a>
                    <div class="infobulle-content">Trier par statut</div>
                </th>
                <th class="actions-head"></th>
            </tr>
        </thead>
        <tbody>
        {% for promotion in promotions %}
            {% set statutTrans = "regles.liste.labels.search." ~ type ~ ".statut-" ~ promotion.STATUT | lower %}
            <tr>
                <td>
                    <div class="regle-liste-title">
                    {% if not promotion.MODIFIABLE %}
                        <svg viewBox="0 0 31.3 21" width="23" height="23" class="svg svg--darkgrey svg--cadenas" aria-label="cadenas"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{ asset('img/sprite-svg.svg#cadenas') }}"></use></svg>
                    {% endif %}
                    <a href="{{ url('aquitem_requeteur_regle_edition', {type: type, idSession: idSession, idRegle: promotion.IDOPERATION}, true) }}" class="operations-list-title">
                    {% set libelleTruncated = false %}
                    {% set words = promotion.TITRE | split(' ') %}
                    {% if words | length > 1 %}
                        {% set libelle = '' %}
                        {% for word in words %}
                            {% if word | length > 18 %}
                                {% set libelleTruncated = true %}
                            {% endif %}
                            {% set libelle = libelle ~ ' ' ~ (word|length > 18 ? word|slice(0, 18) ~ '...' : word) %}
                        {% endfor %}
                    {% else %}
                        {% if promotion.TITRE | length > 18 %}
                            {% set libelleTruncated = true %}
                        {% endif %}
                        {% set libelle = (promotion.TITRE|length > 18 ? promotion.TITRE|slice(0, 18) ~ '...' : promotion.TITRE) %}
                    {% endif %}
                        <span>{{ libelle }}</span>
                    {# INFOBULLE #}
                    {% if promotion.DESCRIPTIF is defined and promotion.DESCRIPTIF | length %}
                        <span class="hover-description"><strong>{{ promotion.TITRE }}</strong><br>{{ promotion.DESCRIPTIF | replace({'\n':'<br>'}) | raw }}</span>
                    {% endif %}
                    </a>
                {% if promotion.CONFLITBLOCANT %}
                    <div class="voir right text-right">
                        <a href="#" class="regle-impact-link little-info underline"
                            data-id-regle="{{ promotion.IDOPERATION }}" data-type-regle-conflit="{% if promotion.CONFLITBLOCANTBRI %}bri{% elseif promotion.CONFLITBLOCANTBRC %}brc{% endif %}">
                            {{ "regles.liste.labels.voirImpact" | trans }}
                        </a>
                    </div>
                {% endif %}
                {% if promotion.CONFLITBLOQUEE %}
                    <div class="voir right">
                        <a href="#" class="regle-priorites-link little-info underline"
                            data-id-regle="{{ promotion.IDOPERATION }}" data-type-regle-conflit="{% if promotion.CONFLITBLOQUEEBRI %}bri{% if promotion.CONFLITBLOQUEEBRC %}bribrc{% endif %}{% elseif promotion.CONFLITBLOQUEEBRC %}brc{% endif %}">
                            {% if promotion.CONFLITBLOQUEEBRI %}
                                {{ "regles.liste.labels.voirPrioritesBRI" | trans }}
                            {% elseif promotion.CONFLITBLOQUEEBRC %}
                                {{ "regles.liste.labels.voirPriorites" | trans }}
                            {% endif %}
                        </a>
                    </div>
                {% endif %}
                    </div>
                </td>
                <td class="date">{{ promotion.DATEDEBUT }}</td>
                <td class="date">{{ promotion.DATEFIN }}</td>
                <td class="statut">
                    <span class="operations-list-statut regle-statut--{{ promotion.STATUT | lower }}">{{ statutTrans | trans }}</span>
                </td>
                <td>
                {% if promotion.SUPPRIMABLE %}
                    <button class="btn-supprimer-regle btn-supprimer-bouton btn-edit" data-regle-id="{{ promotion.IDOPERATION }}" data-regle-nom="{{ promotion.TITRE }}">
                        {{ "global.delete" | trans }}
                    </button>
                {% elseif promotion.ARRETABLE %}
                    <button class="btn-arreter-regle btn-infos-bouton btn-edit" data-regle-id="{{ promotion.IDOPERATION }}" data-regle-nom="{{ promotion.TITRE }}">
                        {{ "global.stop" | trans }}
                    </button>
                {% endif %}
                    <a href="{{ url('aquitem_requeteur_regle_duplication', {type: type, idRegle: promotion.IDOPERATION}) }}" data-regle-id="{{ promotion.IDOPERATION }}" class="btn btn-dupliquer-regle btn-edit">
                        {{ "global.duplicate" | trans }}
                    </a>
                </td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
    <div class="mtl">
        <div id="pagination-nav" class="row show-for-large">
            <div class="column small-6 text-right{% if page - 1 == 0 %} hide{% endif %}">
                <a id="prev" class="btn btn--smallBlue" href="{{ url('aquitem_requeteur_regle_liste', {type: type, idSession: idSession}) }}" data-page="{{ page - 1 }}" data-id-session="{{ idSession }}">{{ "operations.modeles.perso.pagination.less" | trans }}</a>
            </div>
            <div class="column small-6{% if nbParPage >= nbTotal or newPos >= nbTotal %} hide{% endif %}">
                <a id="next" class="btn btn--smallBlue" href="{{ url('aquitem_requeteur_regle_liste', {type: type, idSession: idSession}) }}" data-page="{{ page + 1 }}" data-id-session="{{ idSession }}">{{ "operations.modeles.perso.pagination.more" | trans }}</a>
            </div>
        </div>
        <div id="pagination-nav" class="row collapse hide-for-large">
            <div class="column small-6{% if page - 1 == 0 %} hide{% endif %}">
                <a id="prev" class="btn btn--smallBlue" href="{{ url('aquitem_requeteur_regle_liste', {type: type, idSession: idSession}) }}" data-page="{{ page - 1 }}" data-id-session="{{ idSession }}">{{ "operations.modeles.perso.pagination.less" | trans }}</a>
            </div>
            <div class="column small-6 text-right{% if nbParPage >= nbTotal or newPos >= nbTotal %} hide{% endif %}">
                <a id="next" class="btn btn--smallBlue" href="{{ url('aquitem_requeteur_regle_liste', {type: type, idSession: idSession}) }}" data-page="{{ page + 1 }}" data-id-session="{{ idSession }}">{{ "operations.modeles.perso.pagination.more" | trans }}</a>
            </div>
        </div>
    </div>
{% else %}
    {% set translation = "regles.titles.noResults-" ~ type %}
    <p class="modal-errors error">{{ translation | trans }}</p>
{% endif %}
</div>
