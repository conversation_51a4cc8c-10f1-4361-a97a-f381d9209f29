{% extends '@Requeteur/Regle/regle_base.html.twig' %}
{% block content %}
<div class="page-header">
    <div class="page-header-title">
        <div class="pageWidth">
            <h1>{{ regleSession[idSession]['titreOriginal'] }}</h1>
            <a href="{{ url('aquitem_requeteur_regle_liste', {type: type}) }}" class="btn btn--red right mrxl mbl">
                <span>{{ "regles.boutons.toutesLesRegles" | trans }}</span>
            </a>
        </div>
    </div>
</div>
<div class="regle-creation regle-recap-edition" data-type-regle="{{ type }}">
    <div class="nav-arrows nav-arrow-prev hide-for-large">
        <div class="prev" style="display:none;"></div>
    </div>
    <div class="nav-arrows nav-arrow-next hide-for-large">
        <div class="next" style="display:none;"></div>
    </div>
    {% set folder = "" %}
    {% if type == "avantage-fid" %}
        {% set folder = "promotion" %}
    {% endif %}
    <div class="pageWidth page-container">
        {% include '@Requeteur/Regle/Partials/Promotion/previsualisation.html.twig' %}
    </div>
</div>
<template id="regle-import-edition-tpl" class="etapier">
    <div class="regle-liste-ids">{{ regleSession[idSession]['codesArticles'] | split(',') | join('<br>') | raw }}</div>
</template>
{% endblock %}

{% block javascripts %}
    {% if js_vars is defined %}
    <script>
        php_vars = JSON.parse('{{ js_vars|raw|escape("js") }}');
        console.log(php_vars)
    </script>
    {% endif %}
    {{ encore_entry_script_tags('regles') }}
{% endblock %}