<div id="jobs-items">
    <div class="pageWidth mtxl mbxl">
        <div class="row collapse">
            <div class="column large-7 small-12"></div>
            <div class="column large-5 small-12">
                <div class="op-predic-infos-garage">
                    <div class="op-predic-infos-garage-head relative">
                        <div class="left"><strong>{{ infosGarage.nomGarage }}</strong></div>
                        {% set strMois = "global.mois." ~ infosGarage.moisJobEncours %}
                        <div class="right">{{ strMois | trans }} {{ infosGarage.anneeJobEncours }}</div>
                    </div>
                    <div class="op-predic-infos-garage-body" data-infos="garage"  data-periode="{{ strMois | trans }} {{ infosGarage.anneeJobEncours }}" data-forfait="{{ infosGarage.forfaitSms }}" data-prix-sup="{{ infosGarage.prixSupplementaire }}">
                        {{ "jobs.titles.abonnementDe" | trans }}{{ infosGarage.abonnement }} €
                        <br>
                        <strong>{{ "jobs.titles.forfaitSMS" | trans }}{{ infosGarage.forfaitSms }}</strong>
                        <br>
                        {{ "jobs.titles.selection" | trans }}<span id="nb-contacts-select"{% if infosGarage.coutSup > 0 %}class="lib-error warning"{% endif %}>{% if infosGarage.selection | length %}{{ infosGarage.selection }}{% else %}0{% endif %}</span>
                        <br>
                        {{ "jobs.titles.smsSup" | trans }}<span id="cout-sup">{% if infosGarage.coutSup > 0 %}{{ infosGarage.coutSup }}{% else %}0{% endif %}</span> €
                    </div>
                </div>
            </div>
        </div>
    {% if jobsThemes | length %}
        <div class="op-predic-statut phm pvm">
        {% if opEnCreation %}
            {{ "jobs.titles.enCreation" | trans | upper }}
        {% else %}
            {% set strMois = "global.mois." ~ infosGarage.moisSouscription %}
            {{ "jobs.titles.commandee" | trans | upper }} - {{ strMois | trans }} {{ infosGarage.anneeSouscription }}
        {% endif %}
        </div>
        <table id="op-predic-theme" class="rq-table mvn" data-infos="themes" data-selection="{{ infosGarage.selection }}">
            <thead>
                <tr>
                    <th>
                        <input type="checkbox" id="theme-all" name="theme-all" value="all" data-action="selectAll"{% if not opEnCreation %} disabled{% else %} checked{% endif %}>
                        <label for="theme-all"><span>{{ "jobs.titles.theme" | trans | upper }}</span></label>
                    </th>
                    <th class="text-center">
                        {{ "jobs.titles.clients" | trans | upper }}
                    </th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
            {% for job in jobsThemes %}
                {% set selectedTheme = false %}
                {% if job.selected is not defined or (job.selected is defined and job.selected) %}
                    {% set selectedTheme = true %}
                {% endif %}
                <tr data-theme-id="{{ job.id }}">
                    <td style="width:40%">
                        <input type="checkbox" id="theme-{{ job.id }}" name="theme-{{ job.id }}" value="{{ job.id }}" data-action="select"{% if not opEnCreation or job.nbContacts == 0 %} disabled{% if not opEnCreation and job.nbContactsSelect > 0 %} checked{% endif %}{% else %}{% if selectedTheme %} checked{% endif %}{% endif %}>
                        <label for="theme-{{ job.id }}"><span{% if job.nbContacts == 0 %} class="disabled-theme"{% endif %}>{{ job.libelle }}</span></label>
                    </td>
                    <td class="text-center"><span class="nb-contacts-theme{% if job.nbContacts == 0 %} class="disabled-theme"{% endif %}"><span>{{ job.nbContactsSelect }}</span> / {{ job.nbContacts }}</span></td>
                    <td class="text-right">
                    {% if opEnCreation %}
                        <button class="btn-selection btn btn-edit btn-edit--moredark{% if job.nbContacts == 0 %} btn--disabled{% endif %}" data-nb-contacts="{{ job.nbContacts }}" data-action="{% if job.nbContacts > 0 and job.nbContacts == job.nbContactsSelect %}deselectionAll{% else %}selectionAll{% endif %}"{% if job.nbContacts == 0 %} disabled{% endif %}>
                            {% if job.nbContacts > 0 and job.nbContacts == job.nbContactsSelect %}
                                {{ "jobs.buttons.allDeselection" | trans | upper }}
                            {% else %}
                                {{ "jobs.buttons.allSelection" | trans | upper }}
                            {% endif %}
                        </button>
                    {% endif %}
                        <button id="job-selection" class="btn-selection btn btn-edit btn-edit--moredark{% if job.nbContacts == 0 %} btn--disabled{% endif %}" {% if job.nbContacts == 0 %} disabled{% endif %} data-action="selection">
                            {{ "jobs.buttons.selection" | trans | upper }}
                        </button>
                        <button class="btn btn-edit btn-edit--moredark" data-action="message" data-message="{{ job.message }}">
                            {{ "jobs.buttons.message" | trans | upper }}
                        </button>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
        {% if opEnCreation %}
        <div id="validation-operation" class="text-right mtxl">
            <button class="btn btn--red">{{ "global.validate" | trans }}</button>
        </div>
        {% endif %}
    {% else %}
        <div class="modal-errors mbxxl">{{ "jobs.titles.noJobs" | trans }}</div>
    {% endif %}
    </div>
</div>
<template id="jobs-message-tpl">
    <div class="mbxxl mtxl">
        <div id="messageSMSPrevisu"></div>
    </div>
</template>
<template id="jobs-save-tpl">
    <div class="mts mbl">
        {{ "jobs.titles.confirmValidation" | trans | raw }}
    </div>
</template>