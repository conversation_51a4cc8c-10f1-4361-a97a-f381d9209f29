{% extends '@Requeteur/Jobs/jobsBase.html.twig' %}
{% set pageTitle = "menu.ADsouscriptions" | trans %}
{% set pageMTitle = "menu.ADsouscriptions" | trans %}
{% block title %}{{ pageTitle }}{% endblock %}
{% block mtitle %}{{ pageMTitle }}{% endblock %}

{% block content %}
<div id="jobs-content" data-route="liste_souscriptions">
    <div id="participation-list-container">
        <div class="page-header">
            <div class="page-header-title">
                <div class="pageWidth">
                    <h1>{{ "jobs.titles.souscriptions" | trans }}</h1>
                </div>
            </div>
        </div>
        <div id="participation-list">
            {{ render(controller('App\\Controller\\RequeteurBundle\\JobsController::jobsListeSouscriptionsAction', {'month': currentMonth, 'year': currentYear})) }}
        </div>
    </div>
</div>
{% endblock %}