
{% extends '@Requeteur/CartesCadeaux/cartesCadeaux_base.html.twig' %}
{% block title %}{{ "cartesCadeaux.titles.recherche" | trans({}, "messages", user_locale_langue) }}{% endblock %}
{% block mtitle %}{{ "cartesCadeaux.titles.recherche" | trans({}, "messages", "fr") }}{% endblock %}
{% block content_title %}<h1>{{ "cartesCadeaux.titles.recherche" | trans }}</h1>{% endblock %}
{% block content %}
    <div class="page-container">
        <div id="cartes-cadeaux">
            {% include '@Requeteur/CartesCadeaux/Includes/searchEngine.html.twig' %}
        {% if cartesCadeauxManager.carteDatas | length %}
            {% set carteDatas = cartesCadeauxManager.carteDatas[0].RESULTAT %}
            <div class="row collapse mtxl">
                {% include '@Requeteur/CartesCadeaux/Includes/partieCarte.html.twig' %}
            {% if carteDatas.PartieClient | length %}
                {% include '@Requeteur/CartesCadeaux/Includes/partieClient.html.twig' %}
            {% endif %}
            </div>
            {% if carteDatas.PartieMouvement | length %}
                {% set mouvements = {} %}
                {% if carteDatas.PartieMouvement.mouvementCarte[0] is not defined %}
                    {% set mouvements = mouvements|merge({ '0': carteDatas.PartieMouvement.mouvementCarte }) %}
                {% else %}
                    {% set mouvements = carteDatas.PartieMouvement.mouvementCarte %}
                {% endif %}
            <div id="carte-cadeau-mouvements" class="page-inner row mtm">
                {% include '@Requeteur/CartesCadeaux/Includes/mouvements.html.twig' %}
            </div>
            {% endif %}
        {% elseif cartesCadeauxManager.carteInvalide %}
            <div class="row">
                <p class="modal-errors error">{{ "cartesCadeaux.errors.noCarte" | trans }}</p>
            </div>
        {% endif %}
        </div>
    </div>
{% endblock %}