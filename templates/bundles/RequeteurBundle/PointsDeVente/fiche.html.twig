{% extends '@Requeteur/PointsDeVente/pdv_base.html.twig' %}
{% if app.session.get('current_user_profil')['langue'] is defined %}
	{% set user_locale_langue = app.session.get('current_user_profil')['langue'] %}
{% else %}
	{% set user_locale_langue = "fr" %}
{% endif %}
{% set pageTitle = "global.pageTitle" | trans({}, "messages", user_locale_langue) %}
{% set pageMTitle = "global.pageTitle" | trans({}, "messages", "fr") %}
{% if idPdv is defined %}
	{% set pageTitle = nomPdv %}
	{% set pageMTitle = nomPdv %}
{% else %}
	{% set pageTitle = "pdv.titles.nouveauPdv" | trans({}, "messages", user_locale_langue) %}
	{% set pageMTitle = "pdv.titles.nouveauPdv" | trans({}, "messages", "fr") %}
{% endif %}
{% block title %}{{ pageTitle }}{% endblock %}
{% block mtitle %}{{ pageMTitle }}{% endblock %}
{% block body %}
	<div id="pointsDeVente">
		<div class="page-header">
			{% if idPdv is defined %}
				{% include '@Requeteur/PointsDeVente/Includes/entete_fiche.html.twig' %}
			{% else %}
				<div class="page-header-title">
					<div class="pageWidth">
						{% set typePdv = app.session.get('typeDePointDeVentes') %}
						<h1>{{ "pdv.titles.creation" | trans({'%pdv%': typePdv}) }}</h1>
						<a class="show-for-medium btn btn--green right mrxl" href="{{ url('aquitem_requeteur_pdv_recherche') }}">
							{{ "pdv.titles.rechercher" | trans({'%pdv%': app.session.get('typeDePointDeVentes')}) }}
						</a>
					</div>
				</div>
			{% endif %}
		</div>
		{% if idPdv is not defined %}
			<div class="show-for-small hide-for-medium mtl overflow-hidden">
				<a class="btn btn--green right mrxl" href="{{ url('aquitem_requeteur_pdv_recherche') }}">{{ "pdv.titles.rechercher" | trans }}</a>
			</div>
		{% endif %}
		<div id="fiche-pdv"{%if idPdv is defined %} data-id-pdv="{{ idPdv }}"{% endif %}>
			<div class="donneesPdv{% if idPdv is not defined %} creation{% endif %}"{% if idPdv is defined %} data-id-session="{{ idSession }}"{% endif %}>
				{% include '@Requeteur/PointsDeVente/Includes/donnees_fiche.html.twig' %}
			</div>
		</div>
		{% if idPdv is defined %}
		<template id="fermeture-form-tpl">
			<form id="fermeture-form" name="fermeture-form" data-libpdv="{{ nomPdv }}">
				<input type="hidden" name="idPointDeVente" id="idPointDeVente" value="{{ idPdv }}">
				<div class="input-group mbm">
					<p class="required-legende">{{ "operations.requiredFields" | trans | raw }}</p>
					<div class="row collapse">
						<div class="large-2 medium-12 columns text-right">
							<label for="dateFermeture" class="inline text-left ptxs">
								<span class="required">*</span> {{ "pdv.titles.dateFermeture" | trans }}{{ "global.dots" | trans }}
							</label>
						</div>
						<div class="large-10 medium-12 columns">
							<input class="input-text" type="date" id="dateFermeture" name="dateFermeture" max="{{ 'now' | date('Y-m-d') }}" required>
						</div>
					</div>
				</div>
			</form>
		</template>
		<template id="confirm-fermeture-tpl">
			<p>{{ "pdv.actions.confirmFermeture" | trans({'%libPdv%': nomPdv}) | raw }}</p>
		</template>
			{% set showCodeMagasin = true %}
			{% set showCodeSite = false %}
			{% set showNewCodeSiteOption = false %}

		{# template modal Changement de finess#}
		{% include '@Requeteur/PointsDeVente/ModalsTemplates/changer_finess.html.twig' %}

		{# template modal Confirmation changement de finess #}
		{% include '@Requeteur/PointsDeVente/ModalsTemplates/confirm_changer_finess.html.twig' %}

		{% endif %}
	</div>
{% endblock %}
