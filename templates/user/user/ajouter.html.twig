{% extends "base_header_title.html.twig" %}

{% block title %}
	{% if user.id %}
		{{ "user.edit" }}
	{% else %}
		{{ "user.add" }}
	{% endif %}
{% endblock %}

{% set entityName = "user" %}
{% set pageType = "form" %}

{% block body %}
	<div class="container mt-4 mb-5">
		{{ form_start(form) }}
		{% for group, subForm in form %}
			{% if subForm.children|length > 0 %}
				{% if subForm.vars.name == "password" %}
					{% embed "common/form_card_group_subform.html.twig" with { title: subForm.vars.label, form: subForm } %}
						{% block form_rest %}
							<p class="fst-italic pt-1 mb-1">{{"user.profile.edit.password_rules"|trans}}</p>
							{{ form_row(form.plainPassword) }}
						{% endblock %}
					{% endembed %}
				{% else %}
					{% include 'common/form_card_group_subform.html.twig' with { title: subForm.vars.label, form: subForm } %}
				{% endif %}
			{% endif %}
		{% endfor %}

		{# Section pour les filtres #}
        {% if form.filters is defined %}
            <div class="card card-form">
                <div class="card-body">
                    <div class="card-title">{{ "user.form.filters.label"|trans }}</div>

                    <div class="mb-3">
                        <div class="btn-group">
                            <button id="open-filter-modal" class="btn btn-primary btn-fa" {% if user.id is null %}disabled{% endif %}>
                                <i class="fal fa-filter me-2"></i> Ajouter un filtre
                            </button>
                        </div>
                    </div>

                    <div class="mt-3 fst-italic d-none text-danger" id="filters-need-reload">
                        {{ "user.form.filters.need_reload"|trans }}
                    </div>

                    {# Input caché pour les filtres #}
                    {{ form_widget(form.filters, {'attr': {'class': 'user-filters-input'}}) }}

                    {# Liste des filtres existants #}
                    {% if user.id is not null %}
                        <hr>
                        <div class="mt-3">
                            <div class="mb-3">{{ "user.form.filters.selected"|trans }} :</div>
                            <div class="page-dashboard">
                                <div class="filter-input-group-container" style="width: 300px;">
                                    <div id="filter-container"></div>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <div class="mt-3 fst-italic">
                            {{ "user.form.filters.availability"|trans }}
                        </div>
                    {% endif %}
                </div>
            </div>
        {% endif %}

		<div class="text-center text-lg-end">
			<button id="submit" class="btn btn-secondary">{{"general.save"|trans}}</button>
		</div>
		{{ form_end(form) }}
	</div>

    {% if form.filters is defined %}
        <template id="filters-modal-template">
            <div>
                <div class="container">
                    <div class="mb-3">
                        <label class="form-label">{{ "user.form.filters.source"|trans }}</label>
                        <select id="source-selector" class="form-select">
                            <option value="">{{ "user.form.filters.select_source"|trans }}</option>
                        </select>
                    </div>

                    {# Zone de création de filtre #}
                    <div class="row">
                        {# Liste des champs à gauche #}
                        <div class="col-md-6 border-end">
                            <div id="fields-list-container"></div>
                        </div>

                        {# Zone de création de filtre à droite #}
                        <div class="col-md-6">
                            <div id="filter-box-container"></div>
                            <div id="filter-actions" class="text-end mt-3 d-none">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-flex justify-content-end pt-4 pb-4">
                    <button id="close-modal" class="btn bg-gray me-2">{{ "general.cancel"|trans }}</button>
                    <button id="validate-filter" disabled class="btn btn-primary">{{ "general.apply"|trans }}</button>
                </div>
            </div>
        </template>
    {% endif %}
{% endblock %}

{% block scripts %}
    {% if form.filters is defined %}
        <div {{ stimulus_controller('user_form', {
            userId: user.id
        }) }}/>
    {% endif %}
    <script>
		const $client = {% if form.permissions.client is defined %}$("#{{ form.permissions.client.vars.id }}"){% else %}null{% endif %};
		const $groupes = {% if form.permissions.groupes is defined %}$("#{{ form.permissions.groupes.vars.id }}"){% else %}null{% endif %};
		const $ccClients = {% if form.permissions.ccClients is defined %}$("#{{ form.permissions.ccClients.vars.id }}"){% else %}null{% endif %};
		const $roles = {% if form.permissions.roles is defined %}$("#{{ form.permissions.roles.vars.id }}"){% else %}null{% endif %};

		let showDefaultConnexion = function() {
			let nbDefault = "";
			const nbConnexion = $client.find('option:selected').attr('data-currentLogin');
			if (nbConnexion) {
				nbDefault = "Par défaut : " + nbConnexion;
			}
			$('.help-text').text(nbDefault);
		}

		let hideGroupes = function() {
			const client = $client.find('option:selected').text();
			$.each($groupes.data("multiselect").$container.find('li.ms-optgroup-container'), (index, value) => {
				const $li = $(value);
				value = $li.find('ul.ms-optgroup').find('.ms-optgroup-label').find('span').text();
				if (value != client) {
					$li.addClass('d-none');
				} else {
					$li.removeClass('d-none');
				}
			});
		}

		if ($client) {
			//si on change le client de l'utilisateur
			//on affiche le nombre de connexion par défaut du client
			$client.change(function() {
				$groupes.multiSelect('deselect_all');
				if ($(this).val() !== null) {
					$groupes.closest('.mb-3').removeClass('d-none');
				} else {
					$groupes.closest('.mb-3').addClass('d-none');
				}
				showDefaultConnexion();
				hideGroupes();
			});
		}

		let onSelect = function() {
			if ($ccClients && $roles) {
				let found = false;
				const values = $roles.val();
				if (values !== null) {
					$.each(values, (index, value) => {
						if (value === 'ROLE_CC') {
							found = true;
							$ccClients.closest('.mb-3').removeClass('d-none');
						}
					});
				}
				if (!found) {
					$ccClients.multiSelect('deselect_all');
					$ccClients.closest('.mb-3').addClass('d-none');
				}
			}
		};
		$('.multi-select-roles').multiSelect({
			afterSelect: onSelect,
			afterDeselect: onSelect
		});

		onSelect();

		$('.multi-select').multiSelect();

		if ($client) {
			showDefaultConnexion();
			hideGroupes();
		}

		//bloque la modification si on a pas sélectionner au moins un rôle
		if ($roles) {
			$('#submit').click(() => {
				$('#role_error').remove();
				const values = $roles.val();
				if (values == null) {
					$roles.closest('.mb-3').addClass('has-error');
					$roles.closest('.mb-3').append('<p id="role_error" class="form-text help-text">' + Translator.trans("js.user.role") + '</p>');
				}
			});
		}
    </script>
{% endblock %}