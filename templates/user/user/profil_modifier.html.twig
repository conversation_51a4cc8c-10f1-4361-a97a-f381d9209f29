{% extends "base.html.twig" %}

{% block title %}{{"user.profile.edit.infos"}}{% endblock %}

{% block page_content_class %}page-form{% endblock %}

{% block page_header %}
<header class="page-header">
	<div class="container">
		<h1 class="page-title">
			{{"user.profile.edit.infos"|trans}}
		</h1>
	</div>
</header>
{% endblock %}
	
{% block body %}
	<div class="container flex-fill h-md-auto mt-md-4">
		<div class="card h-100 h-md-auto mb-3 border-0 border-md rounded-0 rounded-md">
			<div class="row g-0">
				<div class="col-xl-4 d-none d-xl-block">
					<img src="{{ asset('/build/images/modification-infos-personnelles.jpg') }}" class="rounded-start h-100 w-auto">
				</div>
				<div class="col-xl-8">
					<div class="card-body h-100 p-0 p-md-3">
						{{ form_start(form, {'attr': {'class': 'h-100'}}) }}
							<div class="d-flex flex-column h-100">
								<div class="d-flex flex-column justify-content-center align-items-center flex-grow-1 pt-4 pb-4">
									<div class="w-md-75">
										
										<div class="row mb-3">
											<div class="col-sm-12 col-lg-3 col-form-label text-lg-end">{{ form_label(form.email) }}</div>
											<div class="col-sm-12 col-lg-9">
												{{ form_widget(form.email) }}
												{{ form_errors(form.email) }}
											</div>
										</div>

										<div class="row mb-3">
											<div class="col-sm-12 col-lg-3 col-form-label text-lg-end">{{ form_label(form.firstname) }}</div>
											<div class="col-sm-12 col-lg-9">
												{{ form_widget(form.firstname) }}
												{{ form_errors(form.firstname) }}
											</div>
										</div>

										<div class="row mb-3">
											<div class="col-sm-12 col-lg-3 col-form-label text-lg-end">{{ form_label(form.lastname) }}</div>
											<div class="col-sm-12 col-lg-9">
												{{ form_widget(form.lastname) }}
												{{ form_errors(form.lastname) }}
											</div>
										</div>

									</div>
								</div>
								<div class="text-center text-lg-end">
									<button id="submit" class="btn btn-primary">{{"general.save"|trans}}</button>
								</div>
							</div>
						{{ form_end(form) }}
					</div>
				</div>
			</div>
		</div>
	</div>
{% endblock %}