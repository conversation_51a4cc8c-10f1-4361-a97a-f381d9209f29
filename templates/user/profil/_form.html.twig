{% extends "base_header_title.html.twig" %}

{% block title %}{{ "profil.edit"|trans }}{% endblock %}

{% set entityName = "profil" %}
{% set pageType = "form" %}

{% block body %}
	{% embed "common/form_card.html.twig" %}
		{% block form_rest %}
			{{ form_row(form.libelle) }}
			<div class="profil-permissions">
				<table id="table-profil-permissions" class="table table-condensed table-hover">
					<thead>
					<th>{{ "permission.name"|trans }}</th>
					<th>{{ "profil.form.auth"|trans }}</th>
					</thead>
					<tbody>
					{% for label, groupe in groupes %}
						<tr id="groupe-{{ loop.index }}" class="treegridgroup treegrid-{{ loop.index }}">
							<td>
								{{ ("permissions.groupsTitle."~label)|trans }}
							</td>
							<td> -</td>
						</tr>
						{% for g_permission in groupe %}
							{% set g_permission = g_permission.createView() %}
							{% for permission in form.permissions %}
								{% if g_permission.vars.value.key == permission.vars.value.key %}
									<tr data-id="{{ permission.vars.value.permission.key }}" class="permission-{{ permission.vars.value.permission.id }} treegrid-parent-{{ loop.parent.loop.parent.loop.index }}">
										<td>{{ ("permissions."~permission.vars.value.permission.parent~"."~permission.vars.value.permission.key)|trans }}</td>
										<td>{{ form_row(permission.access) }}</td>
									</tr>
								{% endif %}
							{% endfor %}
						{% endfor %}
					{% endfor %}

					</tbody>
				</table>
			</div>
			{{ form_rest(form) }}
		{% endblock %}
	{% endembed %}
{% endblock %}

{% block scripts %}
    <script>
		$('.multi-select').multiSelect();
		$('table').treegrid();

		// Pour forcer l'envoi des checkbox qui sont disabled
		$("form").submit(() => {
			$("input").removeAttr("disabled");
		});

		const requirements = {
			"dashboard_view": [],
			"dashboard_edit": ["dashboard_list", "dashboard_view"],
			"dashboard_create": ["dashboard_list", "dashboard_view"],
			"dashboard_delete": ["dashboard_list", "dashboard_view"],
			"dashboard_share": ["dashboard_view"],
			"dashboard_add_widget": ["dashboard_list", "dashboard_view"],
			"dashboard_export_requeteur": ["dashboard_view"],

			"visualisation_view": [],
			"visualisation_edit": ["visualisation_list", "visualisation_view"],
			"visualisation_create": ["visualisation_list", "visualisation_view"],
			"visualisation_delete": ["visualisation_list", "visualisation_view"],
			"visualisation_share": ["visualisation_view"],

			"source_view": [],
			"source_edit": ["source_list", "source_view"],
			"source_create": ["source_list", "source_view"],
			"source_delete": ["source_list", "source_view"],

			"user_view": ["user_list"],
			"user_edit": ["user_list", "user_view"],
			"user_create": ["user_list", "user_view"],
			"user_delete": ["user_list", "user_view"],
			"user_desactivate": ["user_list", "user_view"],
			"user_interpersonate": ["user_list", "user_view"],

			"groupe_view": ["groupe_list"],
			"groupe_edit": ["groupe_list", "groupe_view"],
			"groupe_create": ["groupe_list", "groupe_view"],
			"groupe_delete": ["groupe_list", "groupe_view"],

			"client_view": ["client_list"],
			"client_edit": ["client_list", "client_view"],
			"client_create": ["client_list", "client_view"],
			"client_delete": ["client_list", "client_view"],

			"profil_view": ["profil_list"],
			"profil_edit": ["profil_list", "profil_view"],
			"profil_create": ["profil_list", "profil_view"],
			"profil_delete": ["profil_list", "profil_view"],
			"profil_share": ["profil_list", "profil_view"],
		};

		Object.keys(requirements).forEach(key => {
			requirements[key].forEach(k => {
				const parentChilds = getChilds(k);
				let needLock = false;
				parentChilds.forEach(kc => {
					needLock = needLock || $('[data-id=' + kc + ']').find('input').is(':checked');
				});
				if (needLock) {
					const $perm = $('[data-id=' + k + ']');
					$perm.find('input').attr('disabled', 'disabled');
					$perm.find('.checker').addClass('disabled');
				}
			});
		});

		$('#table-profil-permissions > tbody > tr').mouseover(function() {
			const key = $(this).data('id');
			if (requirements.hasOwnProperty(key)) {
				requirements[key].forEach(k => {
					$('[data-id=' + k + ']').addClass('permission-require');
				});
			}
			getChilds(key).forEach(k => {
				const $perm = $('[data-id=' + k + ']');
				if ($perm.find('input').is(':checked')) {
					$perm.addClass('permission-child');
				}
			});
		}).mouseout(function() {
			const key = $(this).data('id');
			if (requirements.hasOwnProperty(key)) {
				requirements[key].forEach(k => {
					$('[data-id=' + k + ']').removeClass('permission-require');
				});
			}
			getChilds(key).forEach(k => {
				$('[data-id=' + k + ']').removeClass('permission-child');
			});
		});

		$('#table-profil-permissions').find('input[type=checkbox]').on('change', function(e) {
			e.preventDefault();
			const checked = $(this).is(':checked');
			console.log(checked);
			const key = $(this).closest('tr').data('id');
			if (checked) {
				addPermission(key);
			} else {
				removePermission(key);
			}
		});

		function addPermission(key, depth) {
			depth = depth || 0;
			const $perm = $('[data-id=' + key + ']');
			$perm.find('.checker > span').addClass('checked');
			$perm.find('input').prop('checked', true);
			if (depth > 0) {
				$perm.find('input').attr('disabled', 'disabled');
				$perm.find('.checker').addClass('disabled');
			}
			if (requirements.hasOwnProperty(key)) {
				requirements[key].forEach(k => {
					addPermission(k, depth + 1);
				});
			}
		}

		function removePermission(key) {
			const childs = getChilds(key);
			let canRemove = true;
			childs.forEach(k => {
				canRemove = canRemove && !$('[data-id=' + k + ']').find('input').is(':checked');
			});
			console.log(childs, canRemove);
			if (!canRemove) {
				$(this).closest('tr').find('.checker > span').addClass('checked');
				$(this).prop('checked', true);
			} else {
				if (requirements.hasOwnProperty(key)) {
					requirements[key].forEach(k => {
						const parentChilds = getChilds(k);
						let canUnlock = true;
						parentChilds.forEach(kc => {
							canUnlock = canUnlock && !$('[data-id=' + kc + ']').find('input').is(':checked');
						});
						if (canUnlock) {
							const $perm = $('[data-id=' + k + ']');
							$perm.find('input').removeAttr('disabled');
							$perm.find('.checker').removeClass('disabled');

						}
					});
				}
			}
		}

		function getChilds(key) {
			return Object.keys(requirements).filter(k => requirements[k].indexOf(key) !== -1);
		}
    </script>
{% endblock %}