{% set sources_array = [] %}
{% extends "base.html.twig" %}

{% block title %}{{ dashboard.title }}{% endblock %}

{% block page_class %}page-dashboard{% endblock %}

{% block encore_style %}
    {{ encore_entry_link_tags('bootstrap') }}
    {{ encore_entry_link_tags('fonts') }}
    {{ encore_entry_link_tags('dashboard') }}
{% endblock %}

{% block dropdowns %}
    {{ parent() }}
    {% include 'dashboard/dropdowns.html.twig' with {} %}
{% endblock %}

{% block flashbags %}{% endblock %}

{% block page_header %}
<header class="page-header">
    <div class="title-bar">
        <div class="h-100 px-4 d-flex">
            <div class="title-bar-item">
                {% if not embed and nbDashboards > 6 %}
                    {% set bookmarked = app.user.hasBookmark(dashboard) %}
                    <a href="javascript:;" class="btn-toogle-bookmark{% if bookmarked %} bookmarked{% endif %}" id="btn-toogle-bookmark">
                        <i class="fal fa-star p-1 me-2"></i>
                    </a>
                {% endif %}
                <h1>
                    {% if embed or nbClients > 1 %}
                        <strong class="client-name">{{ dashboard.groupe.client.name }}</strong>
                        <span class="dot-primary mx-1"></span>
                    {% endif %}
                    <span class="dashboard-title">{{ dashboard.title }}</span>
                </h1>
            </div>
            {% if not embed %}
            <div class="title-bar-item action-bar ms-auto">
                <a href="javascript:;" data-dropdown-btn="dashboard_action_menu_mobile_navbar" class="h-100 px-2 d-flex align-items-center d-lg-none btn-toggle-actions-menu">
                    <i class="fal fa-ellipsis-h"></i>
                </a>
                <div class="dropdown dropdown-dashboard-actions" data-dropdown="dashboard_action_menu_mobile_navbar" data-dropdown-namespace="app" data-dropdown-anchor=".bottom-navigation-bar" style="display: none;">
                    <a href="javascript:;" data-dropdown-close class="dropdown__title ps-3 pe-3">Menu</a>
                    <div class="dropdown__content p-0">
                        {% if dashboard.shortDescription is not null %}
                            <div class="ps-3 pe-3 pt-3">{{ dashboard.shortDescription|purify }}</div>
                        {% endif %}
                        {# menu des actions #}
                        {% set dashboard_action_menu = knp_menu_get('dashboard_action', [], {'dashboard': dashboard, 'mobile_menu': true}) %}
                        {{ knp_menu_render(dashboard_action_menu, {'template': 'menu/knp_dashboard_action_menu_mobile.html.twig'}) }}
                    </div>
                </div>
                <div class="d-none d-lg-flex">
                    {# menu des actions #}
                    {% set dashboard_action_menu = knp_menu_get('dashboard_action', [], {'dashboard': dashboard, 'mobile_menu': false}) %}
                    {{ knp_menu_render(dashboard_action_menu, {'template': 'menu/knp_dashboard_action_menu.html.twig'}) }}
                    <a href="javascript:;" id="btn-fullscreen" class="btn-fullscreen" data-bs-toggle="tooltip" data-bs-placement="bottom" title="{{ "dashboard.btn.actions.desktop.fullscreen_enter"|trans }}" data-title-enter="{{ "dashboard.btn.actions.desktop.fullscreen_enter"|trans }}" data-title-leave="{{ "dashboard.btn.actions.desktop.fullscreen_leave"|trans }}">
                        <i class="fal fa-expand-arrows" data-fullscreen-toggle="fal fa-expand-arrows" data-undofullscreen-toggle="fal fa-compress-arrows-alt"></i>
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    {# Toast #}
    <div class="w-100 d-flex justify-content-center">
        <div class="toast-container position-fixed pt-3 px-3"></div>
    </div>
</header>
{% endblock %}

{% block body %}
    <div class="d-flex h-100">
        <div id="dashboard" class="border-top h-100 flex-fill pt-5">
            {% if embed and screenshot %}
                <div id="filter-list-pdf"></div>
            {% endif %}
            {% include 'dashboard/dashboard_grid.html.twig' with { embed : embed } %}
        </div>
        <aside class="filter-bar d-none d-lg-block{% if dashboard.filters|length < 1 or (embed and not screenshot) %} collapsed{% endif %}">
            <div class="dropdown" data-dropdown>
                <div class="dropdown__title p-0">
                    <a href="javascript:;" id="filter-bar-toggle" class="d-inline-flex align-items-center w-100">
                        <i class="far fa-chevron-right px-3"></i>
                        <span class="d-inline-flex align-items-center me-auto" data-nb-filtre>{{ "dashboard.filter.sidebar.title"|trans({'%count%': 0}) }}</span>
                    </a>
                    <div class="d-inline-flex me-3">
                        {% if not embed %}
                            <a href="javascript:;" data-filter-add class="filter-bar-title-action me-1"><i class="fal fa-filter"></i></a>
                        {% endif %}
                        <a href="javascript:;" data-filter-clear class="filter-bar-title-action"><i class="fal fa-trash-alt"></i></a>
                    </div>
                </div>
                <div class="dropdown__content">
                    <div id="filters-input-group-container" class="filter-input-group-container">
                        <div id="field-toggles-container" class="px-3 mb-3"></div>
                        <div id="filters-input-group" class="input-group px-3 mb-3"></div>
                    </div>
                    <div class="filter-dropdown-actions px-3 pt-3">
                        <button class="btn btn-light w-100 d-none" data-filter-bookmark-add>
                            {{ "dashboard.filter.sidebar.save_bookmark"|trans }}
                        </button>
                    </div>
                    <div class="dropdown__footer px-3 pt-3 fst-italic d-flex flex-grow-1 align-items-end justify-content-center text-center">
                        <span id="source-update-date"></span>
                    </div>
                </div>
            </div>
        </aside>
    </div>

    <div class="d-none">
        {% include 'dashboard/dropdowns.html.twig' %}
    </div>
    {# TODO revoir #}
    {% include 'dashboard/filters.html.twig' with {} %}
{% endblock %}

{% block encore_script %}
    <script type="text/javascript">
	    let embed = false;
	    let requeteur_liste_url = '{{ app.session.get('login_back_url')|default(requeteur_liste_url) }}';
	    let php_vars = JSON.parse('{{ js_vars|raw|escape("js") }}');
	    let dashboard_token = null;
	    let portlet_padding = 59;
	    window.flashbagMessages = {};
        {% for label, flashes in app.session.flashbag.all %}
	    window.flashbagMessages['{{ label }}'] = [];
        {% for flash in flashes %}
	    window.flashbagMessages['{{ label }}'].push('{{ flash }}')
        {% endfor %}
        {% endfor %}
	    let isPdf = false;
    </script>
    {{ encore_entry_script_tags('dashboard') }}
{% endblock %}