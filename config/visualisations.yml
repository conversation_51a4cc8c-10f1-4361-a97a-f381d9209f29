-
 name: lineChart
 classFile: "lineChart.js"
 displayName: Ligne
 description: "Cette visualisation représente un ensemble de points (visibles ou non), d'un ou plusieurs groupe de valeurs distincts, sur une grille X-Y, reliés par un segment (ligne qui va d'un point à un autre). L'ordonnée (axe des Y - vertical) représente les variations de valeurs de la données, l'abscisse (axe des X - horizontal) représente le plus souvent le temps.\n Cette visualisation est souvent utilisée pour faire apparaître des tendances au cours du temps."
 example: "Nombre de ventes par mois ; Chiffre d’affaires par an ; Nombre de clients journalier"
 position: 1
 displayType: "portlet"
 defaultSize: [6,4]
 minSize: [6,4]
 responsiveH: 6
-
 name: pieChart
 classFile: "pieChart.js"
 displayName: Camembert
 description: "Cette visualisation permet de montrer la part d’un élément au sein d’un ensemble. Le total des parts d’un camembert est toujours égal à 100%.\n Il est possible d'éxaminer plusieurs ensemble en même temps.\n Par exemple il est possible de visualiser une famille de poduits et ses sous produits."
 example: "Répartition du total des ventes par secteur/rayons/magasins ; Proportion d’hommes/femmes ; Répartition des dépenses par pôle"
 position: 2
 displayType: "portlet"
 defaultSize: [4,4]
 minSize: [3,4]
 responsiveH: 4
-
 name: barChart
 classFile: "barChart.js"
 displayName: Barre
 description: "Cette visualisation vous permet de comparer les performances d'un groupe de donnée.\n Un diagramme en bâtons représente un ensemble de données de même nature ; et donc qui peuvent être comparées. À chaque valeur est associée un segment vertical ou un rectangle dont la hauteur est proportionnelle à la valeur connue. Le rapport des hauteurs fournit le rapport des quantités correspondantes.\n Les données peuvent être facilement comparées à l'œil nu."
 example: "Ventes par canal ; Ventes effectuées par secteur, par mois"
 position: 4
 displayType: "portlet"
 defaultSize: [6,6]
 minSize: [4,4]
 responsiveH: ~
-
 name: bubbleChart
 classFile: "bubbleChart.js"
 displayName: Bulles
 description: "Cette visualisation permet une représentation des données en trois dimensions (axe x, axe y, taille de la bulle).\n Les graphiques à bulles peuvent être considérés comme une variante du graphique à nuage de points, dans lequel les points de données sont remplacés par des bulles. Si vos données comportent 3 dimensions.\n Les graphiques à bulles peuvent faciliter la compréhension des interactions sociales, économiques, médicales et autres interactions scientifiques.\n Par exemple dans un magasin proposant plusieurs gammes de produits, il sera possible de regarder le chiffre d'affaire réalisé (axe y) par jour (axe x) avec le nombre de produits vendus (taille de la bulle). Cela permet de visualiser un éventuel changement de comportement d'achat (moins de produit achetés mais plus de chiffre d'affaire indique une tendance d'achat de produit de luxe. A l'inverse plus de produits achetés mais une baisse de chiffre d'affaire indique une tendance d'achat de produits d'entrée de gamme."
 example: "Taux de satisfaction par produits indépendamment du prix (Rapport qualité prix) ; Délai de paiement de clients, en fonction du prix dû ; Chiffres d’affaires par magasins en tenant compte des clients fidélisés"
 position: 6
 displayType: "portlet"
 defaultSize: [6,4]
 minSize: [6,4]
 responsiveH: 4
-
 name: tableChart
 classFile: "tableChart.js"
 displayName: Tableau Top
 description: "Cette visualisation permet d'affficher les valeurs de plusieurs données sous forme de tableau."
 example: "Nombre d’articles restants par rayon ; Récapitulatif du nombre d’employés par magasin ; Récapitulatif des salaires versés"
 position: 7
 displayType: "portlet"
 defaultSize: [9,4]
 minSize: [3,2]
 responsiveH: 4
-
 name: tableSimpleChart
 classFile: "tableSimpleChart.js"
 displayName: Tableau
 description: "Cette visualisation permet d'affficher les valeurs de plusieurs données sous forme de tableau."
 example: "Nombre d’articles restants par rayon ; Récapitulatif du nombre d’employés par magasin ; Récapitulatif des salaires versés"
 position: 36
 displayType: "portlet"
 defaultSize: [9,4]
 minSize: [3,2]
 responsiveH: 4
-
 name: treemapChart
 classFile: "treemapChart.js"
 displayName: Treemap
 description: "Les cartes proportionnelles montrent les parties d'un tout. Elles affichent l'information hiérarchique comme un emboitement de rectangles dont la taille et la couleur varie en fonction de la valeur associée.\n La taille de chaque rectangle représente une quantité et donc proportionnelle à la valeur qu'elle représente, alors que la couleur représente une catégorie de la donnée.\n Les cartes proportionnelles permettent d'identifier les tendances et faire des comparaisons rapidement.\n Il est possible de visualiser plusieurs données (groupe de catégories et sous-groupe de chaque catégorie)."
 example: "Vente par gammes de produits (en %) ; Ventes par canal ; Classement des performances effectuées"
 position: 8
 displayType: "portlet"
 defaultSize: [6,6]
 minSize: [6,4]
 responsiveH: 8
-
 name: heatmapChart
 classFile: "heatmapChart.js"
 displayName: Heat Calendar
 description: "Cette visualisation permet sous forme de calendrier, de faire correspondre chaque jour l'intensité de la valeur d'une donnée à un nuancier de couleurs.\n Ainsi vous repérez facillement les journées exeptionnelles tant positives que négatives."
 example: "Nombre de visites par jour ; Nombre de réclamations par jour"
 position: 9
 displayType: "portlet"
 defaultSize: [12,4]
 minSize: [12,4]
 responsiveH: ~
-
 name: iconColorChart
 classFile: "iconColorChart.js"
 displayName: "Icônes"
 description: "Cette visualisation permet de comparer sous forme de remplissage d'icône les propotions des valeurs d'une donnée.\n Par exemple on pourra comparer la répartition des notes données lors d'une enquête.\n Il est possible de comparer X valeurs et pour chaque valeur il est possible de choisir l'icône et la couleur de remplissage."
 example: ~
 position: 12
 displayType: "portlet"
 defaultSize: [3,2]
 minSize: [3,2]
 responsiveH: ~
-
 name: metricColorChart
 classFile: "metricColorChart.js"
 displayName: Metric Couleur
 description: "Cette visualisation permet d'afficher des valeurs clés d'une donnée (un chiffre d'affaire, le panier moyen des ventes ou encore le record du plus gros panier de vente)"
 example: "Chiffre d'affaires journalier ; Prix d’un panier moyen"
 position: 15
 displayType: "custom"
 defaultSize: [4,2]
 minSize: [3,2]
 responsiveH: 2
-
 name: gaugeLiquidChart
 classFile: "gaugeLiquidChart.js"
 displayName: Jauge liquide
 description: "Cette visualisation permet de comparer une valeur à sa valeur maximal et minimal d'atteinte. Le remplissage de la jauge correspond au taux de la valeur.\n Par exemple on pourra visualiser la moyenne des notes obtenues lors d'une enquête par rapport à la note maximale."
 example: "Taux de fidélité ; Taux de conversion"
 position: 16
 displayType: "portlet"
 defaultSize: [4,2]
 minSize: [3,2]
 responsiveH: 2
-
 name: streamgraphChart
 classFile: "streamgraphChart.js"
 displayName: Streamgraph
 description: "Cette visualisation est une variante du graphique \"Aire\" standard. Avec un graphique en flux vous pouvez visualiser les valeurs cumulées de données au cours du temps. La ligne de base est décalée au centre des valeurs. Chaque couleur indique l'évolution d'une valeur au cours du temps."
 example: "Performance de ventes d’articles au cours du temps"
 position: 18
 displayType: "portlet"
 defaultSize: [6,4]
 minSize: [6,4]
 responsiveH: 4
-
 name: clockChart
 classFile: "clockChart.js"
 displayName: Horloge
 description: "Cette visualisation permet d'afficher la répartition horaire d’une donnée sur une plage horaire définie. Il n'est cependant pas possible de définir plusieurs plages horaires"
 example: "Nombre de ventes par tranche horaire ; Nombre de visites par tranche horaire ; Temps d’attente par heure"
 position: 20
 displayType: "portlet"
 defaultSize: [4,4]
 minSize: [3,4]
 responsiveH: 4
-
 name: iconGaugeChart
 classFile: "iconGaugeChart.js"
 displayName: icône Jauge
 description: "Cette visualisation permet de comparer 2 valeurs d'une donnée.\n Par exemple on pourra comparer la répartition des genres.\n Il est possible de comparer X valeurs et pour chaque valeur il est possible de choisir l'icône et la couleur de la jauge pour chacune des valeurs."
 example: ~
 position: 21
 displayType: "portlet"
 defaultSize: [6,2]
 minSize: [4,2]
 responsiveH: 2
-
 name: mapChart
 classFile: "mapChart.js"
 displayName: Carte
 description: "Cette visualisation vous permet de localiser visuellement les points stratégiques de vos données. Selon le zoom les points sont regroupés pour une meilleure interprétation, la taille du point vous permet d'identifier facilement les zones influentes.\n Il est necessaire que vos données à intégrer sur la carte soit localisées avec les coordonées GPS."
 example: "Répartition des clients en France ; Répartition des magasins"
 position: 22
 displayType: "portlet"
 defaultSize: [6,4]
 minSize: [4,4]
 responsiveH: 4
-
 name: sankeyChart
 classFile: "sankeyChart.js"
 displayName: Sankey
 description: "Cette visulalisation est un type de diagramme de flux qui permet de visualiser l’association des éléments de données.\n Les associations des éléments sont représentées par des flèches dirigées, dont la largeur est proportionnelle à la quantité de flux visualisée. Si un flux est deux fois plus large, il représente le double de la quantité."
 example: ~
 position: 23
 displayType: "portlet"
 defaultSize: [6,4]
 minSize: [6,2]
 responsiveH: ~
-
 name: pyramidChart
 classFile: "pyramidChart.js"
 displayName: Pyramid
 description: "Cette visualisation permet de représenter la répartition de deux valeurs d'une donnée selon une seconde donnée. Elle est constituée de deux histogrammes juxtaposés, un pour chaque valeur de la première donnée, où la seconde donnée est portée horizontalement et les valeurs de la première données verticalement.\n Le plus souvant cette visualisation sera utilisée pour représenter la répartition par sexe et âge d'une population où les effectifs sont portés horizontalement et les âges verticalement."
 example: ~
 position: 23
 displayType: "portlet"
 defaultSize: [6,4]
 minSize: [4,4]
 responsiveH: ~
-
 name: wordcloudChart
 classFile: "wordcloudChart.js"
 displayName: Nuage de mots
 description: "Le nuage de mot permet de mettre en exergue des mots selon une valeur.\n Cela permet par exemple de mettre en avant les mots les plus fréquement rencontrés dans les commentaires d'une enquête ou bien de mettre en avant les marques selon le chiffre d'affaire réalisé sur chacune d'entre elle."
 example: ~
 position: 23
 displayType: "portlet"
 defaultSize: [6,4]
 minSize: [4,4]
 responsiveH: 4
-
 name: heatmapAxisChart
 classFile: "heatmapAxisChart.js"
 displayName: Heatmap
 description: "Cette visualisation permet de faire correspondre l'intensité d'une donnée à un nuancier de couleurs sur une matrice à deux dimensions.\n Ce procédé permet de donner à des données un aspect visuel plus facile à interpréter qu'un tableau de statistiques."
 example: ~
 position: 24
 displayType: "portlet"
 defaultSize: [6,4]
 minSize: [3,2]
 responsiveH: ~
-
 name: listChart
 classFile: "listChart.js"
 displayName: Liste
 description: "Cette visualisation permet d'affficher les valeurs de plusieurs données ayant des relations entre elles. Par exemple il est possible d'afficher le titre des parcours de randonnées et le temps moyen necessaire pour effectuer le parcours."
 example: ~
 position: 25
 displayType: "portlet"
 defaultSize: [9,4]
 minSize: [3,2]
 responsiveH: 4
-
 name: textZone
 classFile: "textZone.js"
 displayName: Zone de texte
 description: "Cela permet de mettre en place du texte et ou images sur votre tableau de bord afin de fournir une explication ou séparer différents thèmes de votre tableau de bord.\n Le format d'écriture utilisé est le Markdown (lien https://github.com/adam-p/markdown-here/wiki/Markdown-Cheatsheet)"
 example: ~
 position: 26
 displayType: "custom"
 defaultSize: [4,2]
 minSize: [3,1]
 responsiveH: 1
-
 name: tableCrossedChart
 classFile: "tableCrossedChart.js"
 displayName: Tableau croisé
 description: "Un tableau croisé vous perment de regrouper des données selon leurs valeurs et faire une opération de calul entre elles.\n Par exemple il est possible de visualiser  la répartition du budget de plusieurs pôles d'activité d'une entreprise, selon l'allocation de ce budget (formation, employé, matériel...)"
 example: ~
 position: 27
 displayType: "portlet"
 defaultSize: [9,4]
 minSize: [3,2]
 responsiveH: 4
-
 name: interactiveBubbleChart
 classFile: "interactiveBubbleChart.js"
 displayName: Bulles dynamiques
 description: "Description de la visualisation Bulles dynamiques"
 example: ~
 position: 28
 displayType: "portlet"
 defaultSize: [6,4]
 minSize: [3,2]
 responsiveH: 4
-
 name: timeDotChart
 classFile: "timeDotChart.js"
 displayName: Time Dot
 description: "Cette visualisation permet une représentation des données en trois dimensions (axe heures, axe date, taille du point).Cette visualisation nécessite l'utilisation d'un champ date complet avec la date et l'heure.\n Cette visualisation vous permet de mettre en évidence la répartition d'une donnée sous forme de points en fonction de l'heure et d'une unité de temps choisie. La valeur est affichée sous forme de points ayant une taille proportionnelle à cette dernière.\n Par exemple vous pouvez visualiser le chiffre d'affaire réalisé selon l'heure et le mois de vente."
 example: ~
 position: 29
 displayType: "portlet"
 defaultSize: [4,2]
 minSize: [4,2]
 responsiveH: 2
-
 name: selectorChart
 classFile: "selectorChart.js"
 displayName: Selecteur
 description: "Cette visualisation vous permet d'afficher les valeurs d'une donnée sous forme de bouton.\n Cela permet d'intégrer des filtres rapidement acessibles, n'ayant pas besoin de mise en situation, mais qui sont souvent utilisés pour étudier les autres données"
 example: ~
 position: 30
 displayType: "portlet"
 defaultSize: [6,2]
 minSize: [3,2]
 responsiveH: 2
-
 name: barHChart
 classFile: "barHChart.js"
 displayName: Barre Horizontale
 description: "Contrairement à un diagramme en barre classique cette visualisation permet d'étudier la ventilation des valeurs de chaque composant.\n Par exemple sur une question nous allons étudier la ventilation des réponses selon le genre. On aura ainsi par exemple 75% oui et 25% nom pour les femmes et 63% oui et 37% non pour les hommes."
 example: ~
 position: 31
 displayType: "portlet"
 defaultSize: [9,4]
 minSize: [4,2]
 responsiveH: ~
-
 name: radarChart
 classFile: "radarChart.js"
 displayName: Radar
 description: "Le radar montre des données multidimensionnelles sous la forme d'un graphique à deux dimensions de plus de trois variables représentées sur l'axe à partir du même point.\n Cette visualisation permet de tracer une ou plusieurs séries de valeur sur un axe gradué partant du point central pour chaque valeur. Ces axes sont disposés de façon équiangles les uns des autres."
 example: ~
 position: 32
 displayType: "portlet"
 defaultSize: [4,4]
 minSize: [3,4]
 responsiveH: 4
-
 name: correlationMatrixChart
 classFile: "correlationMatrixChart.js"
 displayName: Matrice de correlation
 description: "Description de la visualisation matrice de corrélation"
 example: ~
 position: 33
 displayType: "portlet"
 defaultSize: [4,4]
 minSize: [3,2]
 responsiveH: ~
-
 name: gaugeComparaisonChart
 classFile: "gaugeComparaisonChart.js"
 displayName: Jauge de comparaison
 description: "Jauge permettant de comparer une valeur d'une période avec la valeur de la période précédente de même durée"
 example: ~
 position: 34
 displayType: "portlet"
 defaultSize: [4,4]
 minSize: [4,4]
 responsiveH: 4
-
 name: dotRepartitionChart
 classFile: "dotRepartitionChart.js"
 displayName: Répartition de points
 description: "100 points permettent de comprendre la répartition des valeurs"
 example: ~
 position: 35
 displayType: "portlet"
 defaultSize: [4,4]
 minSize: [4,4]
 responsiveH: 4
