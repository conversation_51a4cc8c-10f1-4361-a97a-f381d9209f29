{"extends": "_base_operation_ligne.json", "nom": "<PERSON><PERSON><PERSON>", "identifier": "pharmavie", "xml_parser": {"booleans": ["PROMOM1", "PROMOM12"]}, "mapping": {"properties": {"POSITION_GEO": {"type": "geo_point"}, "LIBELLESEGMENT": {"type": "keyword"}, "MAILINGS": {"type": "nested", "include_in_parent": true, "properties": {"TRANCHEAGE": {"type": "keyword"}, "VENTES": {"type": "nested", "include_in_parent": true, "properties": {"NOMOP": {"type": "keyword"}, "REVENU": {"type": "boolean"}, "TYPECLIENT": {"type": "keyword"}, "SEXE": {"type": "keyword"}, "LIGNES": {"type": "nested", "include_in_parent": true, "properties": {"PROMOM1": {"type": "boolean"}, "PROMOM12": {"type": "boolean"}, "CODELABORATOIRE": {"type": "keyword"}, "LABORATOIRE": {"type": "keyword"}}}}}}}}}}