{"extends": "_base_client.json", "mapping": {"properties": {"CHEQUES": {"include_in_parent": true, "type": "nested", "properties": {"DATEFINVALIDITE": {"format": "strict_date", "type": "date"}, "TYPECHEQUE": {"type": "keyword"}, "VALIDE": {"type": "keyword"}, "CODECLIENT": {"type": "keyword"}, "DATEEMISSION": {"format": "strict_date", "type": "date"}, "CANAL": {"type": "keyword"}, "NBJOUR_ENC": {"type": "long"}, "NBSEM_ENC": {"type": "float"}, "ENCAISSEMENT": {"type": "boolean"}, "NUMEROCHEQUE": {"type": "keyword"}, "DATEENCAISSEMENT": {"format": "strict_date", "type": "date"}, "MONTANT": {"type": "float"}, "PERIODEANTERIEUR": {"type": "integer"}}}}}}