# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
parameters:
  imports.path: "%kernel.project_dir%/config/mappings"
  imports.cache_file: "%kernel.project_dir%/var/supervisor.json"
  credentials_file: "%kernel.project_dir%/config/credentials.yaml"
  old_sound_rabbit_mq.batch_consumer.class: App\Consumer\BatchConsumer
  email.warning.recipients: [ "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>" ]
  email.dev.recipients: [ "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>" ]
  email.verif.recipients: [ "<EMAIL>", "<EMAIL>", "a.beaul<PERSON>@alienor.net", "<EMAIL>", "<EMAIL>" ]
  rabbitmq.url: "%env(RABBITMQ_URL)%"
  elastic.credentials: { user: "%env(ELASTICSEARCH_LOGIN)%", pass: "%env(ELASTICSEARCH_PASS)%" }

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        public: true       # Allows optimizing the container by removing unused services; this also means
                            # fetching services directly from the container via $container->get() won't work.
                            # The best practice is to be explicit about your dependencies anyway.
        bind:
          $importPath: "%imports.path%"
          $cacheFile:  "%imports.cache_file%"
          $credentialsFile:  "%credentials_file%"
          $emailWarningRecipient: "%email.warning.recipients%"
          $emailDevRecipient: "%email.dev.recipients%"
          $emailVerifRecipient: "%email.verif.recipients%"
          $importCheckerProducer: "@old_sound_rabbit_mq.import_checker_producer"
          $defaultCredentials: "%elastic.credentials%"
          $logsDir: "%kernel.logs_dir%"
          $projectDir: "%kernel.project_dir%"
          $nodePath: "%env(NODE_PATH)%"

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/*'
        exclude: '../src/{DependencyInjection,Entity,Migrations,Tests,Kernel.php}'

    # controllers are imported separately to make sure services can be injected
    # as action arguments even if you don't extend any base controller class
#    App\Controller\:
#        resource: '../src/Controller'
#        tags: ['controller.service_arguments']

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones

    App\Consumer\BatchConsumer:
      autowire: false
      autoconfigure: false

    App\Service\RabbitMqSupervisor:
      arguments:
        - "@phobetor_rabbitmq_supervisor.supervisor_service"
        - "%phobetor_rabbitmq_supervisor.paths%"
        - "%phobetor_rabbitmq_supervisor.commands%"
        - "%phobetor_rabbitmq_supervisor.consumers%"
        - "%phobetor_rabbitmq_supervisor.multiple_consumers%"
        - "%phobetor_rabbitmq_supervisor.batch_consumers%"
        - "%phobetor_rabbitmq_supervisor.rpc_servers%"
        - "%phobetor_rabbitmq_supervisor.config%"
        - "%phobetor_rabbitmq_supervisor.sock_file_permissions%"
        - "%kernel.root_dir%"
        - "%kernel.environment%"
        - "%imports.path%"
        - "%kernel.project_dir%"
        - "%phobetor_rabbitmq_supervisor.paths%"

    App\Service\LoadBalancer:
      arguments:
        $consumer: '@old_sound_rabbit_mq.xml_batch_batch'

    phobetor_rabbitmq_supervisor:
      alias: App\Service\RabbitMqSupervisor

    Phobetor\RabbitMqSupervisorBundle\Services\Supervisor:
      alias: phobetor_rabbitmq_supervisor.supervisor_service

    App\Service\IndexManagerLogs:
      arguments:
        - "%env(ELASTICSEARCH_HOST)%"
        - "%env(IMPORT_LOGS_INDEX)%"
        - "%env(IMPORT_LOGS_LOGIN)%"
        - "%env(IMPORT_LOGS_PASS)%"

    App\Service\IndexManagerLogsVerif:
      arguments:
        - "%env(ELASTICSEARCH_HOST)%"
        - "%env(IMPORT_LOGS_VERIF_INDEX)%"
        - "%env(IMPORT_LOGS_LOGIN)%"
        - "%env(IMPORT_LOGS_PASS)%"
