# see https://symfony.com/doc/current/reference/configuration/framework.html
framework:
  secret: "%env(APP_SECRET)%"

  #csrf_protection: true
  http_method_override: false
  handle_all_throwables: false
  annotations:
    enabled: false

  # Enables session support. Note that the session will ONLY be started if you read or write from it.
  # Remove or comment this section to explicitly disable session support.
  session:
    save_path: "%kernel.project_dir%/var/sessions"
    handler_id: session.handler.native_file
    cookie_secure: auto
    cookie_samesite: lax
    storage_factory_id: session.storage.factory.native

  #esi: true
  #fragments: true
  php_errors:
    log: true

artgris_file_manager:
  conf:
    default:
      service: "docs_et_stats_service"

when@test:
  framework:
    test: true
    session:
      storage_factory_id: session.storage.factory.mock_file
