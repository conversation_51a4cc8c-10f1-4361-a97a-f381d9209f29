import bcrypt from 'bcryptjs';
const { hashSync, compareSync } = bcrypt;
import * as jwt from "./jwt";
import prisma from '$lib/prisma'
import type { User } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import { addDays } from "date-fns"
import {sendMail} from "$lib/mailer";
import registerEmailTemplate from "$lib/emails/register";
import resetPasswordEmailTemplate from "$lib/emails/reset-password";
import type {UserWithDevices} from "$lib/webauthn";

type UserToken = User & { accessToken: string }

export type UserRegistration = User & {
	passwordConfirmation: string,
}

export function cleanUser(user: UserWithDevices): User {
	delete user.password;
	delete user.tokenExpiredAt;
	delete user.token;
	delete user.devices;
	delete user.challenge;
	return user;
}

export async function register(data: UserRegistration): Promise<User> {
	data.password = hashSync(data.password, 8);
	data.created_at = new Date();
	data.enabled = false;
	data.token = uuidv4();
	data.tokenExpiredAt = addDays(new Date(), 7);
	delete data.passwordConfirmation;
	let user = await prisma.user.create({
		data
	})

	sendRegistrationMail(data);

	user = cleanUser(user);

	return user;
}

export async function resetPassword(email: string): Promise<User> {
	const user = await prisma.user.findUnique({
		where: {
			email
		}
	});
	if (!user || !user.enabled) {
		return null;
	}

	user.token = uuidv4();

	await prisma.user.update({
		data: {
			token: user.token,
			tokenExpiredAt: addDays(new Date(), 1)
		},
		where: {
			email
		}
	});

	const accessToken = await jwt.signAccessToken(user);

	sendLoginLinkMail({ ...user, accessToken });

	return user;
}

export async function login(data: User): Promise<UserToken> {
	const { email, password } = data;
	const user = await prisma.user.findUnique({
		where: {
			email
		}
	});
	if (!user || !user.enabled) {
		return null;
	}
	const checkPassword = compareSync(password, user.password)
	if (!checkPassword) return null;
	const accessToken = await jwt.signAccessToken(user);
	return { ...await getUser(user.id), accessToken };
}

export async function getUser(id: number): Promise<User> {
	let user = await prisma.user.findUnique({
		where: {
			id: id
		},
		include: {
			bookmarks: {
				include: {
					userBookmarks: {
						where: {
							user: {
								enabled: true
							}
						}
					}
				}
			},
		},
	})
	user = cleanUser(user);
	return user;
}

async function enableUser(id: number): Promise<User> {
	let user = await prisma.user.update({
		data: { enabled: true },
		where: {
			id: id
		}
	});
	user = cleanUser(user);
	return user;
}

export async function updateUser(id: number, data: UserRegistration): Promise<User> {
	if (data.password !== "") {
		data.password = hashSync(data.password, 8);
	} else {
		delete data.password;
	}
	delete data.passwordConfirmation;
	let user = await prisma.user.update({
		data,
		where: {
			id: id
		}
	})
	user = cleanUser(user);
	return user;
}

export async function disableUser(id: number): Promise<User> {
	let user = await prisma.user.update({
		data: {
			enabled: false
		},
		where: {
			id: id
		}
	})
	user = cleanUser(user);
	return user;
}


export async function getUserFromToken(token: string): Promise<User> {
	const user = await prisma.user.findFirst({
		where: {
			token: token
		}
	});
	if (!user || new Date(user.tokenExpiredAt) <= new Date()) {
		return null;
	}
	return user;
}

export async function enableUserFromToken(token: string): Promise<UserToken> {
	let user = await getUserFromToken(token);
	if (!user) {
		return null;
	}
	user = await enableUser(user.id);
	const accessToken = await jwt.signAccessToken(user);
	return { ...user, accessToken };
}

export async function getUserFromJwt(token: string): Promise<User> {
	const data = await jwt.verifyAccessToken(token);
	// @ts-ignore
	return data.payload;
}

export async function getJwtFromUserToken(token: string): Promise<string> {
	const user = getUserFromToken(token);
	if (!user) {
		return null;
	}
	return jwt.signAccessToken(user);
}

export async function addUserChallenge(id: number, challenge: string): Promise<User> {
	return await prisma.user.update({
		data: { challenge },
		where: {
			id: id
		}
	});
}

/*** Emails ***/

export async function sendRegistrationMail(data: User): Promise<any> {
	return await sendMail(data.email, "MyPresence - Inscription", registerEmailTemplate(data));
}

export async function sendLoginLinkMail(data: User): Promise<any> {
	return await sendMail(data.email, "MyPresence - Mot de passe oublié", resetPasswordEmailTemplate(data));
}