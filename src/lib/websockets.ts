import type { Server, Socket as IoSocket } from 'socket.io';
import type { User, Presence } from '@prisma/client';

export const ws: Server = null;

export const socketConnection = {
    _instance: null,
    get instance (): Server {
        return this._instance;
    },
    set instance (io: Server) {
        if (!this._instance) {
            this._instance = io;
        }
    }
}

export default async function initWebsockets(io: Server): Promise<void> {
    socketConnection.instance = io;
    io.on('connection', (socket: IoSocket) => {

        socket.on('join-user-room', ({ id }) => {
            socket.join(`user-${id}`);
        });

        socket.on('join-service-room', ({ id }) => {
            socket.join(`service-${id}`);
        });

        socket.on('update-calendar', ({ user, presence }) => {
            try {
                updateUserCalendar(socket, user, presence);
            } catch (e) {
                console.log(e);
            }
        });

    });
}

export function updateUserCalendar(socket: IoSocket, user: User, presence: Presence): void {
    socket
        .broadcast
        .to("service-" + user.service)
        .to("service-all")
        .to("service-alienor")
        .to("service-aquitem")
        .emit("update-calendar-service", presence);
    socket
        .broadcast
        .to("user-" + user.id)
        .emit("update-calendar-user", presence);
    socket
        .broadcast
        .to("user-" + user.id)
        .emit("update-calendar-self", presence);
}