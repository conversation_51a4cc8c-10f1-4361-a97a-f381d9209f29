<script lang="ts">
    import Switch from "$lib/components/common/Switch.svelte";
    import {browser} from "$app/env";
    import { startRegistration, browserSupportsWebauthn } from '@simplewebauthn/browser';
    import {user} from "$lib/stores";
    import {createForm} from "svelte-forms-lib";
    import {accountSchema, userServices} from "$lib/validation";
    import api from "$lib/api";
    import { toast } from '@zerodevx/svelte-toast'
    import Cookies from 'js-cookie';
    import {goto} from "$app/navigation";
    import {onMount} from "svelte";
    import BookmarkForm from "$lib/components/BookmarkForm.svelte";

    let form, errors, handleSubmit;

    function initForm() {
        ({ form, errors, handleSubmit } = createForm({
            initialValues: {
                firstname: $user.firstname,
                lastname: $user.lastname,
                service: $user.service,
                email: $user.email,
                password: "",
                passwordConfirmation: "",
                hourStartAM: $user.hourStartAM,
                hourEndAM: $user.hourEndAM,
                hourStartPM: $user.hourStartPM,
                hourEndPM: $user.hourEndPM,
            },
            validationSchema: accountSchema,
            onSubmit: async values => {
                try {
                    delete values.email;
                    let response = await api.post("/account", values);
                    $user = response.data;
                    toast.push('Les modifications ont été enregistrées avec succès');
                } catch (e) {
                    if (e.response.data.errors) {
                        e.response.data.errors.forEach((err) => {
                            $errors[err.field] = err.message;
                        })
                    }
                }
            }
        }));
    }

    function logout() {
        Cookies.remove("jwt");
        $user = null;
        goto("/login");
    }

    /*** Dark mode ***/

    let darkMode = isDarkMode();

    function isDarkMode() {
        return typeof localStorage !== 'undefined' && (localStorage.theme === 'dark' || (!('theme' in localStorage) && typeof window !== 'undefined' && window.matchMedia('(prefers-color-scheme: dark)').matches));
    }

    function toggleDarkMode() {
        if (isDarkMode()) {
            localStorage.setItem('theme', 'light');
            document.documentElement.classList.remove('dark');
            darkMode = false;
        } else {
            localStorage.setItem('theme', 'dark');
            document.documentElement.classList.add('dark');
            darkMode = true;
        }
    }

    /*** Notifications ***/

    let notificationEnabled = isNotificationGranted();
    let permissionStatus;
    let notifText: HTMLElement;

    function isNotificationGranted() {
        if (allowNotifications()) {
            return Notification.permission === "granted";
        }
        return false;
    }

    function allowNotifications() {
        return !(!browser || !("Notification" in window) || !("serviceWorker" in navigator));

    }

    async function askPermission() {
        if (!allowNotifications()) {
            return false;
        }
        const permission = await Notification.requestPermission();
        if (permission === "granted") {
            await activateNotifications();
        } else {
            notificationEnabled = false;
        }
    }

    async function toggleNotification(e) {
        if ((permissionStatus.state === "granted" || permissionStatus.state === "denied") && notifText instanceof HTMLElement) {
            notificationEnabled = await Notification.requestPermission() === "granted";
            notifText.classList.add("animate-bounce");
            notifText.classList.add("text-red-800");
            notifText.classList.add("dark:text-red-500");
            await new Promise((r) => setTimeout(r, 1500));
            notifText.classList.remove("animate-bounce");
            notifText.classList.remove("text-red-800");
            notifText.classList.add("dark:text-red-500");
            return;
        }
        if (!isNotificationGranted()) {
            if (e.detail.checked) {
                await askPermission();
            }
        } else {
            notificationEnabled = true;
        }
    }

    async function activateNotifications() {
        const registration = await navigator.serviceWorker.getRegistration();
        let subscription = await registration.pushManager.getSubscription();
        if (!subscription) {
            subscription = await registration.pushManager.subscribe({
                userVisibleOnly: true,
                applicationServerKey: import.meta.env.VITE_VAPID_PUBLIC_KEY
            });
        }
        await saveSubscription(subscription);
    }

    async function saveSubscription(subscription: PushSubscription) {
        await api.post("/push/subscribe", subscription);
    }

    /*** Webauthn ***/

    let webauthnEnabled = false;

    async function webauthnRegister() {

        const response = await api.post("/auth/webauthn/generate-registration-options", { email: $user.email });
        let attResp;

        try {
            attResp = await startRegistration(response.data);
        } catch (error) {
            // Some basic error handling
            if (error.name === 'InvalidStateError') {
                toast.push("Erreur: L'authentificateur est probablement déjà enregistré par l'utilisateur");
            } else {
                toast.push("Une erreur s'est produite");
                webauthnEnabled = false;
            }

            throw error;
        }

        try {
            const verificationResp = await api.post('/auth/webauthn/verify-registration', { ...attResp, email: $user.email });
            if (verificationResp.data && verificationResp.data.verified) {
                toast.push('La connexion par empreinte digitale est désormais activée');
                return;
            }
        } catch (e) {
            console.log(e);
        }
        webauthnEnabled = false;
        toast.push("L'enregistrement de vos paramètres d'authentification a échoué");
    }

    onMount(async () => {
        if (browser) {
            // @ts-ignore
            permissionStatus = await navigator.permissions.query({name: 'push', userVisibleOnly: true });
            permissionStatus.onchange = function (e) {
                permissionStatus = this;
                notificationEnabled = this.state === "granted";
            };
        }
    });

    $: $user && initForm();
</script>

<svelte:head>
    <title>MyPresence - Mon profil</title>
</svelte:head>

{#if $form}

    <form on:submit|preventDefault={handleSubmit}>
        <section class="max-w-4xl p-6 mx-auto bg-white rounded-md shadow-md dark:bg-gray-800">
            <div class="flex justify-center">
                <div class="flex flex-col items-end">
                    <div class="mb-4">
                        <Switch checked={darkMode} on:change={toggleDarkMode} id="theme-sombre" label="Thème sombre"/>
                    </div>
                    {#if allowNotifications()}
                        <div class="mb-2">
                            <Switch bind:checked={notificationEnabled} on:change={toggleNotification} id="push" label="Notifications push"/>
                        </div>
                    {/if}
                </div>
            </div>
            <div class="grid grid-cols-1 gap-3 mt-2 md:grid-cols-1">
                {#if allowNotifications() && permissionStatus && (permissionStatus.state === "granted" || permissionStatus.state === "denied")}
                    <p bind:this={notifText} class="italic text-xs text-center dark:text-white">Utiliser les paramètres de votre navigateur pour gérer les notifications</p>
                {/if}
                {#if browserSupportsWebauthn()}
                    <div class="flex items-center justify-center">
                        <button on:click|preventDefault={webauthnRegister} class="sm:whitespace-nowrap text-sm px-3 py-2 leading-5 text-white transition-colors duration-200 transform bg-fiducial-red rounded-md hover:bg-gray-600 focus:outline-none focus:bg-gray-600">
                            <i class="fa fa-fingerprint mr-2"></i> Activer la connexion via empreinte digitale
                        </button>
                    </div>
                {/if}
                <div class="flex items-center justify-center">
                    <button on:click|preventDefault={logout} class="text-sm px-3 py-2 leading-5 text-white transition-colors duration-200 transform bg-fiducial-red rounded-md hover:bg-gray-600 focus:outline-none focus:bg-gray-600">
                        <i class="fa fa-sign-out-alt mr-2"></i> Déconnexion
                    </button>
                </div>
            </div>
        </section>
        <section class="max-w-4xl mt-6 p-6 mx-auto bg-white rounded-md shadow-md dark:bg-gray-800">
            <h2 class="text-lg font-semibold text-aquitem-dark capitalize dark:text-white">Accès rapide</h2>
            
        </section>
        <section class="max-w-4xl mt-6 p-6 mx-auto bg-white rounded-md shadow-md dark:bg-gray-800">
            <h2 class="text-lg font-semibold text-aquitem-dark capitalize dark:text-white">Mon compte</h2>

                <div class="grid grid-cols-1 mt-4 gap-6 sm:grid-cols-2">
                    <div>
                        <label class="text-aquitem-dark dark:text-gray-200" for="email">Adresse email</label>
                        <input bind:value={$form.email} disabled="disabled" id="email" type="email" class="block w-full px-4 py-2 mt-2 disabled text-aquitem-dark bg-gray-200 border border-gray-300 rounded-md dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-500 focus:outline-none focus:ring">
                    </div>

                    <div class="col-start-1">
                        <label class="text-aquitem-dark dark:text-gray-200" for="service">Service*</label>
                        <select bind:value={$form.service} required id="service" class="block w-full px-4 py-2 mt-2 text-aquitem-dark bg-white border border-gray-300 rounded-md dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-500 focus:outline-none focus:ring">
                            {#each userServices as service }
                                <option value="{service}">{service}</option>
                            {/each}
                        </select>
                        {#if $errors.service}
                            <span class="text-xs text-presence-red">{$errors.service}</span>
                        {/if}
                    </div>

                    <div class="col-start-1">
                        <label class="text-aquitem-dark dark:text-gray-200" for="hourStartAM">Horaires matin*</label>
                        <div class="flex">
                            <input bind:value={$form.hourStartAM} maxlength="5" type="text" id="hourStartAM" placeholder="09:00" class="block w-full px-4 py-2 mt-2 text-aquitem-dark bg-white border border-gray-300 rounded-l-md dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-500 focus:outline-none focus:ring">
                            <input bind:value={$form.hourEndAM} maxlength="5" type="text" id="hourEndAM" placeholder="12:30" class="block w-full px-4 py-2 mt-2 text-aquitem-dark bg-white border border-gray-300 rounded-r-md dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-500 focus:outline-none focus:ring">
                        </div>
                        {#if $errors.hourStartAM || $errors.hourEndAM}
                            <span class="text-xs text-presence-red">{$errors.hourStartAM || $errors.hourEndAM}</span>
                        {/if}
                    </div>

                    <div>
                        <label class="text-aquitem-dark dark:text-gray-200" for="hourStartPM">Horaires après-midi*</label>
                        <div class="flex">
                            <input bind:value={$form.hourStartPM} maxlength="5" type="text" id="hourStartPM" placeholder="13:30" class="block w-full px-4 py-2 mt-2 text-aquitem-dark bg-white border border-gray-300 rounded-l-md dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-500 focus:outline-none focus:ring">
                            <input bind:value={$form.hourEndPM} maxlength="5" type="text" id="hourEndPM" placeholder="18:00" class="block w-full px-4 py-2 mt-2 text-aquitem-dark bg-white border border-gray-300 rounded-r-md dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-500 focus:outline-none focus:ring">
                        </div>
                        {#if $errors.hourStartPM || $errors.hourEndPM}
                            <span class="text-xs text-presence-red">{$errors.hourStartPM || $errors.hourEndPM}</span>
                        {/if}
                    </div>

                    <div class="col-start-1">
                        <label class="text-aquitem-dark dark:text-gray-200" for="firstname">Prénom*</label>
                        <input bind:value={$form.firstname} id="firstname" type="text" class="block w-full px-4 py-2 mt-2 text-aquitem-dark bg-white border border-gray-300 rounded-md dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-500 focus:outline-none focus:ring">
                        {#if $errors.firstname}
                            <span class="text-xs text-presence-red">{$errors.firstname}</span>
                        {/if}
                    </div>

                    <div>
                        <label class="text-aquitem-dark dark:text-gray-200" for="lastname">Nom*</label>
                        <input bind:value={$form.lastname} id="lastname" type="text" class="block w-full px-4 py-2 mt-2 text-aquitem-dark bg-white border border-gray-300 rounded-md dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-500 focus:outline-none focus:ring">
                        {#if $errors.lastname}
                            <span class="text-xs text-presence-red">{$errors.lastname}</span>
                        {/if}
                    </div>

                    <div>
                        <label class="text-aquitem-dark dark:text-gray-200" for="password">Mot de passe</label>
                        <input bind:value={$form.password}  id="password" type="password" class="block w-full px-4 py-2 mt-2 text-aquitem-dark bg-white border border-gray-300 rounded-md dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-500 focus:outline-none focus:ring">
                        {#if $errors.password}
                            <div class="mt-2 text-xs text-presence-red leading-4">{$errors.password}</div>
                        {:else}
                            <div class="mt-2 text-xs text-gray-500 leading-4 italic">{accountSchema.fields.password.tests[0].OPTIONS.message}</div>
                        {/if}
                    </div>

                    <div>
                        <label class="text-aquitem-dark dark:text-gray-200" for="passwordConfirmation">Confirmation</label>
                        <input bind:value={$form.passwordConfirmation} id="passwordConfirmation" type="password" class="block w-full px-4 py-2 mt-2 text-aquitem-dark bg-white border border-gray-300 rounded-md dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-500 focus:outline-none focus:ring">
                        {#if $errors.passwordConfirmation}
                            <span class="text-xs text-presence-red">{$errors.passwordConfirmation}</span>
                        {/if}
                    </div>
                </div>

                <div class="flex justify-between mt-6">
                    <a href="/calendar" class="px-6 py-2 leading-5 text-white transition-colors duration-200 transform bg-fiducial-red rounded-md hover:bg-gray-600 hover:no-underline focus:outline-none focus:bg-gray-600">Annuler</a>
                    <button class="px-6 py-2 leading-5 text-white transition-colors duration-200 transform bg-fiducial-red rounded-md hover:bg-gray-600 focus:outline-none focus:bg-gray-600">Valider</button>
                </div>
        </section>
    </form>
    <BookmarkForm/>
{/if}