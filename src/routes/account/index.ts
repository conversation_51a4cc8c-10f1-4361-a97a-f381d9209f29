import type { RequestHand<PERSON> } from '@sveltejs/kit';
import type { Locals } from '$lib/types';
import {updateUser, getUser, UserRegistration} from "$lib/auth";

// POST /account
export const post: RequestHandler<Locals, UserRegistration> = async (request) => {

	const status = 200;
	if (request.body.email) {
		delete request.body.email;
	}
	await updateUser(request.locals.user.id, request.body);

	const body = await getUser(request.locals.user.id);

	return {
		status,
		body,
	};
};
