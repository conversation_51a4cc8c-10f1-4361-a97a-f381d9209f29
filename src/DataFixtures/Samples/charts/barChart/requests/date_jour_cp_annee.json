{"description": "", "requests": {"initial": [{"key_as_string": "12/06/2023", "key": 1686528000000, "doc_count": 615}, {"key_as_string": "13/06/2023", "key": 1686614400000, "doc_count": 713}, {"key_as_string": "14/06/2023", "key": 1686700800000, "doc_count": 605}, {"key_as_string": "15/06/2023", "key": 1686787200000, "doc_count": 492}, {"key_as_string": "16/06/2023", "key": 1686873600000, "doc_count": 250}, {"key_as_string": "17/06/2023", "key": 1686960000000, "doc_count": 203}], "comparison": [{"key_as_string": "12/06/2022", "key": 1654992000000, "doc_count": 46}, {"key_as_string": "13/06/2022", "key": 1655078400000, "doc_count": 601}, {"key_as_string": "14/06/2022", "key": 1655164800000, "doc_count": 825}, {"key_as_string": "15/06/2022", "key": 1655251200000, "doc_count": 778}, {"key_as_string": "16/06/2022", "key": 1655337600000, "doc_count": 875}, {"key_as_string": "17/06/2022", "key": 1655424000000, "doc_count": 886}, {"key_as_string": "18/06/2022", "key": 1655510400000, "doc_count": 847}], "fetchDateRangeInterval": [{"range": {"minDate": {"value": 1686528000000, "value_as_string": "12/06/2023"}, "maxDate": {"value": 1686960000000, "value_as_string": "17/06/2023"}}, "autoInterval": "d"}, {"range": {"minDate": {"value": 1654992000000, "value_as_string": "12/06/2022"}, "maxDate": {"value": 1655510400000, "value_as_string": "18/06/2022"}}, "autoInterval": "d"}]}, "comparisonFilter": {"field_type": "date", "label": "VENTES.DATEVENTE", "inclusion": "must", "parent": "-1", "source": 4, "type": "range_dynamic", "filter": "last_7_days", "comparison": {"type": "previous_year", "enabled_for_server": false}}}