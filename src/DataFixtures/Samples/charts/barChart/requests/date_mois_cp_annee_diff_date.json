{"description": "", "requests": {"initial": [{"key_as_string": "06/2022", "key": 1654041600000, "doc_count": 2316}, {"key_as_string": "07/2022", "key": 1656633600000, "doc_count": 8153}, {"key_as_string": "08/2022", "key": 1659312000000, "doc_count": 9759}, {"key_as_string": "09/2022", "key": 1661990400000, "doc_count": 10606}, {"key_as_string": "10/2022", "key": 1664582400000, "doc_count": 12910}, {"key_as_string": "11/2022", "key": 1667260800000, "doc_count": 12838}, {"key_as_string": "12/2022", "key": 1669852800000, "doc_count": 19602}, {"key_as_string": "01/2023", "key": 1672531200000, "doc_count": 15419}, {"key_as_string": "02/2023", "key": 1675209600000, "doc_count": 17803}, {"key_as_string": "03/2023", "key": 1677628800000, "doc_count": 21226}, {"key_as_string": "04/2023", "key": 1680307200000, "doc_count": 21862}, {"key_as_string": "05/2023", "key": 1682899200000, "doc_count": 22540}, {"key_as_string": "06/2023", "key": 1685577600000, "doc_count": 13729}], "comparison": [{"key_as_string": "06/2022", "key": 1654041600000, "doc_count": 16809}, {"key_as_string": "07/2022", "key": 1656633600000, "doc_count": 13270}, {"key_as_string": "08/2022", "key": 1659312000000, "doc_count": 12134}, {"key_as_string": "09/2022", "key": 1661990400000, "doc_count": 12745}, {"key_as_string": "10/2022", "key": 1664582400000, "doc_count": 12598}, {"key_as_string": "11/2022", "key": 1667260800000, "doc_count": 11257}, {"key_as_string": "12/2022", "key": 1669852800000, "doc_count": 14512}, {"key_as_string": "01/2023", "key": 1672531200000, "doc_count": 10073}, {"key_as_string": "02/2023", "key": 1675209600000, "doc_count": 10539}, {"key_as_string": "03/2023", "key": 1677628800000, "doc_count": 11731}, {"key_as_string": "04/2023", "key": 1680307200000, "doc_count": 11159}, {"key_as_string": "05/2023", "key": 1682899200000, "doc_count": 10785}, {"key_as_string": "06/2023", "key": 1685577600000, "doc_count": 6139}], "fetchDateRangeInterval": [{"range": {"minDate": {"value": 1623196800000, "value_as_string": "09/06/2021"}, "maxDate": {"value": 1686960000000, "value_as_string": "17/06/2023"}}, "autoInterval": "M"}, {"range": {"minDate": {"value": 1620950400000, "value_as_string": "14/05/2021"}, "maxDate": {"value": 1686960000000, "value_as_string": "17/06/2023"}}, "autoInterval": "M"}]}, "comparisonFilter": {"field_type": "date", "label": "DATECREATION", "inclusion": "must", "parent": "-1", "source": 3, "type": "range_dynamic", "filter": "last_12_months", "comparison": {"type": "previous_year", "enabled_for_server": false}}}