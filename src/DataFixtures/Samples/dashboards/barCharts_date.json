{"title": "BarCharts en folie (date)", "description": "Cas d'utilisation de la visualisation BarChart avec des dates", "shortDescription": "", "icon": "chart-column", "parameters": {"lg": [{"identifier": "barChart:date_jour_cp_annee", "x": 0, "y": 0, "h": 4, "w": 6}, {"identifier": "barChart:date_semaine_cp_annee", "x": 6, "y": 0, "h": 4, "w": 6}, {"identifier": "barChart:date_mois_cp_annee", "x": 0, "y": 0, "h": 4, "w": 6}, {"identifier": "barChart:date_jour_cp_annee_missing", "x": 6, "y": 0, "h": 4, "w": 6}, {"identifier": "barChart:date_mois_cp_annee_2Y", "x": 0, "y": 0, "h": 4, "w": 6}, {"identifier": "barChart:date_mois_cp_annee_2X", "x": 6, "y": 0, "h": 4, "w": 6}, {"identifier": "barChart:date_mois_cp_annee_diff_date", "x": 0, "y": 0, "h": 4, "w": 12}, {"identifier": "barChart:date_jour_cp_periode", "x": 0, "y": 0, "h": 4, "w": 6}, {"identifier": "barChart:date_semaine_cp_periode", "x": 6, "y": 0, "h": 4, "w": 6}, {"identifier": "barChart:date_mois_cp_periode", "x": 0, "y": 0, "h": 4, "w": 6}, {"identifier": "barChart:date_semaine_cp_periode_missing", "x": 6, "y": 0, "h": 4, "w": 6}]}, "comparisonEnabled": true, "requeteurEnabled": true, "colors": [], "baseColors": null, "nestedFilters": true, "filters": [{"field_type": "date", "label": "VENTES.DATEVENTE", "inclusion": "must", "parent": "-1", "source": 6, "type": "range", "range_type": "year", "filter": {"gte": 1609459200000, "lte": 1640995199000}, "comparison": {"type": "previous_year", "enabled_for_server": false}, "nested": true}]}