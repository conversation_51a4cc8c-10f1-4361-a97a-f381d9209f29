{"title": "Field Toggle", "description": "Cas d'utilisation de la feature Field Toggle", "shortDescription": "", "icon": "id-card", "parameters": {"lg": [{"identifier": "metricColorChart:toggle_ca_qtt", "x": 0, "y": 0, "h": 1, "w": 3}]}, "comparisonEnabled": true, "requeteurEnabled": true, "colors": [], "baseColors": null, "nestedFilters": true, "filters": [{"field_type": "date", "label": "DATECREATION", "inclusion": "must", "type": "range_dynamic", "filter": "last_12_months", "comparison": {"type": "previous_year", "filter": "last_12_months", "enabled_for_server": false}, "nested": true}], "fieldToggle": {"source": "field_toggle", "index": 0}}