<?php


namespace App\Services\WebserviceBundle;

use App\Entity\WebserviceBundle\WebserviceResponse;

class WebserviceStatistiquesEtDocuments extends WebserviceBase
{
	CONST ID = "StatistiquesEtDocuments";
	CONST SERVICE_LISTER_CATEGORIE = "listerCategorie";
	CONST SERVICE_AFFICHER_NOMENCLATURE_UTILISATEUR = "afficherNomenclatureUtilisateur";

	protected $id = self::ID;

	/**
	 * @return WebserviceResponse
	 */
	public function listerCategorie() : WebserviceResponse {
		return $this->postRequest(self::SERVICE_LISTER_CATEGORIE);
	}

	public function afficherNomenclatureUtilisateur() : WebserviceResponse
	{
		return $this->postRequest(self::SERVICE_AFFICHER_NOMENCLATURE_UTILISATEUR);
	}
}
