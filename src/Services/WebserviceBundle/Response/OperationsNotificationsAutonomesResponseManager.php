<?php

namespace App\Services\WebserviceBundle\Response;

use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

class OperationsNotificationsAutonomesResponseManager extends AbstractResponseManager implements ResponseManagerInterface
{
    CONST CANAL_WALLET = 'WALLET';
    CONST CANAL_COURRIER = 'COURRIER';
    CONST CANAL_EMAIL = 'EMAIL';
    CONST CANAL_FTP = 'FTP';
    CONST CANAL_SMS = 'SMS';

    CONST TAG_PREFIX = '{';
    CONST TAG_SUFFIX = '}';
    CONST FORMAT_TAG_EMAIL = '%s-%d';

    CONST STATUT_EN_CREATION = 'en création';
    CONST STATUT_EN_PRODUCTION = 'en production';
    CONST STATUT_TERMINE = 'terminée';
    CONST STATUT_EN_ATTENTE_DE_DIFFUSION = 'en attente de diffusion';

    /**
     * @return array
     */
    public function getLibellesValeurs()
    {
        $clearValues = array();
        foreach($this->webserviceResponse->getValeurs() as $values) {
            foreach($values['valeurs'] as $value) {
                $clearValues[$value['id']] = $value['libelle'];
            }
        }
        ksort($clearValues);
        return $clearValues;
    }

    /**
     * @return array
     */
    public function getModeleDetails()
    {
        $columnName = 'nom';
        $modele = array();
        $modeleCollection = $this->webserviceResponse->getValeurs();
        $itemsDetails = $modeleCollection->map(function($element) use($columnName){
            return $element[$columnName];
        });
        foreach($itemsDetails as $item) {
            $modele[$item] = $modeleCollection->get($itemsDetails->indexOf($item));
        }

        return $modele;
    }

    /**
     * @param int $idOperation
     * @param string $type
     * @return array
     */
    static public function getSelectionnerParametersModele($idOperation, $idModele, $idFormatRoutage, $idTarifModele, $type = OperationsResponseManager::CANAL_COURRIER, int $typeEditeur = OperationsResponseManager::TYPE_EDITEUR_HTML) {
        $parameters = array_merge(self::getDefaultParametersModele($idOperation, $type), array(
            'idFormatRoutage' => $idFormatRoutage,
            'idModele' => $idModele,
            'idTarifModele' => $idTarifModele,
            'typeEditeur' => $typeEditeur
        ));
        return $parameters;
    }

    public function getDefaultsTags($message, $tags, $group = 'tagsSmsMessage') {
        preg_match_all('#\\'.OperationsResponseManager::TAG_PREFIX.'([^'.OperationsResponseManager::TAG_SUFFIX.']+)\\'.OperationsResponseManager::TAG_SUFFIX.'#', $message, $matches);
        $matches[1] = array_unique($matches[1]);
        $defaultTags = array();
        if (count($tags)) {
            foreach($matches[1] as $key=>$matche) {
                $tagFound = false;
                foreach($tags as $tag){
                    if($tag['id'] == $matche) {
                        $tagFound = true;
                        if (array_key_exists('idTag', $tag)) {
                            $idTag = $tag['idTag'];
                        }
                        else {
                            $idTag = '';
                        }
                        $lib = $tag['libelle'];
                        $taille = $tag['tailleMaximale'];
                        $valeurParDefaut = $tag['valeurParDefaut'];
                        if (array_key_exists('valeurParDefautUtilisateur', $tag)) {
                            $valeurParDefautUtilisateur = $tag['valeurParDefautUtilisateur'];
                        }
                        else {
                            $valeurParDefautUtilisateur = $valeurParDefaut;
                        }
                        if (array_key_exists('valeurApercu', $tag)) {
                            $valeurApercu = $tag['valeurApercu'];
                        }
                        else {
                            $valeurApercu = '';
                        }
                    }
                }
                $taillePrefixe = strlen(OperationsResponseManager::TAG_PREFIX);
                $tailleSuffixe = strlen(OperationsResponseManager::TAG_SUFFIX);
                $tailleTag = strlen($matche);

                if ($tagFound) {
                    if($group == 'tagsDateSmsMessage') {
                        $defaultTags[] = array(
                            'id' => $matche,
                            'idTag' => $idTag,
                            'libelle' => $lib,
                            'valeurParDefaut' => $valeurParDefaut,
                            'valeurApercu' => $valeurApercu,
                            'tailleMaximale' => $taille,
                            'tailleTag' => $taillePrefixe+$tailleTag+$tailleSuffixe,
                            'group' => $group);
                    }
                    else {
                        $defaultTags[] = array(
                            'id' => $matche,
                            'idTag' => $idTag,
                            'libelle' => $lib,
                            'valeurParDefaut' => $valeurParDefaut,
                            'valeurParDefautUtilisateur' => $valeurParDefautUtilisateur,
                            'valeurApercu' => $valeurApercu,
                            'tailleMaximale' => $taille,
                            'tailleTag' => $taillePrefixe+$tailleTag+$tailleSuffixe,
                            'group' => $group);
                    }
                }
            }
        }
        /* tri par ordre alpha */
        if(!empty($defaultTags)) {
            $keys = array_keys($defaultTags);
            foreach($defaultTags as $k => $v) {
                $libelles[$k] = strtolower($v['libelle']);
            }

            array_multisort($libelles, SORT_ASC, $defaultTags);
            $defaultTags = array_combine($keys, $defaultTags);
        }

        return $defaultTags;
    }

    public function getDefaultsOriginalsTags($originalsTags, $message, $tags, $group) {
        $defaultTags = $this->getDefaultsTags($message, $tags, $group);
        foreach ($originalsTags as $originalTag) {
            foreach ($defaultTags as $key => $defaultTag) {
                if ($defaultTag['idTag'] == $originalTag['id']) {
                    $defaultTags[$key]['valeurParDefaut'] = $originalTag['valeurParDefaut'];
                }
                $defaultTags[$key]['group'] = $group;
            }
        }
        return $defaultTags;
    }

    public function replaceTags($router, $modele, $keyMessage = 'message', $keyTag = 'lesTagsDuModele', $htmlWrap = false)
    {
        $tags = array();
        $messageOrigin = '';
        if (array_key_exists($keyMessage, $modele)) {
            $messageOrigin = $modele[$keyMessage]['valeur'];
            if (array_key_exists($keyTag, $modele)) {
                $tags = $modele[$keyTag]['valeurs'];
            }
        }

        $message = $messageOrigin;
        if(count($tags)) {
            if (!$htmlWrap) {
                foreach($tags as $tag) {
                    if($keyMessage != 'sujet' && isset($modele['typeEditeur']) && $modele['typeEditeur']['valeur'] == "2") {
                        $message = str_replace(self::TAG_PREFIX.$tag['id'].self::TAG_SUFFIX, "{{".$tag['libelle']."}}", $message);
                    } else {
                        if($keyMessage == 'messageSMS') {
                            $message = str_replace(self::TAG_PREFIX.$tag['id'].self::TAG_SUFFIX, self::TAG_PREFIX.$tag['libelle'].self::TAG_SUFFIX, $message);
                        }
                        else {
                            $message = str_replace(self::TAG_PREFIX.$tag['id'].self::TAG_SUFFIX, $this->formatTagForUser($tag, $router, $htmlWrap), $message);
                        }
                    }
                }
            }
            else {
                $pattern = '/({((?<=\{)(?!\s*\{)[^{}]+)})/';
                $message = preg_replace_callback(
                    $pattern,
                    function ($matches) use ($tags, $htmlWrap, $messageOrigin, $router, $keyMessage, $modele) {
                        $id = $matches[2];
                        $lib = $matches[1];
                        foreach($tags as $tag){
                            if($tag['id'] == $id) {
                                $imgJsonMatches = [];
                                $linkImgJsonMatches = [];
                                $linkPattern = '/href="[^"]*(' . $lib . ')[^"]*"/';
                                if ($keyMessage == 'donneesJson') {
                                    $imgJsonPattern = '/\\"src\\":\\"[^"]*(' . $lib . ')[^"]*"/';
                                    preg_match($imgJsonPattern, $messageOrigin, $imgJsonMatches);
                                    $linkImgJsonPattern = '/\\"link\\":\\"[^"]*(' . $lib . ')[^"]*"/';
                                    preg_match($linkImgJsonPattern, $messageOrigin, $linkImgJsonMatches);
                                    $linkPattern = '/href=\\\\\\\\\\\\\\"[^"]*(' . $lib . ')[^"]*"/';
                                }
                                $imgPattern = '/src="[^"]*(' . $lib . ')[^"]*"/';
                                preg_match($imgPattern, $messageOrigin, $imgMatches);
                                preg_match($linkPattern, $messageOrigin, $linkMatches);
                                $preHeaderPattern = '/<div[^>]+class="pre\-header\-text"[^>]*>.*(' . $lib . ').*<\/div>/';
                                preg_match($preHeaderPattern, $messageOrigin, $preHeaderMatches);
                                if(isset($modele['typeEditeur']) && $modele['typeEditeur']['valeur'] == "2") {
                                    if (sizeof($imgMatches) || sizeof($linkMatches) || sizeof($linkImgJsonMatches) || sizeof($imgJsonMatches) || sizeof($preHeaderMatches)) {
                                        $lib = self::TAG_PREFIX.$tag['libelle']."-".str_replace("ref", "", $tag["id"]).self::TAG_SUFFIX;
                                    } else {
                                        $idTag = isset($tag["idTag"]) ? $tag["idTag"] : $tag["id"];
                                        $lib = "<span alt='".self::TAG_PREFIX.$tag['libelle']."-".str_replace("ref", "", $tag["id"]).self::TAG_SUFFIX."' data-id='".$idTag."'>{{".$tag["libelle"]."}}</span>";
                                    }
                                } else {
                                    if (sizeof($imgMatches) || sizeof($linkMatches) || sizeof($linkImgJsonMatches) || sizeof($imgJsonMatches) || sizeof($preHeaderMatches)) {
                                        $lib = $this->formatTagForUser($tag, $router, false);
                                    } else {
                                        $lib = $this->formatTagForUser($tag, $router, $htmlWrap);
                                        if ($keyMessage == 'donneesJson') {
                                            $lib = str_replace('"','\\\\\"',$lib);
                                        }
                                    }
                                }
                            }
                        }
                        return $lib;
                    },
                    $messageOrigin
                );
            }
        }
        return $message;
    }

    public function replacePreHeaderTags($router, $modele, $keyMessage = 'message', $keyTag = 'lesTagsDuModele')
    {
        $tags = array();
        $messageOrigin = '';
        if (array_key_exists($keyMessage, $modele)) {
            $messageOrigin = $modele[$keyMessage]['valeur'];
            if (array_key_exists($keyTag, $modele)) {
                $tags = $modele[$keyTag]['valeurs'];
            }
        }
        $message = $messageOrigin;
        if(count($tags)) {
            $pattern = '/({((?<=\{)(?!\s*\{)[^{}]+)})/';
            $message = preg_replace_callback(
                $pattern,
                function ($matches) use ($tags, $messageOrigin, $router, $keyMessage) {
                    $id = $matches[2];
                    $lib = $matches[1];
                    foreach($tags as $tag){
                        if($tag['id'] == $id) {
                            $preHeaderPattern = '/<div[^>]+class="pre\-header\-text"[^>]*>.*(' . $lib . ').*<\/div>/';
                            preg_match($preHeaderPattern, $messageOrigin, $preHeaderMatches);
                            if (sizeof($preHeaderMatches)) {
                                $lib = $this->formatTagForUser($tag, $router, false);
                            }
                        }
                    }
                    return $lib;
                },
                $messageOrigin
            );
        }
        return $message;
    }

    /**
     * @param array $tag
     * @return string
     */
    public function formatTagForUser($tag, $router, $htmlWrap = false) {
        if (isset($tag['idTag'])) {
            $formatTag = sprintf(self::FORMAT_TAG_EMAIL, $tag['libelle'], (int) str_replace('ref', '', $tag['id']));
        }
        else {
            $formatTag = $tag['libelle'];
        }
        if ($htmlWrap) {
            if (isset($tag['idTag'])) {
                $formatTag = '<img alt="'.self::TAG_PREFIX.$formatTag.self::TAG_SUFFIX.'" class="generated-image-tags" data-id="'.$tag['idTag'].'" src="'. $router->generate('aquitem_requeteur_image_tags', array('libelle' => $formatTag)) .'" style="border:0px;display:inline-block;">';
            }
            else {
                $formatTag = '<img alt="'.self::TAG_PREFIX.$formatTag.self::TAG_SUFFIX.'" class="generated-image-tags" data-id="'.$tag['id'].'" src="'. $router->generate('aquitem_requeteur_image_tags', array('libelle' => $formatTag)) .'" style="border:0px;display:inline-block;">';
            }
        }
        else {
			$formatTag = self::TAG_PREFIX.$formatTag.self::TAG_SUFFIX;
		}
        return $formatTag;
    }

    public function getOffreTags() {
        return $this->webserviceResponse->getValeurs()[2]["valeurs"];
    }

    public function getOffre() {
        $offreValues = $this->webserviceResponse->getValeurs()[1]["valeur"];
        $offreValues['titre'] = html_entity_decode($offreValues['titre']);
        $offreValues['message'] = html_entity_decode($offreValues['message']);
        $offreValues['notification'] = html_entity_decode($offreValues['notification']);
        return $offreValues;
    }

    public function getTarifsDisponibles() {
        $canaux = $this->webserviceResponse->getSingleValeur()['valeurs'][0]['valeurs'];
        $tarifsDispo = array();
        foreach ($canaux as $canal) {
            if(array_key_exists('idTarifSelectionne', $canal)) {
                $idTarif = $canal['idTarifSelectionne'];
            }
            else {
                $idTarif = $canal['idTarif'];
                if($canal['idTarifRemplacement'] != "") {
                    $idTarif = $canal['idTarifRemplacement'];
                }
            }
            $tarifMiniSite = false;
            /* tarifMiniSite a été supprimé du WS mais peut-être reviendra-t-il !? */
            if(array_key_exists('tarifMiniSite', $canal)) {
                $tarifMiniSite = $canal['tarifMiniSite'];
            }

            $tarifsDispo[$canal['idCanal']] = array(
                'idTarif' => $idTarif,
                'hasTarifAutonome' => false,
                'isTarifAutonome' => false,
                'isTarifRich' => $tarifMiniSite,
                'isTarifPack' => $canal['packLibreService'],
                'hasTarifPack' => false,
                'richAutonome' => 0,
                'richValidation' => 0,
                'cc' => array(),
                'ccAutonome' => array(),
                'ccLibelles' => array(),
                'packAutonome' => 0,
                'packValidation' => 0,
                'envoiAutonome' => 0,
                'envoiValidation' => 0,
                'defaut' => 0,
            );
            foreach ($canal['tarifsDisponibles'] as $tarif) {
                /* Recupération des tarifs Rich */
                if($tarif['tarifSpecial'] == 'true') {
                    /* le tarif sélectionné est-il un tarif sms rich ? */
                    if($tarif['id'] == $idTarif) {
                        $tarifsDispo[$canal['idCanal']]['isTarifRich'] = true;
                    }
                    /* Le tarif dispo en cours est-il de type Avec validation ? */
                    if($tarif['necessiteUneValidationOperateur'] == 'true') {
                        if($tarif['tarifParDefaut'] == 'true') {
                            $tarifsDispo[$canal['idCanal']]['richValidation'] = $tarif['id'];
                        }
                    }
                    else {/* Le tarif dispo en cours est-il de type Autonome ? */
                        if($tarif['tarifParDefaut'] == 'true') {
                            $tarifsDispo[$canal['idCanal']]['richAutonome'] = $tarif['id'];
                        }
                    }
                }

                    /* Recupération des tarifs Pack */
                    if($tarif['utiliseUnPack'] == 'true') {
                        if($tarifsDispo[$canal['idCanal']]['isTarifRich']) {
                            if($tarif['tarifSpecial'] == 'true') {
                                if($tarif['necessiteUneValidationOperateur'] == 'true') {
                                    $tarifsDispo[$canal['idCanal']]['packValidation'] = $tarif['id'];
                                }
                                else {
                                    $tarifsDispo[$canal['idCanal']]['packAutonome'] = $tarif['id'];
                                }
                            }
                        }
                        else {
                            if($tarif['tarifSpecial'] == 'false') {
                                if($tarif['necessiteUneValidationOperateur'] == 'true') {
                                    $tarifsDispo[$canal['idCanal']]['packValidation'] = $tarif['id'];
                                }
                                else {
                                    $tarifsDispo[$canal['idCanal']]['packAutonome'] = $tarif['id'];
                                }
                            }
                        }
                    }
                    else {
                        /* Recupération des tarifs CC */
                        if($tarif['reserveCC'] == 'true') {
                            if($tarif['necessiteUneValidationOperateur'] == 'true') {
                                $tarifsDispo[$canal['idCanal']]['cc'][] = $tarif['id'];
                            }
                            else {
                                $tarifsDispo[$canal['idCanal']]['ccAutonome'][] = $tarif['id'];
                            }
                            $tarifsDispo[$canal['idCanal']]['ccLibelles'][$tarif['id']] = $tarif['libelle'];
                        }
                        else {
                            if($tarif['necessiteUneValidationOperateur'] == 'true') {
                                if($tarif['tarifParDefaut'] == 'true') {
                                    $tarifsDispo[$canal['idCanal']]['envoiValidation'] = $tarif['id'];
                                }
                            }
                            else {
                                if($tarif['tarifParDefaut'] == 'true') {
                                    $tarifsDispo[$canal['idCanal']]['envoiAutonome'] = $tarif['id'];
                                }
                            }
                        }
                    }

                /* Récupération du tarif Défaut */
                if($tarif['tarifParDefaut'] == 'true' && $tarif['necessiteUneValidationOperateur'] == 'true') {
                    if($tarif['tarifSpecial'] == 'true') {
                        if ($idTarif == $tarifsDispo[$canal['idCanal']]['richValidation'] || $idTarif == $tarifsDispo[$canal['idCanal']]['richAutonome']) {
                            $tarifsDispo[$canal['idCanal']]['defaut'] = $tarif['id'];
                        }
                    }
                    else {
                        $tarifsDispo[$canal['idCanal']]['defaut'] = $tarif['id'];
                    }
                }
            }
        }

        foreach ($canaux as $canal) {
            if(array_key_exists('idTarifSelectionne', $canal)) {
                $idTarif = $canal['idTarifSelectionne'];
            }
            else {
                $idTarif = $canal['idTarif'];
                if($canal['idTarifRemplacement'] != "") {
                    $idTarif = $canal['idTarifRemplacement'];
                }
            }
            /* Si pas de tarif Defaut pour Rich et Non Rich */
            foreach ($canal['tarifsDisponibles'] as $tarif) {
                if (!$tarifsDispo[$canal['idCanal']]['richAutonome']) {
                    if ($tarif['tarifSpecial'] == 'true') {
                        if($tarifsDispo[$canal['idCanal']]['isTarifPack']) {
                            if ($tarif['utiliseUnPack'] == 'true') {
                                if ($tarif['necessiteUneValidationOperateur'] == 'false') {
                                    $tarifsDispo[$canal['idCanal']]['richAutonome'] = $tarif['id'];
                                }
                            }
                        }
                        else {
                            if ($tarif['utiliseUnPack'] == 'false') {
                                if ($tarif['necessiteUneValidationOperateur'] == 'false') {
                                    $tarifsDispo[$canal['idCanal']]['richAutonome'] = $tarif['id'];
                                }
                            }
                        }
                    }
                }
                if (!$tarifsDispo[$canal['idCanal']]['richValidation']) {
                    if ($tarif['tarifSpecial'] == 'true') {
                        if($tarifsDispo[$canal['idCanal']]['isTarifPack']) {
                            if ($tarif['utiliseUnPack'] == 'true') {
                                if ($tarif['necessiteUneValidationOperateur'] == 'true') {
                                    $tarifsDispo[$canal['idCanal']]['richValidation'] = $tarif['id'];
                                }
                            }
                        }
                        else {
                            if ($tarif['utiliseUnPack'] == 'false') {
                                if ($tarif['necessiteUneValidationOperateur'] == 'true') {
                                    $tarifsDispo[$canal['idCanal']]['richValidation'] = $tarif['id'];
                                }
                            }
                        }
                    }
                }
                if (!$tarifsDispo[$canal['idCanal']]['envoiAutonome']) {
                    if ($tarif['tarifSpecial'] == 'false' and $tarif['utiliseUnPack'] == 'false' and $tarif['reserveCC'] == 'false') {
                        if ($tarif['necessiteUneValidationOperateur'] == 'false') {
                            $tarifsDispo[$canal['idCanal']]['envoiAutonome'] = $tarif['id'];
                        }
                    }
                }
                if (!$tarifsDispo[$canal['idCanal']]['envoiValidation']) {
                    if ($tarif['tarifSpecial'] == 'false' and $tarif['utiliseUnPack'] == 'false' and $tarif['reserveCC'] == 'false') {
                        if ($tarif['necessiteUneValidationOperateur'] == 'true') {
                            $tarifsDispo[$canal['idCanal']]['envoiValidation'] = $tarif['id'];
                        }
                    }
                }
            }
            /* Si aucun tarif n'a été flagé Defaut */
            if (!$tarifsDispo[$canal['idCanal']]['defaut']) {
                if ($tarifsDispo[$canal['idCanal']]['envoiValidation']) {
                    $tarifsDispo[$canal['idCanal']]['defaut'] = $tarifsDispo[$canal['idCanal']]['envoiValidation'];
                } elseif (($idTarif == $tarifsDispo[$canal['idCanal']]['richValidation'] || $idTarif == $tarifsDispo[$canal['idCanal']]['richAutonome'])
                    && $tarifsDispo[$canal['idCanal']]['richValidation']) {
                    $tarifsDispo[$canal['idCanal']]['defaut'] = $tarifsDispo[$canal['idCanal']]['richValidation'];
                }
            }
            /* Peut-on utiliser un pack ? */
            if($tarifsDispo[$canal['idCanal']]['packValidation'] or $tarifsDispo[$canal['idCanal']]['packAutonome']) {
                if($tarifsDispo[$canal['idCanal']]['isTarifRich']) {
                    if(($tarifsDispo[$canal['idCanal']]['packValidation'] and $tarifsDispo[$canal['idCanal']]['richValidation'])
                        or ($tarifsDispo[$canal['idCanal']]['packAutonome'] and $tarifsDispo[$canal['idCanal']]['richAutonome'])) {
                        $tarifsDispo[$canal['idCanal']]['hasTarifPack'] = true;
                    }
                }
                else {
                    if($tarifsDispo[$canal['idCanal']]['packValidation'] or count($tarifsDispo[$canal['idCanal']]['cc'])) {
                        $tarifsDispo[$canal['idCanal']]['hasTarifPack'] = true;
                    }
                    if($tarifsDispo[$canal['idCanal']]['packAutonome'] or count($tarifsDispo[$canal['idCanal']]['ccAutonome'])) {
                        $tarifsDispo[$canal['idCanal']]['hasTarifPack'] = true;
                    }
                }
            }
        }

        foreach ($canaux as $canal) {
            if(array_key_exists('idTarifSelectionne', $canal)) {
                $idTarif = $canal['idTarifSelectionne'];
            }
            else {
                $idTarif = $canal['idTarif'];
                if($canal['idTarifRemplacement'] != "") {
                    $idTarif = $canal['idTarifRemplacement'];
                }
            }
            /* Le tarif en cours est-il de type Autonome */
            if($idTarif == $tarifsDispo[$canal['idCanal']]['richAutonome'] or $idTarif == $tarifsDispo[$canal['idCanal']]['packAutonome'] or $idTarif == $tarifsDispo[$canal['idCanal']]['envoiAutonome'] or in_array($idTarif,$tarifsDispo[$canal['idCanal']]['ccAutonome'])) {
                $tarifsDispo[$canal['idCanal']]['isTarifAutonome'] = true;
            }
            /* Un tarif de type Autonome est-il disponible sur le canal ? */
            if(($tarifsDispo[$canal['idCanal']]['isTarifRich'] and $tarifsDispo[$canal['idCanal']]['richAutonome'])
                or (!$tarifsDispo[$canal['idCanal']]['isTarifRich'] and ($tarifsDispo[$canal['idCanal']]['envoiAutonome'] or count($tarifsDispo[$canal['idCanal']]['ccAutonome'])))) {
                $tarifsDispo[$canal['idCanal']]['hasTarifAutonome'] = true;
            }
        }

        return $tarifsDispo;
    }

    public function getPlageHoraire($canaux, $datesEnvoiMini) {
        $plageHoraire = array();
        if(!$this->webserviceResponse->hasError()) {
            $plageHoraireWS = $this->webserviceResponse->getValeurs()[0]['valeurs'];
            foreach($plageHoraireWS as $key=>$plage) {
                $plageHoraire[$plage['libelle']] = $plage;
            }
        }
        else {
            foreach($canaux as $key=>$canal) {
                if($canal['libelleCanal'] == self::CANAL_EMAIL || $canal['libelleCanal'] == self::CANAL_SMS || $canal['libelleCanal'] == self::CANAL_WALLET) {
                    $dateMini = 0;
                    if(isset($datesEnvoiMini[$canal['idCanal']])) {
                        $date = new \DateTime($datesEnvoiMini[$canal['idCanal']]);
                        $dateMini = $date->format('d/m/Y');
                    }
                    $plageHoraire[$canal['libelleCanal']] = array(
                        'id' => $canal['idCanal'],
                        'libelle' => $canal['libelleCanal'],
                        'dateEnvoiMinimale' =>  $dateMini,
                        'heureEnvoiMinimale' => $canal['libelleCanal'] == self::CANAL_EMAIL ? 0 : 8,
                        'heureEnvoiMaximale' => $canal['libelleCanal'] == self::CANAL_EMAIL ? 23 : 20,
                        'heureEnvoiMinimaleSiSerie' => $canal['libelleCanal'] == self::CANAL_EMAIL ? 4 : 8,
                        'heureEnvoiMaximaleSiSerie' => $canal['libelleCanal'] == self::CANAL_EMAIL ? 21 : 20,
                    );
                }
            }
        }
        return $plageHoraire;
    }
}
