<?php
namespace App\Services\RequeteurBundle;

use App\Services\WebserviceBundle\PhotoContactManager;
use App\Services\WebserviceBundle\Response\ContactsEnseigneResponseManager;
use App\Services\WebserviceBundle\WebserviceContactsEnseigne;
use Symfony\Component\HttpFoundation\RequestStack;

class ContactsEnseigneManager
{
    public $requestStack;
    /**
     * @var WebserviceContactsEnseigne
     */
    protected $webserviceContactsEnseigne;

    protected $UrlAccesFtp;
    protected $UrlAccesHttp;

    public function __construct(RequestStack $requestStack, WebserviceContactsEnseigne $webserviceContactsEnseigne, PhotoContactManager $photoContactManager)
    {
        $this->requestStack = $requestStack;
        $this->webserviceContactsEnseigne = $webserviceContactsEnseigne;
        $this->UrlAccesFtp = $photoContactManager->urlAccesFtp;
        $this->UrlAccesHttp = $photoContactManager->urlAccesHttp;
    }

    public function listerContacts()
    {
        $session = $this->requestStack->getSession();
        return $this->webserviceContactsEnseigne->listerContacts(['niveauUser' => $session->get('niveauUser')]);
    }

    public function listeContacts()
    {
        $listeContacts = $this->listerContacts();
        /** @var ContactsEnseigneResponseManager $responseManager */
        $responseManager = $listeContacts->responseManager;
        $contacts = $responseManager->contacts();
        $defaultImages = $this->defaultImages();
        foreach($contacts as $k=>$contact) {
            $contacts[$k]['imagePerso'] = false;
            $image = $contact['urlImage'];
            if(isset($image) && !in_array($image, $defaultImages)) {
                $contacts[$k]['imagePerso'] = true;
            }
        }
        return $contacts;
    }

    public function listeDroits()
    {
        $listeDroits[""] = "";
        $listeContacts = $this->listerContacts();
        /** @var ContactsEnseigneResponseManager $responseManager */
        $responseManager = $listeContacts->responseManager;
        $droits = $responseManager->droits();
        foreach($droits as $droit) {
            $listeDroits[$droit['libelle']] = $droit['droit'];
        }
        return $listeDroits;
    }

    public function defaultImages()
    {
        return [
            'logo' => $this->UrlAccesHttp . '/logo_A.svg',
            'silhouetteH' => $this->UrlAccesHttp.'/silhouetteH.png',
            'silhouetteF' => $this->UrlAccesHttp.'/silhouetteF.png',
        ];
    }
    
    public function formatContacts($datasForm)
    {
        if(!empty($datasForm)) {
            foreach($datasForm as $key=>$val) {
                if(strpos($key, '2') === false) {
                    $datas[$key] = [$datasForm[$key]];
                }
                if(array_key_exists($key.'2', $datasForm) && $datasForm['intitule2'] != "") {
                    $datas[$key][] = $datasForm[$key.'2'];
                }
            }
            foreach(array_keys($datas['urlImage']) as $k) {
                if($datas['urlImage'][$k] == "perso") {
                    $datas['urlImage'][$k] = $datas['urlImagePerso'][$k];
                    $datas['imagePerso'][$k] = $datas['urlImage'][$k];
                }
            }
            return $datas;
        }
        return false;
    }
}