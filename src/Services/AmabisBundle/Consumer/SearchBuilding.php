<?php

namespace App\Services\AmabisBundle\Consumer;

/**
 * SearchBuilding est un consumer du service "rechbat" de l'API Amabis
 * 
 * Le service "rechbat" permet la recherche de compléments (entrée, bâtiment, résidence)
 * associés à un numéro de voie. Ce numéro de voie peut-être obtenu via le service "rechvoie"
 * et donc via le consumer SearchRoad
 */
class SearchBuilding extends Base
{
    const HTTP_METHOD = "POST";
    const WEB_SERVICE_END_POINT = "rechbat";
    const ENABLE_SAVING_LOGS = false;

    public function check(string $imputation, string $clevoie, string $numero, string $search, $authKey, $wsParams) {
        $response = $this->request($this->getParams($clevoie, $numero, $search, $wsParams), $imputation, $authKey, [])->toArray();
        return $response['result']['batdesc'] ?? [];
    }

    public function getParams($clevoie, $numero, $search, $wsParams) {
        return [
            'options' => [
                "distinction" => isset($wsParams["distinction"]) ? $wsParams["distinction"] : "",
                "scoremini" => isset($wsParams["scoremini"]) ? $wsParams["scoremini"] : ""
            ],
            'clevoie' => $clevoie,
            'numero' => $numero,
            'lib' => $search
        ];
    }

    public function getUrl() {
        return $this->url;
    }
}