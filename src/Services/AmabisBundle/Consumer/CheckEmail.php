<?php

namespace App\Services\AmabisBundle\Consumer;

/**
 * CheckEmail est un consumer du service "testcourriel2" de l'API Amabis
 * 
 * Le service "testcourriel2" permet de vérifier
 * l'existence d'une adresse email
 */
class CheckEmail extends Base
{
    const HTTP_METHOD = "POST";
    const WEB_SERVICE_END_POINT = "testcourriel2";
    const ENABLE_SAVING_LOGS = true;

    public function check(string $imputation, string $email, $authKey, array $additionalLogParams = []) {
        $response = $this->request($this->getParams($email), $imputation, $authKey, $additionalLogParams)->toArray();
        $response = $response['result'] ?? [];

        // Règle spécifique - une adresse email correspondant à une boite email pleine est considéré comme valide
        if($response && $response['statut'] == 'MAILBOX_FULL') {
            $response['statut'] = 'EMAIL_OK';
        }

        return $response;
    }

    public function getParams($email) {
        return [
            'email' => $email
        ];
    }

    public function getUrl() {
        return $this->url;
    }
}