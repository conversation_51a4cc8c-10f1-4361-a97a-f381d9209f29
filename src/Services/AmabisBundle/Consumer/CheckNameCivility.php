<?php

namespace App\Services\AmabisBundle\Consumer;

use Symfony\Component\VarDumper\VarDumper;

/**
 * CheckNameCivility est un consumer du service "analnom" de l'API Amabis
 *
 * Le service "analnom" permet d'analyser la cohérence d'un nom, prénom et d'une civilité
 */
class CheckNameCivility extends Base
{
    const HTTP_METHOD = "POST";
    const WEB_SERVICE_END_POINT = "analnom";
    const ENABLE_SAVING_LOGS = true;

    public function check(string $imputation, string $prenom, string $nom, $authKey, $wsParams, array $additionalLogParams = []) {
        $response = $this->request($this->getParams($prenom, $nom, $wsParams), $imputation, $authKey, $additionalLogParams)->toArray();
        return $response['result'] ?? [];
    }

    public function getParams($prenom, $nom, $wsParams){
        return [
            'options' => [
                "casse" => isset($wsParams["casse"]) ? $wsParams["casse"] : 1,
                "deftitre1" => isset($wsParams["deftitre1"]) ? $wsParams["deftitre1"] : "H",
                "deftitre2" => isset($wsParams["deftitre2"]) ? $wsParams["deftitre2"] : "F",
                "priorite" => isset($wsParams["priorite"]) ? $wsParams["priorite"] : "",
                "ordre" => isset($wsParams["ordre"]) ? $wsParams["ordre"] : "P",
                "longueur" => isset($wsParams["longueur"]) ? $wsParams["longueur"] : "",
            ],
            'nom' => $nom.' '.$prenom
        ];
    }

    public function getUrl() {
        return $this->url;
    }
}
