<?php

namespace App\Config\Model;

class Api
{
    /**
     * @param string     $endpoint        Point d'entrée de l'API
     * @param string     $method          Méthode HTTP (GET, POST, PUT, DELETE)
     * @param array      $requestMapping  Mapping des données de requête
     * @param array|null $responseMapping Mapping des données de réponse
     */
    public function __construct(
        private string $endpoint,
        private string $method,
        private array $requestMapping,
        private ?array $responseMapping = null,
    ) {
    }

    public function getEndpoint(): string
    {
        return $this->endpoint;
    }

    public function getMethod(): string
    {
        return $this->method;
    }

    public function getRequestMapping(): array
    {
        return $this->requestMapping;
    }

    public function getResponseMapping(): ?array
    {
        return $this->responseMapping;
    }

    /**
     * Crée une instance à partir d'un tableau de données.
     *
     * @param array $data Les données de l'API
     */
    public static function fromArray(array $data): self
    {
        return new self(
            $data['endpoint'],
            $data['method'],
            $data['requestMapping'],
            $data['responseMapping'] ?? null
        );
    }

    /**
     * Convertit l'objet en tableau.
     */
    public function toArray(): array
    {
        $result = [
            'endpoint' => $this->endpoint,
            'method' => $this->method,
            'requestMapping' => $this->requestMapping,
        ];

        if (null !== $this->responseMapping) {
            $result['responseMapping'] = $this->responseMapping;
        }

        return $result;
    }
}
