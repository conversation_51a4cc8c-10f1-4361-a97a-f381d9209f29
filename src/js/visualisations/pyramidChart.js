import AxisBase from "./AxisBase";
import * as d3 from "d3";
import acUtils from "../utils";
import moment from "moment";

export default class pyramidChart extends AxisBase {
	constructor() {
        super();
        this.name = "pyramidChart";
        this.data = 0;
        this.current = 0;
        this.hasLegend = false;
        this.defaultMargins = {top: 60, left: 30, right: 30, bottom: 75};
        this.yAxis = [];
        this.y_axis = [];
        this.data = [];
        this.sort_custom = [];
        this.brushes = [];
        this.selectedClass = 'brushed';
        this.unselectedClass = 'unbrushed';
        this.hasDynamicHeight = true;
    }

	setParams(params) {
        let that = this;
        that.params = params;
        that.options = that.validateOptions($.extend(that.defaultOptions(), params.options));
    }

	async construct(params, width, height, element) {
        let that = this;
        this.setParams(params);
        that.data = params.data;
        await this.setSize(width, height);
        that.createAxes();
        that.render(element);
    }

	processData(data) {
        let that = this;
        let values = {};

        let datas = [];

        if (that.options.dataKeys && that.options.dataKeys.length) {
            datas = that.options.dataKeys.map(d => ({ id: d === null ? "" : d, values: [] }));
        }

        data.forEach(d => {
            d.values.forEach(v => {
                if (!values.hasOwnProperty(v.id)) {
                    values[v.id] = [];
                }
                values[v.id].push({
                    y: v.value,
                    x: d.id,
                    id: v.id
                });
            });
        });

        if (datas.length) {
            datas = datas.map(d => {
                if (values.hasOwnProperty(d.id)) {
                    d.values = values[d.id];
                }
                return d;
            });
        } else {
            datas = _.keys(values).map(k => ({ id: k, values: values[k] }));
        }

        return datas.splice(0,2);
    }

    async update(datas) {
        this.data = datas;
        await this.beforeUpdate(datas);
        this.createAxes();
        this.render(this.$parent.selector);
    }

	createAxes() {
        let that = this;

        that.margins = JSON.parse(JSON.stringify(that.defaultMargins));
        if (this.isResponsive()) {
            that.margins.left = 20;
            that.margins.right = 20;
        }

        if (that.data.length && typeof that.data[0].values === "undefined") {
            that.data.forEach(d => {
                d.values = [{
                    id: d.id,
                    value: d.value
                }];
            });
        }

        let datax = [];
        $.each(that.data, (k, v) => {
            datax.push(v.id);
        });

        let maxdataY = Math.max.apply(Math,that.data.map(o => {
			if (!o.values) return 0;
            return Math.max.apply(Math,o.values.map(u => u.value * 1.1));
        }));

        let decalage = 1 / datax.length;
        let mindataX = d3.min(datax) - ((d3.max(datax) - d3.min(datax)) * decalage);
        let maxdataX = d3.max(datax) + ((d3.max(datax) - d3.min(datax)) * decalage);

        if (acUtils.isElasticType("date", that.options.x[0].type)) {
            that.x = d3.scaleUtc().domain([mindataX, maxdataX]).range([0, that.quadrantHeight()]);
        } else if (acUtils.isElasticType("string", that.options.x[0].type)) {
            that.x = d3.scaleBand().domain(datax.reverse()).rangeRound([0, that.quadrantHeight()]).padding(0.1);
        } else {
            let axisPadding = that.options.x[0].interval + 1;
            that.x = d3.scaleLinear().domain([d3.max(datax) + axisPadding, d3.min(datax) - axisPadding]).range([0, that.quadrantHeight()]);
        }

        that.y = d3.scaleLinear().domain([0, maxdataY]).range([0, that.quadrantHeight()]);
    }

	render(element) {
        let that = this;
        that.preRender(element);
        that.nbBars = that.data.length;
        that.barPadding = Math.floor(that.quadrantWidth() / that.nbBars) * 0.2;
        if (that.isNumberType(that.options.x[0].type) && that.nbBars < 5) {
            that.barPadding = Math.floor(that.quadrantWidth() / that.nbBars) * 0.4;
        }
        that.barWidth = Math.floor(that.quadrantWidth() / that.nbBars) - that.barPadding;
        let init = false;

	    if (that.emptyData) {
		    return;
	    }

        if (!that.svg) {
            that.svg = d3.select(element).append("svg").attr("height", that.height).attr("width", that.width).attr('class', 'pyramidChart visualisation-root');
            init = true;
        }

        that.xRight = d3.axisRight(that.x);
        that.formatXaxis({
            chart_type: that.name,
            xAxis: that.xRight,
            x_type: that.options.x[0].type,
            interval: that.options.x[0].interval,
            maxWidth: that.quadrantWidth(),
            nbTick: that.nbBars
        });

        that.xLeft = d3.axisLeft(that.x.copy());
        that.formatXaxis({
            chart_type: that.name,
            xAxis: that.xLeft,
            x_type: that.options.x[0].type,
            interval: that.options.x[0].interval,
            maxWidth: that.quadrantWidth(),
            nbTick: that.nbBars
        });
        that.xLeft.tickFormat("");

        let labels = that.svg.selectAll("text.text-width-calcul")
            .data(that.x.ticks ? that.x.ticks() : that.x.domain())
            .enter()
            .append("text")
            .classed('text-width-calcul', true)
            .text(d => that.xRight.tickFormat()(d));

        that.maxTextWidth = d3.max(labels.nodes(), n => n.getComputedTextLength());

        that.svg.selectAll("text.text-width-calcul").remove();

        that.space = that.maxTextWidth + 20;
        that.yw = (that.quadrantWidth() / 2) - (that.space / 2);
        that.y.range([0, that.yw]);

        that.yBottomLeft = d3.axisBottom(that.y.copy().range(that.y.range().reverse()));
        that.yBottomRight = d3.axisBottom(that.y);

        if (that.yw < 150) {
            that.yBottomLeft.ticks(4);
            that.yBottomRight.ticks(4);
        }

        that.formatYaxis(that.yBottomLeft, that.options.y[0].type);
        that.formatYaxis(that.yBottomRight, that.options.y[0].type);


        if (init) {
            that.renderBody(that.svg);
            that.renderAxes(that.svg);
        } else {
            that.rescaleAxis();
        }

        that.renderBars();

        if (that.selected) {
            that.selected.forEach(v => {
                that.selectNode(v.filter1, v.filter2);
            });
        }

        that.renderLabels();
    }

	renderBody() {
        let that = this;
        if (!that.bodyG) {
            that.bodyG = that.svg.append("g")
                .attr("class", "body")
                .attr("transform", "translate(" + that.xStart() + "," + that.yEnd() + ")");
        }
    }

	renderAxes() {
        let that = this;

        if (!that.axesG) {
            that.axesG = that.bodyG.append("g")
                .attr("class", "axes");
        }

        that.axesG.append("g").attr("class", "x axis left");
        that.axesG.append("g").attr("class", "x axis right");
        that.axesG.append("g").attr("class", "y axis left");
        that.axesG.append("g").attr("class", "y axis right");
        that.axesG.selectAll(".y.axis g.tick")
            .append("line").classed("grid-line", true);
        that.axesG.append("text").attr("class", "x-axis-text");
        that.axesG.append("text").attr("class", "y-axis-text");

        that.rescaleAxis(true);
    }

	rescaleAxis(init = false) {
        let that = this;

        that.y.range([0, that.yw]);

        const transitionDuration = init ? 0 : 250;

        that.axesG.select(".x.axis.left")
            .call(that.xRight)
            .call(acUtils.removeEndingOuterTick, "right", true)
            .transition().duration(transitionDuration)
            .attr("transform", () => "translate(" + (that.yw) + ", 0)")
            .selectAll('text')
            .style("text-anchor", "middle")
            .attr("x", that.space / 2);

        that.axesG.select(".x.axis.right")
            .call(that.xLeft)
            .call(acUtils.removeEndingOuterTick, "left", true)
            .transition().duration(transitionDuration)
            .attr("transform", () => "translate(" + (that.yw + that.space) + ", 0)")
            ;

        that.axesG.select(".y.axis.left")
            .call(that.yBottomLeft)
            .call(acUtils.removeEndingOuterTick, "bottom")
            .transition().duration(transitionDuration)
            .attr("transform", () => "translate(0, " + that.quadrantHeight() + ")")
            .selectAll(".tick text")
            .style("text-anchor", "end")
            .attr("dx", "-0.6em")
            .attr("dy", "0.6em")
            .attr("transform", "rotate(-45)");

        that.axesG.select(".y.axis.right")
            .call(that.yBottomRight)
            .call(acUtils.removeEndingOuterTick, "bottom")
            .transition().duration(transitionDuration)
            .attr("transform", () => "translate(" + (that.yw + that.space) + "," + that.quadrantHeight() + ")")
            .selectAll(".tick text")
            .style("text-anchor", "end")
            .attr("dx", "-0.6em")
            .attr("dy", "0.6em")
            .attr("transform", "rotate(-45)");

        that.axesG.selectAll(".y.axis g.tick line").remove();

        that.axesG.selectAll(".y.axis g.tick")
            .append("line")
            .classed("grid-line", true)
	        .style('stroke', 'black')
	        .style('shape-rendering', 'crispEdges')
	        .style('stroke-opacity', 0.2)
            .attr("x1", 0)
            .attr("y1", 0)
            .attr("x2", 0)
            .attr("y2", -that.quadrantHeight());

        that.axesG.selectAll("text.x-axis-text")
            .attr("x", that.quadrantWidth() / 2)
            .attr("y", - (that.margins.top - 20))
            .style("text-anchor", "middle")
            .text(that.options.label_division ? that.options.label_division : that.options.x[1].label);

        that.axesG.selectAll("text.y-axis-text")
            .attr("x", that.quadrantWidth() / 2)
            .attr("y",  that.quadrantHeight() + that.margins.bottom - 10)
            .style("text-anchor", "middle")
            .text(that.options.label_calcul ? that.options.label_calcul : that.options.y[0].label);
    }

	renderBars() {
        let that = this;

        that.barData = that.processData(that.data);

        let h;
	    let align;

        if (that.options.x[0].ranges) {
	        h = ((that.x.range()[1] - that.x.range()[0]) / that.x.domain().length) * 0.9;
	        align = 0;

        } else {
	        h = that.x(that.x.domain()[1]) - that.x(that.x.domain()[1] + Math.max(that.options.x[0].interval - 1, 0.6));
	        align = that.options.x[0].interval && that.options.x[0].interval && that.options.x[0].interval > 1 ? -h : - h / 2;
        }

        let delay = 300 / that.nbBars;

        let group = that.bodyG.selectAll(".group")
            .data(that.barData, d => d.id);

        group.enter().append("g")
            .attr("class", "group")
            .merge(group);

        group.exit().remove();

        let barsUpdate = that.bodyG.selectAll(".group").selectAll("rect.bar")
            .data(d => d.values, d => d.id + d.x);

        // BARRES DE GAUCHE

        let barsEnter = barsUpdate.enter().append("rect");

        barsEnter.attr('class', 'bar')
            .filter(d => d.id === that.barData[0].id)
            .attr("width", 0)
            .attr("y", d => that.x(d.x) + align)
            .attr("x", that.yw)
            .attr("data-i", (d, i) => i)
            .attr("height", h)
            .style("fill", d => that.getColor(d.id))
            .on('mouseenter', e => {
                that.mouseEnterTooltip(e);
            })
            .on('mouseleave', e => that.mouseLeaveTooltip(e))
            .on('mousemove', e => that.mouseMoveTooltip(e))
	        .attr('data-tooltip', (d, i) => that.getTooltip(d, i))
            .on('click', function(event, d) {
                that.$parent.trigger(that.name  + "." + "click", {
                    element: this,
                    parent: that.parent,
                    chart: that,
                    x: that.options.x,
                    y: that.options.y,
                    data: d,
                    originalData: that.data[this.dataset.i]
                });
            })
            .transition()
            .duration(300)
            .delay((d, i) => i * delay)

            .attr("width", d => that.y(d.y))
            .attr("y", d => that.x(d.x) + align)
            .attr("x", d => that.yw - that.y(d.y))
            .attr("height", h)
            .style("fill", d => that.getColor(d.id))
        ;

        barsUpdate
            .filter(d => d.id === that.barData[0].id)
            .transition()
            .duration(300)
            .delay((d, i) => i * delay)
	        .attr('data-tooltip', (d, i) => that.getTooltip(d, i))
            .attr("width", d => that.y(d.y))
            .attr("y", d => that.x(d.x) + align)
            .attr("x", d => that.yw - that.y(d.y))
            .attr("height", h)
            .style("fill", d => that.getColor(d.id))
        ;

        // BARRES DE DROITE

        barsEnter.attr('class', 'bar')
            .filter(d => d.id === that.barData[1].id)
            .attr("width", 0)
            .attr("y", d => that.x(d.x) + align)
            .attr("x", that.yw + that.space + 1)
            .attr("data-i", (d, i) => i)
            .attr("height", h)
            .style("fill", d => that.getColor(d.id))
            .attr('data-tooltip', (d, i) => that.getTooltip(d, i))
            .on('mouseenter', e => {
                that.mouseEnterTooltip(e);
            })
            .on('mouseleave', e => that.mouseLeaveTooltip(e))
            .on('mousemove', e => that.mouseMoveTooltip(e))
            .on('click', function(event, d) {
                that.$parent.trigger(that.name  + "." + "click", {
                    element: this,
                    parent: that.parent,
                    chart: that,
                    x: that.options.x,
                    y: that.options.y,
                    data: d,
	                originalData: that.data[this.dataset.i]
                });
            })
            .transition()
            .duration(300)
            .delay((d, i) => i * delay)
            .attr("width", d => that.y(d.y))
            .attr("y", d => that.x(d.x) + align)
            .attr("x", that.yw + that.space + 1)
            .attr("height", h)
            .style("fill", d => that.getColor(d.id))
        ;

        barsUpdate
            .filter(d => d.id === that.barData[1].id)
            .transition()
            .duration(300)
            .delay((d, i) => i * delay)
	        .attr('data-tooltip', (d, i) => that.getTooltip(d, i))
            .attr("width", d => that.y(d.y))
            .attr("y", d => that.x(d.x) + align)
            .attr("x", that.yw + that.space)
            .attr("height", h)
            .style("fill", d => that.getColor(d.id))
        ;

        let barsExit = barsUpdate.exit();
        barsExit.remove();

    }

	renderLabels() {
        let that = this;
        let labels = that.svg.selectAll(".label")
            .data(that.barData, d => d.id);

        labels.enter().append("text")
            .attr("class", "label")
            .merge(labels)
            .text(d => {
                if (that.options.hasOwnProperty('custom_label_' + d.id.toFieldName())) {
                    return that.options['custom_label_' + d.id.toFieldName()];
                }
                return d.id;

            })
            .attr('x', (d, i) => {
                if (i === 0) {
                    return that.yw + that.margins.left;
                } else {
                    return that.yw + that.space + that.margins.left;
                }
            })
            .style("text-anchor", (d, i) => {
                if (i === 0) {
                    return "end";
                } else {
                    return "start";
                }
            })
            .attr('y', that.margins.top - 15);
    }

	getTooltip(d) {
        let that = this;
        let tooltip_value_x;
        let valid = (new Date(d.id)).getTime() > 0;
        let t = valid ? d.id : d.x;
        if (acUtils.isElasticType('date', that.options.x[0].type)) {
            tooltip_value_x = moment(t).utc().format(that.formatInterval(that.options.x[0].interval));
        }

        let x = {};
        x[that.options.x[0].label] = tooltip_value_x;

        $.each(that.options.x, (index, val) => {
            let value;
            if (acUtils.isElasticType('date', that.options.x[0].type)) {
                value = moment(t).utc().format(that.formatInterval(that.options.x[0].interval));
            } else {
                if (that.options.x[0].interval && that.options.x[0].interval > 1) {
                    value = d.x + " - " + (d.x + that.options.x[0].interval - 1);
                } else {
                    value = d.x;
                }
            }
            if (index > 0) {
                value = d.id;
            }
            x[val.label] = value;
        });

        let value;

        //si on a dans méthode de calculs "Comptage" ou "Comptage unique"
		if (that.params.hasOwnProperty("esParameters") && (that.params.esParameters.aggregations[0].type === "count" || that.params.esParameters.aggregations[0].type === "cardinality")) {
			// format integer
			value = that.formatType(d.y, "integer", { average: false });

		} else {
			//sinon on applique le format approprié au calcul
			value = that.formatType(d.y, that.options.y[0].type, { average: false });
		}

        let tooltip = {
            x: x,
            y: [{
                key: $.trim(that.options.y[0].label),
                // value: that.formatType(d.y, that.options.y[0].type),
                value: value,
                color: that.getColor(that.key(d))
            }]
        };
        return JSON.stringify(tooltip);
    }

	onItemRemovedSelf() {
        let that = this;
        let filters = this.dashboard.filters.filter(filter => filter.parent === that.parent && !filter.disabled);
        filters.forEach(filter => {
          filter.parent = -1;
        });
		if (!that.selected) return false;
		that.selected = false;
		that.unselectAllNodes();
	}

	selectNode(filter1, filter2) {
        let that = this;
        if (this.getNodes().filter(n => n.classList.contains(that.selectedClass))
            .length === 0) {
            this.getNodes().forEach(n => {
                that.addUnselectRemoveSelect(n);
            });
        }
        this.getNodeByLabel(filter1, filter2).forEach(n => {
            that.addSelectRemoveUnselect(n);
        });
        if (that.selected) {
            let filters = {
                filter1: filter1,
                filter2: filter2
            };
            if ($.isArray(that.selected)) {
                let index = that.selected.map(filter => filter.filter1 === filter1 && filter.filter2 === filter2)
                .indexOf(true);
                if (index === -1) {
                    that.selected.push(filters);
                }
            } else {
                that.selected = filters;
            }
        }
    }

	getNodeByLabel(filter1, filter2) {
        return this.getNodes().filter(d => d.__data__.x === filter1 && d.__data__.id === filter2);
    }

	getNodes() {
        return this.svg.selectAll('rect.bar').nodes();
    }

	reset() {
        let that = this;
	    that.bodyG = false;
	    that.svg = false;
	    that.axesG = false;
    }

	onInitOption() {
        let that = this;
        acUtils.waitForElement('.pyramidChart', function() {
            if (!$('#visu-options-custom').length) {
                $('#visu-options').append('<div id="visu-options-custom"></div>');
            }
            $('#visu-options-custom').empty();
            if (that.barData) {
                that.barData.forEach(data => {
                    that.addOptionsGroup(data.id.toString(), data.id.toString().toFieldName());
                });
            }
        });
    }

	onFinishPreview() {
        this.onInitOption();
    }

	addOptionsGroup(label, name) {
        let that = this;
        let customLabel = _.defaultTo(that.params.options['custom_label_' + name], label);
        let labelParams = {
            value: label,
            name: 'original_label_' + name,
            type: 'hidden',
            translate: false
        };
        let fieldParams = {
            default: customLabel,
            name: 'custom_label_' + name,
            id: 'custom_label',
            label: "Label personnalisé",
            type: 'text'
        };
        let color = _.defaultTo(that.params.options['custom_color_' + name], that.options.color_fill);
        let colorParams = {
            name: 'custom_color_' + name,
            type: "color",
            default: color,
            classes: ['custom-color'],
            translate: false
        };
        let form = new FormBuilder("options", [labelParams, fieldParams, colorParams], {translationKey: this.getTranslationPrefix("options")}, "options");
        let $form = form.render();
        let $options = $('#visu-options-custom');
        $options.append($('<div class="visu-options-group"></div>').append($form));
        $options.find('[data-name="custom_label_' + name + '"]').keyup(function() {
            if ($(this).val() === "") {
                $(this).val(label);
            }
        });
        form.initColorPickers($options);
    }

    getColor(id) {
        if (this.options.hasOwnProperty('custom_color_' + id.toFieldName())) {
            return this.getSchemeColor(this.options['custom_color_' + id.toFieldName()]);
        }
        return super.getColor(id);
    }

	key(d) {
        return d.id;
    }

    disableFilterOnEdit() {
        return false;
    }

	applyVisualFilters(filters) {
        let that = this;
        filters = filters.filter(filter => filter.parent === that.parent && !filter.disabled);
        filters.forEach(filter => {
            filter.parent = -1;
        });
        this.getNodes().forEach(n => {
            that.removeSelectRemoveUnselect(n);
        });
    }

    // En Desktop : la visu prend par défaut 2 hauteur pour 5 barres */
    getDefaultHeight() {
        return Math.max(Math.ceil((this.data.length / 5) * 2), 4);
    }

    // Retourne le nombre de lignes à afficher en fonction du nombre de barres
    // 2 lignes pour 5 barres (Minimum 4 et maximum 8)
    getGridRowsCount() {
        return Math.max(Math.min(Math.ceil(this.data.length / 5) * 2, 8), 4);
    }

	onGenerateParams(params) {
        params.options.dataKeys = [];
        _.keys(params.options).filter(k => k.indexOf('original_label') !== -1).forEach(k => {
            params.options.dataKeys.push(params.options[k]);
        });

        return params;
    }

	parameters() {
        return [
            {
                label: "Calcul",
                id: "axe_y",
                desc: "",
                type: "aggregation",
                size: "single",
                accept: ["number", "string"]
            }, {
                label: "Axe vertical",
                id: "axe_x",
                desc: "",
                type: "bucket",
                size: "single",
                accept: ["date", "number"]
            }, {
                label: "Division",
                id: "division",
                desc: "",
                type: "bucket",
                size: "single",
                accept: ["number", "string"]
            }
        ];
    }

	options() {
        return ([
            {
                label: "Label calcul",
                prelabel: "Label calcul",
                name: "label_calcul",
                type: "text"
            },
            {
                label: "Label axe vertical",
                prelabel: "Label axe vertical",
                name: "label_vertical",
                type: "text"
            },
            {
                label: "Label division",
                prelabel: "Label division",
                name: "label_division",
                type: "text"
            },
            {
                label: null,
                name: "colors_custom",
                type: "svelte",
                component: "form/ColorpickerSwitch",
                props: {
                    "name": "colors_custom",
                    "isCustom": false
                },
            },
        ]);
    }

	defaultOptions() {
        return {
            label_calcul: null,
            label_vertical: null,
            label_division: null
        };
    }

}