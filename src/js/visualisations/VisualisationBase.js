import * as d3 from "d3";
import d3Annotation from 'd3-svg-annotation';
import numbro from "numbro";
import moment from "moment";

import numbroFr from 'numbro/languages/fr-FR';
import * as _ from "lodash";
import {scheme_dark, scheme_transparent, scheme_white} from "../utils";
numbroFr.abbreviations.trillion = "b";
numbro.registerLanguage(numbroFr);
numbro.setLanguage('fr-FR');

/**
 * @property {Legend} legend
 */
export default class VisualisationBase {

    constructor() {
        this.name = "visualisationBase";
        this.dashboard = false;
        this.colors = d3.scaleOrdinal(d3.schemeCategory10);
        this.colorsDeclinations = {};
        this.hasLegend = false;
        this.showLegendForPrintOnly = false;
        this.emptyData = false;
        this.emptyDataElement = false;
        this.enableAnnotations = false;
        this.enableLock = false;
        this.annotations = [];
        this.lockedDatas = false;
        this.allowAnnotation = false;
        this.isSelfFiltrable = false;
        this.filterableBy = [];
        this.customColors = [];
        this.state = {};
        this.filters = [];
        this.selectedClass = 'selected';
        this.unselectedClass = 'unselected';
        this.emptyDataText = Translator.trans('alienor_charts.global.noData');
        this.needUpdateOptionsOnReload = false;
        this.canCreateFilter = true;
        this.canGenerateFilters = true;
        this.hasDynamicHeight = false;
        this.responsive = false;
        this.renderType = null;
        this.duplicateOnCapture = true;
        this.inlineLegendOnCapture = false;
        this.ignoreBucketLimitOnExport = false; // permet de récupérer le max de resultats possible lors des exports CSV
        this.comparisonEnabled = false;
        this.gridItem;
        this.currentBreakpoint;
        this.previousBreakpoint;
    }

    async construct(params, width, height, element) {
        this.setParams(params);
        this.data = params.data;
        await this.setSize(width, height);
        this.render(element);
    }

    /**
     * @param {object} params
     */
    setParams(params) {
        this.params = params;
        this.options = $.extend(this.defaultOptions(), this.params.options);
    }

    /**
     * Retourne le merge des options issues de params et des options pa défaut.
     * N'est pas utilisé de manière générique dans le setParams par peur de créer des effets de bord,
     * puisque certaines visualisations n'utilisent pas la fonction validationOptions.
     * Elle sera appelée si la fonction updateParentElementAfterConstruct() est implémentée par la visu.
     * @param {object} params
     */
    getOptionsFromParams(params) {
        return this.validateOptions($.extend(this.defaultOptions(), params.options));
    }

    /*** Event déclenché lors de la mise à jour de la visualisation ***/
    async beforeUpdate(datas) {}

    /**
     * @param {object} params
     * @param {Function} callback
     */
    update(params, callback) {
        if (typeof params === "undefined") {
            params = this.params;
        }
        this.construct(params, this.width, this.height, callback);
    }

    /**
     * Hook preRender
     * @param element
     */
    preRender(element) {
        let that = this;
        this.canCreateFilter = this.options.hasOwnProperty('canCreateFilter') ? this.options.canCreateFilter : this.canCreateFilter;
        this.canGenerateFilters = this.canGenerateFilters && this.canCreateFilter !== false;
        this.$parent = $(element);
        this.colors = this.getColorScale();
        if (typeof this.options.colors === "object" && !$.isArray(this.options.colors)) {
            this.customColors = this.options.colors;
        }
        if (that.dashboard) {
            that.filters = this.dashboard.filters.filter(filter => filter.parent === that.parent);
        }
        if (!that.hasData()) {
            that.emptyData = true;
            that.onEmptyData(element);
        } else {
            that.emptyData = false;
            if (typeof that.emptyDataElement.remove === "function") {
                that.emptyDataElement.remove();
                that.emptyDataElement = false;
            }
        }
        this.updateCurrentBreakpoint();
    }

    render(element) {}

    /**
     * @param {"visualisation_capture"|"dashboard_capture"} renderType
     * @param {string} selector (Selecteur Jquery du l'element dans lequel faire le rendu)
     */
    renderForCapture(renderType, selector = undefined) {
        if (!['visualisation_capture', 'dashboard_capture'].includes(renderType)) {
            throw new Error('renderType non supporté');
        }
        if (selector) {
            this.$parent = $(selector);
        }
        this.renderType = renderType;
        this.reset();
        this.render(this.$parent.selector);
        // if (this.hasLegend && this.hasOwnProperty('legendState') && this.legendState === true) {
        if (this.hasLegend) {
            this.legend = _.clone(this.legend);
			this.legend.chart = this;
			this.legend.show();
		}
    }

    isVisualisationCapture() {
        return this.renderType === 'visualisation_capture';
    }

    isDashboardCapture() {
        return this.renderType === 'dashboard_capture';
    }

    isCapture() {
        return this.isVisualisationCapture() || this.isDashboardCapture();
    }

    /**
	 * Détermine les breakpoints en fonction de la largeur en pixel de la visualisation
	 * clé_libre: valeur en pixel la plus basse (incluse) pour ce breakpoint.
	 * ex: {
	 *     "small": 0
	 *     "medium": 500
	 *     "large": 750
	 *	}
	 * Ici small de 0 à 499px, medium de 500px à 749px et large de 750 à l'infini.
     * Les breakpoints peuvent être modifié pour une visu en surchargeant cette fonction
	 */
	getBreakpoints() {
		return {
			"small": 0,
		};
	}

	/**
	 * Met à jour la valeur de currentBreakpoint en fonction
	 * de la largeur actuelle du container de la visualisation et des breakpoints
	 * définis dans la fonction getBreakpoints()
	 */
	updateCurrentBreakpoint() {
		let currentWidth = this.getContainer()[0].getBoundingClientRect().width;
		let breakpoints = this.getBreakpoints();
		let breakpointsValues = Object.values(breakpoints);
		let currentBreakpointValue = breakpointsValues.find((breakpoint, index) => {
			return currentWidth >= breakpoint && (typeof breakpointsValues[index + 1] === "undefined" || currentWidth < breakpointsValues[index + 1])
		});
        this.previousBreakpoint = this.currentBreakpoint;
		this.currentBreakpoint = Object.keys(breakpoints)[breakpointsValues.indexOf(currentBreakpointValue)];
    }

    /**
     * Retourne true si le breakpoint courant est plus faible que le paramètre breakpoint
     * Sinon retourne false
     */
    isCurrentBreakpointSmallerThan(breakpoint) {
        let breakpoints = this.getBreakpoints();
        return breakpoints[this.getCurrentBreakpoint()] < breakpoints[breakpoint];
    }

    /**
     * Retourne true si le breakpoint courant est plus grand que le paramètre breakpoint
     * Sinon retourne false
     */
    isCurrentBreakpointGreaterThan(breakpoint) {
        let breakpoints = this.getBreakpoints();
        return breakpoints[this.getCurrentBreakpoint()] > breakpoints[breakpoint];
    }

	/**
	 * Retourne le breakpoint actif (small, medium, etc).
	 */
	getCurrentBreakpoint() {
		return this.currentBreakpoint;
	}

    /**
     * Retourne le breakpoint précédent le dernier rendu
     */
    getPreviousBreakpoint() {
        return this.previousBreakpoint;
    }

    hasSameBreakpointThanBeforeResize() {
        return this.previousBreakpoint === this.currentBreakpoint;
    }

    hasSameWidthThanBeforeResize() {
        return (typeof this.previousWidth === "undefined") || this.previousWidth !== this.width;
    }

    hasSameHeightThanBeforeResize() {
        return (typeof this.previousHeight === "undefined") || this.previousHeight === this.height;
    }

    /**
     * @param container
     * @param {int} width
     * @param {int} height
     */
    async resize(container, width, height) {
        let that = this;
        let $container = $(that.$parent.selector);
        if (typeof width === "undefined") {
            width = $container.width();
        }
        if (typeof height === "undefined") {
            height = $container.height();
        }
        $container.find(`.${this.name}`).remove();
        await that.setSize(width, height);
        that.reset();
        that.setParams(that.params);
        that.preResize(container, width, height);
        that.render(that.$parent.selector);
        that.postResize(container, width, height);
    }

    /*** Event permettant de surcharger la width/height qui vont être appliqués ***/
    async onUpdateSize(width, height) {
        return {
            width, height
        };
    }

    async setSize(width, height) {
        const updatedSize = await this.onUpdateSize(width, height);
        this.previousWidth = this.width;
        this.previousHeight = this.height;
        this.width = updatedSize.width;
        this.height = updatedSize.height;
    }

    /**
     * Hook pre-resize
     * @param container
     * @param width
     * @param height
     */
    // eslint-disable-next-line no-unused-vars
    preResize(container, width, height) {
    }

    /**
     * Hook post-resize
     * @param container
     * @param width
     * @param height
     */
    // eslint-disable-next-line no-unused-vars
    postResize(container, width, height) {
        this.$parent.find('.visualisation-root').data('chart', this);
    }

    /**
     * Retourne la largeur sans les marges
     * @returns {number}
     */
    quadrantWidth() {
        return this.width - this.margins.left - this.margins.right;
    }

    /**
     * Retourne la hauteur sans les marges
     * @returns {number}
     */
    quadrantHeight() {
        return this.height - this.margins.top - this.margins.bottom;
    }

    /**
     * @returns {int}
     */
    xStart() {
        return this.margins.left;
    }

    /**
     * @returns {int}
     */
    yStart() {
        return this.height - this.margins.bottom;
    }

    /**
     * @returns {int}
     */
    xEnd() {
        return this.width - this.margins.right;
    }

    /**
     * @returns {int}
     */
    yEnd() {
        return this.margins.top;
    }

    /**
     *
     * @param {string} val
     * @param {boolean} enableDeclinations
     * @returns {*}
     */
    getColor(val, enableDeclinations = false) {
        let color;
        if (typeof this.options.colorScheme !== "undefined") {
            color = this.getCustomColor(val);
        } else if (this.dashboard !== false) {
            color = this.dashboard.getColor(val);
        } else {
            color = this.colors(val);
        }
        return enableDeclinations ? this.getColorWithDeclination(val, color) : color;
    }

    getSchemeColor(color) {
        if (_.isString(color) && !color.startsWith("__color_")) {
            return color;
        }
        let schemeIndex = 0;
        if (_.isString(color)) {
            schemeIndex = color.split("__color_")[1];
        } else if (_.isObject(color) && 'scheme' in color) {
            schemeIndex = color.scheme;
        }
        if (color === scheme_dark.key) {
            return scheme_dark.color;
        } else if (color === scheme_white.key) {
            return scheme_white.color;
        } else if (color === scheme_transparent.key) {
            return 'transparent';
        }
        if (typeof this.options.colorScheme !== "undefined") {
            return this.options.colorScheme[schemeIndex];
        } else if (this.dashboard !== false) {
            return this.dashboard.colorScaleFlat[schemeIndex];
        }
        return color;
    }

    getCustomColor(key) {
        key = $.trim(key).toFieldName();
        if (typeof (this.customColors[key]) !== "undefined") {
            return this.customColors[key];
        }
        let currentColors = Object.getValues(this.customColors);
        let l = (currentColors.length * 5) % this.options.colorScheme.length;
        let c = this.options.colorScheme[l];
        this.customColors[key] = c;
        return c;
    }

    resetSavedColors() {
        this.customColors = {};
    }

    /**
     *
     * @returns {*}
     */
    getColorScale() {
        if (this.dashboard !== false) {
            return this.dashboard.colorScale;
        }
        return this.colors;
    }

    generateColors(nb_color, color_min, color_max) {
        let generated_colors = d3.scaleLinear().domain([0, nb_color - 1]).range([color_min, color_max]);
        let colors = [];
        for (let i = 0; i < nb_color; i++) {
            colors.push(generated_colors(i));
        }
        return colors;
    }

    getHeatColorScale(colors) {
        if (this.options.value_max) {
            this.moy = (this.max + this.min) / 2;
        }

        let domain = [];
        if (this.options.scalePow) {
            let scale = d3.scalePow().exponent(2).domain([0, this.options.color_nb - 1]).range([this.min, this.max]);
            for (let i = 0; i < this.options.color_nb; i++) {
                domain.push(scale(i));
            }
        } else {
            domain = [this.min, this.moy, this.max];
        }
        return d3.scaleQuantile()
            .domain(domain)
            .range(colors);
    }

    /**
     * Fonction qui enregistre l'association entre "originColor" et "val"
     * et retourne originColor si cette valeur n'a jamais été enregistrée.
     * Les fois suivantes :
     * - si originColor === val alors la fonction retourne originColor
     * - sinon créer une déclinaison de "originColor" et l'enregistre en association avec "val"
     *
     * La création d'une déclinaison de couleur respecte les règles suivantes :
     * - elle correspond à un doublon de couleur initiale avec un niveau de lightness/luminosité différent (voir HSL)
     * - le niveau de lightness de la première déclinaison dépend de la couleur de contraste de la couleur initiale
     * - à partir de la troisième déclinaison, on recherche les déclinaisons existantes qui le plus d'écart dans leur niveau de lightness
     * et on fait la moyenne du lightness des deux pour obtenir le niveau de lightness de la nouvelle déclinaison.
     *
     * Bonne chance s'il faut modifier cette fonction :-)
     *
     * @param val
     * @param originColor
     * @returns string
     */
    getColorWithDeclination(val, originColor) {

        // retourne une couleur à partir de la palette du dashboard, de celle du client ou d'un scale d3.js en fonction de l'input
        let usedColors = typeof this.colorsDeclinations[originColor] !== "undefined" ? this.colorsDeclinations[originColor] : [];
        usedColors = usedColors.sort((firstItem, secondItem) => firstItem.lightness - secondItem.lightness);

        if (typeof this.colorsDeclinations[originColor] !== "undefined") {
            let currentValColorIndex = usedColors.findIndex(usedColor => usedColor.val === val);
            if (currentValColorIndex === -1) {
                let newColor = originColor;
                let isContrastColorLight = this.getContrastYIQ(originColor) === "white";
                let nbUsedColors = Object.keys(usedColors).length;
                if (nbUsedColors === 1) {
                    // création de la première déclinaison de la couleur d'origine
                    // en fonction de la couleur de contraste de celle-ci
                    let hsl = d3.hsl(originColor);
                    hsl.l = isContrastColorLight ? 0.75 : 0.3;
                    newColor = hsl.formatHex();
                } else {

                    // recherche de l'intervalle maximum de la valeur lightness qui se suivent
                    // dans le tableau des déclinaisons de la couleur
                    let orderedDeclinations = usedColors
                        .sort((firstItem, secondItem) => firstItem.lightness - secondItem.lightness)
                        .map(item => item.lightness);
                    // génération de toutes les paires dans l'ordre du tableau orderedDeclinations
                    let pairs = orderedDeclinations.map((item, index) => typeof orderedDeclinations[index + 1] !== "undefined" ? [item, orderedDeclinations[index + 1]] : undefined).filter(item => item !== undefined);
                    // calcul de l'intervalle de lightness pour chaque paire avec un arrondi au centième
                    // afin de pouvoir choisir entre les les paires qui ont des intervalles presque identiques un peu après
                    let intervals = pairs.map(item => Math.round((item[1] - item[0]) * 100) / 100);

                    // recherche de la paire avec l'intervalle le plus grand
                    let maxIntervalValue = Math.max(...intervals);
                    let maxIntervalIndexes = intervals.map((interval, index) => interval === maxIntervalValue ? index : undefined).filter(item => item !== undefined);
                    // on prend toujours l'intervalle avec l'index le plus faible
                    // pour créer les déclinaisons les plus sombres en priorité
                    let maxIntervalIndex = maxIntervalIndexes[0];
                    let maxIntervalPair = pairs[maxIntervalIndex];

                    // création d'un nouvelle déclinaison à partir du code hexa initial
                    // et de la moyenne du lightness des déclinaisons qui ont l'écart de lightness le plus grand
                    // dans la liste des déclinaisons déjà créées
                    let newLightness = (maxIntervalPair[0] + maxIntervalPair[1]) / 2;
                    let hsl = d3.hsl(originColor);
                    hsl.l = newLightness;
                    newColor = hsl.formatHex();
                }

                usedColors.push({val: val, color: newColor, lightness: d3.hsl(newColor).l});
            }
        } else {
            usedColors.push({val: val, color: originColor, lightness: d3.hsl(originColor).l});
        }
        this.colorsDeclinations[originColor] = usedColors;

        return this.colorsDeclinations[originColor].find(item => item.val === val).color;
    }

    /**
     *
     * @returns {string|int}
     */
    getParent() {
        if (typeof this.parent === "undefined") {
            this.parent = Math.random().toString(12).substring(8);
        }
        return this.parent;
    }

    /**
     * @param d
     * @param apply
     * @returns {string}
     */
    getSuffixedLabel(d, apply = true) {
        return d + (apply ? (this.options.legend_suffix ? " " + this.options.legend_suffix : "") : "");
    }

    /**
     * Retourne l'élément jquery qui sert de container
     * @return {*|jQuery|HTMLElement}
     */
    getContainer() {
        return this.$parent;
    }

    /**
     * @param {string} value
     * @param {string} type
     * @param {object} parameters
     * @returns {*}
     */
    formatType(value, type, parameters) {
        let that = this;

        let p = $.extend({
            preventAverageUntil: 100000,
            preventAverageOnIntegerUntil: 1000,
            preventFirstDecimalAfter: null,
            integerAverageFormat: ".0a",
        }, parameters);

        let format;
        if (value === null) {
            return 0;
        }
        if (type === "money") {
            return numbro(value).formatCurrency();
        }
        if (that.isFloatType(type) || (that.isIntType(type) && !_.isInteger(value))) {
            format = '0[,]';
            if (Math.abs(value) < 10) {
                format = '0[,].00';
            } else  if (p.preventFirstDecimalAfter === null || Math.abs(value) < p.preventFirstDecimalAfter) {
                format = '0[,].0';
            }
            if (Math.abs(value) >= p.preventAverageUntil) {
                format += "a";
            }
            return numbro(value).format(format);
        }
        if (that.isIntType(type)) {
            format = '0[,]';
            if (value > p.preventAverageOnIntegerUntil) {
                format += p.integerAverageFormat;
            }
            return numbro(value).format(format);
        }
        if (type === "percent") {
            return d3.format(",.2%")(value > 1 ? value / 100 : value);
        }
        if (type === "date") {
            if (typeof parameters !== 'undefined' && typeof parameters.interval !== 'undefined') {
                return moment(value).utc().format(that.formatInterval(parameters.interval));
            }
            return moment(value).utc().format("DD/MM/YYYY");
        }
        if (type === "bytes") {
            return numbro(value).format('0.0d');
        }

        return value;
    }

    formatInterval(interval) {
        switch (interval) {
            case "y": return "YYYY";
            case "M": return "MM/YYYY";
            case "HH":  return "HH:00";
            case "mm": return "HH:mm";
            case "H": case "m": case "s" : return "DD/MM/YYYY HH:mm:ss";
        }
        return "DD/MM/YYYY";
    }

    getMomentIntervalName(interval) {
        switch (interval) {
            case "y": return "year";
            case "M": return "month";
            case "w": return "isoweek";
            case "d": return "day";
            case "H": return "hour";
            case "m": return "minute";
            case "s": return "second";
        }
        return "day";
    }

    getRangesFromKey(key, interval) {
        const start = moment.utc(key, this.formatInterval(interval));
        const gte = start;
        const lte = start.clone().endOf(this.getMomentIntervalName(interval));
        return {
            gte: gte.unix(),
            lte: lte.unix(),
            start: gte.format(this.formatInterval("s")),
            end: lte.format(this.formatInterval("s")),
        };
    }

    /**
     * Formatte un nombre selon un format personalisé
     * @param value
     * @param format
     * @returns {*}
     */
    customFormat(value, format) {
        if (format === "") {
            return value;
        }
        if (value === null) {
            return 0;
        }
        if (format === "0[,]") {
            format = { mantissa: 0, thousandSeparated: true };
        }
        return numbro(value).format(format); //((d3.format(format)(value)).replace(',', ' ')).replace('.', ',');
    }

    /**
     * @returns {boolean|*}
     */
    isPreview() {
        return typeof this.preview !== "undefined" && this.preview;
    }

    /**
     * Comportement par défaut lorsque la souris entre dans la zone du tooltip
     */
    mouseEnterTooltip(e) {
        this.createTooltip(e.target.getAttribute('data-tooltip'));
    }

    /**
     * Comportement par défaut lorsque la souris se déplace dans la zone du tooltip
     */
    mouseMoveTooltip(e) {
        let tooltip = $('.tooltip-pie');
        if (!tooltip.length) {
            return;
        }
        let options = JSON.parse(decodeURIComponent(tooltip.find('.tooltip-table-multiple').data('tooltip-options')));
        if (options.enableResponsiveAlert === false) {
            this.updateTooltipPosition(e.pageX, e.pageY);
        }
    }

    /**
     * Comportement par défaut lorsque la souris quitte la zone du tooltip
     */
    mouseLeaveTooltip() {
        let tooltip = $('.tooltip-pie');
        if (!tooltip.length) {
            return;
        }
        let options = JSON.parse(decodeURIComponent(tooltip.find('.tooltip-table-multiple').data('tooltip-options')));
        if (options.enableResponsiveAlert === false) {
            this.removeTooltip();
        }
    }

    createTooltip(json, orientation = 'bottom', fade = true, onRemoveResponsiveAlert = () => {}) {
        let that = this;
        // suppression des tooltips existants s'il y en a
        that.removeTooltip(fade);
        let html = this.tooltipJson(json);

        let tooltip;
        let tooltipContent = $(html);
        let options = JSON.parse(decodeURIComponent(tooltipContent.data('tooltip-options')));
        if (options.enableResponsiveAlert === true) {
            tooltip = $(`<div class="tooltip-pie tooltip-pie-alert"><div class="tooltip-pie-content"></div></div>`);
            tooltip.find('.tooltip-pie-content').html(html);
            tooltip.appendTo(that.$parent);
            tooltip.on('click', () => {
                onRemoveResponsiveAlert();
                that.removeTooltip();
            });
        } else {
            tooltip = $(`<div class="tooltip-pie tooltip-pie-${orientation}"><span class="tooltip-pie-arrow"></span><div class="tooltip-pie-content"></div></div>`);
            tooltip.find('.tooltip-pie-content').html(html);
            tooltip.appendTo('body');
        }

        if (fade) {
            tooltip.fadeIn(100);
        } else {
            tooltip.show();
        }
    }

    updateTooltipContent(json) {
        let tooltip = $('.tooltip-pie');
        let html = this.tooltipJson(json);
        tooltip.find('.tooltip-pie-content').html(html);
    }

    updateTooltipPosition(mouseX, mouseY, orientation = 'bottom') {
        // TODO: Refactor tooltip with d3 methods
        // example : http://bl.ocks.org/Caged/6476579

        let tooltip = $('.tooltip-pie');
        let tootipArrow = tooltip.find('.tooltip-pie-arrow');
        const optionsText = decodeURIComponent(tooltip.find('.tooltip-table-multiple').data('tooltip-options'));
        if (!optionsText || optionsText === "undefined") {
            return;
        }
        let options = JSON.parse(optionsText);

        let windowW = $(window).width();
        let windowH = $(window).height();
        let ttW = tooltip.outerWidth();
        let ttH = tooltip.outerHeight();

        // calcul des nouvelles coordonnées
        let offsetX;
        let offsetY;
        let arrowSize = 12;
        switch (orientation) {
            case 'top':
                offsetX = mouseX - (ttW / 2);
                offsetY = mouseY + arrowSize;
                break;
            case 'bottom':
                offsetX = mouseX - (ttW / 2);
                offsetY = mouseY - arrowSize - ttH;
                break;
            case 'left':
                offsetX = mouseX + arrowSize;
                offsetY = mouseY - (ttH / 2);
                break;
            case 'right':
                offsetX = mouseX - arrowSize - ttW;
                offsetY = mouseY - (ttH / 2);
                break;
        }

        // evite le débordement du viewport en modififant la position
        // /!\ Attention ne fonctionne que si la flèche est en bas, je n'ai pas eu le temps de faire plus
        if (options.preventOverflowViewportWithOffset === true) {
            switch (true) {
                case offsetX + ttW > windowW: // overflow viewport on right
                    offsetX -= (offsetX + ttW) - windowW;
                    tootipArrow.css({left: (mouseX - offsetX) + 'px'});
                    break;
                case offsetX < 0: // overflow viewport on left
                    offsetX = 0;
                    tootipArrow.css({left: mouseX + 'px'});
                    break;
                // case offsetY + ttH > windowH: // overflow viewport on bottom
                //     offsetY -= (offsetY + ttH) - windowH;
                //     break;
            }
        }

        // mise à jour de la classe d'orientation du tooltip
        let orientationClass = 'tooltip-pie-' + orientation;
        if (!tooltip.hasClass(orientationClass)) {
            tooltip.removeClass('tooltip-pie-top tooltip-pie-bottom tooltip-pie-left tooltip-pie-right');
            tooltip.addClass(orientationClass);
        }

        // mise à jour des coordonnées
        tooltip.css({top: offsetY, left: offsetX});
    }

    /**
     * Comportement par défaut lorsque la souris quitte la zone du tooltip
     */
    removeTooltip(fade = true) {
        let target = $('.tooltip-pie');
        if (fade) {
            target.fadeOut(300, function() {
                $(this).remove();
            });
        } else {
            target.remove();
        }
    }

    /**
     * Transforme les options d'une manière a ce que l'intégrité des données soit conservé avant/après un enregistrement en base
     * @param options
     * @returns {*}
     */
    validateOptions(options) {
        $.each(options, (index, option) => {
            if (option === "" || typeof option === "undefined") {
                options[index] = null;
            }
            if (option === "true") {
                options[index] = true;
            }
            if (option === "false") {
                options[index] = false;
            }
        });
        return options;
    }

    /**
     * Retourne la couleur d'une lisibilité optimale par rapport à une couleur de fond
     * @param  {string} hexcolor couleur au format hexadécimale
     * @return {string} 'white' ou 'black'
     */
    getContrastYIQ(hexcolor, getYiq, darkColor = 'black', lightColor = 'white') {
        if (hexcolor === 'transparent') {
            return darkColor;
        }
        if (hexcolor.length <= 6 && hexcolor.substr(0, 1) !== "#") {
            hexcolor = "#" + hexcolor;
        }
        let color = d3.color(hexcolor);
        let yiq = ((color.r * 299) + (color.g * 587) + (color.b * 114)) / 1000;
        if (getYiq) {
            return yiq;
        }
        return (yiq >= 175) ? darkColor : lightColor;
    }

    isIntType(type) {
        return ["integer", "long"].indexOf(type) !== -1;
    }

    isFloatType(type) {
        return ["float", "double"].indexOf(type) !== -1;
    }

    isStringType(type) {
        return ["string", "keyword"].indexOf(type) !== -1;
    }

    isNumberType(type) {
        return ["integer", "long", "float", "double"].indexOf(type) !== -1;
    }

    /**
     * @param {object|string} json
     * @returns {string}
     */
    tooltipJson(json) {
        'use strict';

        if (!json) return;

        if (typeof json === "string") {
            json = JSON.parse(json);
        }

        // déclaration des options par defaut
        let options = {
            displaySeparators: true, // conditionne l'affichage d'un séparateur entre les lignes
            preventOverflowViewportWithOffset: true, // décale de la position de quelques pixels en cas de sortie du viewport
            enableResponsiveAlert: false, // en mobile le tooltip se tranforme en une alerte qui recouvre la visu
            tableCustomClasses: "",
        };
        // surcharge des options par defaut à partir des options données dans le JSON
        if (json.hasOwnProperty('options')) {
            Object.keys(options).forEach(key => {
                options[key] = json.options.hasOwnProperty(key) ? json.options[key] : options[key];
            });
        }

        let thead = "";
        if (json.hasOwnProperty('x')) {
            $.each(json.x, (index, val) => {
                let formatted_index = typeof index === "string" ? index.capitalize() : index;
                let values = val instanceof Array ? val : [val];
                thead += `
                    <tr>
                        <th colspan="2">${formatted_index}</th>
                        ${values.map(value => {
                            return `<th>${typeof value === "string" ? value.capitalize() : value}</th>`;
                        }).join('')}
                    </tr>
                `;
            });
        } else if (json?.headHtml) {
            thead = json.headHtml;
        }

        let tbody = "";
        if (json.hasOwnProperty('y')) {
            $.each(json.y, (index, val) => {

                let formatted_index = typeof val.key === "string" ? val.key.capitalize() : val.key;
                let values = typeof val.values !== "undefined" ? val.values : [val.value];
                tbody += `
                    <tr ${options.displaySeparators ? 'class="bottom-separator" ' : ''}>
                        <td><span class="tooltip-color" style="--tooltip-color:${val.color}; background-color: ${val.color}"></span></td>
                        <td>${formatted_index}</td>
                        ${values.map(value => {
                            return `<td>${typeof value === "string" ? value.capitalize() : value}</td>`;
                        }).join((''))}
                    </tr>
                `;
            });
        } else if (json?.bodyHtml) {
            tbody = json.bodyHtml;
        }

        thead = this.getTooltipHead(thead, json);
        tbody = this.getTooltipBody(tbody, json);

        let html = `
        <table class="tooltip-table-multiple ${options.tableCustomClasses}" data-tooltip-options="${encodeURIComponent(JSON.stringify(options))}">
            <thead>
                ${thead}
            </thead>
            <tbody>
                ${tbody}
            </tbody>
        </table>
        `;


        return html;
    }

    // eslint-disable-next-line no-unused-vars
    getTooltipHead(thead, json) {
        return thead;
    }

    // eslint-disable-next-line no-unused-vars
    getTooltipBody(tbody, json) {
        return tbody;
    }


    defaultOptions() {
        return {};
    }

    triggerUpdateTitle() {
        this.$parent.trigger("visualisation.updateTitle", {
            parent: this.parent,
            chart: this,
            title: this.overrideTitle(),
        });
    }

    overrideTitle() {
        return null;
    }

    selfUpdate() {
        let that = this;
        that.preSelfUpdate();
        that.$parent.trigger("visualisation.selfUpdate", {
            element: this,
            parent: that.parent,
            chart: that
        });
    }

    selfUpdateComparison() {
        let that = this;
        that.preSelfUpdate();
        that.$parent.trigger("visualisation.selfUpdateComparison", {
            element: this,
            parent: that.parent,
            chart: that
        });
    }

    preSelfUpdate() {}

    getState() {
        return $.extend(this.getDefaultState(), this.state);
    }

    resetState() {
        this.state = this.getDefaultState();
    }

    getDefaultState() {
        return {};
    }

    destroy() {
        return {};
    }

    getNodeByLabel(name) {
        return this.getNodes().filter(d => d.__data__.key === name);
    }

    getNodes() {
        return [];
    }

    addSelectRemoveUnselect(n) {
        n.classList.add(this.selectedClass);
        n.classList.remove(this.unselectedClass);
    }

    addUnselectRemoveSelect(n) {
        n.classList.remove(this.selectedClass);
        n.classList.add(this.unselectedClass);
    }

    removeSelectRemoveUnselect(n) {
        n.classList.remove(this.selectedClass);
        n.classList.remove(this.unselectedClass);
    }

    getSelectedNodes() {
        return this.getNodes().filter(n => n.classList.contains(this.selectedClass));
    }

    getNodeValue(node) {
        return node.__data__;
    }

    getSelectedNodesValues() {
        return _.uniq(this.getSelectedNodes().map(this.getNodeValue));
    }

    selectNode(name) {
        let that = this;
        if (this.getSelectedNodes().length === 0) {
            this.getNodes().forEach(n => {
                that.addUnselectRemoveSelect(n);
            });
        }
        this.getNodeByLabel(name).forEach(n => {
            that.addSelectRemoveUnselect(n);
        });
        if (that.selected) {
            if ($.isArray(that.selected)) {
                let index = that.selected.indexOf(name);
                if (index === -1) {
                    that.selected.push(name);
                }
            } else {
                that.selected = name;
            }
        }
    }

    unselectNode(name) {
        let that = this;
        name = name.toString();
        this.getNodeByLabel(name).forEach(n => {
            that.addUnselectRemoveSelect(n);
        });
        if (this.getNodes().filter(n => n.classList.contains(that.selectedClass)).length === 0) {
            this.unselectAllNodes();
        }
        if (that.selected) {
            if ($.isArray(that.selected)) {
                let index = that.selected.indexOf(name);
                if (index !== -1) {
                    that.selected.splice(index, 1);
                }
            } else {
                that.selected = false;
            }
        }
    }

    unselectAllNodes() {
        let that = this;
        this.getNodes().forEach(n => {
            that.removeSelectRemoveUnselect(n);
        });
        that.selected = false;
    }

    // eslint-disable-next-line no-unused-vars
    onItemAdded(item) {
    }

    onItemRemoved(item) {
        if (parseInt(item.value.parent) === this.parent) {
            this.onItemRemovedSelf(item);
        }
    }

    onItemRemovedSelf(item) {
        let that = this;
        if (!that.selected) return false;

        if (item.value.type === "term") {
            const filterValues = Array.isArray(item.value.filter) ? item.value.filter : [item.value.filter];
            filterValues.forEach(f => {
                if (Array.isArray(that.selected)) {
                    if (that.selected.indexOf(f) !== -1) {
                        that.selected.splice(that.selected.indexOf(f), 1);
                    }
                } else {
                    that.selected = false;
                }
            });
        }
    }

    /**
     * Rendu de la barre de navigation de la pagination
     * et gestion des events
     */
    renderPagination(container, pagination) {
        container.select('.pagination').remove();
        container.html(`
            <nav class="pagination">
                <span data-pagination-start class="pagination-arrow"><i class="fa-solid fa-chevrons-left"></i></span>
                <span data-pagination-left class="pagination-arrow"><i class="fa-solid fa-chevron-left"></i></span>
                <span data-pagination-status class="pagination-arrow-status"></span>
                <span data-pagination-right class="pagination-arrow"><i class="fa-solid fa-chevron-right"></i></span>
                <span data-pagination-end class="pagination-arrow"><i class="fa-solid fa-chevrons-right"></i></span>
            </nav>
        `);

        // arows
        container.select('[data-pagination-start]')
            .classed("pagination-arrow-active", pagination.getCurrentPage() > 1)
            .on("click", () => pagination.startPage());
        container.select('[data-pagination-left]')
            .classed("pagination-arrow-active", pagination.getCurrentPage() > 1)
            .on("click", () => pagination.previousPage());
        container.select('[data-pagination-right]')
            .classed("pagination-arrow-active", pagination.getCurrentPage() < pagination.getMaxPage())
            .on("click", () => pagination.nextPage());
        container.select('[data-pagination-end]')
            .classed("pagination-arrow-active", pagination.getCurrentPage() < pagination.getMaxPage())
            .on("click", () => pagination.endPage());

        // status
        container.select('[data-pagination-status]')
            .classed("d-none", pagination.getMaxPage() === 1)
            .text(`${pagination.getCurrentPage()} / ${pagination.getMaxPage()}`);
    }

    /**
     * Affichage de la première page
     */
    async startPage() {
        this.state.pagination = 1;
        await this.render(this.$parent.selector);
    }

    /**
     * Affichage de la page suivante
     */
    async nextPage() {
        this.state.pagination++;
        await this.render(this.$parent.selector);
    }

    /**
     * Affichage de la page précédente
     */
    async previousPage() {
        this.state.pagination = Math.max(1, this.getCurrentPage() - 1);
        await this.render(this.$parent.selector);
    }

    /**
     * Affichage de la dernière page
     */
    async endPage() {
        this.state.pagination = this.getMaxPage();
        await this.render(this.$parent.selector);
    }

    hasData() {
        return ($.isArray(this.data) && this.data.length) || (!$.isArray(this.data) && this.data !== null);
    }

    hasComparisonData() {
        return $.isArray(this.comparisonDatas) && this.comparisonDatas.length;
    }

    onEmptyData(element) {
        let that = this;

        if (that.svg) {
            that.svg.remove();
            that.reset();
        }

        if (!that.emptyDataElement) {
            that.emptyDataElement = d3.select(element).append("svg")
                .attr("class", "visualisation-empty-data");

            that.emptyDataElement.append('text')
                .attr("dy", "0.35em")
                .attr('text-anchor', 'middle')
                .text(that.emptyDataText)
                ;
        }

        that.emptyDataElement
            .attr("height", that.height)
            .attr("width", that.width);

        that.emptyDataElement.select('text')
            .attr("x", that.width ? that.width / 2 : 0)
            .attr("y", that.height ? that.height / 2 : 0)
            .call((text, width) => {
                text.each(function() {
                    let text = d3.select(this);
                    let words = text.text().split(/\s+/).reverse();
                    let word;
                    let line = [];
                    let lineNumber = 0;
                    let lineHeight = 1.4; // ems
                    let y = text.attr("y");
                    let x = text.attr("x");
                    let dy = parseFloat(text.attr("dy"));
                    let tspan = text.text(null).append("tspan").attr("x", x).attr("y", y).attr("dy", dy + "em");

                    while (word = words.pop()) {
                        line.push(word);
                        tspan.text(line.join(" "));
                        if (tspan.node().getComputedTextLength() > width) {
                            line.pop();
                            tspan.text(line.join(" "));
                            line = [word];
                            tspan = text.append("tspan").attr("x", x).attr("y", y).attr("dy", ++lineNumber * lineHeight + dy + "em").text(word);
                        }
                    }

                    let box = this.getBoundingClientRect();
                    $(this).find('tspan').each(function() {
                        d3.select(this).attr('y', parseFloat(d3.select(this).attr('y')) - (box.height / 2));
                    });
                });
            }, that.width);
    }

    reset() {
        this.svg = false;
    }

    // eslint-disable-next-line no-unused-vars
    disableFilterOnEdit(tag) {
        return false;
    }

    isResponsive() {
        return this.responsive;
    }

    isComparable() {
        return this.comparisonEnabled &&
            typeof this.comparisonDatas !== "undefined" &&
            typeof this.updateComparison === "function";
    }

    mergeAdditionalOptions(options, additionalOptions) {
        if (typeof additionalOptions.legend !== 'undefined' && additionalOptions.legend) {
            options.push({
                label: "Afficher la légende",
                name: "legend",
                type: "checkbox",
                default: true
            });
        }
        if (typeof additionalOptions.canCreateFilter !== 'undefined' && additionalOptions.canCreateFilter) {
            options.push({
                label: "Permet de créer des filtres",
                name: "canCreateFilter",
                type: "checkbox",
                default: true
            });
        }
        return options;
    }

    /**
     * @deprecated use mergeAdditionalOptions
     */
    mergeLegendOption(options) {
        options.push({
            label: "Afficher la légende",
            name: "legend",
            type: "checkbox",
            default: true
        });
        return options;
    }

    /*** Responsive feature ***/

    /**
     * Retourne le nombre de colonne qui sera appliqué au container de la visu lors de son ajout sur le dashboard
     */
    getDefaultWidth() {
        return null;
    }

    /**
     * Retourne le nombre de hauteur qui sera appliquéau au container de la visu lors de son ajout sur le dashboard
     */
    getDefaultHeight() {
        return null;
    }

    getDefaultSize() {
        return {
            width: this.getDefaultWidth(),
            height: this.getDefaultHeight(),
        };
    }

    /**
     * Retourn le nombre minimum de colonnes que peut occuper le container de la visu sur le dashboard
     */
    getMinWidth() {
        return null;
    }

    /**
     * Retourne le nombre minimum de hauteur que peut occuper le container de la visu sur le dashboard
     */
    getMinHeight() {
        return null;
    }

    /**
     * Retourne le nombre actuel de colonnes qu'occupe le container de la visu sur le dashboard
     */
    getCurrentGridWidth() {
        return typeof this.gridItem !== "undefined" ? this.gridItem.state.grid.w : null;
    }

    /**
     * Retourne le nombre actuel de hauteur qu'occupe le container de la visu sur le dashboard
     */
    getCurrentGridHeight() {
        let gridH = this.gridItem?.state.grid?.h ?? this.gridItem?.state.grid?.initialValues.h ?? null;
        // On divise par 2 car une hauteur = 2 lignes de grid
        return gridH ? gridH / 2 : null;
    }

    /*** Locking feature ***/

    lock() {
        this.lockedDatas = JSON.parse(JSON.stringify(this.data));
        this.onLock();
    }

    unlock() {
        this.lockedDatas = false;
        this.onUnlock();
    }

    onLock() {
        if (this.legendItems) {
            this.legendItems.forEach(i => {
                i.locked = true;
            });
            this.legend.update();
        }
    }

    onUnlock() {
        if (this.legendItems) {
            this.legendItems = this.legendItems.filter(i => !i.locked);
            this.legend.update();
        }
    }

    /*** Annotation feature ***/

    listenAnnotationEvent() {
        this.allowAnnotation = true;
    }

    cancelAnnotationEvent() {
        this.allowAnnotation = false;
    }

    addAnnotation(data) {
        let annotation = {
            id: data.x + "-" + data.i + "-" + data.index,
            note: {
                label: "",
                title: data.x_label
            },
            data: {x: data.x, y_axis: data.i, line: data.index},
            dy: 100,
            dx: 100,
            color: "#E8336D",
            subject: {
                text: "A",
                radius: 14
            }
        };
        this.annotations.push(annotation);
        this.$parent.trigger('annotation.add', annotation);
        this.initAnnotations();
    }

    restoreAnnotations(annotations) {
        this.annotations = annotations;
        this.initAnnotations();
    }

    removeAnnotation(id) {
        _.remove(this.annotations, ['id', id]);
        this.$parent.trigger('annotation.remove', id);
        this.initAnnotations();
    }

    updateAnnotations() {
        if (!this.hasOwnProperty('annotationFactory')) {
            this.initAnnotations();
        } else {
            this.annotationFactory.updatedAccessors();
        }
    }

    toggleAnnotationsEditing(bool) {
        if (!this.hasOwnProperty('annotationFactory')) {
            this.annotationFactory.editMode(bool).update();
        }
    }

    getAnnotationsContainer() {
        return this.bodyG;
    }

    initAnnotations() {
        let that = this;
        let container = this.getAnnotationsContainer();

        container.selectAll('.annotation-group').remove();

        this.annotationFactory = d3Annotation.annotation()
            .editMode(true)
            .type(d3Annotation.annotationCallout)
            .accessors({
                x: function(d) {
                    let x = that.x(d.x);
                    return x;
                },
                y: function(d) {
                    let dataPoint = _.find(that.dataLines[d.line], ['x', d.x]);
                    if (dataPoint === undefined) {
                        return -10000;
                    }
                    return that.y_axis[d.y_axis](dataPoint.y);
                }
            })
            .accessorsInverse({
                x: function(d) {
                    return that.x.invert(d.x);
                },
                y_axis: function(d) {
                    return that.x.invert(d.x);
                },
                line: function(d) {
                    return that.x.invert(d.x);
                }
            })
            .on('dragend', e => {
                let annotation = _.find(that.annotations, ['id', e.id]);
                if (annotation) {
                    annotation.dx = e._dx;
                    annotation.dy = e._dy;
                    that.$parent.trigger('annotation.update', e);
                }
            })
            .annotations(JSON.parse(JSON.stringify(this.annotations)));

        container
            .append("g")
            .attr("class", "annotation-group")
            .call(this.annotationFactory);

        container.selectAll('.annotation')
            .style('transition', that.preview ? null : "0.75s all")
            .each(function(d) {
                let textLength = Math.max(this.querySelector('.annotation-note-title tspan').getBoundingClientRect().width, this.querySelector('.annotation-note-label tspan').getBoundingClientRect().width);
                let textHeight = this.querySelector('.annotation-note-content').getBoundingClientRect().height;
                let diffW = (d.x + d.dx + (d.dx > 0 ? textLength : 0)) - that.quadrantWidth();
                let diffH = (d.y + d.dy + (d.dy > 0 ? textHeight : 0)) - that.quadrantHeight();
                if (diffW > 0) {
                    d.dx = Math.max(d.dx - diffW, -1);
                }
                if (diffH > 0) {
                    d.dy = d.dy - diffH;
                }
            });
    }

    /*** Helpers ***/

    /**
     * Retourne la liste des filtres qu'il est possible d'appliquer à partir d'une action sur la visualisation
     * @returns {{field: string, source: int}[]}
     */
    getApplicableFilters() {
        if (this.canGenerateFilters && 'x' in this.options) {
            const source = this.params.esParameters.source;
            if (_.isArray(this.options.x)) {
                return this.options.x.map(x => ({field: x.field, source}));
            } else {
                return [{field: this.options.x.field, source}];
            }
        }
        return [];
    }

    getTranslationPrefix(suffix) {
        return "alienor_charts." + this.name + (suffix ? "." + suffix : "");
    }

    /**
     * @param {string} suffix
     * @param {'fr' | 'en' | null} locale
     * @return {string}
     */
    trans(suffix, locale = null) {
        return Translator.trans(this.getTranslationPrefix(suffix), undefined, undefined, locale);
    }

    getSourceId() {
        return typeof this.params.esParameters !== "undefined" ? parseInt(this.params.esParameters.source) : null;
    }

	replaceTemplateInString(string, forcedState = null) {
		let state = forcedState ?? this.state;
		if (string.includes('{{field}}')) {
			if (state.fieldToggle) {
				string = string.replaceAll('{{field}}', state.fieldToggle.selectedLabel);
			} else if (this.params) {
				string = string.replaceAll('{{field}}', this.params.esParameters.aggregations[0].label);
			}
		}


		return string;
	}

}