import metricBase from "./metricBase";
import * as d3 from "d3";
import acUtils from "../utils";

export default class gaugeLiquidChart extends metricBase {
	constructor() {
		super();
		this.name = "gaugeLiquidChart";
		this.data = 0;
		this.current = 0;
		this.iconContainer = null;
		this.lockedDatas = false;
		this.lockedValue = false;
		this.enableLock = true;
		this.iconsXml = {};
		this.icons = {};
		this.defaultMargins = {top: 10, left: 0, right: 0, bottom: 0};
		this.margins = JSON.parse(JSON.stringify(this.defaultMargins));
	}

	setParams(params) {
		this.params = params;
		this.options = this.validateOptions($.extend(this.defaultOptions(), this.params.options));
		if (this.params.options.reference) {
			if (this.params.options.refAsCalcul) {
				this.options.value = this.params.options.reference;
			}
			if (this.params.options.refAsMax) {
				this.options.gauge_max = this.params.options.reference;
			}
		}
	}

	render(element) {
        let that = this;
        that.preRender(element);

        if (that.emptyData) {
        	return;
        }

        that.calculatedValue = that.processCalculation(that.value);
        if (that.lockedDatas) {
	        that.lockedValue = that.processCalculation(that.lockedDatas.value, true);
        }
        if (!that.svg || that.isPreview()) {
            let config = {};
	        if (that.options.template === "wave") {
		        config = liquidFillGaugeDefaultSettings();
	        } else if (that.options.template === "circle") {
		        config = circleGaugeDefaultSettings();
	        }
            let identifier = "gaugeLiquid_" + element.replace('#', '');
            config.minValue = that.options.gauge_min;
            config.maxValue = that.options.gauge_max;
            config.circleColor = that.getSchemeColor(that.options.color_fill);
            config.waveColor = that.getSchemeColor(that.options.color_fill);
            config.waveAnimate = that.options.animate;
            config.unit = that.options.unit;
            config.textSize = that.options.font_size;
            config.showPercent = that.options.showPercent;
            config.label = that.options.label;

            if (that.svg) { // = si on est en preview et qu'on modifie les options
                config.waveRise = 0;
                config.valueCountUp = 0;
                that.svg.remove();
            }

			// En capture png, pas d'animation
	        if (that.isVisualisationCapture() || that.isDashboardCapture()) {
		        config.waveAnimate = false;
		        config.waveRise = false;
		        config.waveRiseTime = 10;
	        }

            that.svg = d3.select(element).append("svg")
                .attr("height", that.height)
                .attr("width", that.width)
                .attr("id", identifier)
                .attr("class", "gaugeLiquidChart");
            if (that.options.template === "wave") {
	            that.gauge = loadLiquidFillGauge(identifier, that.calculatedValue, config);
            } else if (that.options.template === "circle") {
	            that.gauge = loadCircleGauge(identifier, that.calculatedValue, config);
            }
            setTimeout(() => {
                $(that.svg[0]).find('#' + identifier + ' path').attr('transform', 'translate(1,0)'); // chrome fix vague invisible
            }, 1000);
	        that.gauge.update(that.calculatedValue);
        } else {
            that.gauge.update(that.calculatedValue);
        }

        function liquidFillGaugeDefaultSettings() {
            return {
                minValue: 0, // The gauge minimum value.
                maxValue: 100, // The gauge maximum value.
                circleThickness: 0.05, // The outer circle thickness as a percentage of it's radius.
                circleFillGap: 0.05, // The size of the gap between the outer circle and wave circle as a percentage of the outer circles radius.
                circleColor: "#000", // The color of the outer circle.
                waveHeight: 0.05, // The wave height as a percentage of the radius of the wave circle.
                waveCount: 1, // The number of full waves per width of the wave circle.
                waveRiseTime: 1000, // The amount of time in milliseconds for the wave to rise from 0 to it's final height.
                waveAnimateTime: 18000, // The amount of time in milliseconds for a full wave to enter the wave circle.
                waveRise: true, // Control if the wave should rise from 0 to it's full height, or start at it's full height.
                waveHeightScaling: true, // Controls wave size scaling at low and high fill percentages. When true, wave height reaches it's maximum at 50% fill, and minimum at 0% and 100% fill. This helps to prevent the wave from making the wave circle from appear totally full or empty when near it's minimum or maximum fill.
                waveAnimate: true, // Controls if the wave scrolls or is static.
                waveColor: "#000", // The color of the fill wave.
                waveOffset: 0, // The amount to initially offset the wave. 0 = no offset. 1 = offset of one full wave.
                textVertPosition: .5, // The height at which to display the percentage text withing the wave circle. 0 = bottom, 1 = top.
                textSize: 1, // The relative height of the text to display in the wave circle. 1 = 50%
                valueCountUp: true, // If true, the displayed value counts up from 0 to it's final value upon loading. If false, the final value is displayed.
                unit: "", // The unit that appears after the value.
                label: null, // The label that appears under the value.
                showPercent: false,
                textColor: "#000", // The color of the value text when the wave does not overlap it.
                waveTextColor: "#fff" // The color of the value text when the wave overlaps it.
            };
        }

        function loadLiquidFillGauge(elementId, value, config) {
            if (config === null) {
	            config = liquidFillGaugeDefaultSettings();
            }

            $.each(config, (index, val) => {
                if (val === "true") {
                    config[index] = true;
                } else if (val === "false") {
                    config[index] = false;
                }
            });

            if (config.label) {
                config.textVertPosition += 0.1;
            }

            let gauge = d3.select("#" + elementId);
            let radius = Math.min(parseInt(gauge.style("width")), parseInt(gauge.style("height")) - that.margins.top) / 2;
            let locationX = parseInt(gauge.style("width")) / 2 - radius;
            let locationY = (parseInt(gauge.style("height")) - that.margins.top) / 2 - radius + that.margins.top;
            //var fillPercent = Math.max(config.minValue, Math.min(config.maxValue, value)) / config.maxValue;
            let fillPercent = (value - config.minValue) / (config.maxValue - config.minValue);
            let fillPercentLocked = (that.lockedValue - config.minValue) / (config.maxValue - config.minValue);
            let waveHeightScale;
            if (config.waveHeightScaling) {
                waveHeightScale = d3.scaleLinear()
                    .range([0, config.waveHeight, 0])
                    .domain([0, 50, 100]);
            } else {
                waveHeightScale = d3.scaleLinear()
                    .range([config.waveHeight, config.waveHeight])
                    .domain([0, 100]);
            }

            let textPixels = (config.textSize * radius / 2);
            let labelPixels = ((config.textSize - 0.1) * radius / 2);
            let textValue = config.showPercent ? (fillPercent * 100) : value;
            let textValueLocked = config.showPercent ? (fillPercentLocked * 100) : that.lockedValue;
            let textFinalValue = parseFloat(textValue).toFixed(2);
            let textFinalValueLocked = parseFloat(textValueLocked).toFixed(2);
            let textStartValue;
            let textStartValueLocked;
            if (config.showPercent && config.valueCountUp) {
                textStartValue = 0;
                textStartValueLocked = 0;
            } else {
                textStartValue = config.valueCountUp ? config.minValue : textFinalValue;
                textStartValueLocked = config.valueCountUp ? config.minValue : textFinalValueLocked;
            }

            let unitText = config.showPercent ? '%' : config.unit;
            if (unitText === null) {
            	unitText = "";
            }
            let circleThickness = config.circleThickness * radius;
            let circleFillGap = config.circleFillGap * radius;
            let fillCircleMargin = circleThickness + circleFillGap;
            let fillCircleRadius = radius - fillCircleMargin;
            let waveHeight = fillCircleRadius * waveHeightScale(fillPercent * 100);
            let waveHeightLocked = fillCircleRadius * waveHeightScale(fillPercentLocked * 100);

            let waveLength = fillCircleRadius * 2 / config.waveCount;
            let waveClipCount = 1 + config.waveCount;
            let waveClipWidth = waveLength * waveClipCount;

            // Rounding functions so that the correct number of decimal places is always displayed as the value counts up.
            let textRounder = function(value) {
            	return that.customFormat(parseFloat(value), that.options.format);
            };

            // Data for building the clip wave area.
            let data = [];
            for (let i = 0; i <= 40 * waveClipCount; i++) {
                data.push({x: i / (40 * waveClipCount), y: (i / (40))});
            }

            // Scales for drawing the outer circle.
            let gaugeCircleX = d3.scaleLinear().range([0, 2 * Math.PI]).domain([0, 1]);
            let gaugeCircleY = d3.scaleLinear().range([0, radius]).domain([0, radius]);

            // Scales for controlling the size of the clipping path.
            let waveScaleX = d3.scaleLinear().range([0, waveClipWidth]).domain([0, 1]);
            let waveScaleY = d3.scaleLinear().range([0, waveHeight]).domain([0, 1]);
            let waveScaleYLocked = d3.scaleLinear().range([0, waveHeightLocked]).domain([0, 1]);

            // Scales for controlling the position of the clipping path.
            let waveRiseScale = d3.scaleLinear()
            // The clipping area size is the height of the fill circle + the wave height, so we position the clip wave
            // such that the it will overlap the fill circle at all when at 0%, and will totally cover the fill
            // circle at 100%.
                .range([(fillCircleMargin + fillCircleRadius * 2 + waveHeight), (fillCircleMargin - waveHeight)])
                .domain([0, 1]);
	        let waveRiseScaleLocked = d3.scaleLinear()
		        .range([(fillCircleMargin + fillCircleRadius * 2 + waveHeightLocked), (fillCircleMargin - waveHeightLocked)])
		        .domain([0, 1]);
            let waveAnimateScale = d3.scaleLinear()
                .range([0, waveClipWidth - fillCircleRadius * 2]) // Push the clip area one full wave then snap back.
                .domain([0, 1]);

            // Scale for controlling the position of the text within the gauge.
            let textRiseScaleY = d3.scaleLinear()
                .range([fillCircleMargin + fillCircleRadius * 2, (fillCircleMargin + textPixels * 0.7)])
                .domain([0, 1]);

            // Center the gauge within the parent SVG.
            let gaugeGroup = gauge.append("g")
                .attr('transform', 'translate(' + locationX + ',' + locationY + ')');

            // Draw the outer circle.
            let gaugeCircleArc = d3.arc()
                .startAngle(gaugeCircleX(0))
                .endAngle(gaugeCircleX(1))
                .outerRadius(gaugeCircleY(radius))
                .innerRadius(gaugeCircleY(radius - circleThickness));
            gaugeGroup.append("path")
                .attr("d", gaugeCircleArc)
                .style("fill", config.circleColor)
                .attr('transform', 'translate(' + radius + ',' + radius + ')');

            // Text where the wave does not overlap.
            let text1 = gaugeGroup.append("text")
                .text(textRounder(textStartValue) + unitText)
                .attr("class", "liquidFillGaugeText")
                .attr("text-anchor", "middle")
                .attr("font-size", textPixels + "px")
                .style("fill", config.textColor)
                .attr('transform', 'translate(' + radius + ',' + textRiseScaleY(config.textVertPosition) + ')');

	        if (config.label) {
		        // Text where the wave does not overlap.
				gaugeGroup.append("text")
			        .text(config.label)
			        .attr("class", "liquidFillGaugeLabel")
			        .attr("text-anchor", "middle")
			        .attr("font-size", labelPixels + "px")
			        .style("fill", config.textColor)
			        .attr('transform', 'translate(' + radius + ',' + (textRiseScaleY(config.textVertPosition) + textRiseScaleY(config.textVertPosition) / 2) + ')');
	        }

            // The clipping wave area.
            let clipArea = d3.area()
                .x(d => waveScaleX(d.x))
                .y0(d => waveScaleY(Math.sin(Math.PI * 2 * config.waveOffset * -1 + Math.PI * 2 * (1 - config.waveCount) + d.y * 2 * Math.PI)))
                .y1(fillCircleRadius * 2 + waveHeight);

            // The clipping wave area.
            let clipAreaLocked = d3.area()
                .x(d => waveScaleX(d.x))
                .y0(d => waveScaleYLocked(Math.sin(Math.PI * 2 * config.waveOffset * -1 + Math.PI * 2 * (1 - config.waveCount) + d.y * 2 * Math.PI)))
                .y1(fillCircleRadius * 2 + waveHeightLocked);

            let waveGroup = gaugeGroup.append("defs")
                .append("clipPath")
                .attr("id", "clipWave" + elementId);
            let waveGroupLocked = gaugeGroup.select("defs")
                .append("clipPath")
                .attr("id", "clipWaveLocked" + elementId);
            let wave = waveGroup.append("path")
                .datum(data)
                .attr("d", clipArea)
                .attr("T", 0);
            let waveLocked = waveGroupLocked.append("path")
                .datum(data)
                .attr("d", clipAreaLocked)
                .attr("T", 0);

            // The inner circle with the clipping wave attached.
            let fillCircleGroup = gaugeGroup.append("g")
                .attr("clip-path", "url(#clipWave" + elementId + ")");
            fillCircleGroup.append("circle")
                .attr("cx", radius)
                .attr("cy", radius)
                .attr("r", fillCircleRadius)
                .style("fill", config.waveColor);

            // Text where the wave does overlap.
            let text2 = fillCircleGroup.append("text")
                .text(textRounder(textStartValue) + unitText)
                .attr("class", "liquidFillGaugeText")
                .attr("text-anchor", "middle")
                .attr("font-size", textPixels + "px")
                .style("fill", config.waveTextColor)
                .attr('transform', 'translate(' + radius + ',' + textRiseScaleY(config.textVertPosition) + ')');

            if (config.label) {
	            // Text where the wave does overlap.
	            fillCircleGroup.append("text")
		            .text(config.label)
		            .attr("class", "liquidFillGaugeLabel")
		            .attr("text-anchor", "middle")
		            .attr("font-size", labelPixels + "px")
		            .style("fill", config.waveTextColor)
		            .attr('transform', 'translate(' + radius + ',' + (textRiseScaleY(config.textVertPosition) + textRiseScaleY(config.textVertPosition) / 2) + ')');
            }

            // The inner circle with the clipping wave attached.
            let fillCircleGroupLocked = gaugeGroup.append("g")
	            .style('display', that.lockedDatas ? "inherit" : "none")
	            .attr("clip-path", "url(#clipWaveLocked" + elementId + ")");
            fillCircleGroupLocked.append("circle")
	            .attr("cx", radius)
	            .attr("cy", radius)
	            .attr("r", fillCircleRadius)
	            .style("fill", config.waveColor);

	        // Text where the wave does overlap.
	        let text2Locked = fillCircleGroupLocked.append("text")
		        .text(textRounder(textStartValueLocked) + unitText)
		        .attr("class", "liquidFillGaugeText")
		        .attr("text-anchor", "middle")
		        .attr("font-size", textPixels + "px")
		        .style("fill", config.waveTextColor)
		        .attr('transform', 'translate(' + radius + ',' + textRiseScaleY(config.textVertPosition) + ')');

	        if (config.label) {
		        // Text where the wave does overlap.
		        fillCircleGroupLocked.append("text")
			        .text(config.label)
			        .attr("class", "liquidFillGaugeLabel")
			        .attr("text-anchor", "middle")
			        .attr("font-size", labelPixels + "px")
			        .style("fill", config.waveTextColor)
			        .attr('transform', 'translate(' + radius + ',' + (textRiseScaleY(config.textVertPosition) + textRiseScaleY(config.textVertPosition) / 2) + ')');
	        }

            // Make the value count up.
            if (config.valueCountUp) {
                let textTween = function repeat() {
					d3.active(this)
						.tween("text", function() {
							let self = d3.select(this);
							let i = d3.interpolate(self.text(), textFinalValue);
							return function(t) {
								self.text(textRounder(i(t)) + unitText);
							};
						});
				};
                text1.transition()
                    .duration(config.waveRiseTime)
					.on("start", textTween);
                text2.transition()
                    .duration(config.waveRiseTime)
                    .on("start", textTween);
                text2Locked.transition()
                    .duration(config.waveRiseTime)
                    .on("start", textTween);
            }

            // Make the wave rise. wave and waveGroup are separate so that horizontal and vertical movement can be controlled independently.
            let waveGroupXPosition = fillCircleMargin + fillCircleRadius * 2 - waveClipWidth;
            if (config.waveRise) {
                waveGroup.attr('transform', 'translate(' + waveGroupXPosition + ',' + waveRiseScale(0) + ')')
                    .transition()
                    .duration(config.waveRiseTime)
                    .attr('transform', 'translate(' + waveGroupXPosition + ',' + waveRiseScale(fillPercent) + ')')
                    .on("start", () => {
                        wave.attr('transform', 'translate(1,0)');
                    }); // This transform is necessary to get the clip wave positioned correctly when waveRise=true and waveAnimate=false. The wave will not position correctly without this, but it's not clear why this is actually necessary.
	            waveGroupLocked.attr('transform', 'translate(' + waveGroupXPosition + ',' + waveRiseScaleLocked(0) + ')')
		            .transition()
		            .duration(config.waveRiseTime)
		            .attr('transform', 'translate(' + waveGroupXPosition + ',' + waveRiseScaleLocked(fillPercentLocked) + ')')
		            .on("start", () => {
			            waveLocked.attr('transform', 'translate(1,0)');
		            }); // This transform is necessary to get the clip wave positioned correctly when waveRise=true and waveAnimate=false. The wave will not position correctly without this, but it's not clear why this is actually necessary.
            } else {
                waveGroup.attr('transform', 'translate(' + waveGroupXPosition + ',' + waveRiseScale(fillPercent) + ')');
                waveGroupLocked.attr('transform', 'translate(' + waveGroupXPosition + ',' + waveRiseScaleLocked(fillPercentLocked) + ')');
            }

            if (config.waveAnimate) {
            	animateWave();
	            animateWaveLocked();
            }

            function animateWave() {
                wave.attr('transform', 'translate(' + waveAnimateScale(wave.attr('T')) + ',0)');
                wave.transition()
                    .duration(config.waveAnimateTime * (1 - wave.attr('T')))
                    .ease(d3.easeLinear)
                    .attr('transform', 'translate(' + waveAnimateScale(1) + ',0)')
                    .attr('T', 1)
                    .on('end', () => {
                        wave.attr('T', 0);
                        animateWave(config.waveAnimateTime);
                    });
            }

            function animateWaveLocked() {
                waveLocked.attr('transform', 'translate(' + waveAnimateScale(waveLocked.attr('T')) + ',0)');
                waveLocked.transition()
                    .duration(config.waveAnimateTime / 2 * (1 - waveLocked.attr('T')))
	                .delay(config.waveAnimateTime / 2)
                    .ease(d3.easeLinear)
                    .attr('transform', 'translate(' + waveAnimateScale(1) + ',0)')
                    .attr('T', 1)
                    .on('end', () => {
                        waveLocked.attr('T', 0);
	                    animateWaveLocked(config.waveAnimateTime / 2);
                    });
            }

            function GaugeUpdater() {
                this.update = function(value) {

                    let fillPercent = (value - config.minValue) / (config.maxValue - config.minValue);
	                let fillPercentLocked = (that.lockedValue - config.minValue) / (config.maxValue - config.minValue);

                    value = config.showPercent ? (fillPercent * 100) : value;
	                let valueLocked = config.showPercent ? (fillPercentLocked * 100) : that.lockedValue;
	                let newFinalValueLocked = parseFloat(valueLocked).toFixed(2);
	                let textRounderUpdater = function(value) {
		                return that.customFormat(parseFloat(value), that.options.format);
                    };

					let textTween = function repeat() {
						d3.active(this)
							.tween("text", function() {
								let self = d3.select(this);
								let i = d3.interpolate(self.text(), parseFloat(value).toFixed(2));
								return function(t) {
									self.text(textRounderUpdater(i(t)) + unitText);
								};
							});
					};

	                let textTweenLocked = function repeat() {
		                d3.active(this)
			                .tween("text", function() {
				                let self = d3.select(this);
				                let i = d3.interpolate(self.select('tspan:last-child').size() > 0 ? self.select('tspan:last-child').text() : 0, parseFloat(value).toFixed(2));
				                let i2 = d3.interpolate(self.text(), parseFloat(newFinalValueLocked).toFixed(2));
				                let oldValue = that.customFormat(parseFloat(newFinalValueLocked), that.options.format);
				                let newValue = that.customFormat(parseFloat(value), that.options.format);
				                let symbol = parseFloat(oldValue) > parseFloat(newValue) ? " \uf103 " : parseFloat(oldValue) < parseFloat(newValue) ? " \uf102 " : " \uf52c ";
				                return function(t) {
					                self.html("<tspan>" + textRounder(i2(t)) + unitText + "</tspan><tspan style='font-family:\"Font Awesome 5 Pro\";'> " + symbol + " </tspan><tspan> " + textRounder(i(t)) + unitText + "</tspan>");
				                };
			                });
	                };


	                text1.transition()
		                .duration(config.waveRiseTime)
		                .on("start", that.lockedDatas ? textTweenLocked : textTween);
                    text2.transition()
                        .duration(config.waveRiseTime)
						.on("start", that.lockedDatas ? textTweenLocked : textTween);
                    text2Locked.transition()
                        .duration(config.waveRiseTime)
						.on("start", that.lockedDatas ? textTweenLocked : textTween);

                    //var fillPercent = Math.max(config.minValue, Math.min(config.maxValue, value)) / config.maxValue;

                    let waveHeight = fillCircleRadius * waveHeightScale(fillPercent * 100);
                    let waveHeightLocked = fillCircleRadius * waveHeightScale(fillPercentLocked * 100);
                    let waveRiseScale = d3.scaleLinear()
                    // The clipping area size is the height of the fill circle + the wave height, so we position the clip wave
                    // such that the it will overlap the fill circle at all when at 0%, and will totally cover the fill
                    // circle at 100%.
                        .range([(fillCircleMargin + fillCircleRadius * 2 + waveHeight), (fillCircleMargin - waveHeight)])
                        .domain([0, 1]);
                    let waveRiseScaleLocked = d3.scaleLinear()
                        .range([(fillCircleMargin + fillCircleRadius * 2 + waveHeightLocked), (fillCircleMargin - waveHeightLocked)])
                        .domain([0, 1]);
                    let newHeight = waveRiseScale(fillPercent);
                    let newHeightLocked = waveRiseScaleLocked(fillPercentLocked);
                    let waveScaleX = d3.scaleLinear().range([0, waveClipWidth]).domain([0, 1]);
                    let waveScaleY = d3.scaleLinear().range([0, waveHeight]).domain([0, 1]);
                    let waveScaleYLocked = d3.scaleLinear().range([0, waveHeightLocked]).domain([0, 1]);
                    let newClipArea;
                    let newClipAreaLocked;
                    if (config.waveHeightScaling) {
                        newClipArea = d3.area()
                            .x(d => waveScaleX(d.x))
                            .y0(d => waveScaleY(Math.sin(Math.PI * 2 * config.waveOffset * -1 + Math.PI * 2 * (1 - config.waveCount) + d.y * 2 * Math.PI)))
                            .y1(fillCircleRadius * 2 + waveHeight);
                        newClipAreaLocked = d3.area()
                            .x(d => waveScaleX(d.x))
                            .y0(d => waveScaleYLocked(Math.sin(Math.PI * 2 * config.waveOffset * -1 + Math.PI * 2 * (1 - config.waveCount) + d.y * 2 * Math.PI)))
                            .y1(fillCircleRadius * 2 + waveHeightLocked);
                    } else {
                        newClipArea = clipArea;
                        newClipAreaLocked = clipAreaLocked;
                    }

                    let newWavePosition = config.waveAnimate ? waveAnimateScale(1) : 0;
                    wave.transition()
                        .duration(0)
                        .transition()
                        .duration(config.waveAnimate ? (config.waveAnimateTime * (1 - wave.attr('T'))) : (config.waveRiseTime))
                        .ease(d3.easeLinear)
                        .attr('d', newClipArea)
                        .attr('transform', 'translate(' + newWavePosition + ',0)')
                        .attr('T', '1')
                        .on("end", () => {
                            if (config.waveAnimate) {
                                wave.attr('transform', 'translate(' + waveAnimateScale(0) + ',0)');
                                animateWave(config.waveAnimateTime);
                            }
                        });

                    waveGroup.transition()
                        .duration(config.waveRiseTime)
                        .attr('transform', 'translate(' + waveGroupXPosition + ',' + newHeight + ')');


	                if (that.lockedDatas) {
		                text1.attr('data-fs', textPixels).attr("font-size", (textPixels / 2) + "px");
		                text2.attr('data-fs', textPixels).attr("font-size", (textPixels / 2) + "px");
		                text2Locked.attr('data-fs', textPixels).attr("font-size", (textPixels / 2) + "px");
		                fillCircleGroup.attr('data-value', fillPercentLocked).select("circle").style('fill', acUtils.invertColor(config.waveColor));
		                fillCircleGroupLocked.attr('data-value', fillPercent).style('display', 'inherit');
		                fillCircleGroup.select('circle').transition().attr('opacity', 0.8);
		                fillCircleGroupLocked.select('circle').transition().attr('opacity', 0.8);
		                waveLocked.transition()
			                .duration(0)
			                .transition()
			                .duration(config.waveAnimate ? (config.waveAnimateTime / 2 * (1 - waveLocked.attr('T'))) : (config.waveRiseTime))
			                .ease(d3.easeLinear)
			                .attr('d', newClipAreaLocked)
			                .attr('transform', 'translate(' + newWavePosition + ',0)')
			                .attr('T', '1')
			                .on("end", () => {
				                if (config.waveAnimate) {
					                wave.attr('transform', 'translate(' + waveAnimateScale(0) + ',0)');
					                animateWaveLocked(config.waveAnimateTime / 2);
				                }
			                });

		                waveGroupLocked.transition()
			                .duration(config.waveRiseTime)
			                .attr('transform', 'translate(' + waveGroupXPosition + ',' + newHeightLocked + ')')
			                .on("end", () => {
				                let orderedNodes = gaugeGroup.selectAll('g').nodes().sort((a, b) => {
					                let aValue = parseFloat(a.attributes["data-value"].value);
					                let bValue = parseFloat(b.attributes["data-value"].value);
					                if (aValue > bValue) return 1;
					                if (aValue < bValue) return -1;
					                return 1;
				                });

				                orderedNodes.forEach(n => {
					                gaugeGroup.nodes()[0].append(n);
				                });

				                fillCircleGroup.select('circle').transition().attr('opacity', 1);
				                fillCircleGroupLocked.select('circle').transition().attr('opacity', 1);
			                });


	                }
                };

	            this.removeLock = function() {
	            	that.resize(that.$parent);
		            // fillCircleGroupLocked.style('display', 'none');
		            // fillCircleGroup.select("circle").style('fill', config.waveColor);
		            // setTimeout(function() {
			        //     text1.transition().attr("font-size", text1.attr('data-fs') + "px");
			        //     text2.transition().attr("font-size", text1.attr('data-fs') + "px");
		            // }, 1000);
	            };
            }

            return new GaugeUpdater();
        }

        function circleGaugeDefaultSettings() {
            return {
                minValue: 0, // The gauge minimum value.
                maxValue: 100, // The gauge maximum value.
                circleThickness: 0.02, // The outer circle thickness as a percentage of it's radius.
                circleFillGap: 0.05, // The size of the gap between the outer circle and wave circle as a percentage of the outer circles radius.
                circleColor: "#000", // The color of the outer circle.
                waveHeight: 0.05, // The wave height as a percentage of the radius of the wave circle.
                waveCount: 1, // The number of full waves per width of the wave circle.
                waveRiseTime: 1000, // The amount of time in milliseconds for the wave to rise from 0 to it's final height.
                waveAnimateTime: 18000, // The amount of time in milliseconds for a full wave to enter the wave circle.
                waveRise: true, // Control if the wave should rise from 0 to it's full height, or start at it's full height.
                waveHeightScaling: true, // Controls wave size scaling at low and high fill percentages. When true, wave height reaches it's maximum at 50% fill, and minimum at 0% and 100% fill. This helps to prevent the wave from making the wave circle from appear totally full or empty when near it's minimum or maximum fill.
                waveAnimate: true, // Controls if the wave scrolls or is static.
                waveColor: "#000", // The color of the fill wave.
                waveOffset: 0, // The amount to initially offset the wave. 0 = no offset. 1 = offset of one full wave.
                textVertPosition: .5, // The height at which to display the percentage text withing the wave circle. 0 = bottom, 1 = top.
                textSize: 1, // The relative height of the text to display in the wave circle. 1 = 50%
                valueCountUp: true, // If true, the displayed value counts up from 0 to it's final value upon loading. If false, the final value is displayed.
                unit: "", // The unit that appears after the value.
                label: null, // The label that appears under the value.
                showPercent: false,
                textColor: "#000", // The color of the value text when the wave does not overlap it.
                waveTextColor: "#fff" // The color of the value text when the wave overlaps it.
            };
        }

        function loadCircleGauge(elementId, value, config) {
            if (config === null) {
            	config = circleGaugeDefaultSettings();
            }

            $.each(config, (index, val) => {
                if (val === "true") {
                    config[index] = true;
                } else if (val === "false") {
                    config[index] = false;
                }
            });

	        config.textVertPosition = 0.3;

            let gauge = d3.select("#" + elementId);
	        let circleHeight = parseInt(gauge.style("height"));
	        if (config.label) {
	        	circleHeight = circleHeight * 0.8;
	        }
			circleHeight -= that.margins.top;
	        that.radius = Math.min(parseInt(gauge.style("width")), circleHeight) / 2;
            that.locationX = parseInt(gauge.style("width")) / 2 - that.radius;
            that.locationY = parseInt(circleHeight) / 2 - that.radius + that.margins.top;
            //var fillPercent = Math.max(config.minValue, Math.min(config.maxValue, value)) / config.maxValue;
            let fillPercent = (value - config.minValue) / (config.maxValue - config.minValue);
            let fillPercentLocked = (that.lockedValue - config.minValue) / (config.maxValue - config.minValue);

            let textPixels = (config.textSize * that.radius / 2);
            let textValue = config.showPercent ? (fillPercent * 100) : value;
            let textFinalValue = parseFloat(textValue).toFixed(2);
            let textStartValue;
            if (config.showPercent && config.valueCountUp) {
                textStartValue = 0;
            } else {
                textStartValue = config.valueCountUp ? config.minValue : textFinalValue;
            }

            let unitText = config.showPercent ? '%' : config.unit;
            if (unitText === null) {
            	unitText = "";
            }
            let circleThickness = config.circleThickness * that.radius;
            let circleFillGap = config.circleFillGap * that.radius;
            let fillCircleMargin = circleThickness + circleFillGap;
            let fillCircleRadius = that.radius - fillCircleMargin;

            // Rounding functions so that the correct number of decimal places is always displayed as the value counts up.
            let textRounder = function(value) {
            	return that.customFormat(parseFloat(value), that.options.format);
            };

            // Scales for drawing the outer circle.
            let gaugeCircleX = d3.scaleLinear().range([0, 2 * Math.PI]).domain([0, 1]);
            let gaugeCircleY = d3.scaleLinear().range([0, that.radius]).domain([0, that.radius]);

            // Scale for controlling the position of the text within the gauge.
            let textRiseScaleY = d3.scaleLinear()
                .range([fillCircleMargin + fillCircleRadius * 2, (fillCircleMargin + textPixels * 0.7)])
                .domain([0, 1]);

            // Center the gauge within the parent SVG.
            let gaugeGroup = gauge.append("g")
                .attr('transform', 'translate(' + that.locationX + ',' + that.locationY + ')');

            // Draw the outer circle.
            let gaugeOuterArc = d3.arc()
                .startAngle(gaugeCircleX(0))
                .endAngle(gaugeCircleX(1))
                .outerRadius(gaugeCircleY(that.radius))
                .innerRadius(gaugeCircleY(that.radius - circleThickness));
            gaugeGroup.append("path")
                .attr("d", gaugeOuterArc)
                .style("fill", config.circleColor)
                .attr('transform', 'translate(' + that.radius + ',' + that.radius + ')');

	        // Draw the outer circle.
	        let gaugeCircleArc = d3.arc()
		        .startAngle(0)
		        .outerRadius(gaugeCircleY(that.radius - (circleThickness * 1.5)))
		        .innerRadius(gaugeCircleY(that.radius - (circleThickness * 5) - (circleThickness * 1.5)));

	        // Draw the outer circle.
	        let gaugeCircleArcLock = d3.arc()
		        .startAngle(0)
		        .outerRadius(gaugeCircleY(that.radius - (circleThickness * 5) - (circleThickness * 1.5) - (circleThickness * 1.5)))
		        .innerRadius(gaugeCircleY(that.radius - (circleThickness * 5) - (circleThickness * 1.5) - (circleThickness * 5) - (circleThickness * 1.5)));

	        let tau = 2 * Math.PI;

	        gaugeGroup.append("path")
		        .attr('class', 'inner-arc')
		        .datum({endAngle: 0})
		        .attr("d", gaugeCircleArc)
		        .style("fill", config.circleColor)
		        .attr('transform', 'translate(' + that.radius + ',' + that.radius + ')');

	        if (that.lockedDatas) {
		        gaugeGroup.append("path")
			        .attr('class', 'locked-arc')
			        .datum({endAngle: 0})
			        .attr("d", gaugeCircleArcLock)
			        .style("fill", acUtils.invertColor(config.circleColor))
			        .attr('transform', 'translate(' + that.radius + ',' + that.radius + ')');
	        }

	        function arcTween(newAngle) {
		        return function(d) {
			        let interpolate = d3.interpolate(d.endAngle, newAngle);
			        return function(t) {
				        d.endAngle = interpolate(t);
				        return gaugeCircleArc(d);
			        };
		        };
	        }

	        function arcTweenLock(newAngle) {
		        return function(d) {
			        let interpolate = d3.interpolate(d.endAngle, newAngle);
			        return function(t) {
				        d.endAngle = interpolate(t);
				        return gaugeCircleArcLock(d);
			        };
		        };
	        }

	        gaugeGroup.select('path.inner-arc')
		        .transition()
		        .duration(750)
		        .attrTween("d", arcTween(fillPercent * tau));

	        gaugeGroup.select('path.locked-arc')
		        .transition()
		        .duration(750)
		        .attrTween("d", arcTweenLock(fillPercentLocked * tau));

            // Text where the wave does not overlap.
            let text1 = gaugeGroup.append("text")
                .text(textRounder(textStartValue) + unitText)
                .attr("class", "liquidFillGaugeText")
                .attr("text-anchor", "middle")
                .attr("font-size", textPixels + "px")
                .style("fill", config.circleColor)
	            .style("font-weight", 400)
	            .attr('transform', 'translate(' + that.radius + ',' + textRiseScaleY(config.textVertPosition) + ')');

	        if (config.label) {
		        // Text where the wave does not overlap.
		        that.svg.append("text")
			        .text(config.label)
			        .attr("class", "liquidFillGaugeLabel")
			        .attr("text-anchor", "middle")
			        .attr("font-size", (parseInt(gauge.style("height")) / 10) + "px")
			        .style("fill", config.circleColor)
			        // .attr('dx', (that.width / 2))
			        // .attr('y',  (parseInt(gauge.style("height")) * 0.90))
			        .call(d => {
			        	// d.attr('y', d.node().getBoundingClientRect().height);
			        	d.attr('transform', 'translate(' + (that.width / 2) + ',' + (that.locationY + (that.radius * 2) + (d.node().getBoundingClientRect().height / 2)) + ')');
			        })
			        .call(wrap, that.width * 0.9);
	        }

	        that.showIcon(config);

            // Make the value count up.
            if (config.valueCountUp) {
                let textTween = function repeat() {
					d3.active(this)
						.tween("text", function() {
							let self = d3.select(this);
							let i = d3.interpolate(self.text(), textFinalValue);
							return function(t) {
								self.text(textRounder(i(t)) + unitText);
							};
						});
				};
                text1.transition()
                    .duration(config.waveRiseTime)
					.on("start", textTween);
            }

	        function wrap(text, width) {
		        text.each(function() {
			        let fontSize = parseFloat(d3.select(this).style('font-size'));
			        let text = d3.select(this);
				        let words = text.text().split(/\s+/).reverse();
				        let word;
				        let line = [];
				        let lineHeight = 1.1; // ems
				        let y = text.attr("y");
				        let tspan = text.text(null).append("tspan").attr("x", 0).attr("y", y).attr("dy", 0 + "em");
			        while (word = words.pop()) {
				        line.push(word);
				        tspan.text(line.join(" "));
				        if (tspan.node().getComputedTextLength() > width) {
					        line.pop();
					        tspan.text(line.join(" "));
					        line = [word];
					        tspan = text.append("tspan").attr("x", 0).attr("y", y).attr("dy", lineHeight +  "em").text(word);
				        }
			        }
			        text.style('font-size', (fontSize / (text.selectAll('tspan').size())) + "px");
			        text.attr('transform', 'translate(' + (that.width / 2) + ',' + (that.locationY + (that.radius * 2) + (tspan.node().getBoundingClientRect().height)) + ')');
		        });
	        }

            function GaugeUpdater() {
                this.update = function(value) {

                    let fillPercent = (parseFloat(that.customFormat(parseFloat(value), that.options.format).replace(/,/, '.')) - config.minValue) / (config.maxValue - config.minValue);
                    let fillPercentLocked = (parseFloat(that.customFormat(parseFloat(that.lockedValue), that.options.format).replace(/,/, '.')) - config.minValue) / (config.maxValue - config.minValue);

                    value = config.showPercent ? (fillPercent * 100) : value;
                    let valueLocked = config.showPercent ? (fillPercentLocked * 100) : that.lockedValue;
                    let newFinalValueLocked = parseFloat(valueLocked).toFixed(2);

					let textTween = function repeat() {
						d3.active(this)
							.tween("text", function() {
								let self = d3.select(this);
								let i = d3.interpolate(self.text(), parseFloat(value).toFixed(2));
								return function(t) {
									self.text(textRounder(i(t)) + unitText);
								};
							});
					};

					let textTweenLocked = function repeat() {
						d3.active(this)
							.tween("text", function() {
								let self = d3.select(this);
								let i = d3.interpolate(self.select('tspan:last-child').size() > 0 ? self.select('tspan:last-child').text() : 0, parseFloat(value).toFixed(2));
								let i2 = d3.interpolate(self.text(), parseFloat(newFinalValueLocked).toFixed(2));
								let oldValue = that.customFormat(parseFloat(newFinalValueLocked), that.options.format);
								let newValue = that.customFormat(parseFloat(value), that.options.format);
								let symbol = parseFloat(oldValue) > parseFloat(newValue) ? " \uf103 " : parseFloat(oldValue) < parseFloat(newValue) ? " \uf102 " : " \uf52c ";
								return function(t) {
									self.html("<tspan>" + textRounder(i2(t)) + unitText + "</tspan><tspan style='font-family:\"Font Awesome 5 Pro\";fill:#333333'> " + symbol + " </tspan><tspan style='fill:" + acUtils.invertColor(config.circleColor) + "'> " + textRounder(i(t)) + unitText + "</tspan>");
								};
							});
					};

	                text1.transition()
                        .duration(config.waveRiseTime)
						.on("start", that.lockedDatas ? textTweenLocked : textTween);

	                gaugeGroup.select('path.inner-arc')
		                .transition()
		                .duration(config.waveRiseTime)
		                .attrTween("d", arcTween((that.lockedDatas ? fillPercentLocked : fillPercent) * tau));

	                if (that.lockedDatas) {

	                	text1.attr('data-fs', textPixels).attr("font-size", (textPixels / 2) + "px");

	                	if (gaugeGroup.select('path.locked-arc').size() === 0) {
			                gaugeGroup.append("path")
				                .attr('class', 'locked-arc')
				                .datum({endAngle: 0})
				                .attr("d", gaugeCircleArcLock)
				                .style("fill", acUtils.invertColor(config.circleColor))
				                .attr('transform', 'translate(' + that.radius + ',' + that.radius + ')');
		                }

		                gaugeGroup.select('path.locked-arc')
			                .transition()
			                .duration(config.waveRiseTime)
			                .attrTween("d", arcTweenLock(fillPercent * tau));

		                that.showIcon(config);
	                }
                };

                this.removeLock = function() {
	                gaugeGroup.select('path.locked-arc')
		                .transition()
		                .duration(config.waveRiseTime)
		                .attrTween("d", arcTweenLock(0))
		                .remove();
	                setTimeout(() => {
		                text1.transition().attr("font-size", text1.attr('data-fs') + "px").text(that.customFormat(that.value, that.options.format));
	                }, 1000);
                };

                this.updateIcon = function() {
	                that.showIcon(config);
                };
            }

            return new GaugeUpdater();
        }
    }

	showIcon(config) {
		let that = this;
		that.loadIcon(svg_icons_host + "/svgs/" + that.options.icon_type + '/' + that.options.icon + '.svg').then(xml => {
			let firstLoad = !that.iconContainer;
			if (firstLoad || that.isPreview()) {
				that.iconContainer = that.svg.append("g");
				that.iconContainer.node().appendChild(document.importNode(xml.documentElement, true).cloneNode(true));
			}

			let iconW = (that.radius / 3) * 2;
			let maxH = that.radius * (that.lockedDatas ? 0.65 : 0.8);
			let ratio = (xml.documentElement.viewBox.baseVal.height / xml.documentElement.viewBox.baseVal.width);
			let iconH = iconW * ratio;
			if (iconH > maxH) {
				iconW = maxH * (xml.documentElement.viewBox.baseVal.width / xml.documentElement.viewBox.baseVal.height);
				iconH = maxH;
			}
			let h = -(that.svg.node().height.baseVal.value / 2) + that.locationY + that.radius - (iconH / 2);
			that.iconContainer.select('svg')
				.transition()
				.duration(firstLoad ? 0 : config.waveRiseTime)
				.attr('width', iconW)
				.attr('fill', config.circleColor)
			;
			that.iconContainer.transition().duration(firstLoad ? 0 : config.waveRiseTime).attr('transform', "translate(" + (that.width / 2 - (iconW / 2)) + "," + h + ")");
		});
	}

	loadIcon(index) {
		let that = this;
		if (this.iconsXml.hasOwnProperty(index)) {
			return Promise.resolve(that.iconsXml[index].cloneNode(true));
		} else {
			return new Promise(resolve => {
				let icon = that.options.hasOwnProperty('icons') ? that.options.icons[index] : index;
				d3.xml(icon).then(xml => {
					that.iconsXml[index] = xml;
					resolve(xml.cloneNode(true));
				});
			});
		}
	}

	onUnlock() {
		this.gauge.removeLock();
		if (this.isCircleTemplate()) {
			this.gauge.updateIcon();
		}
	}

	onCreateOptions() {
        let that = this;

		if ($('.noUi-base').length === 0) {
			$('.noUi-control').noUiSlider({
				start: $.extend(that.defaultOptions(), that.options).font_size,
				connect: "lower",
				step: 0.1,
				range: {
					'min': 0,
					'max': 2
				}
			});
			$('.noUi-control').Link('lower').to($('#chart_params_options_font_size'), changeValue);
		}

		function changeValue(value) {
			$(this).val(value);
			$(this).change();
		}
    }

	onInitOption(params) {
    	let that = this;
		$('.noUi-control').val($.extend(that.defaultOptions(), params.options).font_size);
	}

	async resize(container, width, height) {
        let that = this;
        let $container = $(that.$parent.selector);
        if (typeof width === "undefined") {
            width = $container.width();
        }
        if (typeof height === "undefined") {
            height = $container.height();
        }
        that.setParams(that.params);
        $container.find('.gaugeLiquidChart').remove();
		await that.setSize(width, height);
        that.iconContainer = false;
        that.reset();
        that.render(that.$parent.selector);
		that.postResize(container, width, height);
    }

	canDisplayGaugeMax() {
		return this.params.esParameters.aggregations.length === 1 || _.defaultTo(this.params.esParameters.options.refAsMax, false) === false;
	}

	canDisplayRefAsCalcul() {
		return this.params.esParameters.aggregations.length === 2;
	}

	canDisplayRefAsMax() {
		return this.canDisplayRefAsCalcul();
	}

	isCircleTemplate() {
		return this.options.template === "circle";
	}

	parameters() {
		return [
		    {
                label: "Calcul",
                id: "calcul",
                desc: "Donnée de type texte ou numérique",
                type: "aggregation",
                size: "single",
                accept: ["number", "string", "boolean"]
            },
			{
				label: "Valeur de référence",
				id: "reference",
				desc: "Valeur utilisée pour calculer le seuil de la jauge",
				type: "aggregation",
				size: "single",
				optional: true,
				accept: ["number", "string", "boolean"]
			}
        ];
	}

	options() {
        let defaultOptions = this.defaultOptions();
        return [
	        {
		        label: "Thème",
		        type: "group"
	        },
	        {
		        label: "Template",
		        name: "template",
		        type: "select",
		        default: defaultOptions.template,
		        choices: {
			        "wave": "Vague",
			        "circle": "Cercle",
		        }
	        },
	        {
		        label: null,
		        name: "colors_custom",
		        type: "svelte",
		        component: "form/ColorpickerSwitch",
		        props: {
			        "name": "colors_custom",
			        "isCustom": false
		        },
	        },
	        {
		        label: "Couleur de remplissage",
		        name: "color_fill",
		        type: "color",
		        default: defaultOptions.color_fill,
		        resetOnSwitch: true,
	        },
	        {
		        label: "Taille de la police",
		        prelabel: "Taille de la police",
		        name: "font_size",
		        type: "slider",
		        resetOnSwitch: true,
		        default: defaultOptions.font_size
	        },
	        {
		        label: "Gestion îcone",
		        type: "group",
		        displaycond: ['isCircleTemplate', true]
	        },
	        {
		        label: "Icône",
		        prelabel: "Icône",
		        name: "icon",
		        default: "envelope",
		        type: "text",
		        help: "<a target='_blank' href='https://fontawesome.com/icons' class=\"btn btn-visu btn-icon btn-fa d-inline-flex btn-tertiary mt-2\">"+Translator.trans('alienor_charts.gaugeLiquidChart.global.icon_list')+"<i class=\"fab fa-font-awesome\" aria-hidden=\"true\"></i></a>",
		        displaycond: ['isCircleTemplate', true]
	        },
	        {
		        label: "Type d'icône",
		        prelabel: "Type d'icône",
		        name: "icon_type",
		        type: "select",
		        default: defaultOptions.icon_type,
		        displaycond: ['isCircleTemplate', true],
		        choices: {
			        "solid": "solid",
			        "regular": "regular",
			        "light": "light",
			        "thin": "thin",
			        "duotone": "duotone",
			        "brand": "brand"
		        }
	        },
	        {
		        label: "Personnalisation texte",
		        type: "group"
	        },
	        {
		        label: "Label",
		        prelabel: "label",
		        name: "label",
		        type: "text"
	        },
	        {
		        label: "Unité",
		        prelabel: "Unité",
		        name: "unit",
		        type: "text"
	        },
	        {
		        label: "Formatage",
		        prelabel: "Formatage",
		        name: "format",
		        type: "select",
		        default: "0[,]",
		        choices: {
			        "": "Aucun",
			        "0[,]": "Nombre entier",
			        "0[,].0": "1 chiffre après la virgule",
			        "0[,].00": "2 chiffres après la virgule",
			        "0[,].000": "3 chiffres après la virgule"
		        },
		        need_reload: true
	        },
	        {
		        label: "Afficher la valeur en pourcentage",
		        name: "showPercent",
		        type: "checkbox",
		        default: defaultOptions.showPercent
	        },
	        {
		        label: "Paramètre calcul",
		        type: "group"
	        },
	        {
		        label: "Calcul",
		        prelabel: "Calcul",
		        name: "calculation",
		        type: "calculation"
	        },
            {
                label: "Valeur Minimum",
                prelabel: "Valeur Minimum",
                name: "gauge_min",
                type: "number",
                default: defaultOptions.gauge_min
            },
            {
                label: "Valeur Maximum",
                prelabel: "Valeur Maximum",
                name: "gauge_max",
                default: defaultOptions.gauge_max,
	            displaycond: ['canDisplayGaugeMax', true],
                type: "number"
            },
            {
                label: "Utiliser la valeur de référence comme valeur max",
                name: "refAsMax",
                type: "checkbox",
                default: defaultOptions.refAsMax,
	            displaycond: ['canDisplayRefAsMax', true],
            },
            {
                label: "Utiliser la valeur de référence comme calcul",
                name: "refAsCalcul",
                type: "checkbox",
                default: defaultOptions.refAsCalcul,
	            displaycond: ['canDisplayRefAsCalcul', true],
            },
        ];
    }

	defaultOptions() {
        return {
            gauge_min: 0,
            gauge_max: 1,
            unit: "",
            label: null,
            color_fill: { scheme: 0, default: "#3598dc" },
            font_size: 0.9,
            showPercent: false,
	        refAsCalcul: false,
	        refAsMax: true,
	        template: "wave",
	        icon: "",
	        icon_type: "solid",
	        animate: true,
	        format: "0[,]"
        };
    }
}