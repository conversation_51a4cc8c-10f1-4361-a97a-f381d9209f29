import L from "leaflet";
import "leaflet-draw";

export default class DrawLayer {
    constructor(map) {
        this.name = "DrawLayer";
        this.map = map;

        this.layer = new L.FeatureGroup();

        this.map.pointSelectArea = [];
        this.map.radius = null;

        this.drawOption =  {
            polyline: false,
            marker: false,
            circlemarker: false,
            circle: {
                shapeOptions: {
                    color: 'purple'
                }
            },
            polygon: {
                allowIntersection: false, // Restricts shapes to simple polygons
                shapeOptions: {
                    color: 'purple'
                }
            },
            rectangle: {
                shapeOptions: {
                    color: 'purple'
                }
            }
        };

        // Set the button title text for the polygon button
        L.drawLocal = {
            draw: {
                toolbar: {
                    actions: {
                        title: Translator.trans('alienor_charts.mapChart.global.annulerLeDessin'),
                        text: Translator.trans('alienor_charts.mapChart.global.annuler')
                    },
                    finish: {
                        title: Translator.trans('alienor_charts.mapChart.global.terminerLeDessin'),
                        text: Translator.trans('alienor_charts.mapChart.global.terminer')
                    },
                    undo: {
                        title: Translator.trans('alienor_charts.mapChart.global.supprimerLeDernierPoint'),
                        text: Translator.trans('alienor_charts.mapChart.global.supprimerDernierPoint')
                    },
                    buttons: {
                        polygon: Translator.trans('alienor_charts.mapChart.global.draw_polygon'),
                        rectangle: Translator.trans('alienor_charts.mapChart.global.draw_rectangle'),
                        circle: Translator.trans('alienor_charts.mapChart.global.draw_circle'),
                    }
                },
                handlers: {
                    circle: {
                        tooltip: {
                            start: Translator.trans('alienor_charts.mapChart.global.draw_glisse_circle')
                        },
                        radius: Translator.trans('alienor_charts.mapChart.global.rayon'),
                    },
                    circlemarker: {
                        tooltip: {
                            start: Translator.trans('alienor_charts.mapChart.global.clickOnMapPutCircle'),
                        }
                    },
                    marker: {
                        tooltip: {
                            start: Translator.trans('alienor_charts.mapChart.global.clickOnMapPutPoint'),
                        }
                    },
                    polygon: {
                        tooltip: {
                            start: Translator.trans('alienor_charts.mapChart.global.clickOnMapDrawShape'),
                            cont: Translator.trans('alienor_charts.mapChart.global.clickPousuivreForme'),
                            end: Translator.trans('alienor_charts.mapChart.global.clickFirstPointEndShape'),
                        }
                    },
                    polyline: {
                        error: Translator.trans('alienor_charts.mapChart.global.erreurCroissement'),
                        tooltip: {
                            start: Translator.trans('alienor_charts.mapChart.global.clickStartDrawLine'),
                            cont: Translator.trans('alienor_charts.mapChart.global.clickContinueDrawLine'),
                            end: Translator.trans('alienor_charts.mapChart.global.clickLastPointFinishLine'),
                        }
                    },
                    rectangle: {
                        tooltip: {
                            start: Translator.trans('alienor_charts.mapChart.global.clickGlisseDrawRectangle'),
                        }
                    },
                    simpleshape: {
                        tooltip: {
                            end: Translator.trans('alienor_charts.mapChart.global.clickEndShape'),
                        }
                    }
                }
            },
            edit: {
                toolbar: {
                    actions: {
                        save: {
                            title: Translator.trans('alienor_charts.mapChart.global.saveModifications'),
                            text: Translator.trans('alienor_charts.mapChart.global.save'),
                        },
                        cancel: {
                            title: Translator.trans('alienor_charts.mapChart.global.cancelEdition'),
                            text: Translator.trans('alienor_charts.mapChart.global.cancel'),
                        },
                        clearAll: {
                            title: Translator.trans('alienor_charts.mapChart.global.eraseElements'),
                            text: Translator.trans('alienor_charts.mapChart.global.eraseAll'),
                        }
                    },
                    buttons: {
                        edit: Translator.trans('alienor_charts.mapChart.global.editShape'),
                        editDisabled: Translator.trans('alienor_charts.mapChart.global.noShapeToEdit'),
                        remove: Translator.trans('alienor_charts.mapChart.global.removeShape'),
                        removeDisabled: Translator.trans('alienor_charts.mapChart.global.noShapeToEdit2'),
                    }
                },
                handlers: {
                    edit: {
                        tooltip: {
                            text: Translator.trans('alienor_charts.mapChart.global.movePointEditShape'),
                            subtext: Translator.trans('alienor_charts.mapChart.global.clickEditChange'),
                        }
                    },
                    remove: {
                        tooltip: {
                            text: Translator.trans('alienor_charts.mapChart.global.clickElementToRemoveIt'),
                        }
                    }
                }
            }
        };

        let that = this;
        L.Control.RemoveAll = L.Control.Draw.extend({
            edit: {
                featureGroup: that.layer
            },
            draw: that.drawOption,
            options: {
                position: 'topleft',
            },
            onAdd: function() {
                let controlDiv = L.DomUtil.create('div', 'leaflet-draw-toolbar leaflet-bar');
                let controlUI = L.DomUtil.create('a', 'leaflet-draw-edit-remove', controlDiv);
                controlUI.title = 'Supprimer tous les filtres.';
                controlUI.setAttribute('href', '#');

                L.DomEvent
                    .addListener(controlUI, 'click', e => {
                        e.preventDefault();
                        that.layer.clearLayers();
                        that.map.pointSelectArea = [];
                        that.map.radius = null;
                        that.toggleDrawControl(true);
                        that.map.launchEventFilter();
                        return false;
                    });
                return controlDiv;
            }
        });

        this.removeAllControl = new L.Control.RemoveAll();

        // AJOUT DES OPTIONS DE FILTRAGE
        that.drawControlFull = new L.Control.Draw({
            edit: {
                featureGroup: that.layer,
                remove: false
            },
            draw: that.drawOption
        });

        that.drawControlFullWithoutEdit = new L.Control.Draw({
            draw: that.drawOption
        });

        // EVENEMENT DE SELECTION
        this.map.on("draw:created", e => {
            let layer = e.layer;
            layer.addTo(that.layer);
            if (typeof layer._mRadius !== 'undefined') {
                that.map.radius = layer._mRadius;
                that.map.pointSelectArea = [layer._latlng];
            } else {
                that.map.radius = null;
                that.map.pointSelectArea = that.reverseCounterClockPolygon(layer._latlngs[0]);
            }
            that.toggleDrawControl(false);

            that.map.launchEventFilter();
        });

        this.map.on("draw:edited", e => {
            let layers = e.layers;
            layers.eachLayer(layer => {
                if (typeof layer._mRadius !== 'undefined') {
                    that.map.radius = layer._mRadius;
                    that.map.pointSelectArea = [layer._latlng];
                } else {
                    that.map.radius = null;
                    that.map.pointSelectArea = that.reverseCounterClockPolygon(layer._latlngs[0]);
                }
            });
            that.map.launchEventFilter();
        });

        this.map.on("draw:deleted", e => {
            e.preventDefault();
            e.stopPropagation();
            that.toggleDrawControl(true);
            that.map.pointSelectArea = [];
            that.map.radius = null;
            that.map.launchEventFilter();
            return false;
        });
    }

	addToMap() {
        this.map.addLayer(this.layer);
    }

	clear() {
        this.layer.clearLayers();
    }

	toggleDrawControl(createMode) {
        let that = this;
        if (createMode) {
            that.drawControlFull.setDrawingOptions(that.drawOption);
            that.drawControlFullWithoutEdit.setDrawingOptions(that.drawOption);
            that.map.removeControl(that.drawControlFull);
            that.map.removeControl(that.drawControlFullWithoutEdit);
	        if (that.map.pointSelectArea.length) {
                that.map.addControl(that.drawControlFull);
	        } else {
		        that.map.addControl(that.drawControlFullWithoutEdit);
	        }
        } else {
            that.drawControlFull.setDrawingOptions({
                circle: false,
                rectangle: false,
                polygon: false
            });
            that.drawControlFullWithoutEdit.setDrawingOptions({
                circle: false,
                rectangle: false,
                polygon: false
            });
            that.map.removeControl(that.drawControlFull);
            that.map.removeControl(that.drawControlFullWithoutEdit);
	        if (that.map.pointSelectArea.length) {
		        that.map.addControl(that.drawControlFull);
	        } else {
		        that.map.addControl(that.drawControlFullWithoutEdit);
	        }
        }
	    if (that.map.pointSelectArea.length || that.map.circle) {
		    that.map.addControl(that.removeAllControl);
        } else {
		    that.map.removeControl(that.removeAllControl);
        }
		return {};
    }

	forceHideControl() {
        this.map.removeControl(this.drawControlFull);
        this.map.removeControl(this.drawControlFullWithoutEdit);
        this.map.removeControl(this.removeAllControl);
    }

	restoreFilter(data) {
        let that = this;
        that.map.pointSelectArea = [];
        that.map.radius = null;

        // AJOUT DES OPTIONS DE FILTRAGE
        if (typeof data.radius === 'undefined') {
            for (let int = 0; int < data.points.length; int++) {
                that.map.pointSelectArea.push([parseFloat(data.points[int].lat),parseFloat(data.points[int].lon)]);
            }
            L.polygon(that.map.pointSelectArea).addTo(that.layer);
        } else {
            that.map.radius = data.radius;
            that.map.pointSelectArea = [[parseFloat(data.points[0].lat),parseFloat(data.points[0].lon)]];
            L.circle([
                parseFloat(data.points[0].lat),
                parseFloat(data.points[0].lon)],
                that.map.radius
                ).addTo(that.layer);
        }
		that.toggleDrawControl(false);
    }

    reverseCounterClockPolygon(polygon) {
        let NumPoints = polygon.length;
        if (polygon[NumPoints - 1].lng == polygon[0].lng && polygon[NumPoints - 1].lat == polygon[0].lat) {
            NumPoints--;
        } else { //Add the first point at the end of the array.
            polygon[NumPoints] = polygon[0];
        }

        let area = 0;
        for (let i = 1; i < NumPoints; i++) {
            area += polygon[i].lat * (polygon[i + 1].lng - polygon[i - 1].lng);
        }
        area /= 2;
        polygon.pop();
        return area > 0 ? polygon.reverse() : polygon;
    }
}