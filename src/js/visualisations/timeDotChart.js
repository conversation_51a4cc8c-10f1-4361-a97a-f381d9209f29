import VisualisationBase from "./VisualisationBase";
import * as d3 from "d3";
import moment from "moment";
import acUtils from "../utils";

export default class timeDotChart extends VisualisationBase {
	constructor() {
		super();
		this.name = "timeDotChart";
		this.defaultMargins = {top: 10, left: 10, right: 10, bottom: 10};
		this.margins = JSON.parse(JSON.stringify(this.defaultMargins));
		this.ticksW = 40;
		this.ticksH = 45;
		this.padding = 10;
		this.data = [];
		this.values = {};
		this.current = 0;
		this.x = null;
		this.y = null;
		this.bodyG = false;
		this.hasLegend = false;
		this.hourColor = "#0066ff";
		this.transitionDuration = 300;
		this.monthNames = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', '<PERSON><PERSON><PERSON>', 'Aout', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];
		this.dayNames = ['D', 'L', 'M', 'M', 'J', 'V', 'S'];
		this.canGenerateFilters = false;
	}

	setParams(params) {
        let that = this;
        this.params = params;
        this.options = that.validateOptions($.extend(this.defaultOptions(), this.params.options));
		this.options.startHour = parseInt(this.options.startHour);
		this.options.endHour = parseInt(this.options.endHour);
    }

	update(datas) {
        this.data = datas;
        this.render(this.$parent.selector);
    }

	preRender(element) {
        super.preRender(element);
        let that = this;

	    if (that.emptyData) {
		    return;
	    }

		this.qw = this.quadrantWidth() - this.ticksW;
		this.qh = this.quadrantHeight() - this.ticksH;

        that.values = that.data.map(d => ({
            value: d.y,
	        time: d.x,
	        date: d3.utcDay(d.x),
	        hour: parseInt(moment(d.x).utc().format("HH"))
        })).filter(d => d.hour >= that.options.startHour && d.hour <= that.options.endHour);

        this.minDate = d3.min(that.values, d => d.date);

        this.maxDate = d3.max(that.values, d => d.date);

        this.minValue = this.options.valueMin ? this.options.valueMin : d3.min(that.values, d => d.value);

        this.maxValue = this.options.valueMax ? this.options.valueMax : d3.max(that.values, d => d.value);

        this.minHour = that.options.startHour;
        this.maxHour = that.options.endHour;

        this.nbDays = d3.utcDay.count(this.minDate, this.maxDate);
        this.nbHours = this.maxHour - this.minHour + 1;
        this.maxR = Math.min(that.qh / that.nbHours, that.qw / that.nbDays) / 2;

        this.x = d3.scaleLinear()
	        .domain([this.minDate, this.maxDate])
	        .range([0, that.qw]);

        this.y = d3.scaleLinear()
	        .domain([this.minHour, this.maxHour])
	        .range([0, that.qh]);

        this.r = d3.scaleLinear()
	        .clamp(true)
	        .domain([this.minValue, this.maxValue])
	        .range([0, that.maxR]);

    }

	render(element) {
        let that = this;
        that.preRender(element);

	    if (that.emptyData) {
		    return;
	    }

        if (!that.svg) {
	        that.svg = d3.select(element)
		        .append("svg")
		        .attr("height", that.height)
		        .attr("width", that.width)
		        .attr("class", "timeDotChart visualisation-root");

	        if (!that.bodyG) that.bodyG = that.svg.append("g").attr("transform", "translate(" + that.margins.left + "," + that.margins.top + ")").append("g").attr("class", "body");
        }

	    that.renderLabels();
	    that.renderBody();
    }

	renderBody() {
        let that = this;

        if (!that.circleG) that.circleG = that.bodyG.append("g").attr("class", "circles").attr("transform", "translate(" + that.ticksW + ", 0)");

	    let circleAll = that.circleG.selectAll('circle.dot-hour').data(that.values, d => d.time);

	    circleAll.exit().transition().attr('r', 0).remove();

		let circleEnter = circleAll.enter().append("circle");

	    circleEnter
		    .attr('cx', d => that.x(d.date))
		    .attr('cy', d => that.y(d.hour))
		    .attr('r', 0)
			.style("fill", that.hourColor)
			.attr("class", "dot-hour")
			.on('mouseenter', mouseEnterPathHour)
			.on('mouseleave', mouseLeavePathHour)
			.on('mousemove', e => that.mouseMoveTooltip(e))
			;

	    circleEnter = circleEnter.merge(circleAll);

		circleEnter
			.attr("data-value", d => d.value)
			.attr("data-tooltip", d => that.getTooltip(d))
			.transition()
			.duration((d, i) => that.transitionDuration + (i / that.values.length * 600))
			.attr('cx', d => that.x(d.date))
			.attr('cy', d => that.y(d.hour))
			.attr('r', d => that.r(d.value))
			;

	    function mouseEnterPathHour(e) {
			let date = this.__data__.date;
			let hour = this.__data__.hour;
		    d3.select(this).transition().duration(100).style("fill", "#007aff");

		    that.days.selectAll('.tick-days').filter(d => d.getTime() === date.getTime()).transition().duration(100).attr('stroke-opacity', 0.5);

		    that.hours.selectAll('.tick-hours').filter(d => d === hour).transition().duration(100).attr('stroke-opacity', 0.5);

		    if (that.options.tooltips) {
			    that.mouseEnterTooltip(e);
		    }
	    }

	    function mouseLeavePathHour(e) {
		    d3.select(this).transition().duration(100).style("fill", that.hourColor);
		    $(this).attr('title', $(this).data('tipText'));

		    that.days.selectAll('.tick-days').transition().duration(100).attr('stroke-opacity', 0);
		    that.hours.selectAll('.tick-hours').transition().duration(100).attr('stroke-opacity', 0);

		    if (that.options.tooltips) {
			    that.mouseLeaveTooltip(e);
		    }
	    }

    }

	renderLabels() {
    	let that = this;

    	let months = d3.utcMonth.range(moment(this.minDate).utc().toDate(), moment(this.maxDate).utc().toDate());
    	let days = d3.utcDay.range(moment(this.minDate).utc().toDate(), moment(this.maxDate).utc().add(1, 'day').toDate());
    	let hours = d3.range(moment(this.minHour).utc().toDate(), moment(this.maxHour + 1).utc().toDate());

    	// Month labels

    	if (months.length > 1) {
		    if (!that.months) that.months = that.bodyG.append("g").attr("class", "labels-months");

		    let monthAll = that.months.selectAll('.label-months').data(months);

		    monthAll.exit().remove();

		    let monthEnter = monthAll.enter().append("text");

		    monthEnter
			    .attr('class', 'label-months')
			    .attr('text-anchor', 'middle')
			    .on('mouseenter', d => {
				    that.days.selectAll('.tick-days').filter(h => d.getTime() === h.getTime()).transition().duration(100).attr('stroke-opacity', 1);
			    })
			    .on('mouseleave', () => {
				    that.days.selectAll('.tick-days').transition().duration(100).attr('stroke-opacity', 0);
			    })
		    ;


		    monthEnter = monthEnter.merge(monthAll);

		    monthEnter
			    .attr('x', d => that.x(d) + that.ticksW)
			    .attr('y', that.quadrantHeight())
			    .text(d => that.monthNames[d.getMonth()])
		    ;
	    }

	    // Hours labels

	    if (!that.hours) that.hours = that.bodyG.append("g").attr("class", "labels-hours");

	    let hourAll = that.hours.selectAll('.label-hours').data(hours);

	    hourAll.exit().remove();
		that.hours.selectAll('.tick-hours').remove();

	    let hourEnter = hourAll.enter().append("text");

	    hourEnter
		    .attr('class', 'label-hours')
		    .attr('dominant-baseline', 'middle')
		    .attr('x', 0)
		    .on('mouseenter', d => {
			    that.hours.selectAll('.tick-hours').filter(h => d === h).transition().duration(100).attr('stroke-opacity', 1);
		    })
		    .on('mouseleave', () => {
			    that.hours.selectAll('.tick-hours').transition().duration(100).attr('stroke-opacity', 0);
		    });

	    hourEnter = hourEnter.merge(hourAll);

	    hourEnter
		    .attr('y', d => that.y(d))
		    .each(function(d) {
		    	d3.select(this.parentNode)
				    .insert('line')
				    .attr('class', 'tick-hours')
				    .attr('x1', that.ticksW)
				    .attr('x2', that.quadrantWidth())
				    .attr('y1', that.y(d))
				    .attr('y2', that.y(d))
				    .attr('stroke', '#333')
				    .attr('stroke-width', 0.3)
				    .attr('stroke-opacity', 0)
				    .node().__data__ = d;
		    })
		    .text(d => that.formatHour(d))
		    .call(acUtils.fixDominantBaseline)
	    ;

	    // Days labels

		if (!that.days) that.days = that.bodyG.append("g").attr("class", "labels-days");

		let dayAll = that.days.selectAll('.label-days').data(days);

		dayAll.exit().remove();

		if (that.maxR > 6) {

			let dayEnter = dayAll.enter().append("text");

			dayEnter
				.attr('class', 'label-days')
				.attr('text-anchor', 'middle')
				.on('mouseenter', d => {
					that.days.selectAll('.tick-days').filter(h => d.getTime() === h.getTime()).transition().duration(100).attr('stroke-opacity', 1);
				})
				.on('mouseleave', () => {
					that.days.selectAll('.tick-days').transition().duration(100).attr('stroke-opacity', 0);
				})
			;

			dayEnter = dayEnter.merge(dayAll);

			dayEnter
				.attr('x', d => that.x(d) + that.ticksW)
				.attr('y', that.quadrantHeight() - 20)
				.text(d => that.dayNames[d.getDay()])
			;
		}

		// Days ticks

		if (!that.tickDays) that.tickDays = that.bodyG.append("g").attr("class", "ticks-days");

	    let tickDayAll = that.days.selectAll('.tick-days').data(days);

		tickDayAll.exit().remove();

	    let tickDayEnter = tickDayAll.enter().append("line");

		tickDayEnter
		    .attr('class', 'tick-days')
		    .attr('x1', d => that.x(d) + that.ticksW)
		    .attr('x2', d => that.x(d) + that.ticksW)
		    .attr('y1', 0)
		    .attr('y2', that.qh)
		    .attr('stroke', '#333')
		    .attr('stroke-width', 0.3)
		    .attr('stroke-opacity', 0)
		    .attr('x', 0)
		    .attr('y', d => that.y(d));

		tickDayEnter = tickDayEnter.merge(tickDayAll);

		tickDayEnter
			.attr('x1', d => that.x(d) + that.ticksW)
			.attr('x2', d => that.x(d) + that.ticksW)
			.attr('y1', 0)
			.attr('y2', that.qh)
			.attr('stroke', '#333')
	    ;

	}

	async resize(container, width, height) {
        let that = this;
        let $container = $(container);
        if (typeof width === "undefined") {
            width = $container.width();
        }
        if (typeof height === "undefined") {
            height = $container.height();
        }
        if (that.legendState) {
            that.margins.right = that.defaultMargins.right + that.$legend.width();
        }
        $container.find('.timeDotChart').remove();
		await that.setSize(width, height);
        that.setParams(that.params);
		that.reset();
        that.render(that.$parent.selector);
		that.postResize(container, width, height);
    }

	reset() {
		let that = this;
		that.svg = false;
		that.bodyG = false;
		that.clock = false;
		that.hours = false;
		that.months = false;
		that.days = false;
		that.circleG = false;
	}

	getTooltip(d) {
        let that = this;
        let x = {};
        x[that.options.x[0].label] = that.formatType(d.date, "date") + " " + that.formatHour(d.hour);

        let y = [];
        let value;

        // var value = that.formatType(d.value, that.options.y[0].type);

		//si on a dans méthode de calculs "Comptage" ou "Comptage unique"
		if (that.params.hasOwnProperty("esParameters") && (that.params.esParameters.aggregations[0].type === "count" || that.params.esParameters.aggregations[0].type == "cardinality")) {
			// format integer
			value = that.formatType(d.value, "integer");
		} else {
			//sinon on applique le format approprié au calcul
			value = that.formatType(d.value, that.options.y[0].type);
		}

        let yy = {
            key: $.trim(that.options.y.label),
            value: value,
			color: that.hourColor
        };
        y.push(yy);

        let tooltip = {
            x: x,
            y: y
        };
        return JSON.stringify(tooltip);
    }

	formatHour(hour) {
    	return d3.format("0>2")(hour) + "h";
	}

	onGenerateParams(params) {
    	if (params.buckets.length > 1) {
		    params.buckets[0].interval = 'd';
		    params.buckets[1].interval = 'HH';
	    } else if (params.buckets.length > 0) {
		    params.buckets[0].interval = 'HH';
	    }
		return params;
	}

	options() {
        let defaultOptions = this.defaultOptions();
		return [
			{
				label: "Heure de début",
				prelabel: "Heure de début",
				name: "startHour",
				type: "number",
				default: defaultOptions.startHour,
				need_reload: true
			},
			{
				label: "Heure de fin",
				prelabel: "Heure de fin",
				name: "endHour",
				type: "number",
				default: defaultOptions.endHour,
				need_reload: true
			},
			{
				label: "Afficher les heures",
				name: "ticks",
				type: "checkbox",
				default: defaultOptions.ticks,
				need_reload: true
			},
			{
				label: "Valeur min",
				prelabel: "Valeur min",
				name: "valueMin",
				type: "number"
			},
			{
				label: "Valeur max",
				prelabel: "Valeur max",
				name: "valueMax",
				type: "number"
			}
		];
    }

	parameters() {
        return [{
            label: "Dimension",
            id: "regroupement",
            desc: "Donnée de type date",
            type: "bucket",
            size: 2,
            accept: ["date"],
        }, {
            label: "Calcul",
            id: "calcul",
            desc: "Donnée de type texte ou numérique",
            type: "aggregation",
            size: "single",
            accept: ["number", "string"]
        }];
    }

	defaultOptions() {
        return {
        	tooltips: true,
            ticks: true,
			startHour: 0,
			endHour: 23,
	        valueMin: null,
	        valueMax: null
        };
    }
}