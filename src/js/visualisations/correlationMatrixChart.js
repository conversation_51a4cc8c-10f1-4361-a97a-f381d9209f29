import VisualisationBase from "./VisualisationBase";
import * as d3 from "d3";
import acUtils from "../utils";

export default class correlationMatrixChart extends VisualisationBase {
	constructor() {
        super();
        this.name = "correlationMatrix<PERSON>hart";
        this.defaultMargins = {top: 20, left: 20, right: 20, bottom: 20};
        this.margins = JSON.parse(JSON.stringify(this.defaultMargins));
        this.padding = 10;
        this.data = [];
        this.values = {};
        this.current = 0;
        this.x = null;
        this.y = null;
        this.bodyG = false;
        this.radius = 0;
        this.division_keys = [];
        this.division = null;
        this.maxY = 0;
        this.arc = null;
        this.arcOver = null;
        this.stash = [];
        this.pieG = null;
        this.hasLegend = true;
        this.stashInner = [];
		this.canGenerateFilters = false;
		this.hasDynamicHeight = true;
    }

	setParams(params) {
        this.params = params;
        this.options = $.extend(this.defaultOptions(), this.validateOptions(this.params.options));
	}

	async update(datas) {
        this.data = datas;
		await this.beforeUpdate(datas);
        this.render(this.$parent.selector);
    }

	preRender(element) {
        super.preRender(element);
        let that = this;

        //transformation données sous forme de tableau
        this.values = [];
        this.data.forEach((item, lig) => {
            let correlations = Object.entries(item.correlation);

            for (let col = 0; col < correlations.length; col++) {
                that.values.push({
                    column_x: item.name,
                    column_y: correlations[col][0],
                    correlation: correlations[col][1],
                    row: lig + 1,
                    column: col + 1
                });
            }
        });
    }

    //définition du svg
	render(element) {
        let that = this;
        that.preRender(element);

        if (that.emptyData) {
		 return;
		}

        if (!that.svg) {
            that.svg = d3.select(element)
                .append("svg")
                .attr("height", that.height)
                .attr("width", that.width)
                .attr("class", "correlationMatrixChart visualisation-root");
        }
        that.renderBody(that.svg);
    }

    //corps du svg
	renderBody() {
        let that = this;

        that.colorScale = that.getHeatColorScale(that.options);
        let cols = "abcdefghijklmnopqrstuvwxyz".split("");

        let margin = {top: 70, bottom: 1, left: 20, right: 1};

        if (!that.bodyG) {
            that.bodyG = that.svg
            .append("g")
            .attr("transform", "translate(" + margin.left + ", " + margin.top + ")");
        }

        //#########################################################################
        //gestion des axes
        //#########################################################################
        let padding = .01;

        //pour avoir un cadran carré
        let min = Math.min(that.quadrantHeight(), that.quadrantWidth());

        that.y = d3.scaleBand() //scale : échelle
            .range([0, min]) //range : largeur en pixel
            .paddingInner(padding)
            .domain(d3.range(1, that.options.y.fields.length + 1)); // domain : plage des données

        that.x = d3.scaleBand()
            .range([0, min])
            .paddingInner(padding)
            .domain(d3.range(1, that.options.y.fields.length + 1));

        let y_axis = d3.axisLeft(that.y).tickFormat((d, i) => cols[i]);
        let x_axis = d3.axisTop(that.x).tickFormat((d, i) => cols[i]);

        that.bodyG.selectAll('g.x.axis').remove();
        that.xAxis = that.bodyG.append("g")
            .attr("class", "x axis")
            .call(x_axis);

        that.bodyG.selectAll('g.y.axis').remove();
            that.yAxis = that.bodyG.append("g")
                .attr("class", "y axis")
                .call(y_axis);

        //#########################################################################
        //data : rectangle des corrélations
        //#########################################################################
        that.bodyG.selectAll('rect').remove();

        let rect = that.bodyG.selectAll('rect')
            .data(that.values, d => d.row + "_" + d.column);

        rect.enter()
            .append("g")
            .attr("class", "cell")
            .append("rect")
            .attr("x", d => that.y(d.column))
            .attr("y", d => that.x(d.row))
            .attr("width", that.x.bandwidth())
            .attr("height", that.y.bandwidth())
            .attr('fill', d => that.colorScale(d.correlation))
            // .merge(rect)
            // .attr('fill', function(d) { return that.colorScale(d.correlation); });
            .attr("data-tooltip", d => that.getTooltip(d))
            // .on("click", click)
		    .on('mouseenter', e => {
		    	that.mouseEnterTooltip(e);
		    })
		    .on('mouseleave', e => {
			    that.mouseLeaveTooltip(e);
		    })
		    .on('mousemove', e => that.mouseMoveTooltip(e));

        //#########################################################################
        //Echelle de la corrélation (légende)
        //#########################################################################
        that.svg.selectAll('g.legend_svg').remove();
            that.legend_svg = that.svg.append("g")
                .attr("class", "legend_svg")
                .attr("transform", "translate(" + margin.left + ", 15)");

        let defs = that.legend_svg.append('g')
            .append("defs");

        let gradient = defs.append("linearGradient")
            .attr("width", min)
            .attr("id", "linear-gradient");

        // //récupération des valeurs de correlation sous forme de tableau
        // this.tabCorrelation = [];
        // that.values.forEach(function(item){ that.tabCorrelation.push(item.correlation); });

        // var corrMax = Math.max.apply(Math, that.tabCorrelation);
        // var corrMin = Math.min.apply(Math, that.tabCorrelation);

        let corrMax = 1; let corrMin = -1;

        let stops = [
            {offset: 0, color: that.getSchemeColor(that.options.color_min), value: corrMin},
            {offset: .5, color: "#FFFFFF", value: 0},
            {offset: 1, color: that.getSchemeColor(that.options.color_max), value: corrMax}
        ];

        gradient.selectAll("stop")
            .data(stops)
            .enter().append("stop")
            .attr("offset", d => (100 * d.offset) + "%")
            .attr("stop-color", d => d.color);

        that.legend_svg.append("rect")
            .attr("width", min)
            .attr("height", 15)
            .style("fill", "url(#linear-gradient)");

        that.legend_svg.selectAll("text")
            .data(stops)
            .enter().append("text")
            .attr("x", d => min * d.offset)
            .attr("dy", -3)
            .style("text-anchor", (d, i) => i == 0 ? "start" : i == 1 ? "middle" : "end")
            // .text(function(d, i){ return d.value==0 ? 0 : d.value.toFixed(3); });
            .text(d => d.value);

        //#########################################################################
        //Affichage de la légende
        //#########################################################################

		that.legendItems = this.data.map((d, i) => ({
                color: null,
                label: cols[i] + " : " + that.getSuffixedLabel(d.name)
            }));

        // function click(d) {
        //     console.log(d);
        //     if(d.row==d.column){
        //         console.log(d.column_x);
        //     }else{
        //         console.log(d.column_x);
        //         console.log(d.column_y);
        //     }
		// }
    }

	legendEvent() {
        let that = this;
        let matrix = that.$parent;

        matrix.find('.chart-legend-item')
        .on('mouseover', function() {
            //item légende pointé
            let value = $(this).find('.chart-legend-label').data('label');
            value = value.substring(4);
            $.each(matrix.find('.cell rect'), (index, val) => {
                if (val.__data__.column_x !== value && val.__data__.column_y !== value) {
                    val.classList.add('arc-unselected');
                    val.classList.remove('arc-selected');
                }
            });
        }).on('mouseleave', () => {
            $.each(matrix.find('.cell rect'), (index, val) => {
                val.classList.remove('arc-selected');
                val.classList.remove('arc-unselected');
            });
        });
    }

	async resize(container, width, height) {
        let that = this;
        let $container = $(container);
        if (typeof width === "undefined") {
            width = $container.width();
        }
        if (typeof height === "undefined") {
            height = $container.height();
        }
        if (that.legendState) {
            that.margins.right = that.defaultMargins.right + that.$legend.width();
        }
        that.reset();
        $container.find('.correlationMatrixChart').remove();
		await that.setSize(width, height);
        that.setParams(that.params);
        that.render(that.$parent.selector);
		that.postResize(container, width, height);
    }

	reset() {
		let that = this;
		that.svg = false;
		that.bodyG = false;
		that.pieG = false;
		delete(that.labels);
		delete(that.marker);
	}

	key(d) {
        let k = []; let p = d;
        while (p.depth) k.push(p.data ? p.data.name : p.name), p = p.parent;
        return k.reverse().join(".");
    }

	getColor(d) {
        return super.getColor(this.getColorKey(d));
    }

	getColorKey(d) {
        let p = d;
        if (this.options.color_hierarchy) {
            while (p.depth > 1) p = p.parent;
		}
        return p.data.name;
    }

	getColorContrast(hex) {
		let r = parseInt(hex.substr(1, 2), 16);
			let g = parseInt(hex.substr(3, 2), 16);
			let b = parseInt(hex.substr(5, 2), 16);
			let yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;
		return (yiq >= 177) ? '#000000' : '#FFFFFF';
	}

	rgb2hex(rgb) {
		rgb = rgb.match(/^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i);
		return (rgb && rgb.length === 4) ? "#" +
		 ("0" + parseInt(rgb[1],10).toString(16)).slice(-2) +
		 ("0" + parseInt(rgb[2],10).toString(16)).slice(-2) +
		 ("0" + parseInt(rgb[3],10).toString(16)).slice(-2) : '';
    }

	getHeatColorScale(colors) {
        const domain = [-1, 0, 1];
        return d3.scaleLinear()
            .domain(domain)
            .range([d3.rgb(this.getSchemeColor(colors.color_min)), d3.rgb("#FFFFFF"), d3.rgb(this.getSchemeColor(colors.color_max))]);

    }

	getSuffixedLabel(d) {
		return super.getSuffixedLabel(d);
	}

	getTooltip(d) {
        return JSON.stringify(d);
    }

	getTooltipHead(thead) {
        thead += '<tr>' + '<th colspan = "2">CORRELATION</th>' + '</tr>';
        return thead;
    }

	getTooltipBody(tbody, json) {
        tbody += '<tr><td>Ligne : ' + json.column_x + '</td><td>Colonne : ' + json.column_y + '</td></tr>' +
        '<tr><td>Valeur : ' + json.correlation.toFixed(3) + '</td></tr>';
        return tbody;

    }

	getNodes() {
		return this.svg.selectAll('path.arc').nodes();
	}

	getNodeByLabel(name) {
        return this.getNodes().filter(d => {
            let validParent = false;
            if (d.__data__.parent) {
                validParent = d.__data__.parent.key === name || d.__data__.parent.data.name === name;
            }
            return validParent || d.__data__.key === name || (d.__data__.hasOwnProperty('data') && d.__data__.data.name === name);
        });
    }

	getNodesFromFilters(filters) {
        let that = this;
        let nodes = filters.map(filter => _.flatten(filter.filter.map(value => that.getNodeByLabel(value))));
        return _.intersection.apply(this, nodes);
    }

	applyVisualFilters(filters) {
        let that = this;
        filters = filters.filter(filter => filter.parent === that.parent && !filter.disabled);
        that.selected = [];
        if (filters.length) {
            this.getNodes().forEach(n => {
                that.addUnselectRemoveSelect(n);
            });
            this.getNodesFromFilters(filters).forEach(n => {
                that.selected.push(n.__data__.key);
                that.addSelectRemoveUnselect(n);
            });
        } else {
            this.getNodes().forEach(n => {
                that.removeSelectRemoveUnselect(n);
            });
        }
    }

	// Retourne le nombre de lignes à afficher en fonction du nombre de valeurs
	// 2 lignes pour 3 valeurs (Minimum 4 et maximum 8)
	getGridRowsCount() {
		return Math.max(Math.min(Math.ceil(this.data.length / 3) * 2, 8), 4);
	}

	options() {
        let defaultOptions = this.defaultOptions();
        return this.mergeLegendOption(
		[
			{
				label: null,
				name: "colors_custom",
				type: "svelte",
				component: "form/ColorpickerSwitch",
				props: {
					"name": "colors_custom",
					"isCustom": false
				},
			},
            {
                label: "Couleur corrélation minimum",
                name: "color_min",
                type: "color",
                default: defaultOptions.color_min
            },
            {
                label: "Couleur corrélation maximum",
                name: "color_max",
                type: "color",
                default: defaultOptions.color_max
            }
        ]);
    }

	parameters() {
        return [{
            label: "Calcul",
            id: "calcul",
            desc: "Donnée de type texte ou numérique",
            type: "aggregation",
            size: "multiple",
            accept: ["number"],
            subtype: "matrix_stats"
        }];
    }

	defaultOptions() {
        return {
            color_min: { scheme: 1, default: "#FF0000" },
            color_max: { scheme: 2, default: "#4169E1" },
            padding: 0.05
        };
    }
}