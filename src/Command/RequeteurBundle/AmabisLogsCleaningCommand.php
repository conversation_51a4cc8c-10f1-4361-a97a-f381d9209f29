<?php

namespace App\Command\RequeteurBundle;

use DateTime;
use FOS\ElasticaBundle\Index\IndexManager;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ContainerBagInterface;

class AmabisLogsCleaningCommand extends Command
{
    private $indexManager;
    private $params;

    public function __construct(IndexManager $indexManager, ContainerBagInterface $params)
    {
        $this->indexManager = $indexManager;
        $this->params = $params;
        parent::__construct();
    }

    /**
     * {@inheritdoc}
     */
    protected function configure(): void
    {
        $this
            ->setName('requeteur:amabis:logs-clean')
            ->setDescription('Supression des logs supérieur à un an');
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $date = new DateTime("now");
        $limit = new DateTime($date->format('Y') - 1 . "-". $date->format('m-d'));

        $projectDir = $this->params->get('kernel.project_dir');
        $logsDir = $projectDir . "/datas/amabis_logs/";

        if ($dh = opendir($logsDir)) {
            while (($file = readdir($dh)) !== false) {
                $fileDate = isset(explode("_", explode(".json", $file)[0])[1]) ? explode("_", explode(".json", $file)[0])[1] : null;
                if ($fileDate) {
                    $fileDate = new DateTime($fileDate);
                    if ($fileDate < $limit) {
                        unlink($logsDir . "/" . $file);
                    }
                }
            }
            closedir($dh);
        }
    }
}
