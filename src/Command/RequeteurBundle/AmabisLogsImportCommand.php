<?php

namespace App\Command\RequeteurBundle;

use DateTime;
use FOS\ElasticaBundle\Index\IndexManager;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Elastica\Document;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\DependencyInjection\ParameterBag\ContainerBagInterface;

class AmabisLogsImportCommand extends Command
{

    private $amabisExportCsv;
    private $indexManager;
    private $params;

    public function __construct(IndexManager $indexManager, ContainerBagInterface $params)
    {
        $this->indexManager = $indexManager;
        $this->params = $params;
        parent::__construct();
    }

    /**
     * {@inheritdoc}
     */
    protected function configure(): void
    {
        $this
            ->setName('requeteur:amabis:logs-import')
            ->setDescription('Import des logs d\'Amabis dans elastic')
            ->addArgument('date', InputArgument::OPTIONAL, 'date');
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {

        $date = $input->getArgument('date') ? new \DateTime($input->getArgument('date')) : new DateTime("yesterday");
  
        $projectDir = $this->params->get('kernel.project_dir');
        $date = new DateTime("yesterday");
        $yesterdayLogs = $projectDir . "/datas/amabis_logs/log_" . $date->format('Y-m-d') . ".json";
        if(file_exists($yesterdayLogs)) {
            $resultSet = file_get_contents($yesterdayLogs);

            $resultSet = explode("\n", $resultSet);
            
            $type = $this->params->get('fos_elastica.index.app');
            $batch = [];
            foreach($resultSet as $result) {
                if($result != "") {
                    $element = json_decode(json_encode(json_decode($result)), true);
                    $batch[] = new Document(null, $element);
                }
            }
            if($batch != []) {
                $type->addDocuments($batch);
            }
        }
        return 0;
    }
}
