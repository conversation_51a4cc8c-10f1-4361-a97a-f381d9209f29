<?php

namespace App\Command;

use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Filesystem\Exception\FileNotFoundException;

class GenerateEnqueteConfigCommand extends GenerateConfigCommand
{
    public const TYPE_NOTE = 'note';
    public const TYPE_CASE_A_COCHER = 'caseacocher';
    public const TYPE_CASE_A_COCHER_AUTRE = 'caseacocherautre';
    public const TYPE_BOUTON_RADIO = 'boutonradio';
    public const TYPE_TEXTE_LIBRE = 'textelibre';
    public const TYPE_MULTI_TEXTE = 'multitexte';
    public const TYPE_TEXTE = 'texte';
    public const TYPE_SELECT = 'select';
    public const TYPE_COCHE_IMAGE = 'cocheimage';

    protected $initialIdentifier;

    protected function configure()
    {
        $this
            ->setName('app:generate:enquete')
            ->addArgument('name', InputArgument::REQUIRED, 'Client Name')
            ->addArgument('identifier', InputArgument::REQUIRED, 'Client Identifier')
            ->addArgument('login', InputArgument::REQUIRED, 'Client Login')
            ->addArgument('pass', InputArgument::REQUIRED, 'Client Pass')
            ->addArgument('enqueteId', InputArgument::REQUIRED, 'Enquete Id')
            ->addArgument('xmlPath', InputArgument::REQUIRED, 'Chemin du xml des questions de l\'enquête')
            ->addOption('operation', 'o', InputOption::VALUE_NONE, 'Generate for operation')
        ;
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     */
    protected function initialize(InputInterface $input, OutputInterface $output)
    {
        parent::initialize($input, $output);
        $this->initialIdentifier = $input->getArgument("identifier");
        $input->setArgument("identifier", $input->getArgument("identifier") . "_enquete_" . $input->getArgument("enqueteId"));
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     */
    public function createConfig(InputInterface $input, OutputInterface $output) {

        $sample = file_get_contents($this->importPath . "/_sample_enquete");
        $sample = str_replace("%name%", $input->getArgument("name"), $sample);
        $sample = str_replace("%identifier%", $this->initialIdentifier.'_enquete', $sample);
        $sample = str_replace("%login%", $input->getArgument("login"), $sample);
        $sample = str_replace("%pass%", $input->getArgument("pass"), $sample);

        // ecriture du fichier de configuration pour l'enseigne si il n'existe pas déjà
        $filePath = sprintf("%s/%s.json", $this->importPath, $this->initialIdentifier.'_enquete');
        if(!file_exists($filePath)) {
            file_put_contents($filePath, $sample);
        }

        $sample = str_replace($this->initialIdentifier.'_enquete', $input->getArgument("identifier"), $sample);
        // ajout des champs relatifs aux question de l'enquête au mapping elatic
        $sample = json_decode($sample, true);
        $sample['mapping']['properties'] = array_merge(
            $sample['mapping']['properties'],
            $this->getQuestionFields($input->getArgument("xmlPath"))
        );
        $sample = json_encode($sample, JSON_PRETTY_PRINT);

        // ecriture du fichier de configuration
        $filePath = sprintf("%s/%s.json", $this->importPath, $input->getArgument("identifier"));
        file_put_contents($filePath, $sample);
    }

    /**
     * getQuestionFields
     *
     *
     * @return void
     */
    protected function getQuestionFields(mixed $xmlPath) {
        $enquete = $this->getEnqueteFromXmlFile($xmlPath);
        $questionFields = [];
        foreach ($enquete['pave'] as $key => $pave) {
            foreach ($pave['groupe'] as $key => $groupe) {
                foreach ($groupe['question'] as $key => $question) {
                    $questionId = $question['idcampagnequestion'];
                    $type = array_keys($question["type"])[0];
                    $questionFields = array_merge($questionFields, [
                        "Q".$questionId."_ID_QUESTION" => ["type" => "keyword"],
                        "Q".$questionId."_TYPE_QUESTION" => ["type" => "keyword"],
                        "Q".$questionId."_LIBELLE_QUESTION" => ["type" => "keyword"],
                        "Q".$questionId."_VALEUR" => $this->getElasticFieldConfByQuestionType($type)
                    ]);

                    if($type == self::TYPE_CASE_A_COCHER_AUTRE || $type == self::TYPE_SELECT) {
                        $questionFields["Q".$questionId."_VALEUR_AUTRE"] = ["type" => "keyword"];
                    }
                    if($type == self::TYPE_TEXTE || $type == self::TYPE_TEXTE_LIBRE) {
                        $questionFields["Q".$questionId."_VALEUR_ANALYSE"] = ["type" => "text", "fielddata" => true, "analyzer" => "french_analyzer"];
                    }
                }
            }
        }
        return $questionFields;
    }

    /**
     * getEnqueteFromXmlFile
     *
     *
     * @return void
     */
    protected function getEnqueteFromXmlFile(mixed $xmlPath) {
        if (!file_exists($xmlPath)) {
            throw new FileNotFoundException(null, 0, null, $xmlPath);
        }

        $xml = json_decode(json_encode(simplexml_load_string(file_get_contents($xmlPath))), true);
        $enquete = $xml['resultat']['enquete'];

        if (isset($enquete['pave']['titre'])) {
            $enquete['pave'] = [$enquete['pave']];
        }
        if(!is_array($enquete['pave'])) {
            $enquete['pave'] = [$enquete['pave']];
        }
        foreach ($enquete['pave'] as &$pave) {
            if (isset($pave['groupe']['libellegroupe'])) {
                $pave['groupe'] = [$pave['groupe']];
            }
            foreach ($pave['groupe'] as &$groupe) {
                if (isset($groupe['question']['idcampagnequestion'])) {
                    $groupe['question'] = [$groupe['question']];
                }
            }
        }

        return $enquete;
    }

    /**
     * getElasticFieldConfByQuestionType
     *
     *
     * @return void
     */
    protected function getElasticFieldConfByQuestionType(mixed $type) {
        $types = [
            self::TYPE_NOTE => ["type" => "float"],
            self::TYPE_CASE_A_COCHER => ["type" => "keyword"],
            self::TYPE_CASE_A_COCHER_AUTRE => ["type" => "keyword"],
            self::TYPE_BOUTON_RADIO => ["type" => "keyword"],
            self::TYPE_TEXTE_LIBRE => ["type" => "keyword"],
            self::TYPE_MULTI_TEXTE => ["type" => "keyword"],
            self::TYPE_TEXTE => ["type" => "keyword"],
            self::TYPE_SELECT => ["type" => "keyword"],
            self::TYPE_COCHE_IMAGE => ["type" => "keyword"],
        ];
        if(!isset($types[$type])) { throw new \Exception("Ce type de question n'est pas supporté"); }
        
        return $types[$type];
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @throws \Exception
     */
    public function launchReindexCommand(InputInterface $input, OutputInterface $output)
    {
        $reindexCommand = $this->getApplication()->find('app:reindex');
        $arguments = array(
            'name' => $input->getArgument("identifier"),
            '--no-data' => true,
        );
        $input = new ArrayInput($arguments);
        $reindexCommand->run($input, $output);
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @throws \Exception
     */
    public function launchSupervisorUpdateCommand(InputInterface $input, OutputInterface $output)
    {
        $reindexCommand = $this->getApplication()->find('app:reindex');
        $arguments = array(
            'name' => $input->getArgument("identifier"),
            '--no-data' => true,
        );
        $input = new ArrayInput($arguments);
        $reindexCommand->run($input, $output);
    }
}