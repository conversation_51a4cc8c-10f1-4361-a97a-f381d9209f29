<?php

namespace App\Command;

use App\Consumer\BatchConsumer;
use App\Service\ConfigManager;
use App\Service\IndexManager;
use OldSound\RabbitMqBundle\Command\BaseRabbitMqCommand;
use OldSound\RabbitMqBundle\Command\BatchConsumerCommand;
use OldSound\RabbitMqBundle\Command\ConsumerCommand;
use Phobetor\RabbitMqSupervisorBundle\Services\Supervisor;
use PhpAmqpLib\Exception\AMQPTimeoutException;
use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\Input;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * @phpstan-import-type Config from ConfigManager
 */
class DebugCredentialsCommand extends ContainerAwareCommand
{
    /**
     * @var string
     */
    protected $importPath;

    /**
     * @var IndexManager
     */
    protected $indexManager;

    /**
     * @var string
     */
    protected $client;

    /**
     * @var array
     */
    protected $config;

    /**
     * @var Supervisor
     */
    protected $supervisor;

    /**
     * @var string
     */
    protected $indexName;

    /**
     * @var string
     */
    protected $reindexAlias;

    /**
     * @var ConfigManager
     */
    private $configManager;

    /**
     * ReindexClientCommand constructor.
     * @param $importPath
     * @param IndexManager $indexManager
     * @param Supervisor $supervisor
     */
    public function __construct(
        $importPath,
        IndexManager $indexManager,
        Supervisor $supervisor,
        ConfigManager $configManager
    )
    {
        parent::__construct();
        $this->importPath = $importPath;
        $this->indexManager = $indexManager;
        $this->supervisor = $supervisor;
        $this->configManager = $configManager;
    }

    protected function configure()
    {
        $this
            ->setName('app:debug:credentials')
            ->addArgument('enseigne', InputArgument::OPTIONAL, 'Enseigne')
            ->addOption('all', '--all', InputOption::VALUE_NONE, 'Vérifie que toutes les configs sont les mêmes qu\'avant')
        ;
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     */
    protected function initialize(InputInterface $input, OutputInterface $output)
    {
        parent::initialize($input, $output);

    }

    /**
     * Executes the current command.
     *
     * @param   InputInterface      $input      An InputInterface instance
     * @param   OutputInterface     $output     An OutputInterface instance
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $enseigne = $input->getArgument('enseigne');
        $all = $input->getOption('all');

        if ($enseigne) {
            /** @var Config[] $enseignes */
            $enseignes = [$enseigne];
        } else {
            /** @var Config[] $enseignes */
            $enseignes = $this->configManager->getConfigs();
        }

        foreach ($enseignes as $enseigne) {
            $output->writeln("<info>" . $enseigne['identifier'] . "</info>");
            $output->writeln($enseigne['parameters']['pass']);
            $output->writeln("");
            $this->indexManager->setHosts(
                array('https://aqui-es-swarm.alienor.net:443/es'),
                $enseigne["parameters"]
            );
            $indexes = array_keys($this->indexManager->getIndexesList());
            dump($indexes);
        }
        return 0;
    }

}