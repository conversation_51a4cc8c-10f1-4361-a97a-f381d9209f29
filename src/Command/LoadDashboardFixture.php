<?php

namespace App\Command;

use App\Service\DashboardFixturesLoader;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

#[AsCommand(
    name: 'load:dashboard:fixture',
    description: '<PERSON><PERSON><PERSON> ou met à jour tous les dashboards à partir des fichiers samples en fonction de leur identifier',
)]
class LoadDashboardFixture extends Command
{
    /**
     * {@inheritdoc}
     */
    protected function configure(): void
    {
        $this
            ->addOption('list', 'l', InputOption::VALUE_NONE, 'Liste les samples existants')
            ->addOption('sample', 's', InputOption::VALUE_REQUIRED, '<PERSON><PERSON><PERSON> ou met à jour le dashboard correspondant au sample donné en paramètre')
        ;
    }

    public function __construct(
        private readonly DashboardFixturesLoader $dashboardFixturesLoader
    )
    {
        parent::__construct();
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        if ($input->getOption('list')) {
            $io->title('Listing des samples disponibles');
            $samples = $this->dashboardFixturesLoader->getAllSamples();
            $io->table(
                ['Nom', 'Description'],
                array_map(fn($sample) => [$sample['name'] ?? '', $sample['description'] ?? ''], $samples)
            );
            return Command::SUCCESS;
        }

        if ($input->getOption('sample')) {
            $selectedSample = $input->getOption('sample');
            $io->title(sprintf("Création ou mise à jour d'un dashboard à partir du sample %s", $selectedSample));
            $this->dashboardFixturesLoader->createDashboardFromTemplate($selectedSample);
            return Command::SUCCESS;
        }

        $io->title("Création ou mise à jour de tous les dashboards liés à un sample");
        $this->dashboardFixturesLoader->createDashboardsFromTemplates();

        return Command::SUCCESS;
    }
}
