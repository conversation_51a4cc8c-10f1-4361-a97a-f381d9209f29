<?php

namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Exception\InvalidArgumentException;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

#[AsCommand(
    name: 'dashboard:maintenance',
    description: '',
)]
class ToggleMaintenanceCommand extends Command
{
    /**
     * {@inheritdoc}
     */
    protected function configure(): void
    {
        $this
            ->addArgument('state', InputArgument::REQUIRED, 'Statut de la maintenance à appliquer, true ou false')
        ;
    }

    public function __construct(private readonly ParameterBagInterface $parameterBag)
    {
        parent::__construct();
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $envPath = sprintf("%s/.env.local", $this->parameterBag->get("kernel.project_dir"));
        $state = $input->getArgument("state");

        if (!in_array($state, array("true", "false"))) {
            throw new InvalidArgumentException("l'argument state doit être égal à true ou false");
        }

        file_put_contents(
            $envPath,
            preg_replace('/MAINTENANCE_MODE=(true|false)/', sprintf('MAINTENANCE_MODE=%s', $state) , file_get_contents($envPath))
        );

        $output->writeln("Maintenance passé à <info>$state</info>");

        return 0;
    }
}
