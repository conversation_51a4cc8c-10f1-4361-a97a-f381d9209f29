<?php

namespace App\Controller\Admin;

use App\Entity\Survey;
use App\Entity\User;
use App\Enum\Activity;
use App\Enum\SessionType;
use App\Enum\Software;
use Doctrine\ORM\QueryBuilder;
use EasyCorp\Bundle\EasyAdminBundle\Collection\FieldCollection;
use EasyCorp\Bundle\EasyAdminBundle\Collection\FilterCollection;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\Filters;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Context\AdminContext;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Dto\EntityDto;
use EasyCorp\Bundle\EasyAdminBundle\Dto\SearchDto;
use EasyCorp\Bundle\EasyAdminBundle\Exception\ForbiddenActionException;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TimeField;
use EasyCorp\Bundle\EasyAdminBundle\Filter\ChoiceFilter;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use EasyCorp\Bundle\EasyAdminBundle\Security\Permission;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\Form\Extension\Core\Type\EnumType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class SurveyCrudController extends AbstractCrudController
{
    public function __construct(
        protected TranslatorInterface $translator, private readonly AdminUrlGenerator $adminUrlGenerator,
    ) {
    }

    public static function getEntityFqcn(): string
    {
        return Survey::class;
    }

    public function configureFields(string $pageName): iterable
    {
        $identifier = TextField::new('identifier', 'N° de dép')
            ->setHelp('Commençant par DEP suivi de maximum 8 chiffres');

        $label = TextField::new('label', 'Intitulé de la formation');

        $sessionStartDate = DateField::new('sessionStartDate', 'Date de la session')
            ->setTemplatePath('admin/field/survey_date.html.twig')
            ->onlyOnIndex();

        $sessionStartDateForm = DateField::new('sessionStartDate', 'Date de début de la session de formation')
            ->onlyOnForms();

        $sessionStartTime = TimeField::new('sessionStartTime', 'Heure de début de la session de formation')
            ->renderAsChoice()
            ->setFormTypeOptions([
                'hours' => range(8, 19),
                'minutes' => [0, 30],
            ])
            ->onlyOnForms();

        $sessionEndDate = DateField::new('sessionEndDate', 'Date de fin de la session de formation')
            ->onlyOnForms();

        $sessionEndTime = TimeField::new('sessionEndTime', 'Heure de fin de la session de formation')
            ->renderAsChoice()
            ->setFormTypeOptions([
                'hours' => range(8, 19),
                'minutes' => [0, 30],
            ])
            ->onlyOnForms();

        $activity = ChoiceField::new('activity', 'Activité')
            ->setFormType(EnumType::class)
            ->onlyOnForms()
            ->setFormTypeOptions([
                'required' => true,
                'class' => Activity::class,
                'choices' => $this->getUserActivities(),
                'choice_label' => function (Activity $value): string {
                    return $value->trans($this->translator);
                },
            ]);

        $software = ChoiceField::new('software', 'Logiciel')
            ->setFormType(EnumType::class)
            ->setFormTypeOptions([
                'required' => true,
                'class' => Software::class,
                'choice_attr' => function (Software $value): array {
                    return [
                        'data-activity' => $value->getActivity()->value,
                    ];
                },
                'choices' => Software::cases(),
                'choice_label' => function (Software $value): string {
                    return $value->trans($this->translator);
                },
            ]);

        $sessionType = ChoiceField::new('sessionType', 'Type de formation')
            ->setFormType(EnumType::class)
            ->setFormTypeOptions([
                'required' => true,
                'expanded' => true,
                'class' => SessionType::class,
                'choices' => SessionType::cases(),
                'choice_label' => function (SessionType $value): string {
                    return $value->trans($this->translator);
                },
            ]);

        $participantSurveyHotCompletedCount = IntegerField::new('participantSurveyHotCompletedCount', 'Nb. répondants')
            ->setSortable(true)
            ->setTemplatePath('admin/field/survey_answers_count.html.twig')
            ->onlyOnIndex();

        $registeredParticipantCount = IntegerField::new('registeredParticipantCount', "Nombre d'inscrits")
            ->onlyOnForms();

        $former = AssociationField::new('author', 'Formateur')
            ->onlyOnForms()
            ->setPermission('ROLE_ADMIN')
            ->setRequired(true)
            ->setQueryBuilder(
                function (QueryBuilder $queryBuilder) {
                    return $queryBuilder
                        ->where('entity.role = :role')
                        ->setParameter('role', 'ROLE_FORMER');
                }
            );

        // Ordre des champs différent selon la page

        if (Action::NEW === $pageName || Action::EDIT === $pageName) {
            return [
                $identifier,
                $activity,
                $software,
                $label,
                $sessionStartDateForm,
                $sessionStartTime,
                $sessionEndDate,
                $sessionEndTime,
                $sessionType,
                $registeredParticipantCount,
                ...(Action::NEW === $pageName ? [$former] : []), // Ajout du formateur en création si admin
            ];
        }

        return [
            $identifier,
            $label,
            $sessionStartDate,
            $sessionStartDateForm,
            $sessionStartTime,
            $sessionEndDate,
            $sessionEndTime,
            $software,
            $participantSurveyHotCompletedCount,
            $registeredParticipantCount,
        ];
    }

    public function configureCrud(Crud $crud): Crud
    {
        return parent::configureCrud($crud)
            ->setEntityLabelInSingular('Enquête')
            ->setEntityLabelInPlural('Enquêtes')
            ->showEntityActionsInlined()
            ->setDefaultSort(['sessionStartDate' => 'desc'])
            ->setSearchFields(null)
            ->setPageTitle(Crud::PAGE_INDEX, 'Enquêtes de satisfaction')
            ->setPageTitle(Crud::PAGE_NEW, 'Créer une enquête de satisfaction')
            ->setPageTitle(Crud::PAGE_EDIT, 'Modifier une enquête de satisfaction')
            ->overrideTemplate('crud/new', 'admin/crud/new_survey.html.twig')
            ->setFormOptions(['attr' => ['novalidate' => 'novalidate']], ['attr' => ['novalidate' => 'novalidate']])
        ;
    }

    public function configureFilters(Filters $filters): Filters
    {
        return $filters
            ->add('identifier')
            ->add('label')
            ->add('sessionStartDate')
            ->add('sessionEndDate')
            ->add(
                ChoiceFilter::new('activity', 'Activité')
                    ->setChoices($this->getUserActivities())
                    ->setFormTypeOptions([
                        'value_type_options.choice_label' => function ($value) {
                            return $value->trans($this->translator);
                        },
                    ]),
            )
            ->add(
                ChoiceFilter::new('software', 'Logiciel')
                    ->setChoices(Software::cases())
                    ->setFormTypeOptions([
                        'value_type_options.choice_label' => function ($value) {
                            return $value->trans($this->translator);
                        },
                    ]),
            )
        ;
    }

    public function configureActions(Actions $actions): Actions
    {
        $actions->update(Crud::PAGE_INDEX, Action::NEW, function ($action) {
            return $action->setLabel('Créer une enquête de satisfaction');
        });

        $actions->update(Crud::PAGE_INDEX, Action::EDIT, function ($action) {
            return $action
                ->setHtmlAttributes(['title' => 'Modifier l\'enquête'])
                ->addCssClass('btn btn-warning')
                ->setLabel('')
                ->setIcon('fa fa-edit');
        });

        $actions->update(Crud::PAGE_INDEX, Action::DELETE, function ($action) {
            return $action
                ->setHtmlAttributes(['title' => 'Supprimer l\'enquête'])
                ->addCssClass('btn btn-danger text-white')
                ->setLabel('')
                ->setIcon('fa fa-trash');
        });

        $actions->remove(Crud::PAGE_INDEX, Action::BATCH_DELETE);

        // Prévisualisation enquêtes
        $previewSurveyHot = Action::new('previewSurveyHot', '', 'fa fa-eye')
            ->displayAsLink()
            ->setHtmlAttributes(['target' => '_blank', 'title' => 'Afficher la prévisualisation des enquêtes'])
            ->linkToCrudAction('previewSurveyHot')
            ->addCssClass('btn btn-secondary');

        $previewSurveyCold = Action::new('previewSurveyCold', '', 'fa fa-eye')
            ->displayAsLink()
            ->setHtmlAttributes(['target' => '_blank', 'title' => 'Afficher la prévisualisation des enquêtes'])
            ->linkToCrudAction('previewSurveyCold')
            ->addCssClass('btn btn-secondary');

        $actions->add(Crud::PAGE_INDEX, $previewSurveyHot);
        $actions->add(Crud::PAGE_DETAIL, $previewSurveyCold);

        // QR Code
        $qrCode = Action::new('qrCode', '', 'fa fa-qrcode')
            ->displayAsLink()
            ->setHtmlAttributes(['target' => '_blank', 'title' => 'Afficher le QR Code'])
            ->linkToCrudAction('qrCode')
            ->addCssClass('btn btn-secondary');

        $actions->add(Crud::PAGE_INDEX, $qrCode);

        // On peut modifier ou supprimer une enquête uniquement si pas de participants et que l'auteur soit l'utilisateur ou que l'on soit super admin
        $canEditOrDeleteExpression = new Expression('auth_checker.isGranted("ROLE_SUPER_ADMIN") or (auth_checker.isGranted("ROLE_ADMIN") and subject.canBeEditedOrDeleted()) or (auth_checker.isGranted("ROLE_SURVEY_WRITE") and subject.canBeEditedOrDeleted() and subject.getAuthor() == user)');

        $actions->setPermissions([
            Action::INDEX => 'ROLE_SURVEY_READ',
            Action::NEW => 'ROLE_SURVEY_WRITE',
            Action::EDIT => $canEditOrDeleteExpression,
            Action::DELETE => $canEditOrDeleteExpression,
            'qrCode' => 'ROLE_SURVEY_READ',
            'previewSurveyHot' => 'ROLE_SURVEY_READ',
        ]);

        $actions->disable(Action::SAVE_AND_CONTINUE);

        return parent::configureActions($actions);
    }

    public function configureResponseParameters(KeyValueStore $responseParameters): KeyValueStore
    {
        if (Crud::PAGE_NEW === $responseParameters->get('pageName')) {
            $responseParameters->set('preview_url', $this->adminUrlGenerator->setController(self::class)->setAction('previewSurveyHot')->generateUrl());
        }

        return $responseParameters;
    }

    public function createEntity(string $entityFqcn)
    {
        $entity = parent::createEntity($entityFqcn);
        /** @var User $user */
        $user = $this->getUser();
        $userActivities = $this->getUserActivities();
        $entity->setActivity($userActivities[0]);

        return $entity;
    }

    public function createIndexQueryBuilder(SearchDto $searchDto, EntityDto $entityDto, FieldCollection $fields, FilterCollection $filters): QueryBuilder
    {
        $queryBuilder = parent::createIndexQueryBuilder($searchDto, $entityDto, $fields, $filters);

        if (!$this->isGranted('ROLE_ADMIN')) {
            /** @var User $user */
            $user = $this->getUser();
            if ($user->isManager()) {
                $queryBuilder
                    ->leftJoin('entity.author', 'author')
                    ->andWhere('author.manager = :manager')
                    ->setParameter('manager', $user->getId())
                ;
            } elseif ($user->isFormer()) {
                $queryBuilder
                    ->andWhere('entity.author = :author')
                    ->setParameter('author', $user->getId());
            }
        }

        if (isset($searchDto->getSort()['participantSurveyHotCompletedCount'])) {
            $queryBuilder
                ->addSelect('count(CASE WHEN participant.completedHot = true THEN 1 ELSE :null END) AS HIDDEN participantCount')
                ->leftJoin('entity.participants', 'participant')
                ->setParameter('null', null)
                ->orderBy('participantCount', $searchDto->getSort()['participantSurveyHotCompletedCount'])
                ->groupBy('entity.id')
            ;
        }

        return $queryBuilder;
    }

    public function createNewFormBuilder(EntityDto $entityDto, KeyValueStore $formOptions, AdminContext $context): FormBuilderInterface
    {
        $formBuilder = parent::createNewFormBuilder($entityDto, $formOptions, $context);

        return $this->addAuthorListener($formBuilder);
    }

    private function addAuthorListener(FormBuilderInterface $formBuilder): FormBuilderInterface
    {
        return $formBuilder->addEventListener(FormEvents::POST_SUBMIT, function (FormEvent $event) {
            $form = $event->getForm();
            if (!$form->isValid()) {
                return;
            }

            /** @var Survey $survey */
            $survey = $form->getData();

            $survey->setAuthor($this->getUser());
        });
    }

    #[Route('/admin/survey/qr-code', name: 'admin_survey_qr_code')]
    public function qrCode(
        Request $request,
        AdminContext $context,
        RouterInterface $router,
    ): Response {
        /** @var Survey $survey */
        $survey = $context->getEntity()->getInstance();

        if (!$this->isGranted(Permission::EA_EXECUTE_ACTION, ['action' => 'qrCode', 'entity' => $context->getEntity(), 'entityFqcn' => $context->getEntity()->getFqcn()])) {
            throw new ForbiddenActionException($context);
        }

        return $this->render('admin/survey_qr_code.html.twig', [
            'survey' => $survey,
            'url' => $router->generate('app_survey_hot_index', [
                'id' => $survey->getId(),
                'token' => $survey->getToken(),
            ], UrlGeneratorInterface::ABSOLUTE_URL),
        ]);
    }

    #[Route('/admin/survey/preview-survey-hot', name: 'admin_survey_preview_hot_index')]
    public function previewSurveyHot(
        ?AdminContext $context = null,
    ): Response {
        return $this->previewSurvey('hot', $context);
    }

    #[Route('/admin/survey/preview-survey-cold', name: 'admin_survey_preview_cold_index')]
    public function previewSurveyCold(
        ?AdminContext $context = null,
    ): Response {
        return $this->previewSurvey('cold', $context);
    }

    private function previewSurvey(string $type, ?AdminContext $context = null): Response
    {
        $parameters = [
            'type' => $type,
            'maxSteps' => 10,
            'back_url' => $this->adminUrlGenerator->setController(self::class)->setAction(Action::INDEX)->generateUrl(),
            'hot_url' => $this->adminUrlGenerator->setController(self::class)->setAction('previewSurveyHot')->generateUrl(),
            'cold_url' => $this->adminUrlGenerator->setController(self::class)->setAction('previewSurveyCold')->generateUrl(),
        ];

        if (null === $context) {
            return $this->render('admin/survey_preview.html.twig', [
                'survey' => new Survey()->setLabel(''),
                ...$parameters,
            ]);
        }

        /** @var Survey $survey */
        $survey = $context->getEntity()->getInstance();

        if (!$this->isGranted(Permission::EA_EXECUTE_ACTION, ['action' => 'hot' === $type ? 'previewSurveyHot' : 'previewSurveyCold', 'entity' => $context->getEntity(), 'entityFqcn' => $context->getEntity()->getFqcn()])) {
            throw new ForbiddenActionException($context);
        }

        return $this->render('admin/survey_preview.html.twig', [
            'survey' => $survey,
            ...$parameters,
        ]);
    }

    /**
     * @return Activity[]|array
     */
    private function getUserActivities(): array
    {
        /** @var User $user */
        $user = $this->getUser();

        return $this->isGranted('ROLE_ADMIN') ? Activity::cases() : array_map(fn ($a) => Activity::from($a), $user->getActivities());
    }
}
