<?php

namespace App\Controller\Admin;

use App\Entity\Participant;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;

class ParticipantCrudController extends AbstractCrudController
{
    public static function getEntityFqcn(): string
    {
        return Participant::class;
    }

    public function configureFields(string $pageName): iterable
    {
        return [
            TextField::new('firstName', 'Prénom'),
            TextField::new('lastName', 'Nom'),
            TextField::new('email', 'Email'),
            AssociationField::new('survey', 'Enquête'),
            BooleanField::new('completedHot', 'Enquête à chaud')
                ->renderAsSwitch(false)
                ->setTemplatePath('admin/field/survey_link.html.twig')
                ->onlyOnIndex(),
            BooleanField::new('completedCold', 'Enquête à froid')
                ->renderAsSwitch(false)
                ->setTemplatePath('admin/field/survey_link.html.twig')
                ->onlyOnIndex(),
        ];
    }

    public function configureCrud(Crud $crud): Crud
    {
        return parent::configureCrud($crud)
        ->setEntityLabelInSingular('Participant')
        ->setEntityLabelInPlural('Participants')
        ->showEntityActionsInlined()
        ->setSearchFields(['survey.identifier', 'survey.label', 'firstName', 'lastName', 'email']);
    }

    public function configureActions(Actions $actions): Actions
    {
        $actions->remove(Crud::PAGE_INDEX, Action::EDIT);
        $actions->remove(Crud::PAGE_INDEX, Action::NEW);

        $actions->setPermissions([
            Action::INDEX => 'ROLE_SUPER_ADMIN',
            Action::DELETE => 'ROLE_SUPER_ADMIN',
        ]);

        return parent::configureActions($actions);
    }
}
