<?php

namespace App\Controller\Admin;

use App\Controller\Admin\Filters\UserActivityFilter;
use App\Entity\User;
use App\Enum\Activity;
use App\Service\UserRegistrationHandler;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use EasyCorp\Bundle\EasyAdminBundle\Collection\FieldCollection;
use EasyCorp\Bundle\EasyAdminBundle\Collection\FilterCollection;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\Filters;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Context\AdminContext;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Dto\EntityDto;
use EasyCorp\Bundle\EasyAdminBundle\Dto\SearchDto;
use EasyCorp\Bundle\EasyAdminBundle\Exception\ForbiddenActionException;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Filter\BooleanFilter;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use EasyCorp\Bundle\EasyAdminBundle\Security\Permission;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;

class FormerCrudController extends AbstractCrudController
{
    public function __construct(
        protected AdminUrlGenerator $adminUrlGenerator,
        protected UserRegistrationHandler $userRegistrationHandler,
        protected TranslatorInterface $translator,
    ) {
    }

    public static function getEntityFqcn(): string
    {
        return User::class;
    }

    public function configureFields(string $pageName): iterable
    {
        return [
            TextField::new('email', 'Email')
                ->setFormType(EmailType::class)->setDisabled('edit' === $pageName),
            TextField::new('firstName', 'Prénom'),
            TextField::new('lastName', 'Nom'),
            ChoiceField::new('activities', 'Activités')
                ->setFormTypeOptions([
                    'required' => true,
                    'choices' => Activity::values(),
                    'choice_label' => function ($value) {
                        return Activity::from($value)->trans($this->translator);
                    },
                ])
                ->formatValue(function ($value, ?User $entity) {
                    return join('<br>', array_map(fn ($activity) => Activity::from($activity)->trans($this->translator), $entity->getActivities()));
                })
                ->allowMultipleChoices(),

            BooleanField::new('confirmed', 'Confirmé')->renderAsSwitch(false)->onlyOnIndex(),
            AssociationField::new('manager', 'Gestionnaire')
                ->onlyOnForms()
                ->setPermission('ROLE_ADMIN')
                ->setRequired(true)
                ->setQueryBuilder(
                    function (QueryBuilder $queryBuilder) {
                        return $queryBuilder
                            ->where('entity.role = :role')
                            ->setParameter('role', 'ROLE_MANAGER');
                    }
                ),
        ];
    }

    public function configureFilters(Filters $filters): Filters
    {
        return $filters
            ->add('email')
            ->add('firstName')
            ->add('lastName')
            ->add(BooleanFilter::new('confirmed', 'Confirmé'))
            ->add(BooleanFilter::new('enabled', 'Activé'))
            ->add(
                UserActivityFilter::new('activities', 'Activités')
                    ->setChoices(Activity::values())
                    ->canSelectMultiple()
                    ->setFormTypeOptions([
                        'value_type_options.choice_label' => function ($value) {
                            return Activity::from($value)->trans($this->translator);
                        },
                    ]),
            )
        ;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return parent::configureCrud($crud)
            ->setEntityLabelInSingular('Formateur')
            ->setEntityLabelInPlural('Formateurs')
            ->showEntityActionsInlined()
            ->setSearchFields(['email', 'firstName', 'lastName'])
            ->setPageTitle(Crud::PAGE_INDEX, 'Formateurs')
            ->setPageTitle(Crud::PAGE_NEW, 'Créer un formateur')
            ->setPageTitle(Crud::PAGE_EDIT, 'Modifier un formateur')
            ->setDefaultSort(['enabled' => 'desc'])
            ->setFormOptions(['attr' => ['novalidate' => 'novalidate']], ['attr' => ['novalidate' => 'novalidate']])
        ;
    }

    public function configureActions(Actions $actions): Actions
    {
        $actions->update(Crud::PAGE_INDEX, Action::NEW, function ($action) {
            return $action->setLabel('Ajouter un formateur');
        });

        $actions->update(Crud::PAGE_INDEX, Action::EDIT, function ($action) {
            return $action
                ->setHtmlAttributes(['title' => 'Modifier le formateur'])
                ->addCssClass('btn btn-secondary')
                ->setLabel('')
                ->setIcon('fa fa-edit')
            ;
        });

        $actions->update(Crud::PAGE_INDEX, Action::DELETE, function ($action) {
            return $action
                ->setHtmlAttributes(['title' => 'Supprimer le formateur'])
                ->addCssClass('btn btn-danger text-white')
                ->setLabel('')
                ->setIcon('fa fa-trash');
        });

        $impersonate = Action::new('impersonate', '')
            ->setHtmlAttributes(['title' => 'Simuler l\'utilisateur'])
            ->addCssClass('btn btn-secondary')
            ->setIcon('fa fa-user')
            ->linkToUrl(function (User $user): string {
                return $this->adminUrlGenerator
                    ->setDashboard(DashboardController::class)
                    ->setRoute('admin_dashboard')
                    ->set('_switch_user', $user->getEmail())
                    ->generateUrl()
                ;
            });

        $actions->add(Crud::PAGE_INDEX, $impersonate);

        $resendConfirmationEmail = Action::new('resendConfirmationEmail', '')
            ->setHtmlAttributes(['title' => 'Envoyer l\'email de confirmation à nouveau'])
            ->addCssClass('btn btn-secondary')
            ->setIcon('fa fa-envelope')
            ->linkToCrudAction('resendConfirmationEmail')
        ;

        $actions->add(Crud::PAGE_INDEX, $resendConfirmationEmail);

        $enableAction = Action::new('enableFormer', '')
            ->setHtmlAttributes([
                'title' => 'Réactiver le compte formateur',
                'onclick' => 'return confirm("Souhaitez-vous vraiment réactiver ce compte Formateur ?")',
            ])
            ->addCssClass('btn btn-secondary')
            ->setIcon('fa fa-user-xmark')
            ->linkToCrudAction('enableFormer')
        ;

        $actions->add(Crud::PAGE_INDEX, $enableAction);

        $disableAction = Action::new('disableFormer', '')
            ->setHtmlAttributes([
                'title' => 'Désactiver le compte formateur',
                'onclick' => 'return confirm("Souhaitez-vous désactiver ce compte Formateur ?")',
            ])
            ->addCssClass('btn btn-secondary')
            ->setIcon('fa fa-user-check')
            ->linkToCrudAction('disableFormer')
        ;

        $actions->add(Crud::PAGE_INDEX, $disableAction);

        $isManager = 'auth_checker.isGranted("ROLE_MANAGER")';
        $isFormerManagerOrAdmin = '(subject.getManager() == user or auth_checker.isGranted("ROLE_ADMIN"))';

        $actions->setPermissions([
            Action::INDEX => 'ROLE_MANAGER',
            Action::NEW => 'ROLE_MANAGER',
            Action::EDIT => new Expression("{$isManager} and subject.isEnabled() and {$isFormerManagerOrAdmin}"),
            Action::DELETE => new Expression("{$isManager} and {$isFormerManagerOrAdmin}"),
            'impersonate' => 'ROLE_SUPER_ADMIN',
            'enableFormer' => new Expression("{$isManager} and subject.isConfirmed() and not subject.isEnabled() and {$isFormerManagerOrAdmin}"),
            'disableFormer' => new Expression("{$isManager} and subject.isConfirmed() and subject.isEnabled() and {$isFormerManagerOrAdmin}"),
            'resendConfirmationEmail' => new Expression("{$isManager} and not subject.isConfirmed() and subject.isEnabled() and {$isFormerManagerOrAdmin}"),
        ]);

        $actions->disable(Action::BATCH_DELETE, Action::SAVE_AND_ADD_ANOTHER, Action::SAVE_AND_CONTINUE, Action::DETAIL);

        return parent::configureActions($actions);
    }

    public function createEntity(string $entityFqcn)
    {
        $entity = parent::createEntity($entityFqcn);
        /** @var User $user */
        $user = $this->getUser();
        $entity->setManager($user);

        return $entity;
    }

    public function createIndexQueryBuilder(SearchDto $searchDto, EntityDto $entityDto, FieldCollection $fields, FilterCollection $filters): QueryBuilder
    {
        $queryBuilder = parent::createIndexQueryBuilder($searchDto, $entityDto, $fields, $filters);

        $queryBuilder
            ->andWhere('entity.role LIKE :role')
            ->setParameter('role', 'ROLE_FORMER')
        ;

        /** @var User $user */
        $user = $this->getUser();
        if ($user->isManager()) {
            $queryBuilder
                ->andWhere('entity.manager = :manager')
                ->setParameter('manager', $user->getId())
            ;
        }

        return $queryBuilder;
    }

    public function createNewFormBuilder(EntityDto $entityDto, KeyValueStore $formOptions, AdminContext $context): FormBuilderInterface
    {
        $formBuilder = parent::createNewFormBuilder($entityDto, $formOptions, $context);

        return $this->addSendEmailListener($formBuilder);
    }

    private function addSendEmailListener(FormBuilderInterface $formBuilder): FormBuilderInterface
    {
        return $formBuilder->addEventListener(FormEvents::POST_SUBMIT, function (FormEvent $event) {
            $form = $event->getForm();
            if (!$form->isValid()) {
                return;
            }

            /** @var User $user */
            $user = $form->getData();

            $this->userRegistrationHandler->startRegistration($user);
        });
    }

    public function resendConfirmationEmail(
        AdminContext $context,
        AdminUrlGenerator $adminUrlGenerator,
        EntityManagerInterface $entityManager,
    ): Response {
        if (!$this->isGranted(Permission::EA_EXECUTE_ACTION, ['action' => 'resendConfirmationEmail', 'entity' => $context->getEntity(), 'entityFqcn' => $context->getEntity()->getFqcn()])) {
            throw new ForbiddenActionException($context);
        }

        /** @var User $user */
        $user = $context->getEntity()->getInstance();
        $this->userRegistrationHandler->startRegistration($user);
        $entityManager->persist($user);
        $entityManager->flush();
        $this->addFlash('success', 'Un email de relance a été envoyé au formateur');

        return $this->redirect($adminUrlGenerator->setController(self::class)->setAction(Action::INDEX)->generateUrl());
    }

    public function enableFormer(
        AdminContext $context,
        AdminUrlGenerator $adminUrlGenerator,
        EntityManagerInterface $entityManager,
    ): Response {
        if (!$this->isGranted(Permission::EA_EXECUTE_ACTION, ['action' => 'enableFormer', 'entity' => $context->getEntity(), 'entityFqcn' => $context->getEntity()->getFqcn()])) {
            throw new ForbiddenActionException($context);
        }

        /** @var User $user */
        $user = $context->getEntity()->getInstance();
        $user->setEnabled(true);

        $entityManager->persist($user);
        $entityManager->flush();

        $this->addFlash('success', 'Le compte formateur a été réactivé avec succès.');

        return $this->redirect($adminUrlGenerator->setController(self::class)->setAction(Action::INDEX)->generateUrl());
    }

    public function disableFormer(
        AdminContext $context,
        AdminUrlGenerator $adminUrlGenerator,
        EntityManagerInterface $entityManager,
    ): Response {
        if (!$this->isGranted(Permission::EA_EXECUTE_ACTION, ['action' => 'disableFormer', 'entity' => $context->getEntity(), 'entityFqcn' => $context->getEntity()->getFqcn()])) {
            throw new ForbiddenActionException($context);
        }

        /** @var User $user */
        $user = $context->getEntity()->getInstance();
        $user->setEnabled(false);

        $entityManager->persist($user);
        $entityManager->flush();

        $this->addFlash('success', 'Le compte formateur a été désactivé avec succès. Vous pouvez le réactiver à tout moment.');

        return $this->redirect($adminUrlGenerator->setController(self::class)->setAction(Action::INDEX)->generateUrl());
    }

    /**
     * @return Activity[]|array
     */
    private function getUserActivities(): array
    {
        /** @var User $user */
        $user = $this->getUser();

        return $this->isGranted('ROLE_SUPER_ADMIN') ? Activity::cases() : array_map(fn ($a) => Activity::from($a), $user->getActivities());
    }
}
