<?php

namespace App\Controller;

use App\Config\Model\AssistantConfig;
use App\Prompt\PromptBuilder;
use App\Service\OrchestrationApi\Dto\InvokeRequest;
use App\Service\OrchestrationApi\Dto\StreamRequest;
use App\Service\OrchestrationApi\Dto\TokenEvent;
use App\Service\OrchestrationApi\Dto\TranscriptRequest;
use App\Service\OrchestrationApi\OrchestrationApiClient;
use Aws\S3\S3Client;
use League\Flysystem\FilesystemException;
use League\Flysystem\FilesystemOperator;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\HttpKernel\Attribute\MapUploadedFile;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Uid\Uuid;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

#[Route('/api', name: 'app_api_')]
class ApiController extends AbstractController
{
    public function __construct(
        private readonly OrchestrationApiClient $orchestrationApiClient,
    ) {
    }

    #[Route('/chat', name: 'chat', methods: ['POST'])]
    public function chat(
        #[MapRequestPayload] InvokeRequest $invokeRequest,
    ): JsonResponse {
        try {
            $response = $this->orchestrationApiClient->chat($invokeRequest);

            return $this->json([
                'id' => $response->getId(),
                'output' => $response->getOutput(),
                'input_tokens' => $response->getInputTokens(),
                'output_tokens' => $response->getOutputTokens(),
                'time_elapsed' => $response->getTimeElapsed(),
            ]);
        } catch (\Exception $e) {
            return $this->json(['error' => $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/{assistantId}/stream', name: 'stream', methods: ['POST'])]
    public function streamResponse(
        AssistantConfig $assistantConfig,
        #[MapRequestPayload] StreamRequest $streamRequest,
        PromptBuilder $promptBuilder,
    ): StreamedResponse {
        $invokeRequest = new InvokeRequest(
            model_id: 'gpt-4o-mini',
            prompt: $promptBuilder->__invoke($assistantConfig, $streamRequest->assistantParams),
            temperature: 0.0,
            max_tokens: 2048,
        );

        return new StreamedResponse(function () use ($invokeRequest) {
            try {
                $stream = $this->orchestrationApiClient->stream($invokeRequest);

                foreach ($stream as $chunk) {
                    $event = $this->orchestrationApiClient->processStreamChunk($chunk);

                    if (null !== $event) {
                        if ($event instanceof TokenEvent) {
                            echo json_encode([
                                'status' => $event->getStatus(),
                                'created_at' => $event->getCreatedAt()->format(\DateTimeInterface::ATOM),
                                'token' => $event->getToken(),
                            ])."\n";
                        } else {
                            // For other event types, we'd need similar handling
                            echo json_encode([
                                'status' => $event->getStatus(),
                                'created_at' => $event->getCreatedAt()->format(\DateTimeInterface::ATOM),
                                // Add other properties based on event type
                            ])."\n";
                        }
                        flush();
                    }
                }
            } catch (\Exception $e) {
                echo json_encode(['error' => $e->getMessage()])."\n";
                flush();
            }
        }, Response::HTTP_OK, [
            'Content-Type' => 'application/x-ndjson',
            'Cache-Control' => 'no-cache',
            'X-Accel-Buffering' => 'no',
        ]);
    }

    #[Route('/{assistantId}/transcript', name: 'transcript', methods: ['POST'])]
    public function transcript(
        AssistantConfig $assistantConfig,
        #[MapRequestPayload] TranscriptRequest $transcriptRequest,
        OrchestrationApiClient $orchestrationApiClient,
    ): JsonResponse {
        try {
            $response = $orchestrationApiClient->transcript($transcriptRequest);

            return $this->json([
                'text' => $response->text,
            ]);
        } catch (ClientExceptionInterface|DecodingExceptionInterface|RedirectionExceptionInterface|ServerExceptionInterface|TransportExceptionInterface $e) {
            $errorContent = $e->getResponse() ? $e->getResponse()->getContent(false) : $e->getMessage();

            return $this->json(['error' => $errorContent], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/{assistantId}/file-upload', name: 'file_upload', methods: ['POST'])]
    public function fileUpload(
        AssistantConfig $assistantConfig,
        #[MapUploadedFile] UploadedFile $file,
        FilesystemOperator $bucketStorage,
        S3Client $s3Client,
        #[Autowire(env: 'MINIO_BUCKET_NAME')] string $bucketName,
    ) {
        // TODO mettre en place un service charger de faire des assertions sur le fichier
        // en fonction de la config de l'assistant

        $targetPath = Uuid::v7()->toRfc4122().'.'.$file->getClientOriginalExtension();
        try {
            $bucketStorage->write($targetPath, $file->getContent());
        } catch (FilesystemException $e) {
            return new JsonResponse(['error' => $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
        $bucketStorage->publicUrl($targetPath);
        $publicUri = $s3Client->createPresignedRequest($s3Client->getCommand('GetObject', [
            'Bucket' => $bucketName,
            'Key' => $targetPath,
        ]), '+10 minutes')->getUri();

        return new JsonResponse([
            'path' => $publicUri,
        ]);
    }
}
