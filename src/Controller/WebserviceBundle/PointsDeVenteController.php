<?php

namespace App\Controller\WebserviceBundle;

use App\Services\WebserviceBundle\WebservicePointsDeVente;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Routing\Annotation\Route;

#[Route(path: '/points-de-vente')]
class PointsDeVenteController extends AbstractWebserviceController
{
	public function __construct(private WebservicePointsDeVente $pointsDeVente) {}

	#[Route(path: '/changerNumeroPointDeVente', name: 'aquitem_webservice_changerNumeroPointDeVente', options: ['expose' => true])]
    public function changerNumeroPointDeVenteAction(Request $request): JsonResponse
	{
		return $this->returnJson($this->pointsDeVente->changerNumeroPointDeVente($request->request->all()));
	}

	#[Route(path: '/creerPointDeVente', name: 'aquitem_webservice_creerPointDeVente', options: ['expose' => true])]
    public function creerPointDeVenteAction(Request $request): JsonResponse
	{
		return $this->returnJson($this->pointsDeVente->creerPointDeVente($request->request->all()));
	}

	#[Route(path: '/fermerPointDeVente', name: 'aquitem_webservice_fermerPointDeVente', options: ['expose' => true])]
    public function fermerPointDeVenteAction(Request $request): JsonResponse
	{
		return $this->returnJson($this->pointsDeVente->fermerPointDeVente($request->request->all()));
	}

	#[Route(path: '/modifierPointDeVente', name: 'aquitem_webservice_modifierPointDeVente', options: ['expose' => true])]
    public function modifierPointDeVenteAction(Request $request): JsonResponse
	{
		return $this->returnJson($this->pointsDeVente->modifierPointDeVente($request->request->all()));
	}

	#[Route(path: '/export/{idSession}', name: 'aquitem_webservice_exporterPointDeVente', options: ['expose' => true])]
	public function exporterPointDeVenteAction(Request $request, $idSession = 0): Response
	{
		$session = $request->getSession();
		$parameters = [
			'idsCriteres' => [],
			'valeurs' => [],
			'typesDonnees' => [],
			'colsDonnees' => []
		];
		if($idSession) {
			$pdvSession = $session->get($this->pointsDeVente::NOM_SESSION)[$idSession];
			$parameters['idsCriteres'] = $pdvSession['form']['idsCriteres'];
			$parameters['valeurs'] = $pdvSession['form']['valeurs'];
			$parameters['typesDonnees'] = $pdvSession['form']['typesDonnees'];
			$parameters['colsDonnees'] = $pdvSession['form']['colsDonnees'];
		}
		$response = new Response();
		$response->headers->set('Content-Type', 'application/vnd.ms-excel');
		$now = new \DateTime();
		$disposition = $response->headers->makeDisposition(
			ResponseHeaderBag::DISPOSITION_INLINE,
			'points_de_vente_'.$now->format('d').'_'.$now->format('m').'_'.$now->format('Y').'_'.$now->getTimestamp().'.xlsx'
		);
		$response->headers->set('Content-Disposition', $disposition);
		$response->setContent($this->pointsDeVente->exporterPointDeVente($parameters));

		return $response;
	}
}
