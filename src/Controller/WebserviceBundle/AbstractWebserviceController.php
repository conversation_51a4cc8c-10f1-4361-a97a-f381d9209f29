<?php

namespace App\Controller\WebserviceBundle;

use App\Entity\WebserviceBundle\WebserviceResponse;
use App\Services\WebserviceBundle\ResponseFactory;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Psr\Container\ContainerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Contracts\Service\Attribute\Required;

abstract class AbstractWebserviceController extends AbstractController
{
    #[Required]
    public ResponseFactory $responseFactory;

    //DEUXIEME OPTION A LA PLACE DE REQUIRED SUR L'ATRIBUT ResponseFactory :
    // #[Required]
    // public function setResponseFactory(ResponseFactory $responseFactory)
    // {
    //     $this->responseFactory = $responseFactory;
    // }

    public function setContainer(ContainerInterface $container): ?ContainerInterface
    {
        $previous = parent::setContainer($container);
        return $previous;
    }


    /**
     * @param WebserviceResponse $response
     * @return JsonResponse
     */
    public function returnJson(WebserviceResponse $response)
    {
        return $this->responseFactory->createJsonResponse($response);
    }
}
