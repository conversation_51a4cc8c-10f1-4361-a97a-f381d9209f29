<?php


namespace App\Controller\WebserviceBundle;

use App\Services\WebserviceBundle\WebservicePortailEnseigne;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\Request;

#[Route("/api/module-client")]
class PortailEnseigneController extends AbstractWebserviceController
{
    public function __construct(private WebservicePortailEnseigne $portailEnseigne) {}

	#[Route("/listerCriteresRecherche", name: "aquitem_webservice_listerCriteresRecherche", options: ["expose" => true])]
    public function listerCriteresRechercheAction(): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->listerCriteresRecherche());
    }

	#[Route("/rechercherClient", name: "aquitem_webservice_rechercherClient", options: ["expose" => true])]
    public function rechercherClientAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->rechercherClient($request->request->all()));
    }

	#[Route("/afficherEnteteClient", name: "aquitem_webservice_afficherEnteteClient", options: ["expose" => true])]
    public function afficherEnteteClientAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->afficherEnteteClient($request->request->all()));
    }

	#[Route("/afficherDonneesClient", name: "aquitem_webservice_afficherDonneesClient", options: ["expose" => true])]
    public function afficherDonneesClientAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->afficherDonneesClient($request->request->all()));
    }

	#[Route("/afficherFidelisationClient", name: "aquitem_webservice_afficherFidelisationClient", options: ["expose" => true])]
    public function afficherFidelisationClientAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->afficherFidelisationClient($request->request->all()));
    }

	#[Route("/listerFiltresFidelisationClient", name: "aquitem_webservice_listerFiltresFidelisationClient", options: ["expose" => true])]
    public function listerFiltresFidelisationClientAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->listerFiltresFidelisationClient($request->request->all()));
    }

	#[Route("/modifierFicheClient", name: "aquitem_webservice_modifierFicheClient", options: ["expose" => true])]
    public function modifierFicheClientAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->modifierFicheClient($request->request->all()));
    }

	#[Route("/listerMagasins", name: "aquitem_webservice_listerMagasins", options: ["expose" => true])]
    public function listerMagasinsAction(): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->listerMagasins());
    }

	#[Route("/verifierCodeCarteClient", name: "aquitem_webservice_verifierCodeCarteClient", options: ["expose" => true])]
    public function verifierCodeCarteClientAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->verifierCodeCarteClient($request->request->all()));
    }

	#[Route("/fusionnerClient", name: "aquitem_webservice_fusionnerClient", options: ["expose" => true])]
    public function fusionnerClientAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->fusionnerClient($request->request->all()));
    }

	#[Route("/afficherDonneesClientPourFusion", name: "aquitem_webservice_afficherDonneesClientPourFusion", options: ["expose" => true])]
    public function afficherDonneesClientPourFusionAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->afficherDonneesClientPourFusion($request->request->all()));
    }

	#[Route("/recupererIdClient", name: "aquitem_webservice_recupererIdClient", options: ["expose" => true])]
    public function recupererIdClientAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->recupererIdClient($request->request->all()));
    }

	#[Route("/afficherDetailTicket", name: "aquitem_webservice_afficherDetailTicket", options: ["expose" => true])]
    public function afficherDetailTicketAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->afficherDetailTicket($request->request->all()));
    }

	#[Route("/creerClient", name: "aquitem_webservice_creerClient", options: ["expose" => true])]
    public function creerClientAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->creerClient($request->request->all()));
    }

	#[Route("/saisieNouveauClient", name: "aquitem_webservice_saisieNouveauClient", options: ["expose" => true])]
    public function saisieNouveauClientAction(): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->saisieNouveauClient());
    }

	#[Route("/ajouterBonus", name: "aquitem_webservice_ajouterBonus", options: ["expose" => true])]
    public function ajouterBonusAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->ajouterBonus($request->request->all()));
    }

	#[Route("/ajouterBonusViaCodeCarte", name: "aquitem_webservice_ajouterBonusViaCodeCarte", options: ["expose" => true])]
    public function ajouterBonusViaCodeCarteAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->ajouterBonusViaCodeCarte($request->request->all()));
    }

	#[Route("/ajouterVente", name: "aquitem_webservice_ajouterVente", options: ["expose" => true])]
    public function ajouterVenteAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->ajouterVente($request->request->all()));
    }

	#[Route("/ajouterVenteViaCodeCarte", name: "aquitem_webservice_ajouterVenteViaCodeCarte", options: ["expose" => true])]
    public function ajouterVenteViaCodeCarteAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->ajouterVenteViaCodeCarte($request->request->all()));
    }

	#[Route("/ajouterRetourProduitViaCodeCarte", name: "aquitem_webservice_ajouterRetourProduitViaCodeCarte", options: ["expose" => true])]
    public function ajouterRetourProduitViaCodeCarteAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->ajouterRetourProduitViaCodeCarte($request->request->all()));
    }

	#[Route("/ajouterRetourProduit", name: "aquitem_webservice_ajouterRetourProduit", options: ["expose" => true])]
    public function ajouterRetourProduitVia(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->ajouterRetourProduit($request->request->all()));
    }

	#[Route("/listerFamillesDeProduits", name: "aquitem_webservice_listerFamillesDeProduits", options: ["expose" => true])]
    public function listerFamillesDeProduitsAction(): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->listerFamillesDeProduits());
    }

	#[Route("/listerMotifsBonus", name: "aquitem_webservice_listerMotifsBonus", options: ["expose" => true])]
    public function listerMotifsBonusAction(): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->listerMotifsBonus());
    }

	#[Route("/verifierCodeCheque", name: "aquitem_webservice_verifierCodeCheque", options: ["expose" => true])]
    public function verifierCodeChequeAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->verifierCodeCheque($request->request->all()));
    }

	#[Route("/listerMotifsRetranchementBonus", name: "aquitem_webservice_listerMotifsRetranchementBonus", options: ["expose" => true])]
    public function listerMotifsRetranchementBonusAction(): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->listerMotifsRetranchementBonus());
    }

	#[Route("/retrancherBonus", name: "aquitem_webservice_retrancherBonus", options: ["expose" => true])]
    public function retrancherBonusAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->retrancherBonus($request->request->all()));
    }

	#[Route("/retrancherBonusViaCodeCarte", name: "aquitem_webservice_retrancherBonusViaCodeCarte", options: ["expose" => true])]
    public function retrancherBonusViaCodeCarteAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->retrancherBonusViaCodeCarte($request->request->all()));
    }

	#[Route("/creerEtFusionnerClient", name: "aquitem_webservice_creerEtFusionnerClient", options: ["expose" => true])]
    public function creerEtFusionnerClientAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->creerEtFusionnerClient($request->request->all()));
    }

	#[Route("/modifierEtFusionnerFicheClient", name: "aquitem_webservice_modifierEtFusionnerFicheClient", options: ["expose" => true])]
    public function modifierEtFusionnerFicheClientAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->modifierEtFusionnerFicheClient($request->request->all()));
    }

	#[Route("/anonymiserClient", name: "aquitem_webservice_anonymiserClient", options: ["expose" => true])]
    public function anonymiserClientAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->portailEnseigne->anonymiserClient($request->request->all()));
    }
}
