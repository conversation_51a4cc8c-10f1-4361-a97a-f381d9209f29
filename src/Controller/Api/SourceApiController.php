<?php

namespace App\Controller\Api;

use App\Entity\User;
use App\Service\ElasticIndexManager;
use App\Entity\Server;
use App\Entity\Source;
use App\Service\CsvToElasticService;
use App\Service\DataElasticsearchReader;
use App\Entity\Groupe;
use App\Entity\Visualisation;
use Doctrine\ORM\EntityManagerInterface;
use PhpOffice\PhpSpreadsheet\Calculation\Functions;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route(path: '/api/source')]
class SourceApiController extends AbstractController
{
    #[Route(path: '/get-mapping', name: 'api_source_get_mapping', options: ['expose' => true], condition: 'request.isXMLHttpRequest()')]
    public function getMapping(#[MapQueryString] GetMappingDto $query, EntityManagerInterface $entityManager, DataElasticsearchReader $reader): JsonResponse
    {
        if ($query->type === "elasticsearch" || $query->type === "request") {
            $server = $entityManager->getRepository(Server::class)->find($query->server);
            $groupe = $entityManager->getRepository(Groupe::class)->find($query->groupe);

            $this->denyAccessUnlessGranted('belongs', $groupe);

            $mapping = $reader->getMapping([
                "hosts" => $server->getHosts(),
                "esParameters" => $groupe->getClient()->getEsParameters(),
                "index" => $query->index,
            ]);
        } else if ($query->type === "csv") {
            $mapping = file_get_contents($query->url);
            $lines = explode(PHP_EOL, $mapping);
            $mapping = $lines[0];
        }

        return new JsonResponse($mapping);
    }

    #[Route(path: '/get-indexes/{server}/{groupe}', name: 'api_source_get_indexes', options: ['expose' => true])]
    public function getIndexList(Server $server, Groupe $groupe): JsonResponse
    {
        $this->denyAccessUnlessGranted('belongs', $groupe);
        $response = new JsonResponse();

        $indexManager = ElasticIndexManager::fromServerAndGroupe($server, $groupe);
        $content = $indexManager->getIndexesList();

        if ($content === FALSE) {
            $response->setStatusCode(Response::HTTP_OK);
            $response->setData("error");
        } else {
            $indexes = [];
            $content = json_decode($content, true);
            foreach ($content as $key => $index) {
                if ($key[0] !== ".") {
                    $indexes[$key] = $index;
                }
            }

            ksort($indexes);
            $response->setStatusCode(Response::HTTP_OK);
            $response->setData($indexes);
        }

        return $response;
    }

    #[Route(path: '/get-json/{source}', name: 'api_source_get_json', options: ['expose' => true])]
    public function getJsonSource(Source $source, SerializerInterface $serializer): Response
    {
        $this->denyAccessUnlessGranted('view', $source);
        return new JsonResponse($serializer->serialize($source, 'json', array("groups" => "Default")), Response::HTTP_OK, array(), true);
    }

    #[Route(path: '/create-index/{server}/{groupe}', name: 'api_source_create_index', options: ['expose' => true], condition: 'request.isXMLHttpRequest()')]
    public function createIndex(#[MapRequestPayload] CreateIndexDto $payload, Server $server, Groupe $groupe): JsonResponse
    {
        $this->denyAccessUnlessGranted('belongs', $groupe);

        /** @var ElasticIndexManager $indexManager */
        $indexManager = ElasticIndexManager::fromServerAndGroupe($server, $groupe);

        if (!$groupe->getCanUploadCsv()) {
            $response = new JsonResponse(['error' => "Vous n'avez pas les droits nécessaires"]);
            $response->setStatusCode(Response::HTTP_BAD_REQUEST);
            return $response;
        }

        $index = strtolower(str_replace(' ', '_', sprintf("%s_%s_%s", $groupe->getClient()->getPrefix(), $this->getUser()->getUsername(), $payload->index)));

        if (!$indexManager->indexExist($index)) { // Si l'index n'existe pas on le créé
            $response = $indexManager->createIndex($index, '_doc', $payload->fields);
        } else {
            $response = $indexManager->putMapping($index, '_doc', $payload->fields);
        }

        return new JsonResponse([
            'elastic_response' => $response,
            'index' => $index,
        ]);
    }

    #[Route(path: '/delete-index/{server}/{groupe}', name: 'api_source_delete_index', options: ['expose' => true], condition: 'request.isXMLHttpRequest()')]
    public function deleteIndex(#[MapRequestPayload] DeleteIndexDto $payload, Server $server, Groupe $groupe): JsonResponse
    {
        $this->denyAccessUnlessGranted('belongs', $groupe);
        $indexManager = ElasticIndexManager::fromServerAndGroupe($server, $groupe);

        $index = strtolower($payload->index);

        $response = $indexManager->deleteIndex($index);

        return new JsonResponse($response);
    }

    #[Route(path: '/add-to-index-file/{server}/{groupe}', name: 'api_source_add_to_index_file', options: ['expose' => true], condition: 'request.isXMLHttpRequest()')]
    public function addToIndexFile(
        Request $request,
        #[MapRequestPayload] AddToIndexFileDto $payload,
        CsvToElasticService $csvToElasticService,
        Server $server,
        Groupe $groupe
    ): JsonResponse
    {
        $this->denyAccessUnlessGranted('belongs', $groupe);

        $filePath = null;
        $addType = $payload->add_type;
        $mapping = json_decode($payload->mapping, true);
        $isExcel = false;

        if ($addType === "upload") {
            $filePath = $request->files->all()[0]->getRealPath();
            $fileType = IOFactory::identify($filePath);
            $isExcel = $fileType === "Xls";
            $csvContent = file_get_contents($filePath);
        } else if ($addType === "url") {
            $csvContent = file_get_contents($request->request->get('url'));
        } else if ($addType === "csv") {
            $csvContent = $request->request->get('csv');
        } else {
            throw new BadRequestHttpException("Type d'ajout non reconnu");
        }

        /** @var User $user */
        $user = $this->getUser();
        $esIndex = strtolower(str_replace(' ', '_', sprintf("%s_%s_%s", $groupe->getClient()->getPrefix(), $user->getUsername(), $payload->index)));

        if ($isExcel && $filePath) {
            $response = $csvToElasticService->excelToElasticIndex($server, $groupe, $esIndex, $filePath, $payload->spreadsheet, $mapping, $payload->header);
        } else {
            $response = $csvToElasticService->csvToElasticIndex($server, $groupe, $esIndex, $csvContent, $mapping, $payload->header, $payload->delimiter);
        }

        return new JsonResponse($response);
    }

    #[Route(path: '/get-spreadsheets', name: 'api_source_get_spreadsheets', options: ['expose' => true], condition: 'request.isXMLHttpRequest()')]
    public function getSpreadSheets(Request $request): JsonResponse
    {
        $file = $request->files->all()[0]->getRealPath();

        $fileType = IOFactory::identify($file);
        $reader = IOFactory::createReader($fileType);
        $reader->setReadDataOnly(true);
        $spreadsheet = $reader->load($file);

        return new JsonResponse($spreadsheet->getSheetNames());
    }

    #[Route(path: '/source/get-spreadsheet-data', name: 'api_source_get_spreadsheet_data', options: ['expose' => true], condition: 'request.isXMLHttpRequest()')]
    public function getSpreadSheetData(Request $request, #[MapRequestPayload] GetSpreadSheetDataDto $payload): JsonResponse
    {
        $file = $request->files->all()[0]->getRealPath();

        Functions::setReturnDateType(Functions::RETURNDATE_PHP_OBJECT);
        $fileType = IOFactory::identify($file);
        $reader = IOFactory::createReader($fileType);
        $reader->setReadDataOnly(true);

        $filterSubset = new MyReadFilter();
        $reader->setReadFilter($filterSubset);

        if ($reader instanceof Csv) {
            $reader->setDelimiter($payload->delimiter);
        }


        $spreadsheet = $reader->load($file);

        if (!($reader instanceof Csv) && $payload->spreadsheet && $payload->spreadsheet !== "null") {
            $data = $spreadsheet->getSheetByName($payload->spreadsheet)->toArray();
        } else {
            $data = $spreadsheet->getSheet(0)->toArray();
        }


        return new JsonResponse($data);
    }

    #[Route(path: '/liste-json', name: 'api_source_liste_json', options: ['expose' => true])]
    public function liste(EntityManagerInterface $entityManager): JsonResponse
    {
        $this->denyAccessUnlessGranted('list', new Source());

        return $this->listeUser($this->getUser(), $entityManager);
    }

    #[Route(path: '/liste-json/{id}', name: 'api_source_liste_json_user', options: ['expose' => true])]
    #[IsGranted('ROLE_SUPER_ADMIN')]
    public function listeUser(User $user, EntityManagerInterface $entityManager): JsonResponse
    {
        /** @var Source[] $sources */
        $sources = $entityManager->getRepository(Source::class)->findVisibles($user);
        return new JsonResponse(array_map(static function ($v) {
            return array(
                "id"   => $v->getId(),
                "name" => $v->getNom(),
            );
        }, $sources));
    }
}


/**  Define a Read Filter class implementing \PhpOffice\PhpSpreadsheet\Reader\IReadFilter  */
class MyReadFilter implements \PhpOffice\PhpSpreadsheet\Reader\IReadFilter
{
    public function readCell($columnAddress, $row, $worksheetName = '') {
        if ($row >= 1 && $row <= 11) {
            return true;
        }
        return false;
    }
}

/*** DTO utilisés par ce Controller ***/

readonly class GetMappingDto {
    public function __construct(
        public string $type,
        public int $server,
        public int $groupe,
        public string $index,
        public ?string $url = null,
    ) {}
}

readonly class CreateIndexDto {
    public function __construct(
        public string $index,
        public ?array $fields = [],
    ) {}
}

readonly class DeleteIndexDto {
    public function __construct(
        public string $index,
    ) {}
}

readonly class GetSpreadSheetDataDto {
    public function __construct(
        public string $spreadsheet,
        public string $delimiter = ',' // ";" | ","
    ) {}
}

readonly class AddToIndexFileDto {
    public function __construct(
        public int $server,
        public int $groupe,
        public string $index,
        public string $mapping, // json string
        public string $spreadsheet, // "null"
        public string $add_type, // "upload" | "url" | "csv"
        public bool $header = false,
        public string $delimiter = ',' // ";" | ","
    ) {}
}