<?php

namespace App\Controller\Api;

use App\Entity\Dashboard;
use App\Entity\Filter;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

#[Route(path: '/api/filter')]
class FilterApiController extends AbstractController
{
    private function getJsonResponse(array $data = array()): JsonResponse {
        return new JsonResponse(array(
            "status" => "OK",
            "data" => $data
        ));
    }

    #[Route(path: '/dashboard/{dashboard}', name: 'api_filter_dashboard_list', options: ['expose' => true])]
    public function liste(Dashboard $dashboard, SerializerInterface $serializer, EntityManagerInterface $entityManager): Response
    {
        $this->denyAccessUnlessGranted('view', $dashboard);
        $filters = $entityManager->getRepository(Filter::class)->findBy(array(
            "user" => $this->getUser()->getId(),
            "dashboard" => $dashboard->getId()
        ), ['createdAt' => 'DESC']);

        return new JsonResponse($serializer->serialize($filters, 'json', array("groups" => "Default")), \Symfony\Component\HttpFoundation\Response::HTTP_OK, array(), true);
    }

    #[Route(path: '', name: 'api_filter_create', methods: ['POST'], options: ['expose' => true])]
    public function create(#[MapRequestPayload] FilterCreateDto $payload, EntityManagerInterface $entityManager): JsonResponse
    {
        $dashboard = $entityManager->getRepository(Dashboard::class)->find($payload->dashboard);
        $this->denyAccessUnlessGranted('view', $dashboard);

        /** @var User $user */
        $user = $this->getUser();
        $filter = Filter::fromCreateDto(
            $payload,
            $dashboard,
            $user
        );

        $entityManager->persist($filter);
        $entityManager->flush();
        return $this->getJsonResponse(array(
            "id" => $filter->getId()
        ));
    }

    #[Route(path: '/{filter}', methods: ['DELETE'], name: 'api_filter_delete', options: ['expose' => true])]
    public function delete(Filter $filter, EntityManagerInterface $entityManager): JsonResponse
    {
        if ($filter->getUser() !== $this->getUser() && !$this->isGranted('ROLE_SUPER_ADMIN')) {
            throw new AccessDeniedHttpException();
        }
        $entityManager->remove($filter);
        $entityManager->flush();
        return $this->getJsonResponse();
    }

    #[Route(path: '/{filter}', methods: ['PUT'], name: 'api_filter_update', options: ['expose' => true])]
    public function update(Filter $filter, #[MapRequestPayload] FilterCreateDto $payload, EntityManagerInterface $entityManager): JsonResponse
    {
        if ($filter->getUser() !== $this->getUser() && !$this->isGranted('ROLE_SUPER_ADMIN')) {
            throw new AccessDeniedHttpException();
        }

        if ($payload->vars) {
            $filter->setVars($payload->vars);
        }
        if ($payload->title) {
            $filter->setTitle($payload->title);
        }

        $entityManager->persist($filter);
        $entityManager->flush();

        return $this->getJsonResponse();
    }
}

readonly class FilterCreateDto {
    public function __construct(
        public ?string $dashboard = null,
        public ?string $title = null,
        public ?array $vars = null,
    ) {}
}