<?php

namespace App\Controller\RequeteurBundle;

use App\Services\RequeteurBundle\GalleryManager;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route("/gallery")]
class GalleryController extends AbstractController
{
	private GalleryManager $galleryManager;

	public function __construct(GalleryManager $galleryManager)
	{
		$this->galleryManager = $galleryManager;
	}

	#[Route("/upload/{libraryType}", name: "aquitem_requeteur_gallery_upload", defaults: ["libraryType" => ""], options: ["expose" => true], methods: ["GET", "POST"])]
    public function uploadAction(Request $request, $libraryType = ""): JsonResponse
    {
        $files = [];
        $message = "";
        // GET = Retourne la liste des images
        if ($request->getMethod() === "GET") {
			$images = $this->galleryManager->listImages($this->getUser(), $libraryType);
            foreach ($images as $key=>$image) {
                if($key < count($images)) {
                    $file = [
                        "name" => $image,
                        "url" => $image,
                        "thumbnailUrl" => $image,
                        "width" => getimagesize($image)[0],
                        "height" => getimagesize($image)[1],
                    ];
                    $files[] = $file;
                }
            }
        } elseif ($request->getMethod() === "POST") {
            if ($request->files->count() !== 0) {
                try {
                    $file = $this->galleryManager->uploadImage($request->files->get('files')[0], $this->getUser(), $libraryType);
                    $files[] = [
                        "name" => $file["name"],
                        "url" => $file["url"],
                        "thumbnailUrl" => $file["thumbnailUrl"],
                    ];
                } catch (\Exception $e) {
                    return new JsonResponse(array("files" => $files, "message" => "js.fileAlreadyExist"));
                };
            }
        }
        return new JsonResponse(array("files" => $files, "message" => $message));
    }

	#[Route("/delete/{folder}/", name: "aquitem_requeteur_gallery_delete", defaults: ["folder" => "user"], options: ["expose" => true])]
	public function deleteAction(Request $request, $folder = "user"): JsonResponse
	{
		$urls = [];
		if ($request->getMethod() === "POST") {
			$file = $request->request->get('file');
			$urls = $this->galleryManager->deleteImage($this->getUser(), $file, $folder == "user");
		}
		return new JsonResponse(["urls" => $urls]);
	}

	#[Route("/browse-library/{libraryType}/{referer}", name: "aquitem_requeteur_gallery_browse_library", defaults: ["libraryType" => "", "referer" => "editeur"], options: ["expose" => true])]
	public function browseLibraryAction($libraryType = "", $referer = "editeur"): Response
	{
		$libraryName = "";
		if($libraryType == "common") {
			if (isset($this->galleryManager->userProfil['common_library_folder'])) {
				if (array_key_exists('common_library_name', $this->galleryManager->userProfil)) {
					$libraryName = $this->galleryManager->userProfil['common_library_name'];
				}
			}
		}
		elseif($libraryType == "personal") {
			if(isset($this->galleryManager->userProfil['personal_library_name'])) {
				$libraryName = $this->galleryManager->userProfil['personal_library_name'];
			}
		}
		return $this->render('@Requeteur/Default/gallery-browser.html.twig', array(
			"libraryType" => $libraryType,
			"referer" => $referer,
			"maxFileSize" => $this->galleryManager->userConfig["TailleMaxImageEnOctets"],
			"libraryName" => $libraryName,
		));
	}

	#[Route("/browse-personal-library", name: "aquitem_requeteur_gallery_browse_personal_library", options: ["expose" => true])]
	public function browsePersonalLibraryAction(): Response
	{
		$personalLibraryName = "";
		if(isset($this->galleryManager->userProfil['personal_library_name'])) {
			$personalLibraryName = $this->galleryManager->userProfil['personal_library_name'];
		}
		return $this->render('@Requeteur/Default/gallery-browser.html.twig', array(
			"libraryType" => "personal",
			"referer" => "editeur",
			"maxFileSize" => $this->galleryManager->userConfig["TailleMaxImageEnOctets"],
			"libraryName" => $personalLibraryName,
		));
	}
}
