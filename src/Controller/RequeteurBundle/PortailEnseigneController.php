<?php


namespace App\Controller\RequeteurBundle;

use App\Services\WebserviceBundle\WebservicePortailEnseigne;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;

#[Route("/module-client")]
class PortailEnseigneController extends AbstractRequeteurController
{
    protected $webservice;

    public function __construct(WebservicePortailEnseigne $webservice)
    {
        $this->webservice = $webservice;
    }

	#[Route('/rechercher/{idSession}', name: 'aquitem_requeteur_portail_enseigne_recherche', options: ['expose' => true])]
    public function indexAction(Request $request, $idSession = 0): Response
    {
        $session = $request->getSession();
        $searchCriteres = array();
        if ($session->get('clients') && array_key_exists($idSession, $session->get('clients'))) {
            if ($idSession > 0) {
                $searchCriteres = $session->get('clients')[$idSession];
            }
        }
        $listerCriteresRecherche = $this->webservice->listerCriteresRecherche()->getValeurs();
        $valeursPredefinies = $listerCriteresRecherche[0]['valeurs'];
        $criteres = $listerCriteresRecherche[1]['valeurs'];
        $template = '@Requeteur/PortailEnseigne/recherche.html.twig';
        return $this->render($template, array(
            'valeursPredefinies' => $valeursPredefinies,
            'criteres' => $criteres,
			'refColonneIdArchive' => $criteres[array_search('true', array_column($criteres, 'identifiantArchive'))]['refColonne'],
            'searchCriteres' => $searchCriteres,
            'idSession' => $idSession,
        ));
    }

	#[Route('/liste-clients/{idSession}', name: 'aquitem_requeteur_portail_enseigne_liste_clients', options: ['expose' => true])]
    public function listeClientsAction(Request $request, $idSession = 0): Response|RedirectResponse
    {
        $session = $request->getSession();
        $nb = 50;
        $searchCriteres = $resultats = array();
        $nbTotal = 0;
        $newPos = $page = 1;
        $position = $triAsc = "";
        if ($session->get('clients')) {
            if (array_key_exists($idSession, $session->get('clients'))) {
                if ($idSession > 0) {
                    $searchCriteres = $session->get('clients')[$idSession];
                    $recherche = $this->webservice->rechercherClient($searchCriteres["form"]);
					if($recherche->hasError()) {
						$resultats = [];
					} else {
						$resultats = $recherche->getValeurs();
						$nbTotal = $resultats[0]['valeur'];
					}
                    $page = $searchCriteres['page'];
                    $newPos = ($page * $nb) + 1;
                    $position = $searchCriteres["form"]['positionColonneTri'];
                    $triAsc = $searchCriteres["form"]['triAsc'];
                }
            } else {
                return $this->redirectToRoute('aquitem_requeteur_portail_enseigne_recherche');
            }
        } else {
            return $this->redirectToRoute('aquitem_requeteur_portail_enseigne_recherche');
        }
        $template = '@Requeteur/PortailEnseigne/resultats.html.twig';
        // Si requête ajax
        if ($request->isXmlHttpRequest()) {
            $template = '@Requeteur/PortailEnseigne/Includes/searchResultDatas.html.twig';
        }
		$listeCriteresRecherche = $this->webservice->listerCriteresRecherche()->getValeurs();

        return $this->render($template, array(
            'resultats' => $resultats,
            'nbParPage' => $nb,
            'newPos' => $newPos,
            'page' => $page ? $page : 1,
            'nbTotal' => $nbTotal,
            'position' => $position,
            'triAsc' => $triAsc,
            'searchCriteres' => $searchCriteres,
            'idSession' => $idSession,
            'valeursPredefinies' => $listeCriteresRecherche[0]['valeurs'],
            'criteres' => $listeCriteresRecherche[1]['valeurs'],
			'refColonneIdArchive' => $listeCriteresRecherche[1]['valeurs'][array_search('true', array_column($listeCriteresRecherche[1]['valeurs'], 'identifiantArchive'))]['refColonne'],
        ));
    }

	#[Route('/clients-session', name: 'aquitem_requeteur_portail_session', options: ['expose' => true])]
    public function clientsSessionAction(Request $request): JsonResponse
    {
        $session = $request->getSession();
        foreach ($request->request->all() as $key => $data) {
            $datas[$key] = $data;
        }
        $datas['page'] = (int) $datas['page'];
        $page = $datas['page'];
        $lastKey = 1;
        $sessionClients = $session->get('clients');
        if ($sessionClients) {
            end($sessionClients);
            $lastKey = key($sessionClients) + 1;
        }
        if ($page !== 0) {
            $sessionClients[key($sessionClients)] = $datas;
        } else {
            $sessionClients[$lastKey] = $datas;
            $datas['idSession'] = $lastKey;
        }
        $session->set('clients', $sessionClients);
        return new JsonResponse($datas);
    }

	#[Route('/fiche-client/{rubrique}/{idClient}/{idSession}/{retourFusion}/{params}', defaults: ['rubrique' => 'donnees', 'idSession' => '0', 'retourFusion' => 'no-fusion', 'params' => '0|1|1|-1'], name: 'aquitem_requeteur_portail_enseigne_fiche_client', options: ['expose' => true])]
    public function ficheClientAction(Request $request, $idClient, $idSession, $params, $retourFusion, $rubrique = "donnees"): Response|RedirectResponse
    {
        $session = $request->getSession();
        if ($idSession && $session->get('clients') && !array_key_exists($idSession, $session->get('clients'))) {
            return $this->redirectToRoute('aquitem_requeteur_portail_enseigne_recherche');
        }
        $donneesFiche = $this->webservice->afficherDonneesClient(array('idClient' => $idClient));
		$monClient = $donneesFiche->getValeurs()[1]['nom'] === "monClient" ? filter_var($donneesFiche->getValeurs()[1]['valeur'], FILTER_VALIDATE_BOOLEAN) : true;
        $enteteFiche = $this->webservice->afficherEnteteClient(array('idClient' => $idClient));
        $magasins = $this->webservice->listerMagasins()->getSingleValeurs();
        $motifsBonus = $this->webservice->listerMotifsBonus()->getSingleValeurs();
        $motifsRetraitBonus = $this->webservice->listerMotifsRetranchementBonus()->getSingleValeurs();
        $famillesProduits = $this->webservice->listerFamillesDeProduits()->getSingleValeurs();
        $alerteFusion = $enteteFiche->getValeurs()[0]['valeur'];
        if (!$this->getUser()->checkDroitsFusionClients()) {
            $alerteFusion = false;
        }
        $template = '@Requeteur/PortailEnseigne/fiche_client.html.twig';
        switch ($rubrique) {
            case 'donnees':
                $markers = $this->getDonneesClient($idClient);
                // Si requête ajax
                if ($request->isXmlHttpRequest()) {
                    $template = '@Requeteur/PortailEnseigne/Includes/donnees_client.html.twig';
                }

				// condition sur présence de la session, car pas de session si auto login depuis une caisse
				$markers['idArchive'] = "";
				if($session->get('clients')) {
					if (array_key_exists($idSession, $session->get('clients'))) {
						if ($idSession > 0) {
							// la carte recherchée est-elle archivée ?
							$searchCriteres = $session->get('clients')[$idSession]['form'];
							$refColonneIdArchive = $session->get('clients')[$idSession]['refColonneIdArchive'];
							$indexColonne = array_search($refColonneIdArchive, $searchCriteres['colsDonnees']);
							if(isset($searchCriteres['valeurs'][(string) $indexColonne]) && $refColonneIdArchive != "") {
								$markers['idArchive'] = $searchCriteres['valeurs'][$indexColonne];
							}
						}
					}
				}
                break;
            case 'fidelite':
                $markers = $this->getFideliteClient($idClient, $params);
                // Si requête ajax
                if ($request->isXmlHttpRequest()) {
                    $template = '@Requeteur/PortailEnseigne/Includes/fidelite_client.html.twig';
                }
                break;
            case 'fusion':
                $markers = $this->getFusionClient($idClient, $idSession, $request);
                if (array_key_exists('idClientAfterFusion', $markers)) {
                    return $this->redirectToRoute('aquitem_requeteur_portail_enseigne_fiche_client', array(
                        'rubrique' => 'donnees',
                        'idClient' => $markers['idClientAfterFusion'],
                        'idSession' => $idSession,
                        'retourFusion' => 'fusion-ok'
                    ));
                }
                break;
            case 'anonymisation':
                if (!$this->getUser()->checkDroitsAnonymisation()) {
                    throw new AccessDeniedHttpException();
                }
                break;
            case 'vente':
                if (!$this->getUser()->checkDroitsAjoutVente()) {
                    throw new AccessDeniedHttpException();
                }
                break;
            case 'retour':
                if (!$this->getUser()->checkDroitsRetourProduit()) {
                    throw new AccessDeniedHttpException();
                }
                break;
            case 'bonus':
                if (!$this->getUser()->checkDroitsAjoutBonus()) {
                    throw new AccessDeniedHttpException();
                }
                break;
            case 'offres':
                $userProfil = $session->get('current_user_profil');
                if(!array_key_exists('desactiver_consultation_offres_en_cours', $userProfil) ||
                    (array_key_exists('desactiver_consultation_offres_en_cours', $userProfil) && $userProfil['desactiver_consultation_offres_en_cours'] != "1")) {
                    // Si requête ajax
                    if ($request->isXmlHttpRequest()) {
                        $template = '@Requeteur/PortailEnseigne/Includes/offres_client.html.twig';
                    } else {
                        $params = "1|7";
                    }
                    $markers = $this->getOffresEnCoursClient($idClient, $params);
                }
                break;
        }

        $clientModifiable = filter_var($donneesFiche->getValeurs()[0]['valeur'], FILTER_VALIDATE_BOOLEAN);

        $markers['idClient'] = $idClient;
        $markers['rubrique'] = $rubrique;
        $markers['entete'] = $enteteFiche->getValeurs()[1]['valeurs'];
        $markers['badges'] = isset($enteteFiche->getValeurs()[2]) ? $enteteFiche->getValeurs()[2]['valeurs'] : [];
        $markers['idSession'] = $idSession;
        $markers['magasins'] = $magasins;
        $markers['retourFusion'] = $retourFusion;
        $markers['alertFusion'] = $alerteFusion;
        $markers['famillesProduits'] = $famillesProduits;
        $markers['motifsBonus'] = $motifsBonus;
        $markers['motifsRetraitBonus'] = $motifsRetraitBonus;
        $markers["motifsRetourProduit"] = $this->webservice->listerMotifsRetourProduit()->getSingleValeurs();
        $markers['clientModifiable'] = $clientModifiable;
		$markers['monClient'] = $monClient;

        $amaAdress = 0;
        $amaTel = 0;
        $amaEmail = 0;

        if ($clientModifiable) {
            $amaGeneral = $this->webservice->listerOptionsAideSaisie(array(
                'codeSite' => $magasins[0]["codeSite"],
                'codeMagasin' => $magasins[0]["codeMagasin"],
            ));

            if(!empty($amaGeneral->getValeurs()[4]['valeurs'])) {
                foreach ($amaGeneral->getValeurs()[4]['valeurs'] as $package) {
                    switch($package["libelle"]) {
                        case 'ADRESSE' : $amaAdress = 1;
                        break;
                        case 'EMAIL' : $amaEmail = 1;
                        break;
                        case 'TELEPHONE' : $amaTel = 1;
                    }
                }
            }
        }

        $markers['amaAdress'] = $amaAdress;
        $markers['amaTel'] = $amaTel;
        $markers['amaEmail'] = $amaEmail;

        if ($rubrique == "donnees") {
            $cpManquant = false;
            $indiManquant = false;
            if ($amaAdress && !isset($markers['donneesCoord']["CODE"])) {
                $cpManquant = true;
            }
            if ($amaTel && !isset($markers['donneesCoord']["INDI"])) {
                $indiManquant = true;
            }

            if ($cpManquant || $indiManquant) {
                $template = '@Requeteur/PortailEnseigne/fiche_client_error_cp_indicatif.html.twig';
                return $this->render($template, array('cpManquant' => $cpManquant, 'indiManquant' => $indiManquant));
            }
        }

        return $this->render($template, $markers);
    }

	#[Route('/creer-client', name: 'aquitem_requeteur_portail_enseigne_creer_client', options: ['expose' => true])]
    public function creerClientAction(): Response
    {
        if (!$this->getUser()->checkDroitsCreationClient()) {
            throw new AccessDeniedHttpException();
        }
        $magasins = $this->webservice->listerMagasins()->getSingleValeurs();
        $donneesFiche = $this->webservice->saisieNouveauClient();

        $amaGeneral = $this->webservice->listerOptionsAideSaisie(array(
            'codeSite' => $magasins[0]["codeSite"],
            'codeMagasin' => $magasins[0]["codeMagasin"],
        ));

        $amaAdress = 0;
        $amaTel = 0;
        $amaEmail = 0;
        foreach ($amaGeneral->getValeurs()[4]['valeurs'] as $package) {
            switch ($package["libelle"]) {
                case 'ADRESSE':
                    $amaAdress = 1;
                    break;
                case 'EMAIL':
                    $amaEmail = 1;
                    break;
                case 'TELEPHONE':
                    $amaTel = 1;
            }
        }
        foreach ($donneesFiche->getValeurs()[2]['valeurs'] as $donnee) {
            $donneesCom[$donnee['categorie']][] = $donnee;
        }
        $donneesCompl = $donneesFiche->getValeurs()[3]['valeurs'];
        $donneesCoord = $donneesFiche->getValeurs()[1]['valeurs'];
        $predefineValues = $donneesFiche->getValeurs()[0]['valeurs'];
        $sortedDonneesCoord = [];
        foreach ($donneesCoord as $field) {
            if ($field["idValeursPredefinies"] != "") {
                foreach ($predefineValues as $predefineValue) {
                    if ($predefineValue["id"] == $field["idValeursPredefinies"]) {
                        $field["valeursPredefinies"] = $predefineValue["valeursPredefinies"];
                    }
                }
            }
            $sortedDonneesCoord[$field['referenceAideSaisie']] = $field;
        }

        $cpManquant = false;
        $indiManquant = false;
        if ($amaAdress && !isset($sortedDonneesCoord["CODE"])) {
            $cpManquant = true;
        }
        if ($amaTel && !isset($sortedDonneesCoord["INDI"])) {
            $indiManquant = true;
        }

        if (($amaEmail || $amaTel || $amaAdress)  && $amaGeneral->getValeurs()[2]['valeur'] == "") {
            $template = '@Requeteur/PortailEnseigne/fiche_client_error_imputation_code.html.twig';
            return $this->render($template);
        }

        if ($cpManquant || $indiManquant) {
            $template = '@Requeteur/PortailEnseigne/fiche_client_error_cp_indicatif.html.twig';
            return $this->render($template, array('cpManquant' => $cpManquant, 'indiManquant' => $indiManquant));
        }

        $markers = array(
            'magasins' => $magasins,
            'largeurs' => $this->getDatasWidths($donneesCompl),
            'valeursPredefinies' => $donneesFiche->getValeurs()[0]['valeurs'],
            'typologies' => $donneesFiche->getValeurs()[4]['valeurs'],
            'donneesCoord' => $sortedDonneesCoord,
            'donneesCom' => $donneesCom,
            'donneesCompl' => $donneesCompl,
            'rubrique' => 'donnees',
            'amaAdress' => $amaAdress,
            'amaTel' => $amaTel,
            'amaEmail' => $amaEmail

        );
        $template = '@Requeteur/PortailEnseigne/fiche_client.html.twig';
        return $this->render($template, $markers);
    }

	#[Route('/creer-vente', name: 'aquitem_requeteur_portail_enseigne_creer_vente', options: ['expose' => true])]
    public function creerVenteAction(): Response
    {
        if (!$this->getUser()->checkDroitsAjoutVente()) {
            throw new AccessDeniedHttpException();
        }
        $magasins = $this->webservice->listerMagasins()->getSingleValeurs();
        $famillesProduits = $this->webservice->listerFamillesDeProduits()->getSingleValeurs();
        $motifsBonus = $this->webservice->listerMotifsBonus()->getSingleValeurs();
        $markers = array(
            'magasins' => $magasins,
            'famillesProduits' => $famillesProduits,
            'motifsBonus' => $motifsBonus,
        );
        $template = '@Requeteur/PortailEnseigne/creation_vente.html.twig';
        return $this->render($template, $markers);
    }

	#[Route('/ajout-bonus', name: 'aquitem_requeteur_portail_enseigne_ajout_bonus', options: ['expose' => true])]
    public function AjoutBonusAction(): Response
    {
        if (!$this->getUser()->checkDroitsAjoutBonus()) {
            throw new AccessDeniedHttpException();
        }
        $magasins = $this->webservice->listerMagasins()->getSingleValeurs();
        $motifsBonus = $this->webservice->listerMotifsBonus()->getSingleValeurs();
        $motifsRetraitBonus = $this->webservice->listerMotifsRetranchementBonus()->getSingleValeurs();
        $markers = array(
            'magasins' => $magasins,
            'motifsBonus' => $motifsBonus,
            'motifsRetraitBonus' => $motifsRetraitBonus,
        );
        $template = '@Requeteur/PortailEnseigne/ajout_bonus.html.twig';
        return $this->render($template, $markers);
    }

	#[Route('/retour-produit', name: 'aquitem_requeteur_portail_enseigne_retour_produit', options: ['expose' => true])]
    public function retourProduitAction(): Response
    {
        if (!$this->getUser()->checkDroitsRetourProduit()) {
            throw new AccessDeniedHttpException();
        }
        $magasins = $this->webservice->listerMagasins()->getSingleValeurs();
        $famillesProduits = $this->webservice->listerFamillesDeProduits()->getSingleValeurs();
        $motifsRetourProduit = $this->webservice->listerMotifsRetourProduit()->getSingleValeurs();

        $markers = array(
            'magasins' => $magasins,
            'famillesProduits' => $famillesProduits,
            'motifsRetourProduit' => $motifsRetourProduit
        );
        $template = '@Requeteur/PortailEnseigne/retour_produit.html.twig';
        return $this->render($template, $markers);
    }


    /* utils */
    private function getDatasWidths($donneesCompl): array
    {
        $k = 0;
        $tabLigne = array();
        foreach ($donneesCompl as $i => $donneeCompl) {
            $tabLigne[$i] = [];
            $cle = 0;
            foreach ($donneeCompl['colonnes'] as $key => $colonne) {
                if ($colonne['nouvelleLigne']) {
                    $cle++;
                }
                $tabLigne[$i][$cle][] = $key;
            }
            $k++;
        }
        $largeurs = [];
        foreach ($tabLigne as $a => $ligne) {
            foreach ($ligne as $b => $items) {
                if (count($items) > 1) {
                    foreach (array_keys($items) as $c) {
                        $largeurs[$a][] = 12 / count($items);
                    }
                } else {
                    $largeurs[$a][] = 12;
                }
            }
        }
        return $largeurs;
    }

    private function getDonneesClient($idClient): array
    {
        $donneesFiche = $this->webservice->afficherDonneesClient(array('idClient' => $idClient));
		$donneesCommunication = $donneesFiche->getValeurs()[3]['nom'] === "lesDonneesCommunications" ? $donneesFiche->getValeurs()[3]['valeurs'] : $donneesFiche->getValeurs()[4]['valeurs'];
        foreach ($donneesCommunication as $donnee) {
            $donneesCom[$donnee['categorie']][] = $donnee;
        }
        $donneesCompl = $donneesFiche->getValeurs()[4]['nom'] === "lesDonneesComplementaires" ? $donneesFiche->getValeurs()[4]['valeurs'] : $donneesFiche->getValeurs()[5]['valeurs'];
        $donneesCoord = $donneesFiche->getValeurs()[2]['nom'] === "lesDonneesCoordonnees" ? $donneesFiche->getValeurs()[2]['valeurs'] : $donneesFiche->getValeurs()[3]['valeurs'];
        $predefineValues = $donneesFiche->getValeurs()[1]['nom'] === "lesValeursPredefinies" ? $donneesFiche->getValeurs()[1]['valeurs'] : $donneesFiche->getValeurs()[2]['valeurs'];
        $sortedDonneesCoord = [];

        foreach ($donneesCoord as $field) {
            if ($field["idValeursPredefinies"] != "") {
                foreach ($predefineValues as $predefineValue) {
                    if ($predefineValue["id"] == $field["idValeursPredefinies"]) {
                        $field["valeursPredefinies"] = $predefineValue["valeursPredefinies"];
                    }
                }
            }
            $sortedDonneesCoord[$field['referenceAideSaisie']] = $field;
        }
        $markers['largeurs'] = $this->getDatasWidths($donneesCompl);
        $markers['valeursPredefinies'] = $predefineValues;
        $markers['donneesCoord'] = $sortedDonneesCoord;
        $markers['donneesCom'] = $donneesCom;
        $markers['donneesCompl'] = $donneesCompl;
        return $markers;
    }

    private function getOffresEnCoursClient($idClient, $params): array
	{
        $erreur = "";
        $paramsExplode = explode('|', $params);
        $triAsc = $paramsExplode[0];
        $position = $paramsExplode[1];
        $datas = array(
            'idClient' => $idClient,
            'positionColonneTri' => $position,
            'triAsc' => $triAsc == "1" ? "true" : "false",
        );
		$donneesOffres = [];
		$nbTotal = 0;
		$erreur = "";
        $offres = $this->webservice->afficherOffresEnCours($datas);
        if($offres->hasError()) {
            $erreur = $offres->getErreurs()[0]['message'];
        } else {
			$nbTotal = $offres->getValeurs()[0]['valeur'];
			$donneesOffres = $offres->getValeurs();
		}
        $markers['donneesOffres'] = $donneesOffres;
        $markers['triAsc'] = $triAsc == "1" ? "true" : "false";
        $markers['position'] = $position;
        $markers['newTriAsc'] = $triAsc == "1" ? "false" : "true";
        $markers['nbTotal'] = (int) $nbTotal;
        $markers['erreur'] = $erreur;
        return $markers;
    }

	private function getFideliteClient($idClient, $params): array
	{
        $idsFiltres = $valeursFiltres = array();
        $filtres = $this->webservice->listerFiltresFidelisationClient(array('idClient' => $idClient))->getSingleValeurs();
        $paramsExplode = explode('|', $params);
        $triAsc = $paramsExplode[0];
        $position = $paramsExplode[1];
        $newPos = $paramsExplode[2];
        $idFiltre = $paramsExplode[3];
        if ($idFiltre > -1) {
            $idsFiltres = array($filtres[0]['id']);
            $valeursFiltres = array($filtres[0]['valeurs'][$idFiltre]);
        } else {
            $idsFiltres = $valeursFiltres = array();
        }
        $erreur = "";
        $nb = 50;
        $page = ceil($newPos / $nb);
        $datas = array(
            'idClient' => $idClient,
            'idsFiltres' => $idsFiltres,
            'valeursFiltres' => $valeursFiltres,
            'startResult' => $newPos,
            'nbResults' => $nb,
            'positionColonneTri' => $position,
            'triAsc' => $triAsc == "1" ? "true" : "false",
        );
        $donneesFiche = $this->webservice->afficherFidelisationClient($datas);
        $donneesFid = $donneesFiche->getValeurs();
        if ($donneesFiche->hasError()) {
            $nbTotal = 0;
            $erreur = $donneesFiche->getErreurs()[0]['message'];
        } else {
            $nbTotal = $donneesFiche->getValeurs()[0]['valeur'];
        }
        $markers['filtres'] = $filtres;
        $markers['donneesFid'] = $donneesFid;
        $markers['triAsc'] = $triAsc == "1" ? "true" : "false";
        $markers['newTriAsc'] = $triAsc == "1" ? "false" : "true";
        $markers['position'] = $position;
        $markers['nbParPage'] = $nb;
        $markers['nbTotal'] = (int) $nbTotal;
        $markers['newPos'] = (int) ($page * $nb) + 1;
        $markers['page'] = (int) $page;
        $markers['erreur'] = $erreur;
        $markers['idFiltre'] = $idFiltre;
        return $markers;
    }

    private function getFusionClient($idClient, $idSession, $request): array
    {
        if (!$this->getUser()->checkDroitsFusionClients()) {
            throw new AccessDeniedHttpException();
        }
        $client = $this->webservice->afficherEnteteClient(array('idClient' => $idClient))->getValeurs()[1]['valeurs'];
        $markers['idClientSource'] = $idClient;
        $codeCarteClientSource = $client[3]['valeur'];
        $markers['codeCarteClientSource'] = $codeCarteClientSource;
        $markers['typeFusion'] = null;
        $markers['fusionOK'] = null;
        $markers['erreurFusion'] = null;
        $markers['erreurCarteSource'] = false;
        $markers['erreurCarteDestination'] = false;
        // si formulaire choix soumis
        if ($request->isMethod('post')) {
            $idClientDestination = $request->request->get('idClientDestination');
            $markers['idClientDestination'] = $idClientDestination;
            $codeCarteClientDestination = $request->request->get('codeCarteClientDestination');
            $markers['codeCarteClientDestination'] = $codeCarteClientDestination;
            $typeFusion = $request->request->get('fusionTypeCarte');
            $markers['typeFusion'] = $typeFusion;
            // fusion
            if ($request->request->get('confirm-fusion')) {
                $fusionCarte = $this->webservice->fusionnerClient($request->request->all());
                $markers['fusionOK'] = true;
                if ($fusionCarte->hasError()) {
                    if ($fusionCarte->getValidations()[0]['message'] != "") {
                        $markers['erreurFusion'] = $fusionCarte->getValidations()[0]['message'];
                    } else {
                        $markers['erreurFusion'] = $fusionCarte->getErreurs()[0]['message'];
                    }
                } else {
                    // si client destination n'est pas le client courant OU fusion carte vierge => redirect sur fiche client fusionné
                    $recupIdClientDestination = $this->webservice->recupererIdClient(array(
                        'codeCarteClient' => $request->request->get('codeCarteClientDestination'),
                    ));
                    $idClientAfterFusion = $recupIdClientDestination->getSingleValeur();
                    $markers['idClientAfterFusion'] = $idClientAfterFusion;
                }
            } else { // confirmation avant fusion
                $markers['response'] = $request->request->all();
                // verification carte existante
                if ($typeFusion == "2") {
                    // recuperation id client
                    $recupIdClientSource = $this->webservice->recupererIdClient(array(
                        'codeCarteClient' => $codeCarteClientSource,
                    ));
                    $recupIdClientDestination = $this->webservice->recupererIdClient(array(
                        'codeCarteClient' => $codeCarteClientDestination,
                    ));
                    if ($recupIdClientSource->hasError() || $recupIdClientDestination->hasError()) {
                        if ($recupIdClientSource->getValidations()[0]['message'] != "") {
                            $markers['erreurCarteSource'] = true;
                            $markers['erreurCarte'] = $recupIdClientSource->getValidations()[0]['message'];
                        } elseif ($recupIdClientSource->getErreurs()[0]['message'] != "") {
                            $markers['erreurCarteSource'] = true;
                            $markers['erreurCarte'] = $recupIdClientSource->getErreurs()[0]['message'];
                        }
                        if ($recupIdClientDestination->getValidations()[0]['message'] != "") {
                            $markers['erreurCarteDestination'] = true;
                            $markers['erreurCarte'] = $recupIdClientDestination->getValidations()[0]['message'];
                        } elseif ($recupIdClientDestination->getErreurs()[0]['message'] != "") {
                            $markers['erreurCarteDestination'] = true;
                            $markers['erreurCarte'] = $recupIdClientDestination->getErreurs()[0]['message'];
                        }
                    } else {
                        $idClientDestination = $recupIdClientDestination->getSingleValeur();
                        $markers['idClientDestination'] = $idClientDestination;
                        // recup infos des 2 clients
                        $infoClientSource = $this->webservice->afficherDonneesClientPourFusion(array(
                            'idClient' => $idClient
                        ))->getValeurs();
                        $modificationAutoriseeSource = $infoClientSource[3]['valeur'];
                        $infoClientDestination = $this->webservice->afficherDonneesClientPourFusion(array(
                            'idClient' => $idClientDestination,
                        ))->getValeurs();
                        $modificationAutoriseeDestination = $infoClientDestination[3]['valeur'];

                        // verification du code carte
                        $verifDatas = array(
                            'codeCarteClient' => $codeCarteClientDestination,
                            'nouvelleCarte' => "false",
                            'codeSite' => $infoClientDestination[1]['valeur'],
                            'codeMagasin' => $infoClientDestination[2]['valeur'],
                        );
                        $verifCarte = $this->webservice->verifierCodeCarteClient($verifDatas);
                        if ($verifCarte->hasError()) {
                            $markers['erreurCarte'] = $verifCarte->getValidations()[0]['message'] != null ? $verifCarte->getValidations()[0]['message'] : $verifCarte->getErreurs()[0]['message'];
                        } elseif ($infoClientSource[0]['valeur'] == $infoClientDestination[0]['valeur']) {
                            // code carte source identique au code carte de destination
                            $markers['erreurMemeCarte'] = true;
                        } else {
                            $markers['codeSiteClientDestination'] = $infoClientDestination[1]['valeur'];
                            $markers['codeMagasinClientDestination'] = $infoClientDestination[2]['valeur'];
                            $markers['infoClientSource'] = $infoClientSource;
                            $markers['infoClientDestination'] = $infoClientDestination;
                            $markers['modificationAutoriseeSource'] = $modificationAutoriseeSource;
                            $markers['modificationAutoriseeDestination'] = $modificationAutoriseeDestination;
                        }
                    }
                } else { // verification carte vierge
                    $magasin = json_decode($request->request->get('magasinFusion'));
                    $markers['codeSiteClientDestination'] = $magasin->{'codeSite'};
                    $markers['codeMagasinClientDestination'] =  $magasin->{'codeMagasin'};
                    // verification du code carte
                    $verifDatas = array(
                        'codeCarteClient' => $codeCarteClientDestination,
                        'nouvelleCarte' => "true",
                        'codeSite' => $magasin->{'codeSite'},
                        'codeMagasin' => $magasin->{'codeMagasin'},
                    );
                    $verifCarte = $this->webservice->verifierCodeCarteClient($verifDatas);
                    if ($verifCarte->hasError()) {
                        if ($verifCarte->getValidations()[0]['message'] != "") {
                            $markers['erreurCarte'] = $verifCarte->getValidations()[0]['message'];
                        } else {
                            $markers['erreurCarte'] = $verifCarte->getErreurs()[0]['message'];
                        }
                    }
                }
            }
        }
        return $markers;
    }
}
