<?php

namespace App\Controller\RequeteurBundle;

use App\Services\WebserviceBundle\WebserviceCartesCadeaux;
use Symfony\Component\Routing\Attribute\Route;
use App\Form\CartesCadeaux\SearchForm;
use App\Form\CartesCadeaux\ActiverForm;
use App\Form\CartesCadeaux\UtiliserForm;
use App\Form\CartesCadeaux\ProlongerForm;
use App\Form\CartesCadeaux\AnnulerForm;
use App\Form\CartesCadeaux\ControleSearchForm;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use App\Services\RequeteurBundle\CartesCadeauxManager;
use App\Services\RequeteurBundle\Utils;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;

#[Route("/cartes-cadeaux")]
class CartesCadeauxController extends AbstractRequeteurController
{
    /**
     * @var CartesCadeauxManager
     */
    protected $cartesCadeauxManager;

    /**
     * @var WebserviceCartesCadeaux
     */
    protected $webserviceCartesCadeaux;

    /**
     * @var WebserviceResponse
     */
    protected $config;

    /**
     * @var Utils
     */
    public $utils;

    public function __construct(CartesCadeauxManager $cartesCadeauxManager, WebserviceCartesCadeaux $webserviceCartesCadeaux, Utils $utils)
    {
        $this->cartesCadeauxManager = $cartesCadeauxManager;
        $this->webserviceCartesCadeaux = $webserviceCartesCadeaux;
        $this->utils = $utils;
    }

	#[Route('/recherche/{numeroCarte}', name: 'aquitem_requeteur_cartes_cadeaux_recherche', defaults: ['numeroCarte' => ''], options: ['expose' => true])]
    public function rechercheAction(Request $request, $numeroCarte): Response
    {
        $cancelForm = false;
        $form = $this->createForm(SearchForm::class);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $numeroCarte = $data['numeroCarte'];
        } else {
            $form->get('numeroCarte')->setData($numeroCarte);
        }
        if($numeroCarte) {
            $this->cartesCadeauxManager->initCarte($numeroCarte);
            if(isset($this->cartesCadeauxManager->carteDatas[0])
                && is_array($this->cartesCadeauxManager->carteDatas[0]['RESULTAT']['PartieMouvement'])) {
                $this->cartesCadeauxManager->setPointsDeVenteCarte();
                $createCancelForm = $this->createForm(AnnulerForm::class, [
                    'numeroCarte' => $this->cartesCadeauxManager->numeroCarte,
                    'site' => $this->cartesCadeauxManager->site,
                    'magasin' => $this->cartesCadeauxManager->magasin,
                ]);
                $cancelForm = $createCancelForm->createView();
                $this->cartesCadeauxManager->setNbMouvementsCarte();
            }
        }

        return $this->render('@Requeteur/CartesCadeaux/recherche.html.twig', array(
            "searchForm" => $form->createView(),
            "cartesCadeauxManager" => $this->cartesCadeauxManager,
            "cancelForm" => $cancelForm,
        ));
    }

	#[Route('/controle', name: 'aquitem_requeteur_cartes_cadeaux_controle', options: ['expose' => true])]
    public function controleAction(): Response
    {
        $listePdv = $this->cartesCadeauxManager->pointsDeVentes();
        $searchFields = $this->cartesCadeauxManager->typologies();
        $form = $this->createForm(ControleSearchForm::class, null, ['listePdv' => $listePdv, 'searchFields' => $searchFields]);
        return $this->render('@Requeteur/CartesCadeaux/controle.html.twig', array(
            'searchForm' => $form->createView(),
            'showTransactions' => false
        ));
    }

	#[Route('/controle-liste/{page}', name: 'aquitem_requeteur_cartes_cadeaux_controle_liste', defaults: ['page' => '1'], options: ['expose' => true])]
    public function controleListeAction(Request $request, $page = 1): Response
    {
        $startResult = $this->utils->startResult($page, CartesCadeauxManager::NB_PAR_PAGE);
        $showTransactions = false;

        if($request->isXmlHttpRequest()) {
            if ($request->getMethod() == Request::METHOD_POST){
                $showTransactions = true;
                $postDatas = $request->request->all();
                $parameters = $this->cartesCadeauxManager->parametersForSearchAndExport($postDatas);
                $parameters['startResult'] = $startResult;
                $parameters['nbResults'] = CartesCadeauxManager::NB_PAR_PAGE;
                $debut = new \DateTimeImmutable($postDatas['controle_search_form']['dateDebut']);
                $fin = new \DateTimeImmutable($postDatas['controle_search_form']['dateFin']);
                /** @var WebserviceResponse $transactionsResponse */
                $transactionsResponse = $this->webserviceCartesCadeaux->listerTransactions($parameters);
                $transactions = $transactionsResponse->responseManager->transactions();
                $nbTotalPages = $this->utils->nbTotalPages($transactions['nbTotal'], CartesCadeauxManager::NB_PAR_PAGE);
                $markers = [
                    'nbTotal' => $transactions['nbTotal'],
                    'nbTotalPages' => $nbTotalPages,
                    'startResult' => $startResult,
                    'resume' => $transactions['resume'],
                    'mouvements' => $transactions['mouvements'],
                    'site' => $parameters['site'],
                    'magasin' => $parameters['magasin'],
                    'typologie' => $parameters['typologie'],
                    'tsDateDebut' => $debut->getTimestamp(),
                    'tsDateFin' => $fin->getTimestamp(),
                ];
            }

        }
        $markers['page'] = $page;
        $markers['showTransactions'] = $showTransactions;

        return $this->render('@Requeteur/CartesCadeaux/controleListe.html.twig', $markers);
    }

	#[Route('/action-carte/{action}', name: 'aquitem_requeteur_cartes_cadeaux_action_carte', options: ['expose' => true])]
	public function actionCarteAction(Request $request, $action, $securisation = false): Response
    {
        $session = $request->getSession();
        $resultAction = [];
        $jsonResponse = ['valeurs' => [], 'erreurs' => []];
        switch ($action) {
            case 'activer':
                $form = $this->createForm(ActiverForm::class);
                break;
            case 'utiliser':
                $options = ['securisation' => false];
                if($securisation) {
                    $options['securisation'] = true;
                }
                $form = $this->createForm(UtiliserForm::class, null, $options);
                break;
            case 'prolonger':
                $form = $this->createForm(ProlongerForm::class);
                break;
            case 'annuler':
                $form = $this->createForm(AnnulerForm::class);
                break;
        }

        $form->handleRequest($request);
        if ($request->isXmlHttpRequest()) {
            if ($form->isSubmitted() && $form->isValid()) {
                $data = $form->getData();
                $parameters = array_map(function ($value) {
                    return $value instanceof \DateTime ? $value->format('d/m/Y') : $value;
                }, $data);
                $parameters['ipUser'] = $request->getClientIp();
                $parameters['idSession'] = $session->getId();
                $parameters['userAgent'] = $request->headers->get('User-Agent');
                $resultAction = $this->webserviceCartesCadeaux->$action($parameters);
                $jsonResponse = ['erreurs' => $resultAction->getErreurs()[0]];
            }
        }
        return new JsonResponse($jsonResponse);
    }

	#[Route('/activer/{numeroCarte}', name: 'aquitem_requeteur_cartes_cadeaux_activer', options: ['expose' => true])]
    public function activerAction(Request $request, $numeroCarte): Response
    {
        $this->checkAccessAction($request);
        $this->cartesCadeauxManager->initCarte($numeroCarte);
        if($this->cartesCadeauxManager->carteActivable) {
            $this->cartesCadeauxManager->setPointsDeVenteCarte();
            $form = $this->createForm(ActiverForm::class);
            $form->get('numeroCarte')->setData($numeroCarte);
            $markers['form'] = $form->createView();
        }
        $markers['cartesCadeauxManager'] = $this->cartesCadeauxManager;
        return $this->render('@Requeteur/CartesCadeaux/activer.html.twig', $markers);
    }

	#[Route('/utiliser/{numeroCarte}', name: 'aquitem_requeteur_cartes_cadeaux_utiliser', options: ['expose' => true])]
    public function utiliserAction($numeroCarte): Response
    {
        $this->cartesCadeauxManager->initCarte($numeroCarte);
        if($this->cartesCadeauxManager->carteUtilisable) {
            $this->cartesCadeauxManager->setPointsDeVenteCarte();
            $form = $this->createForm(UtiliserForm::class, null, ['securisation' => $this->cartesCadeauxManager->securisation]);
            $form->get('numeroCarte')->setData($numeroCarte);
            $markers['form'] = $form->createView();
        }
        $markers['cartesCadeauxManager'] = $this->cartesCadeauxManager;
        return $this->render('@Requeteur/CartesCadeaux/utiliser.html.twig', $markers);
    }

	#[Route('/prolonger/{numeroCarte}', name: 'aquitem_requeteur_cartes_cadeaux_prolonger', options: ['expose' => true])]
    public function prolongerAction(Request $request, $numeroCarte): Response
    {
        $this->checkAccessAction($request);

        $this->cartesCadeauxManager->initCarte($numeroCarte);
        if($this->cartesCadeauxManager->carteProlongeable) {
            $this->cartesCadeauxManager->setDatesActionProlonger();
            $form = $this->createForm(ProlongerForm::class);
            $form->get('dateProlongation')->setData($this->cartesCadeauxManager->minObjDateProlongation);
            $form->get('numeroCarte')->setData($numeroCarte);
            $markers['form'] = $form->createView();
        }
        $markers['cartesCadeauxManager'] = $this->cartesCadeauxManager;
        return $this->render('@Requeteur/CartesCadeaux/prolonger.html.twig', $markers);
    }
}
