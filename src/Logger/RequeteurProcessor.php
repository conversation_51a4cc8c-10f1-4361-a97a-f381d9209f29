<?php
namespace App\Logger;

use Monolog\LogRecord;
use Monolog\Processor\ProcessorInterface;
use Symfony\Bundle\SecurityBundle\Security;


class RequeteurProcessor implements ProcessorInterface
{
    private $security;

    public function __construct(Security $security)
    {
        $this->security = $security;
    }

    public function __invoke(LogRecord $record): LogRecord
    {
        if (null !== $token = $this->security->getToken()) {
            if ($user = $token->getUser()) {
                $record->extra['enseigne'] = $user->getEnseigne();
            }
        }
        return $record;
    }
}