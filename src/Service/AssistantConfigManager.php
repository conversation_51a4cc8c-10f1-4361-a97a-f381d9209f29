<?php

namespace App\Service;

use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Finder\Finder;

class AssistantConfigManager
{
    private string $configDir;
    private Filesystem $filesystem;

    public function __construct(string $projectDir)
    {
        $this->configDir = $projectDir . '/config/assistants/config';
        $this->filesystem = new Filesystem();
    }

    /**
     * Récupère la liste de tous les assistants
     */
    public function getAllAssistants(): array
    {
        $assistants = [];
        $finder = new Finder();
        $finder->files()->in($this->configDir)->name('*.json');

        foreach ($finder as $file) {
            $content = json_decode(file_get_contents($file->getRealPath()), true);
            $assistants[] = [
                'filename' => $file->getFilename(),
                'id' => $content['id'] ?? '',
                'title' => $content['title'] ?? '',
                'description' => $content['description'] ?? '',
            ];
        }

        return $assistants;
    }

    /**
     * Récupère un assistant par son nom de fichier
     */
    public function getAssistant(string $filename): ?array
    {
        $filePath = $this->configDir . '/' . $filename;
        
        if (!$this->filesystem->exists($filePath)) {
            return null;
        }

        return json_decode(file_get_contents($filePath), true);
    }

    /**
     * Sauvegarde un assistant
     */
    public function saveAssistant(string $filename, array $data): bool
    {
        $filePath = $this->configDir . '/' . $filename;
        
        try {
            $jsonContent = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            $this->filesystem->dumpFile($filePath, $jsonContent);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Supprime un assistant
     */
    public function deleteAssistant(string $filename): bool
    {
        $filePath = $this->configDir . '/' . $filename;
        
        if (!$this->filesystem->exists($filePath)) {
            return false;
        }

        try {
            $this->filesystem->remove($filePath);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Récupère les modèles de composants disponibles
     */
    public function getComponentTemplates(): array
    {
        return [
            'Text' => [
                'id' => 'text-component',
                'component' => 'Text',
                'props' => [
                    'content' => 'Texte à afficher',
                    'variant' => 'paragraph',
                    'size' => 'medium'
                ]
            ],
            'Button' => [
                'id' => 'button-component',
                'component' => 'Button',
                'props' => [
                    'label' => 'Bouton',
                    'variant' => 'primary',
                    'size' => 'medium',
                    'icon' => 'arrow-right'
                ],
                'action' => [
                    'type' => 'navigate',
                    'target' => 'target-page-id'
                ]
            ],
            'ButtonGroup' => [
                'id' => 'button-group',
                'component' => 'ButtonGroup',
                'props' => [
                    'orientation' => 'horizontal',
                    'gap' => 'medium',
                    'alignment' => 'center'
                ],
                'children' => []
            ],
            'Textarea' => [
                'id' => 'textarea-component',
                'component' => 'Textarea',
                'props' => [
                    'id' => 'textarea-field',
                    'label' => 'Champ de texte',
                    'placeholder' => 'Saisissez votre texte ici...',
                    'rows' => 5,
                    'required' => true
                ],
                'bind' => 'textarea-field'
            ],
            'RichTextarea' => [
                'id' => 'rich-textarea-component',
                'component' => 'RichTextarea',
                'props' => [
                    'id' => 'rich-text-field',
                    'label' => 'Éditeur de texte riche',
                    'placeholder' => 'Saisissez votre texte ici...',
                    'rows' => 10,
                    'required' => true
                ],
                'bind' => 'rich-text-field'
            ],
            'Select' => [
                'id' => 'select-component',
                'component' => 'Select',
                'props' => [
                    'id' => 'select-field',
                    'label' => 'Liste déroulante',
                    'options' => [
                        ['value' => 'option1', 'label' => 'Option 1'],
                        ['value' => 'option2', 'label' => 'Option 2'],
                        ['value' => 'option3', 'label' => 'Option 3']
                    ],
                    'defaultValue' => 'option1'
                ],
                'bind' => 'select-field'
            ],
            'Checkbox' => [
                'id' => 'checkbox-component',
                'component' => 'Checkbox',
                'props' => [
                    'id' => 'checkbox-field',
                    'label' => 'Case à cocher',
                    'defaultValue' => false
                ],
                'bind' => 'checkbox-field'
            ],
            'CheckboxGroup' => [
                'id' => 'checkbox-group-component',
                'component' => 'CheckboxGroup',
                'props' => [
                    'id' => 'checkbox-group-field',
                    'label' => 'Groupe de cases à cocher',
                    'options' => [
                        ['value' => 'option1', 'label' => 'Option 1'],
                        ['value' => 'option2', 'label' => 'Option 2'],
                        ['value' => 'option3', 'label' => 'Option 3']
                    ],
                    'defaultValue' => ['option1']
                ],
                'bind' => 'checkbox-group-field'
            ],
            'Radio' => [
                'id' => 'radio-component',
                'component' => 'Radio',
                'props' => [
                    'id' => 'radio-field',
                    'label' => 'Boutons radio',
                    'options' => [
                        ['value' => 'option1', 'label' => 'Option 1'],
                        ['value' => 'option2', 'label' => 'Option 2'],
                        ['value' => 'option3', 'label' => 'Option 3']
                    ],
                    'defaultValue' => 'option1'
                ],
                'bind' => 'radio-field'
            ],
            'FileUpload' => [
                'id' => 'file-upload-component',
                'component' => 'FileUpload',
                'props' => [
                    'id' => 'file-field',
                    'label' => 'Téléchargement de fichier',
                    'accept' => '*/*',
                    'required' => true,
                    'maxSize' => 10000000,
                    'dropzoneText' => 'Déposez votre fichier ici ou cliquez pour parcourir'
                ],
                'bind' => 'file-field'
            ],
            'Panel' => [
                'id' => 'panel-component',
                'component' => 'Panel',
                'props' => [
                    'title' => 'Panneau',
                    'collapsible' => true,
                    'expanded' => true
                ],
                'children' => []
            ],
            'Tabs' => [
                'id' => 'tabs-component',
                'component' => 'Tabs',
                'props' => [
                    'tabs' => [
                        ['id' => 'tab1', 'label' => 'Onglet 1'],
                        ['id' => 'tab2', 'label' => 'Onglet 2']
                    ]
                ],
                'children' => [
                    [
                        'id' => 'tab1-content',
                        'component' => 'TabPanel',
                        'props' => [
                            'tabId' => 'tab1'
                        ],
                        'children' => []
                    ],
                    [
                        'id' => 'tab2-content',
                        'component' => 'TabPanel',
                        'props' => [
                            'tabId' => 'tab2'
                        ],
                        'children' => []
                    ]
                ]
            ],
            'List' => [
                'id' => 'list-component',
                'component' => 'List',
                'props' => [
                    'items' => [
                        'Item 1',
                        'Item 2',
                        'Item 3'
                    ],
                    'type' => 'bullet'
                ]
            ]
        ];
    }

    /**
     * Récupère les modèles d'actions disponibles
     */
    public function getActionTemplates(): array
    {
        return [
            'navigate' => [
                'type' => 'navigate',
                'target' => 'target-page-id',
                'setValues' => [
                    'key' => 'value'
                ]
            ],
            'submit' => [
                'type' => 'submit',
                'target' => 'target-page-id'
            ],
            'copy' => [
                'type' => 'copy',
                'target' => 'field-id',
                'successMessage' => 'Copié dans le presse-papier !'
            ],
            'export' => [
                'type' => 'export',
                'target' => 'field-id',
                'options' => [
                    'filename' => 'export.txt',
                    'format' => 'text'
                ]
            ]
        ];
    }

    /**
     * Récupère les modèles d'API disponibles
     */
    public function getApiTemplates(): array
    {
        return [
            'standard' => [
                'endpoint' => '/api/endpoint',
                'method' => 'POST',
                'requestMapping' => [
                    'field1' => 'bind-name1',
                    'field2' => 'bind-name2'
                ],
                'responseMapping' => [
                    'result' => 'bind-result'
                ]
            ]
        ];
    }

    /**
     * Génère un modèle d'assistant vide
     */
    public function getEmptyAssistantTemplate(): array
    {
        return [
            'id' => 'new-assistant-' . uniqid(),
            'title' => 'Nouvel assistant',
            'description' => 'Description de l\'assistant',
            'pages' => [
                [
                    'id' => 'welcome',
                    'title' => 'Page d\'accueil',
                    'components' => [
                        [
                            'id' => 'welcome-text',
                            'component' => 'Text',
                            'props' => [
                                'content' => 'Bienvenue dans ce nouvel assistant !',
                                'variant' => 'heading',
                                'size' => 'large'
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }
}
