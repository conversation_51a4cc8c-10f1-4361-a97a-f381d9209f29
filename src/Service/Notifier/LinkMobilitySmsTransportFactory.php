<?php

namespace App\Service\Notifier;

use App\Entity\Enseigne;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Notifier\Exception\UnsupportedSchemeException;
use Symfony\Component\Notifier\Transport\AbstractTransportFactory;
use Symfony\Component\Notifier\Transport\Dsn;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

final class LinkMobilitySmsTransportFactory extends AbstractTransportFactory
{
    private RequestStack $requestStack;
    private string $linkMobilityName;

    public function __construct(RequestStack $requestStack, string $linkMobilityName, EventDispatcherInterface $dispatcher = null, HttpClientInterface $client = null)
    {
        parent::__construct($dispatcher, $client);
        $this->requestStack = $requestStack;
        $this->linkMobilityName = $linkMobilityName;
    }

    public function create(Dsn $dsn): LinkMobilitySmsTransport
    {
        $scheme = $dsn->getScheme();

        if ('linkmobility-sms' !== $scheme) {
            throw new UnsupportedSchemeException($dsn, 'linkmobility-sms', $this->getSupportedSchemes());
        }

        $user = $this->getUser($dsn);
        $from = $dsn->getRequiredOption('from');
        $host = 'default' === $dsn->getHost() ? null : $dsn->getHost();
        $port = $dsn->getPort();
        $enseigne = $this->requestStack->getCurrentRequest()->attributes->get('enseigne');

        $accp = null;
        if ($enseigne instanceof Enseigne) {
            $accp = $enseigne->getLinkMobilityAccp();
        }

        return (new LinkMobilitySmsTransport($user, $accp, $from, $this->client, $this->dispatcher))->setHost($host)->setPort($port)->setName($this->linkMobilityName);
    }

    protected function getSupportedSchemes(): array
    {
        return ['linkmobility-sms'];
    }
}
