<?php

namespace App\Service;

use App\Entity\Dashboard;
use App\Entity\Visualisation;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Finder\Finder;

class DashboardFixturesLoader
{
    private readonly string $samplesDir;

    public function __construct(
        string $projectDir,
        private readonly DataImporter $dataImporter,
        private readonly ManagerRegistry $doctrine
    )
    {
        $this->samplesDir = $projectDir . "/src/DataFixtures/Samples/dashboards";
    }

    public function getSamplePath(string $name): string
    {
        return sprintf("%s/%s.json", $this->samplesDir, $name);
    }

    public function getSample(string $name)
    {
        return json_decode(file_get_contents($this->getSamplePath($name)), true);
    }

    public function getAllSampleNames(): array
    {
        $finder = new Finder();
        $samples = [];
        foreach ($finder->files()->in($this->samplesDir)->name('*.json') as $file) {
            $samples[] = $file->getFilenameWithoutExtension();
        }
        return $samples;
    }

    public function getAllSamples(): array
    {
        $finder = new Finder();
        $samples = [];
        foreach ($finder->files()->in($this->samplesDir)->name('*.json') as $file) {
            $sample = ["name" => $file->getFilenameWithoutExtension()];
            $sample = array_merge($sample, json_decode($file->getContents(), true));
            $samples[] = $sample;
        }
        return $samples;
    }

    public function createDashboardsFromTemplates()
    {
        foreach ($this->getAllSampleNames() as $sampleName) {
            $this->createDashboardFromTemplate($sampleName);
        }
    }

    public static function getTemplateIdentifier($sampleName): string
    {
        return sprintf("_sample_%s", $sampleName);
    }

    public function createDashboardFromTemplate(string $sampleName)
    {
        $identifier = self::getTemplateIdentifier($sampleName);

        /** @var VisualisationRepository $visualisationRepo */
        $visualisationRepo = $this->doctrine->getRepository(Visualisation::class);

        $sample = $this->getSample($sampleName);
        $sample['identifier'] = $identifier;

        foreach ($sample["parameters"] as &$layout) {
            foreach ($layout as &$param) {
                $visualisation = $visualisationRepo->findOneBy([
                    'identifier' => VisualisationFixturesLoader::getTemplateIdentifier(...explode(":", $param['identifier']))
                ]);
                if ($visualisation instanceof Visualisation) {
                    $param["id"] = $visualisation->getId();
                } else {
                    unset($param);
                }
            }
        }

        $defaultEntities = $this->dataImporter->getDefaultEntities();

        $dashboard = $this->dataImporter->importDashboard(
            $defaultEntities['source'],
            $defaultEntities['groupe'],
            $defaultEntities['user'],
            ["dashboard" => $sample],
            false
        );

        /** @var Dashboard|null $existing */
        $existing = $this->doctrine->getRepository(Dashboard::class)->findOneByIdentifier($identifier);

        if ($existing) {
            $dashboard->setId($existing->getId());
            $this->doctrine->getManager()->merge($dashboard);
        } else {
            $this->doctrine->getManager()->persist($dashboard);
        }

        $this->doctrine->getManager()->flush();
    }
}