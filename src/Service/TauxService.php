<?php

namespace App\Service;

use App\Entity\Tache;
use App\Repository\HeureContratRepository;
use App\Repository\PosteRepository;
use App\Repository\TacheRepository;
use App\Repository\TauxRepository;

class TauxService
{

    private $tauxRepository;
    private $heureContratRepository;


    public function __construct(HeureContratRepository $heureContratRepository, TauxRepository  $tauxRepository)
    {
        $this->tauxRepository = $tauxRepository;
        $this->heureContratRepository = $heureContratRepository;
    }


    public function getTaux(Tache $tache): int
    {
        $poste = $tache->getPoste();
        $projet = $tache->getProjet();
        $etat = $projet->getEtat();
        $etatId = $etat->getId();
        $dateTache = $tache->getDate();
        $formattedDate = $dateTache->format('Y-m-d');

        $heureContrat = $this->heureContratRepository->findHeureContratsByProjetAndPoste($projet->getId(), $poste->getId());
        if ($heureContrat) {
            $taux = $heureContrat[0]->getTaux();
            // Si c'est devis émis le taux ne doit pas changer donc on garde le premier déclaré pour ce poste ?
            if ($etatId == 2) {
                return $taux;
            } else {
                $libellePoste = $this->tauxRepository->getLibelleByTaux($taux);
                $libelle = $libellePoste[0]->getLibelle();
                $taux = $this->tauxRepository->getTauxByPosteAndDate($formattedDate, $libelle);
                $taux = $taux[0]->getValeur();
                return $taux;
            }
        } else {
            return 0;
        }
    }
}
