<?php

namespace App\Service\OrchestrationApi\Dto;

/**
 * Base class for stream events
 */
abstract class StreamEvent
{
    /**
     * @param string $status The status of the event
     * @param \DateTimeInterface|null $created_at The date and time of the event
     */
    public function __construct(
        protected string $status,
        protected ?\DateTimeInterface $created_at = null,
    ) {
        if ($this->created_at === null) {
            $this->created_at = new \DateTimeImmutable();
        }
    }

    /**
     * Get the status of the event
     */
    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * Get the date and time of the event
     */
    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->created_at;
    }

    /**
     * Create a stream event from a JSON string
     */
    public static function fromJson(string $json): self
    {
        $data = json_decode($json, true);
        
        if (!$data) {
            throw new \InvalidArgumentException('Invalid JSON data');
        }

        return static::fromArray($data);
    }

    /**
     * Create a stream event from an array
     */
    abstract public static function fromArray(array $data): self;
}
