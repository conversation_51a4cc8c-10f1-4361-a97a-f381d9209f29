<?php

namespace App\Service;

use App\Entity\User;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Bundle\SecurityBundle\Security;

class ElasticAccessLogger
{

    public const LOG_TYPE = "_doc";
    public const FEATURE_KEY = "access_logger";
    private readonly ?string $userIp;
    private readonly bool $enabled;

    /**
     * ElasticAccessLogger constructor.
     * @param TokenStorageInterface $tokenStorage
     * @param $logsIndex
     */
    public function __construct(
        private readonly ElasticIndexManager $indexManager,
        FeatureManager $featureManager,
        RequestStack $requestStack,
        private readonly Security $security,
        private readonly string $logsIndex
    )
    {
        $this->enabled = $featureManager->hasFeature(self::FEATURE_KEY);
        $this->userIp = $requestStack->getMainRequest()->getClientIp();
    }

    /**
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    /**
     * @param string|null $type
     * @param string|null $identifier
     * @param string|null $label
     * @param User|null $user
     */
    public function addLog(?string $type, ?string $identifier = null, ?string $label = null, ?string $username = null): void
    {
        $user = $this->security->getUser();
        if (is_null($user)) {
            $user = $username;
        }
        $this->indexManager->addToIndex($this->logsIndex, self::LOG_TYPE, array($this->getLog($type, $identifier, $label, $user)));
    }

    /**
     * @param string|null $type
     * @param string|null $identifier
     * @param string|null $label
     * @param User|string|null $user
     * @return array
     */
    public function getLog(?string $type, ?string $identifier = null, ?string $label = null, $user = null): array
    {
        return array(
            "user"       => $user instanceof User ? $user->getUsername() : $user,
            "client"     => $user instanceof User ? sprintf("%s (%s)", $user->getClient()->getName(), $user->getClient()->getId()) : null,
            "groups"     => $user instanceof User ? $user->getAssignablesGroupesLabels() : null,
            "ip"         => $this->userIp,
            "date"       => (new \DateTime())->format("Y-m-d H:i:s"),
            "type"       => $type,
            "identifier" => $identifier,
            "label"      => $label
        );
    }

    public function createIndex(): void
    {
        $this->indexManager->createCustomIndex($this->logsIndex, array(
            self::LOG_TYPE => array(
                "properties" => array(
                    "user"       => array(
                        "type" => "keyword"
                    ),
                    "client"     => array(
                        "type" => "keyword"
                    ),
                    "groups"     => array(
                        "type" => "keyword"
                    ),
                    "ip"         => array(
                        "type" => "keyword"
                    ),
                    "date"       => array(
                        "type"   => "date",
                        "format" => "yyyy-MM-dd HH:mm:ss"
                    ),
                    "type"       => array(
                        "type" => "keyword"
                    ),
                    "identifier" => array(
                        "type" => "keyword"
                    ),
                    "label"      => array(
                        "type" => "keyword"
                    )
                )
            )
        ));
    }

}