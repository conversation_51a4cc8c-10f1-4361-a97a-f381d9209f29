<?php

namespace App\Form;

use App\Entity\Taux;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolverInterface;
use App\Repository\TauxRepository;
use Doctrine\ORM\EntityManagerInterface;
use App\Entity\Poste;

class HeureContratType extends AbstractType
{
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        // Query for 'libelle' field
        $tauxQueryBuilder = function (TauxRepository $er) {
            return $er->createQueryBuilder('t')
                ->select('t')
                ->orderBy('t.archive', 'ASC');
        };

        // Get the TauxRepository instance
        $tauxRepository = $this->entityManager->getRepository(Taux::class);

        // Use the query builder function to retrieve the results
        $query = $tauxQueryBuilder($tauxRepository);
        $results = $query->getQuery()->getResult();

        $tauxArray = [];
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $result = $results[$i];
            $string = "";
            if ($result->getArchive()) {
                $string = "[ARCHIVE] ";
            }
            $string .= $result->getLibelle() . " (" . strval($result->getValeur()) . "€/jour)";
            $tauxArray[$string] = $result->getValeur();
        }


        $postes = $this->entityManager->getRepository(Poste::class)->findAll();
        $postesChoices = [];
        foreach ($postes as $poste) {
            $postesChoices[$poste->getLibelle()] = $poste;
        }

        $builder
            ->add('nb', null, array(
                'label' => "Nombre d'heures",
            ))
            ->add('taux', ChoiceType::class, array(
                'required' => true,
                'choices' => $tauxArray,
                'label' => 'Taux',
                'choice_value' => function ($choice) {
                    return $choice;
                },
            ))
            ->add('poste', ChoiceType::class, array(
                'choices' => $postesChoices,
            ));
    }

    public function getName()
    {
        return 'alienor_timebundle_heurecontrattype';
    }
}
