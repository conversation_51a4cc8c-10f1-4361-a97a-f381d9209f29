<?php
namespace App\Form;

use App\Entity\Contrat;
use App\Entity\FiltreContrat;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class FiltreContratType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('projet', EntityType::class, array(
                'class' => Contrat::class,
                'query_builder' => function($repository) {
                    return $repository->createQueryBuilder('Contrat')
			->leftJoin("Contrat.societe", "societe")
			->OrderBy('societe.nom','ASC')
			->addOrderBy('Contrat.id','DESC');
                },
                'required' => true,
                'attr' => array(
                    'class' => 'chosen-select',
                    'data-placeholder' => 'Choisissez un projet',
                ),
            ))
            ->add('submit', SubmitType::class, array(
                'label' => 'Chercher',
                'attr' => array(
                    'class' => 'btn btn-primary',
                ),
            ))
        ;
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'data_class' => FiltreContrat::class
        ));
    }

    public function getName()
    {
        return 'filtrecontrattype';
    }
}

