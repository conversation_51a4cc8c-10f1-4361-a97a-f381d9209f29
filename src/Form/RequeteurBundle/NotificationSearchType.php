<?php

namespace App\Form\RequeteurBundle;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;

class NotificationSearchType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('keywords', TextType::class, [
                'label' => '',
            ])
            ->add('search', SubmitType::class, [
                'label' => 'global.save',
                'attr' => ['class' => 'btn btn--red btn--largePadding'],
            ]);
    }
}