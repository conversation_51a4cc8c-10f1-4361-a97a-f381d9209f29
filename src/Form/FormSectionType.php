<?php

namespace App\Form;

use App\Entity\FormSection;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class FormSectionType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('title', null, [
                'label' => 'Nom de la catégorie',
                'required' => true,
                'attr' => [
                    'placeholder' => 'Nom de la catégorie',
                ],
                'row_attr' => [
                    'class' => 'form-floating mb-3',
                ],
            ])
            ->add('description', TextareaType::class, [
                'label' => 'Description de la catégorie',
                'required' => false,
                'attr' => [
                    'placeholder' => 'Description de la catégorie',
                ],
                'row_attr' => [
                    'class' => 'form-floating mb-3',
                ],
            ])
            ->add('position', HiddenType::class, [
                'label' => 'Position',
                'required' => true,
                'attr' => ['class' => 'position'],
            ])
            ->add('fields', CollectionType::class, [
                'entry_type' => FormFieldType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
                'label' => 'Champs',
                'entry_options' => [
                    'enseigne' => $options['enseigne'],
                ],
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'enseigne' => null,
            'data_class' => FormSection::class,
        ]);
    }
}
