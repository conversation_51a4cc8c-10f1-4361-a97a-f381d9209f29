<?php

namespace App\Form;

use App\Dto\ParticipationColdV1;
use Craue\FormFlowBundle\Event\PostValidateEvent;
use Craue\FormFlowBundle\Form\FormFlowEvents;
use Craue\FormFlowBundle\Form\FormFlowInterface;

class ParticipationColdFlow extends AbstractParticipationFlow
{
    protected function loadStepsConfig(): array
    {
        foreach (range(1, 10) as $step) {
            $stepConfig = [
                'label' => 'answer_'.$step,
                'form_type' => SurveyColdAnswer1Type::class,
            ];

            /* Si la réponse à answer2 est "Autres", on affiche la page 3, sinon on la passe */
            if (3 === $step) {
                $stepConfig['skip'] = function ($estimatedCurrentStepNumber, FormFlowInterface $flow) {
                    return $estimatedCurrentStepNumber >= 3 && 'other' !== $flow->getFormData()->answer2;
                };
            }

            $steps[] = $stepConfig;
        }

        return $steps;
    }

    public function onPostValidate(PostValidateEvent $event): void
    {
        if ($this->isPreviewMode() || self::class !== $event->getFlow()::class) {
            return;
        }

        $completed = $event->getFlow()->getLastStepNumber() === $event->getFlow()->getCurrentStepNumber();
        /** @var ParticipationColdV1 $formData */
        $formData = $event->getFormData();
        if ($completed) {
            $formData->completed = true;
        }
        $this->participationPersister->saveParticipationCold($formData);
    }

    public function getDisplayedCurrentStepNumber(): int
    {
        return match ($this->getCurrentStepNumber()) {
            1 => 1,
            2, 3 => 2,
            4 => 3,
            5 => 4,
            6 => 5,
            7 => 6,
            8 => 7,
            9, 10 => 8,
        };
    }

    public function getDisplayedMaxSteps(): int
    {
        return $this->getStepCount() - 2;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            FormFlowEvents::POST_VALIDATE => 'onPostValidate',
        ];
    }
}
