<?php

namespace App\Form;

use App\Entity\FiltreSuivi;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class SuiviType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $user = $options['user'];

        $builder
            ->add('chef_de_projet', EntityType::class, array(
                'label' => 'Chef de projet',
                'class' => 'App\Entity\Personne',
                'query_builder' => function ($repository) {
                    return $repository->createQueryBuilder('personne')
                        ->where('personne.cp = 1');
                },
                'required' => false,
                'preferred_choices' => array(
                    $user,
                ),
            ))
            ->add('etat', EntityType::class, array(
                'label' => 'Signé avant vente',
                'class' => 'App\Entity\Etat',
                'query_builder' => function ($repository) {
                    return $repository->createQueryBuilder('e');
                },
                'required' => false,
            ))
            ->add('archive', ChoiceType::class, array(
                'choices' => array(
                    'Non' => 'non',
                    'Oui' => 'oui',
                ),
                'required' => false,
            ))
            ->add('date_mise_en_ligne', CheckboxType::class, array(
                'required' => false,
            ))
            ->add('date_fin_garantie', CheckboxType::class, array(
                'required' => false,
            ))
            ->add('submit', SubmitType::class, array(
                'label' => 'Chercher',
                'attr' => array(
                    'class' => 'btn btn-primary',
                ),
            ));
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'data_class' => FiltreSuivi::class,
            'user' => null,
        ));
    }

    public function getName()
    {
        return 'suivi';
    }
}
