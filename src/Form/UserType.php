<?php

namespace App\Form;

use App\Entity\Client;
use App\Entity\Groupe;
use App\Entity\Profil;
use App\Entity\User;
use App\Repository\ProfilRepository;
use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\FormType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\LanguageType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\RepeatedType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class UserType extends AbstractType
{
    private readonly UserInterface $user;

    public function __construct(
        TokenStorageInterface $tokenStorage,
        private readonly AuthorizationCheckerInterface $authorizationChecker,
        private readonly TranslatorInterface $translator
    ) {
        $this->user = $tokenStorage->getToken()->getUser();
    }

    public function buildForm(
        FormBuilderInterface $builder,
        array $options
    ): void {
        $ROLES = [
            "roles.magasin" => "ROLE_MAGASIN",
        ];
        if ($this->authorizationChecker->isGranted("ROLE_ADMIN")) {
            $ROLES["roles.admin"] = "ROLE_SUPER_ADMIN";
            $ROLES["roles.cc"] = "ROLE_CC";
            $ROLES["roles.enseigne"] = "ROLE_ENSEIGNE";
        } elseif ($this->authorizationChecker->isGranted("ROLE_CC")) {
            $ROLES["roles.enseigne"] = "ROLE_ENSEIGNE";
        }
        if ($this->authorizationChecker->isGranted("ROLE_ENSEIGNE")) {
            $ROLES["roles.structure"] = "ROLE_STRUCTURE";
        }

        /**
         * Informations personnelles
         */
        $builder->add(
            $builder
                ->create("infos", FormType::class, [
                    "inherit_data" => true,
                    "label" => "user.infos",
                ])
                ->add("username", null, ["label" => "user.username"])
                ->add("email", EmailType::class, ["label" => "user.email"])
                ->add("firstname", TextType::class, [
                    "label" => "user.firstname",
                    "required" => false,
                ])
                ->add("lastname", TextType::class, [
                    "label" => "user.lastname",
                    "required" => true,
                ])
                ->add("lang", LanguageType::class, [
                    "required" => false,
                    "label" => "groupe.form.lang",
                    "choices" => [$this->translator->trans("general.lang.en") => "en", $this->translator->trans("general.lang.fr") => "fr"],
                    "choice_loader" => null,
                ])
        );

        /**
         * Mot de passe
         */
        $builder->add(
            $builder
                ->create("password", FormType::class, [
                    "inherit_data" => true,
                    "label" => "user.password",
                ])
                ->add("plainPassword", RepeatedType::class, [
                    "type" => PasswordType::class,
                    "required" => $builder->getData()->getId() === null,
                    "first_options" => ["label" => "user.password"],
                    "second_options" => ["label" => "user.confirm"],
                ])
        );

        /**
         * Paramètres de connexion
         */
        $builder->add(
            $builder
                ->create("login_params", FormType::class, [
                    "inherit_data" => true,
                    "label" => "user.login_params",
                    "is_granted_attribute" => "ROLE_CC",
                ])
                ->add("concurrentLogin", IntegerType::class, [
                    "label" => "user.concurrentLogin",
                    "required" => false,
                    "help" => " ",
                    "is_granted_attribute" => "ROLE_ADMIN",
                ])
                ->add("desactivatedOn", DateType::class, [
                    "label" => "user.desactivatedOn",
                    "required" => false,
                    "widget" => "single_text",
                    "is_granted_attribute" => "ROLE_ADMIN",
                ])
        );

        /**
         * Groupes et permissions
         */
        $builder->add(
            $builder->create("permissions", FormType::class, [
                "inherit_data" => true,
                "label" => "user.permissions",
            ])
        );

        $clientOptions = [
            "placeholder" => "user.client_empty",
            "label" => "user.client",
            "class" => Client::class,
            "multiple" => false,
            "choice_attr" => function ($choice, $key, $value) {
                // ajoute un attribut (data-currentLogin) avec comme valeur le nbConnexion possible
                return [
                    "data-currentLogin" => $choice->getConcurrentLogin(),
                ];
            },
        ];

        $groupesOptions = [
            "required" => false,
            "attr" => ["class" => "multi-select"],
            "label" => "user.groupes",
            "class" => Groupe::class,
            "group_by" => "client",
            "multiple" => true,
        ];

        /***
         * Admin : Tous les client et tous les groupes
         * CC : Seulement ses clients et leurs groupes
         * Autre : Seulement les groupes de son client
         */
        if ($this->authorizationChecker->isGranted("ROLE_ADMIN")) {
            $builder
                ->get("permissions")
                ->add("client", EntityType::class, $clientOptions)
                ->add("groupes", EntityType::class, $groupesOptions);
        } elseif ($this->authorizationChecker->isGranted("ROLE_CC")) {
            $builder
                ->get("permissions")
                ->add(
                    "client",
                    EntityType::class,
                    array_merge($clientOptions, [
                        "query_builder" => function (EntityRepository $er) {
                            $ids = $this->user
                                ->getCcClients()
                                ->map(function (Client $client) {
                                    return $client->getId();
                                })
                                ->toArray();
                            $ids[] = $this->user->getClient()->getId();
                            return $er
                                ->createQueryBuilder("c")
                                ->where("c.id IN (:ids)")
                                ->setParameter("ids", $ids);
                        },
                    ])
                )
                ->add(
                    "groupes",
                    EntityType::class,
                    array_merge($groupesOptions, [
                        "query_builder" => function (EntityRepository $er) {
                            $ids = $this->user
                                ->getCcClients()
                                ->map(function (Client $client) {
                                    return $client->getId();
                                })
                                ->toArray();
                            $ids[] = $this->user->getClient()->getId();
                            return $er
                                ->createQueryBuilder("g")
                                ->innerJoin("g.client", "c")
                                ->where("c.id IN (:ids)")
                                ->setParameter("ids", $ids);
                        },
                    ])
                );
        } else {
            $builder->get("permissions")->add(
                "groupes",
                EntityType::class,
                array_merge($groupesOptions, [
                    "query_builder" => function (EntityRepository $er) {
                        return $er
                            ->createQueryBuilder("g")
                            ->where("g.id = :id")
                            ->orWhere("g.client = :id")
                            ->setParameter(
                                "id",
                                $this->user->getClient()->getId()
                            );
                    },
                ])
            );
        }

        $isSelfEditing = $this->user === $builder->getData();

        $builder
            ->get("permissions")
            ->add("roles", ChoiceType::class, [
                "label" => "user.roles",
                "multiple" => true,
                "choices" => $ROLES,
                "translation_domain" => "messages",
                "attr" => ["class" => "multi-select-roles"],
                "is_granted_condition" => !$isSelfEditing,
                "is_granted_attribute" => "ROLE_CC",
            ])
            ->add("ccClients", EntityType::class, [
                "required" => false,
                "attr" => ["class" => "multi-select"],
                "label" => "user.ccClient",
                "class" => Client::class,
                "multiple" => true,
                "is_granted_attribute" => "ROLE_ADMIN",
            ])
            ->add("profil", EntityType::class, [
                "label" => "user.profile.form_field",
                "required" => true,
                "class" => Profil::class,
                "query_builder" => function (ProfilRepository $er) {
                    return $er->findAssignablesQuery($this->user);
                },
                "is_granted_condition" => !$isSelfEditing,
                "is_granted_attribute" => "ROLE_CC",
            ]);

        if (
            $isSelfEditing &&
            !$this->authorizationChecker->isGranted("ROLE_ADMIN")
        ) {
            $builder->get("permissions")->remove("client");
            $builder->get("permissions")->remove("groupes");
        }
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            "data_class" => User::class,
        ]);
    }
}
