<script>
	import Translator from "bazinga-translator";
	import Dropdown from "../dropdown/Dropdown.svelte";
	import DropdownSearch from "../dropdown/DropdownSearch.svelte";
	import DropdownButtons from "../dropdown/DropdownButtons.svelte";
	import DetailedList from "./DetailedList.svelte";
	import { writable } from "svelte/store";
	let T = Translator;

	// PROPS
	/** @type pieChart */
	export let chart;
	/** @type Writable<string[]> */
	export let selected = writable([]);
	export let onRemove = () => {};

	/** @type string */
	let search = "";
	/** @type PieChartNode[] */
	let values = [];


	// REACTIVE STATEMENTS
	$: values = chart?.nodes?.[0]?.children ?? [];

	// EVENTS

	// Lors du clic sur 'Supprimer le filtre'
	function handleRemove() {
		if ($selected?.length) {
			$selected = [];
			onRemove();
		}
	}

	/**
	 * @param {PieChartNode} d
	 */
	export let onMouseEnter = (d) => {}

	/**
	 * @param {PieChartNode} d
	 */
	export let onMouseLeave = (d) => {}
</script>

<Dropdown fullWidth={false} align="right" minWidth={300} selectedLabel="{chart.trans('global.legend')}" menuClasses="dropdown-menu-end" {chart}>
    {#if values.length >= 5}
        <DropdownSearch bind:search={search}/>
    {/if}
    <DropdownButtons buttons="{[
        { label: chart.trans('global.filter.remove'), callback: handleRemove, disabled: $selected.length === 0 },
    ]}"/>
    <div class="dropdown-legend-menu-container p-2">
        <DetailedList {...chart.getDetailedListProps(false)} {search} {onMouseEnter} {onMouseLeave} inLegend/>
    </div>
</Dropdown>

<style>
    /* Copié depuis le dropdown depuis la légende du lineChart, à uniformiser */
    :global(.pieChart .dropdown-toggle, .pieChart .dropdown-toggle.show) {
        font-size: .75rem;
        border-color: #ddd;
        color: #000;
        padding: 8px 20px;
        display: flex;
        align-items: center;
        background-color: unset
    }

    .dropdown-legend-menu-container {
        max-height: 300px;
        overflow-y: auto;
    }
</style>
