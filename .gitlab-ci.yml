image: gitlab.alienor.net:5050/dev-projets-aquitem/ma-carte-fid-v3:api-dev

variables:
    DEPLOY: "false"

default:
    tags:
        - anetdev

cache:
    - key:
          files:
              - api/composer.lock
          prefix: "$CI_COMMIT_REF_SLUG"
      paths:
          - api/vendor/
    - key:
          files:
              - pwa/yarn.lock
      paths:
          - pwa/node_modules/
          - pwa/.yarn/
    - paths:
        - /root/.cache/ms-playwright/

stages:
    - build-image-dev
    - build
    - test
    - build-image-preprod
    - build-image-prod
    - e2e
    - deploy
    - analyze
    - sonarqube-check

build-image-dev:
    stage: build-image-dev
    image: docker:latest
    variables:
        DOCKER_TLS_CERTDIR: "/certs"
    before_script:
        - mkdir -p $HOME/.docker
        - echo $DOCKER_AUTH_CONFIG > $HOME/.docker/config.json
    script:
        - docker compose build php
        - echo "Image build successfully"
        - docker compose push php
        - echo "Image push successfully"

build:
    stage: build
    needs:
        - job: build-image-dev
          optional: true
    script:
        - cd api
        - composer install
php-cs-fixer:
    stage: test
    needs:
        - job: build
          optional: true
    script:
        - cd api
        - composer install
        - PHP_CS_FIXER_IGNORE_ENV=1 ./vendor/bin/php-cs-fixer fix --dry-run --diff

phpunit:
    stage: test
    needs:
        - job: build
          optional: true
    variables:
        DATABASE_URL: '*****************************************/app?serverVersion=15'
        XDEBUG_MODE: coverage
    services:
        - name: postgres:15-alpine
          alias: database
          variables:
              POSTGRES_DB: app
              POSTGRES_PASSWORD: '!ChangeMe!'
              POSTGRES_USER: app
    script:
        - cd api
        - composer install
        - php bin/console lexik:jwt:generate-keypair --overwrite
        - php bin/console doctrine:database:drop --force --if-exists --env=test
        - php bin/console doctrine:database:create --if-not-exists --env=test
        - php bin/console doctrine:migration:migrate --no-interaction --env=test
        - php vendor/bin/phpunit --testdox --coverage-clover=coverage.xml  --log-junit report.xml
    artifacts:
        when: always
        paths:
            - api/coverage.xml
            - api/var/browser/source
            - api/var/log
        reports:
            junit: api/report.xml
        expire_in: 30 minutes

build-php-image-preprod:
    stage: build-image-preprod
    image: docker:latest
    needs:
        - job: phpunit
          optional: true
    variables:
        IMAGE_TAG: preprod
        DOCKER_TLS_CERTDIR: "/certs"
    before_script:
        - mkdir -p $HOME/.docker
        - echo $DOCKER_AUTH_CONFIG > $HOME/.docker/config.json
    script:
        - docker compose -f compose.yaml -f compose.prod.yaml build php
        - echo "Image build successfully"
        - docker compose -f compose.yaml -f compose.prod.yaml push php
        - echo "Image push successfully"
    only:
        - preprod

build-pwa-image-preprod:
    stage: build-image-preprod
    image: docker:latest
    needs:
        - job: phpunit
          optional: true
    variables:
        IMAGE_TAG: preprod
        DOCKER_TLS_CERTDIR: "/certs"
    before_script:
        - mkdir -p $HOME/.docker
        - echo $DOCKER_AUTH_CONFIG > $HOME/.docker/config.json
    script:
        - docker compose -f compose.yaml -f compose.prod.yaml build pwa
        - echo "Image build successfully"
        - docker compose -f compose.yaml -f compose.prod.yaml push pwa
        - echo "Image push successfully"
    only:
        - preprod

build-php-image-prod:
    stage: build-image-prod
    image: docker:latest
    needs:
        - job: phpunit
          optional: true
    variables:
        IMAGE_TAG: prod
        DOCKER_TLS_CERTDIR: "/certs"
    before_script:
        - mkdir -p $HOME/.docker
        - echo $DOCKER_AUTH_CONFIG > $HOME/.docker/config.json
    script:
        - docker compose -f compose.yaml -f compose.prod.yaml build php
        - echo "Image build successfully"
        - docker compose -f compose.yaml -f compose.prod.yaml push php
        - echo "Image push successfully"
    only:
        - main

build-pwa-image-prod:
    stage: build-image-prod
    image: docker:latest
    needs:
        - job: phpunit
          optional: true
    variables:
        IMAGE_TAG: prod
        DOCKER_TLS_CERTDIR: "/certs"
    before_script:
        - mkdir -p $HOME/.docker
        - echo $DOCKER_AUTH_CONFIG > $HOME/.docker/config.json
    script:
        - docker compose -f compose.yaml -f compose.prod.yaml build pwa
        - echo "Image build successfully"
        - docker compose -f compose.yaml -f compose.prod.yaml push pwa
        - echo "Image push successfully"
    only:
        - main

playwright:
    stage: e2e
    needs:
        - job: build-php-image-prod
          optional: true
        - job: build-pwa-image-prod
          optional: true
    image: node:22-bookworm
    variables:
        FF_NETWORK_PER_BUILD: 1
        NODE_TLS_REJECT_UNAUTHORIZED: 0
    services:
        - name: postgres:15-alpine
          alias: database
          variables:
              POSTGRES_DB: app
              POSTGRES_PASSWORD: '!ChangeMe!'
              POSTGRES_USER: app
        - name: gitlab.alienor.net:5050/dev-projets-aquitem/ma-carte-fid-v3:api-prod
          alias: api
          variables:
              APP_ENV: dev
              DATABASE_URL: '*****************************************/app?serverVersion=15'
              PLAYWRIGHT_ENV: true
              MAILER_DSN: 'null://null'
              USE_MAIL_NOTIFIER: true
              SERVER_NAME: "http://api, php:80"
              TRUSTED_HOSTS: "^(localhost|caddy|api)$"
              PWA_UPSTREAM: "pwa:3000"
              MERCURE_PUBLISHER_JWT_KEY: "!ChangeThisMercureHubJWTSecretKey!"
              CADDY_MERCURE_JWT_SECRET: "!ChangeThisMercureHubJWTSecretKey!"
              MERCURE_SUBSCRIBER_JWT_KEY: "!ChangeThisMercureHubJWTSecretKey!"
        - name: gitlab.alienor.net:5050/dev-projets-aquitem/ma-carte-fid-v3:pwa-prod
          alias: pwa
          command: ['node', 'build']
    script:
        - echo "Waiting for API to be ready..."
        - |
            for i in {1..10}; do 
              if curl -sSf http://api/docs; then 
                echo "API is ready!"; 
                break; 
              fi
              echo "Waiting for API... ($i/30)"
              sleep 5
            done
        - cd pwa
        - yarn install --frozen-lockfile
        - npx playwright install --with-deps chromium
        - export NODE_TLS_REJECT_UNAUTHORIZED=0
        - npx playwright test
    artifacts:
        when: always
        paths:
            - pwa/playwright-report/
            - pwa/test-results/
    only:
        - feature/symfony-update
    allow_failure: true

deploy-preprod:
    image: gitlab.alienor.net:5050/dev-docker/docker-tools
    stage: deploy
    needs:
        -   job: build-php-image-preprod
            optional: true
        -   job: build-pwa-image-preprod
            optional: true
    environment:
        name: staging
        url: https://preprod.macartefid.fr
    variables:
        GIT_STRATEGY: none
    script:
        - docker-tools update aquitem-preprod web-4656_front_web -i gitlab.alienor.net:5050/dev-projets-aquitem/ma-carte-fid-v3:api-preprod --detach
        - docker-tools update aquitem-preprod web-4656_pwa -i gitlab.alienor.net:5050/dev-projets-aquitem/ma-carte-fid-v3:pwa-preprod --detach
        - docker-tools update aquitem-preprod web-4656_scheduler -i gitlab.alienor.net:5050/dev-projets-aquitem/ma-carte-fid-v3:api-preprod --detach
        - docker-tools update aquitem-preprod web-4656_consumer -i gitlab.alienor.net:5050/dev-projets-aquitem/ma-carte-fid-v3:api-preprod --detach
    when: manual
    only:
        - preprod

deploy-prod:
    image: gitlab.alienor.net:5050/dev-docker/docker-tools
    stage: deploy
    needs:
        - job: build-php-image-prod
          optional: true
        - job: build-pwa-image-prod
          optional: true
    environment:
        name: production
        url: https://app.macartefid.fr
    variables:
        GIT_STRATEGY: none
    script:
        - docker-tools update aquitem-prod web-3182_temp_front_web -i gitlab.alienor.net:5050/dev-projets-aquitem/ma-carte-fid-v3:api-prod --detach
        - docker-tools update aquitem-prod web-3182_temp_pwa -i gitlab.alienor.net:5050/dev-projets-aquitem/ma-carte-fid-v3:pwa-prod --detach
        - docker-tools update aquitem-prod web-3182_temp_scheduler -i gitlab.alienor.net:5050/dev-projets-aquitem/ma-carte-fid-v3:api-prod --detach
        - docker-tools update aquitem-prod web-3182_temp_consumer -i gitlab.alienor.net:5050/dev-projets-aquitem/ma-carte-fid-v3:api-prod --detach
    when: manual
    only:
        - main

security-checker:
    stage: analyze
    needs:
        - job: build
          optional: true
    image: jakzal/phpqa:php8.2
    script:
        - local-php-security-checker --path=./api/composer.lock --format=junit > local-php-security-checker.xml
    allow_failure: true
    artifacts:
        when: always
        paths:
            - local-php-security-checker.xml
        reports:
            junit: local-php-security-checker.xml
        expire_in: 30 minutes

sonarqube-check:
    stage: sonarqube-check
    needs:
        - job: phpunit
          optional: true
    image:
        name: sonarsource/sonar-scanner-cli:5.0
        entrypoint: [""]
    variables:
        SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
        GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
    cache:
        key: "${CI_JOB_NAME}"
        paths:
            - .sonar/cache
    script:
        - sonar-scanner
    allow_failure: true
    only:
        - merge_requests
        - master
        - main
        - develop
        - ci
