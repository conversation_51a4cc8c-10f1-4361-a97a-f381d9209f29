{"type": "project", "license": "proprietary", "minimum-stability": "dev", "prefer-stable": true, "require": {"php": ">=8.1", "ext-ctype": "*", "ext-iconv": "*", "doctrine/annotations": "^1.0", "doctrine/doctrine-bundle": "^2.7", "doctrine/doctrine-migrations-bundle": "^3.2", "doctrine/orm": "^2.13", "karser/karser-recaptcha3-bundle": "^0.1.23", "knplabs/knp-snappy-bundle": "^1.9", "mikehaertl/php-pdftk": "^0.12.1", "phpdocumentor/reflection-docblock": "^5.3", "phpstan/phpdoc-parser": "^1.13", "sensio/framework-extra-bundle": "^6.2", "silvertipsoftware/wkhtmltopdf-amd64": "^0.12.5", "symfony/apache-pack": "^1.0", "symfony/asset": "6.3.*", "symfony/console": "6.3.*", "symfony/dotenv": "6.3.*", "symfony/expression-language": "6.3.*", "symfony/fake-sms-notifier": "6.3.*", "symfony/flex": "^2", "symfony/form": "6.3.*", "symfony/framework-bundle": "6.3.*", "symfony/html-sanitizer": "6.3.*", "symfony/http-client": "6.3.*", "symfony/intl": "6.3.*", "symfony/lock": "6.3.*", "symfony/mailer": "6.3.*", "symfony/mime": "6.3.*", "symfony/monolog-bundle": "^3.0", "symfony/notifier": "6.3.*", "symfony/process": "6.3.*", "symfony/property-access": "6.3.*", "symfony/property-info": "6.3.*", "symfony/proxy-manager-bridge": "6.3.*", "symfony/rate-limiter": "6.3.*", "symfony/runtime": "6.3.*", "symfony/security-bundle": "6.3.*", "symfony/serializer": "6.3.*", "symfony/string": "6.3.*", "symfony/translation": "6.3.*", "symfony/twig-bundle": "6.3.*", "symfony/uid": "6.3.*", "symfony/ux-turbo": "2.5.0", "symfony/validator": "6.3.*", "symfony/web-link": "6.3.*", "symfony/webpack-encore-bundle": "^1.16", "symfony/yaml": "6.3.*", "twig/cssinliner-extra": "^3.4", "twig/extra-bundle": "^2.12|^3.0", "twig/twig": "^2.12|^3.0"}, "config": {"allow-plugins": {"symfony/flex": true, "symfony/runtime": true}, "optimize-autoloader": true, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"], "php-cs-fixer": "php-cs-fixer --config=./php-cs-fixer.dist.php"}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "6.3.*"}}, "require-dev": {"doctrine/doctrine-fixtures-bundle": "^3.4", "friendsofphp/php-cs-fixer": "^3.13", "phpunit/phpunit": "^9.5", "symfony/browser-kit": "6.3.*", "symfony/css-selector": "6.3.*", "symfony/debug-bundle": "6.3.*", "symfony/maker-bundle": "^1.47", "symfony/phpunit-bridge": "^6.1", "symfony/stopwatch": "6.3.*", "symfony/web-profiler-bundle": "6.3.*"}}