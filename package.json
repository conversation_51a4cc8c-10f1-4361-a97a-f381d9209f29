{"name": "alienor-charts", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "watch": "webpack --watch", "build": "webpack"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/d3": "^6.3.0", "bootstrap": "3.3.4", "eslint": "^7.25.0", "lodash": "^4.17.21", "moment": "^2.29.1", "webpack": "^5.36.2", "webpack-bundle-analyzer": "^4.4.1", "webpack-cli": "^4.6.0"}, "dependencies": {"bootstrap-tagsinput": "0.5.0", "d3": "^6.7.0", "d3-cloud": "^1.2.5", "d3-sankey": "^0.12.3", "d3-svg-annotation": "^2.5.1", "eslint-webpack-plugin": "^2.5.4", "leaflet": "1.3.1", "leaflet-draw": "0.4.14", "leaflet-fullscreen": "^1.0.2", "leaflet-webgl-heatmap": "^0.2.7", "list.js": "^2.3.1", "marked": "^2.0.3", "moment-locales-webpack-plugin": "^1.2.0", "moment-timezone": "^0.5.33", "numbro": "^2.3.2", "wellknown": "^0.5.0"}}